import { Body, Controller, Delete, Get, Param, Post, Put, UseGuards, Req, Query } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RegisterDeviceDto } from './dto/register-device.dto';
import { UpdateNotificationPreferencesDto } from './dto/update-notification-preferences.dto';
import { FilterNotificationsDto, PaginatedNotificationsResponseDto } from './dto';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags, ApiQuery } from '@nestjs/swagger';
import { DeviceTokensRepository } from './repositories/device-tokens.repository';
import { NotificationPreferencesRepository } from './repositories/notification-preferences.repository';
import { NotificationHistoryRepository } from './repositories/notification-history.repository';
import { NotificationType } from './types/notification-types';

@ApiTags('notifications')
@Controller('api/notifications')
@ApiBearerAuth()
export class NotificationsController {
  constructor(
    private readonly notificationsService: NotificationsService,
    private readonly deviceTokensRepository: DeviceTokensRepository,
    private readonly notificationPreferencesRepository: NotificationPreferencesRepository,
    private readonly notificationHistoryRepository: NotificationHistoryRepository,
  ) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get all notifications for the current user with pagination' })
  @ApiResponse({
    status: 200,
    description: 'Returns paginated list of user notifications',
    type: PaginatedNotificationsResponseDto
  })
  @ApiQuery({ name: 'type', required: false, description: 'Filter by notification type' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by notification status' })
  @ApiQuery({ name: 'search', required: false, description: 'Search in title and body' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Sort field' })
  @ApiQuery({ name: 'sortOrder', required: false, description: 'Sort order (ASC/DESC)' })
  async getAllNotifications(
    @Req() req,
    @Query() filters: FilterNotificationsDto
  ): Promise<PaginatedNotificationsResponseDto> {
    const userId = req.user.id;
    return this.notificationHistoryRepository.findByUser(userId, filters);
  }

  @Post('register-device')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Register a device token for push notifications' })
  @ApiResponse({
    status: 201,
    description: 'Device token registered successfully'
  })
  async registerDevice(
    @Req() req,
    @Body() registerDeviceDto: RegisterDeviceDto
  ) {
    const userId = req.user.id;
    const deviceToken = await this.deviceTokensRepository.registerDevice(
      userId,
      registerDeviceDto
    );

    return {
      message: 'Device registered successfully',
      deviceId: deviceToken.id
    };
  }

  @Get('preferences')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get user notification preferences' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns user notification preferences' 
  })
  async getNotificationPreferences(
    @Req() req
  ) {
    const userId = req.user.id;
    return this.notificationPreferencesRepository.getPreferences(userId);
  }

  @Put('preferences')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Update user notification preferences' })
  @ApiResponse({ 
    status: 200, 
    description: 'Notification preferences updated successfully' 
  })
  async updateNotificationPreferences(
    @Req() req,
    @Body() updatePreferencesDto: UpdateNotificationPreferencesDto
  ) {
    const userId = req.user.id;
    const preferences = await this.notificationPreferencesRepository.updatePreferences(
      userId, 
      updatePreferencesDto
    );
    
    return { 
      message: 'Notification preferences updated successfully',
      preferences
    };
  }

  @Delete('devices/:deviceId')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Remove a device' })
  @ApiResponse({ 
    status: 200, 
    description: 'Device removed successfully' 
  })
  async removeDevice(
    @Req() req,
    @Param('deviceId') deviceId: string
  ) {
    const userId = req.user.id;
    await this.deviceTokensRepository.deleteDevice(userId, deviceId);
    
    return { message: 'Device removed successfully' };
  }
  
  @Post('test-notification')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Send a test notification to the user\'s devices' })
  @ApiResponse({ 
    status: 200, 
    description: 'Test notification sent successfully' 
  })
  async sendTestNotification(
    @Req() req,
    @Body() body: { title?: string; body?: string }
  ) {
    const userId = req.user.id;
    const title = body.title || 'Test Notification';
    const notificationBody = body.body || 'This is a test notification from Power Up!';
    
    await this.notificationsService.sendNotificationWithPreferences(
      userId, 
      NotificationType.TASK_REMINDER,
      {
        title,
        body: notificationBody,
        type: NotificationType.TASK_REMINDER,
        data: {
          test: 'true',
        },
      }
    );
    
    return { message: 'Test notification sent successfully' };
  }
  
  @Get('devices')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get all registered devices for the user' })
  @ApiResponse({
    status: 200,
    description: 'Returns user\'s registered devices'
  })
  async getUserDevices(
    @Req() req
  ) {
    const userId = req.user.id;
    const devices = await this.deviceTokensRepository.getActiveDeviceTokens(userId);

    return devices;
  }

  @Put(':id/read')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Mark a notification as read' })
  @ApiResponse({
    status: 200,
    description: 'Notification marked as read successfully'
  })
  async markAsRead(@Req() req, @Param('id') id: string) {
    const userId = req.user.id;
    const notification = await this.notificationHistoryRepository.markAsRead(id, userId);
    return {
      message: 'Notification marked as read',
      notification
    };
  }
}
