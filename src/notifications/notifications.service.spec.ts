import { Test, TestingModule } from '@nestjs/testing';
import { NotificationsService } from './notifications.service';
import { FirebaseService } from '../firebase/firebase.service';
import { DeviceTokensRepository } from './repositories/device-tokens.repository';
import { NotificationPreferencesRepository } from './repositories/notification-preferences.repository';
import { UsersService } from '../users/users.service';
import { NotificationType } from './types/notification-types';

describe('NotificationsService', () => {
  let service: NotificationsService;
  let firebaseService: FirebaseService;
  let deviceTokensRepository: DeviceTokensRepository;
  let notificationPreferencesRepository: NotificationPreferencesRepository;
  let usersService: UsersService;

  const mockFirebaseService = {
    sendPushNotification: jest.fn(),
    sendPushNotificationToMultipleDevices: jest.fn(),
  };

  const mockDeviceTokensRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    createDeviceToken: jest.fn(),
    getActiveDeviceTokens: jest.fn(),
    removeDeviceToken: jest.fn(),
    registerDevice: jest.fn(),
  };

  const mockNotificationPreferencesRepository = {
    findOne: jest.fn(),
    findByUserId: jest.fn(),
    createDefaultPreferences: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
    getPreferences: jest.fn(),
  };

  const mockUsersService = {
    findOne: jest.fn(),
  };

  const defaultPreferences = {
    taskReminders: true,
    habitReminders: true,
    streakAlerts: true,
    milestoneCelebrations: true,
    challengeUpdates: true,
    newMessages: true,
    podcastReady: true,
  };

  beforeEach(async () => {
    jest.clearAllMocks();
    
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationsService,
        {
          provide: FirebaseService,
          useValue: mockFirebaseService,
        },
        {
          provide: DeviceTokensRepository,
          useValue: mockDeviceTokensRepository,
        },
        {
          provide: NotificationPreferencesRepository,
          useValue: mockNotificationPreferencesRepository,
        },
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
      ],
    }).compile();

    service = module.get<NotificationsService>(NotificationsService);
    firebaseService = module.get<FirebaseService>(FirebaseService);
    deviceTokensRepository = module.get<DeviceTokensRepository>(DeviceTokensRepository);
    notificationPreferencesRepository = module.get<NotificationPreferencesRepository>(NotificationPreferencesRepository);
    usersService = module.get<UsersService>(UsersService);

    // Default mock implementations
    mockNotificationPreferencesRepository.getPreferences.mockResolvedValue(defaultPreferences);
    mockDeviceTokensRepository.registerDevice.mockImplementation((userId, data) => 
      Promise.resolve({ id: 'device-1', userId, ...data }));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendToUser', () => {
    const userId = 'user-id';
    const deviceToken = 'device-token';
    const notification = {
      title: 'Test Notification',
      body: 'This is a test notification',
      type: NotificationType.TASK_REMINDER,
      data: {}  // Add empty data object as it's required by interface
    };

    beforeEach(() => {
      mockDeviceTokensRepository.getActiveDeviceTokens.mockResolvedValue([
        { token: deviceToken, userId, isActive: true }
      ]);
      mockNotificationPreferencesRepository.getPreferences.mockResolvedValue(defaultPreferences);
      mockFirebaseService.sendPushNotification.mockResolvedValue(undefined);
    });

    it('should send notification to user device', async () => {
      await service.sendToUser(userId, deviceToken, notification);

      expect(mockFirebaseService.sendPushNotification).toHaveBeenCalledWith(
        deviceToken,
        expect.objectContaining({
          notification: {
            title: notification.title,
            body: notification.body,
          },
          data: expect.objectContaining({
            type: notification.type,
            userId,
          })
        })
      );
    });

    it('should handle notification send error', async () => {
      const error = new Error('Failed to send notification');
      mockFirebaseService.sendPushNotification.mockRejectedValue(error);

      await expect(service.sendToUser(userId, deviceToken, notification))
        .rejects.toThrow('Failed to send notification');
    });
  });

  describe('sendToUserDevices', () => {
    const userId = 'user-id';
    const deviceTokens = [
      { deviceToken: 'token1', deviceType: 'ios', isActive: true },
      { deviceToken: 'token2', deviceType: 'android', isActive: true },
    ];
    const notification = {
      title: 'Test Notification',
      body: 'This is a test notification',
      type: NotificationType.TASK_REMINDER,
      data: {}
    };

    it('should send notifications to all user devices if preferences allow it', async () => {
      mockNotificationPreferencesRepository.getPreferences.mockResolvedValue(defaultPreferences);
      mockDeviceTokensRepository.getActiveDeviceTokens.mockResolvedValue(deviceTokens);
      mockFirebaseService.sendPushNotificationToMultipleDevices.mockResolvedValue({
        successCount: 2,
        failureCount: 0,
      });

      const result = await service.sendToUserDevices(userId, notification);

      expect(mockNotificationPreferencesRepository.getPreferences).toHaveBeenCalledWith(userId);
      expect(mockDeviceTokensRepository.getActiveDeviceTokens).toHaveBeenCalledWith(userId);
      expect(mockFirebaseService.sendPushNotificationToMultipleDevices).toHaveBeenCalledWith(
        deviceTokens.map(d => d.deviceToken),
        expect.objectContaining({
          notification: {
            title: notification.title,
            body: notification.body,
          },
          data: expect.objectContaining({
            type: notification.type,
            userId,
          })
        })
      );
      expect(result).toEqual({ successCount: 2, failureCount: 0 });
    });

    it('should not send notification if notification type is disabled in preferences', async () => {
      mockNotificationPreferencesRepository.getPreferences.mockResolvedValue({
        ...defaultPreferences,
        taskReminders: false
      });

      const result = await service.sendToUserDevices(userId, notification);

      expect(mockNotificationPreferencesRepository.getPreferences).toHaveBeenCalledWith(userId);
      expect(mockDeviceTokensRepository.getActiveDeviceTokens).not.toHaveBeenCalled();
      expect(mockFirebaseService.sendPushNotificationToMultipleDevices).not.toHaveBeenCalled();
      expect(result).toBeNull();
    });
  });

  describe('registerDeviceToken', () => {
    const userId = 'user-id';
    const deviceToken = 'device-token';
    const deviceType = 'ios';
    const deviceName = 'iPhone 12';

    it('should create a new device token if it does not exist', async () => {
      mockDeviceTokensRepository.findOne.mockResolvedValue(null);
      const createdToken = {
        id: 'device-1',
        userId,
        deviceToken,
        deviceType,
        deviceName,
      };
      mockDeviceTokensRepository.registerDevice.mockResolvedValue(createdToken);

      const result = await service.registerDeviceToken(
        userId,
        deviceToken,
        deviceType,
        deviceName
      );

      expect(result).toEqual(createdToken);
      expect(mockDeviceTokensRepository.registerDevice).toHaveBeenCalledWith(userId, {
        deviceToken,
        deviceType,
        deviceName,
      });
    });

    it('should update an existing device token if it belongs to the same user', async () => {
      const existingToken = {
        id: 'device-1',
        userId,
        deviceToken,
        deviceType: 'android',
        deviceName: 'Old Device',
      };
      const updatedToken = {
        ...existingToken,
        deviceType,
        deviceName,
      };
      mockDeviceTokensRepository.findOne.mockResolvedValue(existingToken);
      mockDeviceTokensRepository.registerDevice.mockResolvedValue(updatedToken);

      const result = await service.registerDeviceToken(
        userId,
        deviceToken,
        deviceType,
        deviceName
      );

      expect(result).toEqual(updatedToken);
      expect(mockDeviceTokensRepository.registerDevice).toHaveBeenCalledWith(userId, {
        deviceToken,
        deviceType,
        deviceName,
      });
    });


  });
});
