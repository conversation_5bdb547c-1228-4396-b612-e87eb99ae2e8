import { Test, TestingModule } from '@nestjs/testing';
import { NotificationsController } from './notifications.controller';
import { NotificationsService } from './notifications.service';
import { DeviceTokensRepository } from './repositories/device-tokens.repository';
import { NotificationPreferencesRepository } from './repositories/notification-preferences.repository';
import { NotificationHistoryRepository } from './repositories/notification-history.repository';
import { FilterNotificationsDto } from './dto';

describe('NotificationsController', () => {
  let controller: NotificationsController;
  let notificationHistoryRepository: NotificationHistoryRepository;

  const mockNotificationHistoryRepository = {
    findByUser: jest.fn(),
    markAsRead: jest.fn(),
  };

  const mockNotificationsService = {};
  const mockDeviceTokensRepository = {};
  const mockNotificationPreferencesRepository = {};

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [NotificationsController],
      providers: [
        {
          provide: NotificationsService,
          useValue: mockNotificationsService,
        },
        {
          provide: DeviceTokensRepository,
          useValue: mockDeviceTokensRepository,
        },
        {
          provide: NotificationPreferencesRepository,
          useValue: mockNotificationPreferencesRepository,
        },
        {
          provide: NotificationHistoryRepository,
          useValue: mockNotificationHistoryRepository,
        },
      ],
    }).compile();

    controller = module.get<NotificationsController>(NotificationsController);
    notificationHistoryRepository = module.get<NotificationHistoryRepository>(NotificationHistoryRepository);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getAllNotifications', () => {
    it('should return paginated notifications for user', async () => {
      const userId = 'test-user-id';
      const filters: FilterNotificationsDto = { page: 1, limit: 20 };
      const mockResult = {
        data: [],
        total: 0,
        page: 1,
        limit: 20,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      };

      mockNotificationHistoryRepository.findByUser.mockResolvedValue(mockResult);

      const req = { user: { id: userId } };
      const result = await controller.getAllNotifications(req, filters);

      expect(notificationHistoryRepository.findByUser).toHaveBeenCalledWith(userId, filters);
      expect(result).toEqual(mockResult);
    });
  });

  describe('markAsRead', () => {
    it('should mark notification as read', async () => {
      const userId = 'test-user-id';
      const notificationId = 'test-notification-id';
      const mockNotification = { id: notificationId, status: 'read' };

      mockNotificationHistoryRepository.markAsRead.mockResolvedValue(mockNotification);

      const req = { user: { id: userId } };
      const result = await controller.markAsRead(req, notificationId);

      expect(notificationHistoryRepository.markAsRead).toHaveBeenCalledWith(notificationId, userId);
      expect(result).toEqual({
        message: 'Notification marked as read',
        notification: mockNotification,
      });
    });
  });
});
