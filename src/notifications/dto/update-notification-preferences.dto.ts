import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional } from 'class-validator';

export class UpdateNotificationPreferencesDto {
  @ApiProperty({ 
    description: 'Enable/disable task reminder notifications',
    example: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  taskReminders?: boolean;

  @ApiProperty({ 
    description: 'Enable/disable habit reminder notifications',
    example: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  habitReminders?: boolean;

  @ApiProperty({ 
    description: 'Enable/disable streak alert notifications',
    example: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  streakAlerts?: boolean;

  @ApiProperty({ 
    description: 'Enable/disable milestone celebration notifications',
    example: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  milestoneCelebrations?: boolean;

  @ApiProperty({ 
    description: 'Enable/disable challenge update notifications',
    example: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  challengeUpdates?: boolean;

  @ApiProperty({ 
    description: 'Enable/disable new message notifications',
    example: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  newMessages?: boolean;

  @ApiProperty({ 
    description: 'Enable/disable podcast ready notifications',
    example: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  podcastReady?: boolean;
}
