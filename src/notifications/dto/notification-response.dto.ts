import { ApiProperty } from '@nestjs/swagger';
import { NotificationType } from '../types/notification-types';
import { NotificationStatus } from '../entities/notification-history.entity';

export class NotificationResponseDto {
  @ApiProperty({ description: 'Notification ID' })
  id: string;

  @ApiProperty({ description: 'User ID' })
  userId: string;

  @ApiProperty({ 
    description: 'Notification type',
    enum: NotificationType
  })
  type: NotificationType;

  @ApiProperty({ description: 'Notification title' })
  title: string;

  @ApiProperty({ description: 'Notification body' })
  body: string;

  @ApiProperty({ 
    description: 'Notification status',
    enum: NotificationStatus
  })
  status: NotificationStatus;

  @ApiProperty({
    description: 'Additional notification data',
    type: 'object',
    additionalProperties: true
  })
  data?: Record<string, any>;

  @ApiProperty({
    description: 'Device token used for sending'
  })
  deviceToken?: string;

  @ApiProperty({
    description: 'Firebase message ID'
  })
  firebaseMessageId?: string;

  @ApiProperty({
    description: 'Error message if failed'
  })
  errorMessage?: string;

  @ApiProperty({
    description: 'When notification was sent'
  })
  sentAt?: Date;

  @ApiProperty({
    description: 'When notification was delivered'
  })
  deliveredAt?: Date;

  @ApiProperty({
    description: 'When notification was read'
  })
  readAt?: Date;

  @ApiProperty({ description: 'When notification was created' })
  createdAt: Date;

  @ApiProperty({ description: 'When notification was last updated' })
  updatedAt: Date;
}

export class PaginatedNotificationsResponseDto {
  @ApiProperty({ 
    description: 'Array of notifications',
    type: [NotificationResponseDto]
  })
  data: NotificationResponseDto[];

  @ApiProperty({ description: 'Total number of notifications' })
  total: number;

  @ApiProperty({ description: 'Current page number' })
  page: number;

  @ApiProperty({ description: 'Number of items per page' })
  limit: number;

  @ApiProperty({ description: 'Total number of pages' })
  totalPages: number;

  @ApiProperty({ description: 'Whether there is a next page' })
  hasNext: boolean;

  @ApiProperty({ description: 'Whether there is a previous page' })
  hasPrev: boolean;
}
