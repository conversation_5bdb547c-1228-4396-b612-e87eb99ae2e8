import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class RegisterDeviceDto {
  @ApiProperty({ 
    description: 'Device token for push notifications',
    example: 'exampleDeviceToken123456789' 
  })
  @IsNotEmpty()
  @IsString()
  deviceToken: string;

  @ApiProperty({ 
    description: 'Device type',
    example: 'ios',
    enum: ['ios', 'android', 'web'] 
  })
  @IsNotEmpty()
  @IsString()
  deviceType: 'ios' | 'android' | 'web';

  @ApiProperty({ 
    description: 'Device name',
    example: '<PERSON>\'s iPhone',
    required: false
  })
  @IsString()
  deviceName?: string;
}
