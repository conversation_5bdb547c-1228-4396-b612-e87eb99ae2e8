import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotificationPreferences } from '../entities/notification-preferences.entity';
import { UpdateNotificationPreferencesDto } from '../dto/update-notification-preferences.dto';

@Injectable()
export class NotificationPreferencesRepository {
  constructor(
    @InjectRepository(NotificationPreferences)
    private readonly notificationPreferencesRepository: Repository<NotificationPreferences>,
  ) {}

  /**
   * Get notification preferences for a user
   */
  async getPreferences(userId: string): Promise<NotificationPreferences> {
    // Find existing preferences
    let preferences = await this.notificationPreferencesRepository.findOne({
      where: { userId },
    });

    // Create default preferences if none exist
    if (!preferences) {
      preferences = await this.createDefaultPreferences(userId);
    }

    return preferences;
  }

  /**
   * Update notification preferences for a user
   */
  async updatePreferences(userId: string, preferencesDto: UpdateNotificationPreferencesDto): Promise<NotificationPreferences> {
    // Find existing preferences
    let preferences = await this.notificationPreferencesRepository.findOne({
      where: { userId },
    });

    // Create default preferences if none exist
    if (!preferences) {
      preferences = await this.createDefaultPreferences(userId);
    }

    // Update with new preferences
    Object.assign(preferences, preferencesDto);

    // Save updated preferences
    return this.notificationPreferencesRepository.save(preferences);
  }

  /**
   * Create default notification preferences for a user
   */
  private async createDefaultPreferences(userId: string): Promise<NotificationPreferences> {
    const preferences = this.notificationPreferencesRepository.create({
      userId,
      taskReminders: true,
      habitReminders: true,
      streakAlerts: true,
      milestoneCelebrations: true,
      challengeUpdates: true,
      newMessages: true,
      podcastReady: true,
    });

    return this.notificationPreferencesRepository.save(preferences);
  }

  /**
   * Get users who have enabled specific notification type
   */
  async getUsersWithEnabledNotificationType(notificationType: keyof NotificationPreferences): Promise<string[]> {
    const preferences = await this.notificationPreferencesRepository
      .createQueryBuilder('preferences')
      .select('preferences.userId')
      .where(`preferences.${notificationType} = true`)
      .getMany();

    return preferences.map(pref => pref.userId);
  }
}
