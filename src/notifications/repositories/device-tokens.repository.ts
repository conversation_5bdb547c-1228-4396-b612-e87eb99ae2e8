import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DeviceToken } from '../entities/device-token.entity';
import { RegisterDeviceDto } from '../dto/register-device.dto';

@Injectable()
export class DeviceTokensRepository {
  constructor(
    @InjectRepository(DeviceToken)
    private readonly deviceTokensRepository: Repository<DeviceToken>,
  ) {}

  /**
   * Register a new device token for a user
   */
  async registerDevice(userId: string, registerDeviceDto: RegisterDeviceDto): Promise<DeviceToken> {
    // Check if this device token already exists for this user
    const existingToken = await this.deviceTokensRepository.findOne({
      where: {
        userId,
        deviceToken: registerDeviceDto.deviceToken,
      },
    });

    if (existingToken) {
      // Update the existing token
      existingToken.isActive = true;
      existingToken.deviceName = registerDeviceDto.deviceName || existingToken.deviceName;
      existingToken.lastUsedAt = new Date();
      return this.deviceTokensRepository.save(existingToken);
    }

    // Create a new device token
    const deviceToken = this.deviceTokensRepository.create({
      userId,
      deviceToken: registerDeviceDto.deviceToken,
      deviceType: registerDeviceDto.deviceType,
      deviceName: registerDeviceDto.deviceName,
      isActive: true,
      lastUsedAt: new Date(),
    });

    return this.deviceTokensRepository.save(deviceToken);
  }

  /**
   * Get all active device tokens for a user
   */
  async getActiveDeviceTokens(userId: string): Promise<DeviceToken[]> {
    return this.deviceTokensRepository.find({
      where: {
        userId,
        isActive: true,
      },
    });
  }

  /**
   * Get a device token by ID
   */
  async getDeviceTokenById(deviceId: string): Promise<DeviceToken | null> {
    return this.deviceTokensRepository.findOne({
      where: { id: deviceId },
    });
  }

  /**
   * Deactivate a device token (e.g., when a user logs out)
   */
  async deactivateDevice(userId: string, deviceId: string): Promise<void> {
    await this.deviceTokensRepository.update(
      { id: deviceId, userId },
      { isActive: false },
    );
  }

  /**
   * Delete a device token
   */
  async deleteDevice(userId: string, deviceId: string): Promise<void> {
    await this.deviceTokensRepository.delete({
      id: deviceId,
      userId,
    });
  }

  /**
   * Get active device tokens for all users
   */
  async getAllActiveDeviceTokens(): Promise<DeviceToken[]> {
    return this.deviceTokensRepository.find({
      where: { isActive: true },
    });
  }
}
