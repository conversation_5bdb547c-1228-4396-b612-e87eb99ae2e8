import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { NotificationHistory, NotificationStatus } from '../entities/notification-history.entity';
import { FilterNotificationsDto } from '../dto/filter-notifications.dto';
import { NotificationType } from '../types/notification-types';

export interface CreateNotificationHistoryData {
  userId: string;
  type: NotificationType;
  title: string;
  body: string;
  data?: Record<string, any>;
  deviceToken?: string;
}

export interface PaginatedNotifications {
  data: NotificationHistory[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

@Injectable()
export class NotificationHistoryRepository {
  constructor(
    @InjectRepository(NotificationHistory)
    private readonly notificationHistoryRepository: Repository<NotificationHistory>,
  ) {}

  /**
   * Create a new notification history record
   */
  async create(data: CreateNotificationHistoryData): Promise<NotificationHistory> {
    const notification = this.notificationHistoryRepository.create({
      ...data,
      status: NotificationStatus.PENDING,
    });

    return this.notificationHistoryRepository.save(notification);
  }

  /**
   * Update notification status
   */
  async updateStatus(
    id: string, 
    status: NotificationStatus, 
    additionalData?: {
      firebaseMessageId?: string;
      errorMessage?: string;
      sentAt?: Date;
      deliveredAt?: Date;
      readAt?: Date;
    }
  ): Promise<NotificationHistory> {
    const notification = await this.notificationHistoryRepository.findOne({
      where: { id }
    });

    if (!notification) {
      throw new Error('Notification not found');
    }

    notification.status = status;
    
    if (additionalData) {
      Object.assign(notification, additionalData);
    }

    // Set timestamps based on status
    if (status === NotificationStatus.SENT && !notification.sentAt) {
      notification.sentAt = new Date();
    } else if (status === NotificationStatus.DELIVERED && !notification.deliveredAt) {
      notification.deliveredAt = new Date();
    } else if (status === NotificationStatus.READ && !notification.readAt) {
      notification.readAt = new Date();
    }

    return this.notificationHistoryRepository.save(notification);
  }

  /**
   * Get notifications for a user with pagination and filtering
   */
  async findByUser(userId: string, filters: FilterNotificationsDto): Promise<PaginatedNotifications> {
    const { page = 1, limit = 20, type, status, search, sortBy = 'createdAt', sortOrder = 'DESC' } = filters;
    
    const queryBuilder = this.notificationHistoryRepository
      .createQueryBuilder('notification')
      .where('notification.userId = :userId', { userId });

    // Apply filters
    this.applyFilters(queryBuilder, { type, status, search });

    // Apply sorting
    const sortField = this.getSortField(sortBy);
    queryBuilder.orderBy(sortField, sortOrder);

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Execute query
    const [data, total] = await queryBuilder.getManyAndCount();

    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNext,
      hasPrev,
    };
  }

  /**
   * Get all notifications with pagination and filtering (admin use)
   */
  async findAll(filters: FilterNotificationsDto): Promise<PaginatedNotifications> {
    const { page = 1, limit = 20, type, status, search, sortBy = 'createdAt', sortOrder = 'DESC' } = filters;
    
    const queryBuilder = this.notificationHistoryRepository
      .createQueryBuilder('notification')
      .leftJoinAndSelect('notification.user', 'user');

    // Apply filters
    this.applyFilters(queryBuilder, { type, status, search });

    // Apply sorting
    const sortField = this.getSortField(sortBy);
    queryBuilder.orderBy(sortField, sortOrder);

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Execute query
    const [data, total] = await queryBuilder.getManyAndCount();

    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNext,
      hasPrev,
    };
  }

  /**
   * Mark notification as read
   */
  async markAsRead(id: string, userId: string): Promise<NotificationHistory> {
    const notification = await this.notificationHistoryRepository.findOne({
      where: { id, userId }
    });

    if (!notification) {
      throw new Error('Notification not found');
    }

    return this.updateStatus(id, NotificationStatus.READ, {
      readAt: new Date()
    });
  }

  /**
   * Get notification by ID
   */
  async findById(id: string): Promise<NotificationHistory | null> {
    return this.notificationHistoryRepository.findOne({
      where: { id },
      relations: ['user']
    });
  }

  /**
   * Apply filters to query builder
   */
  private applyFilters(
    queryBuilder: SelectQueryBuilder<NotificationHistory>,
    filters: { type?: NotificationType; status?: NotificationStatus; search?: string }
  ): void {
    const { type, status, search } = filters;

    if (type) {
      queryBuilder.andWhere('notification.type = :type', { type });
    }

    if (status) {
      queryBuilder.andWhere('notification.status = :status', { status });
    }

    if (search) {
      queryBuilder.andWhere(
        '(notification.title ILIKE :search OR notification.body ILIKE :search)',
        { search: `%${search}%` }
      );
    }
  }

  /**
   * Get sort field with proper table prefix
   */
  private getSortField(sortBy: string): string {
    const validSortFields = ['createdAt', 'sentAt', 'readAt', 'type', 'status'];
    
    if (!validSortFields.includes(sortBy)) {
      return 'notification.createdAt';
    }

    return `notification.${sortBy}`;
  }
}
