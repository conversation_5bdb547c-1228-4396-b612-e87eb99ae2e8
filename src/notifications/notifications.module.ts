import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NotificationsService } from './notifications.service';
import { FirebaseModule } from '../firebase/firebase.module';
import { NotificationsController } from './notifications.controller';
import { DeviceToken } from './entities/device-token.entity';
import { NotificationPreferences } from './entities/notification-preferences.entity';
import { NotificationHistory } from './entities/notification-history.entity';
import { DeviceTokensRepository } from './repositories/device-tokens.repository';
import { NotificationPreferencesRepository } from './repositories/notification-preferences.repository';
import { NotificationHistoryRepository } from './repositories/notification-history.repository';

@Module({
  imports: [
    FirebaseModule,
    TypeOrmModule.forFeature([DeviceToken, NotificationPreferences, NotificationHistory]),
  ],
  providers: [
    NotificationsService,
    DeviceTokensRepository,
    NotificationPreferencesRepository,
    NotificationHistoryRepository,
  ],
  exports: [NotificationsService, NotificationHistoryRepository],
  controllers: [NotificationsController],
})
export class NotificationsModule {}
