import { Injectable } from '@nestjs/common';
import { FirebaseService } from '../firebase/firebase.service';
import { DeviceTokensRepository } from './repositories/device-tokens.repository';
import { NotificationPreferencesRepository } from './repositories/notification-preferences.repository';
import { NotificationHistoryRepository } from './repositories/notification-history.repository';
import { NotificationStatus } from './entities/notification-history.entity';
import * as admin from 'firebase-admin';

import { NotificationType } from './types/notification-types';

export interface NotificationData {
  title: string;
  body: string;
  imageUrl?: string;
  type: NotificationType;
  data?: Record<string, string>; // Additional data for the notification
}

@Injectable()
export class NotificationsService {
  constructor(
    private readonly firebaseService: FirebaseService,
    private readonly deviceTokensRepository: DeviceTokensRepository,
    private readonly notificationPreferencesRepository: NotificationPreferencesRepository,
    private readonly notificationHistoryRepository: NotificationHistoryRepository,
  ) {}

  /**
   * Send a notification to a specific user's device
   */
  async sendToUser(userId: string, deviceToken: string, notification: NotificationData) {
    // Create notification history record
    const notificationHistory = await this.notificationHistoryRepository.create({
      userId,
      type: notification.type,
      title: notification.title,
      body: notification.body,
      data: notification.data,
      deviceToken,
    });

    const payload: admin.messaging.MessagingPayload = {
      notification: {
        title: notification.title,
        body: notification.body,
        imageUrl: notification.imageUrl,
      },
      data: {
        type: notification.type,
        userId,
        ...notification.data,
      },
    };

    try {
      const messageId = await this.firebaseService.sendPushNotification(deviceToken, payload);

      // Update notification history with success status
      await this.notificationHistoryRepository.updateStatus(
        notificationHistory.id,
        NotificationStatus.SENT,
        {
          firebaseMessageId: messageId,
          sentAt: new Date(),
        }
      );

      return messageId;
    } catch (error) {
      console.error('Failed to send notification:', error);

      // Update notification history with failure status
      await this.notificationHistoryRepository.updateStatus(
        notificationHistory.id,
        NotificationStatus.FAILED,
        {
          errorMessage: error.message,
        }
      );

      throw error;
    }
  }

  /**
   * Send a notification to multiple devices of a user
   */
  async sendToUserDevices(userId: string, notification: NotificationData) {
    // Get user's notification preferences
    let preferences = await this.notificationPreferencesRepository.getPreferences(userId);

    // Check if this type of notification is allowed
    const preferenceKey = this.getPreferenceKey(notification.type);
    if (!preferences[preferenceKey]) {
      return null;
    }

    // Get user's device tokens
    const devices = await this.deviceTokensRepository.getActiveDeviceTokens(userId);
    const deviceTokens = devices.map(d => d.deviceToken);
    const payload: admin.messaging.MessagingPayload = {
      notification: {
        title: notification.title,
        body: notification.body,
        imageUrl: notification.imageUrl,
      },
      data: {
        type: notification.type,
        userId,
        ...notification.data,
      },
    };

    try {
      return await this.firebaseService.sendPushNotificationToMultipleDevices(deviceTokens, payload);
    } catch (error) {
      console.error('Failed to send notification to multiple devices:', error);
      throw error;
    }
  }

  /**
   * Send notification to all subscribers of a topic
   */
  async sendToTopic(topic: string, notification: NotificationData) {
    const payload: admin.messaging.MessagingPayload = {
      notification: {
        title: notification.title,
        body: notification.body,
        imageUrl: notification.imageUrl,
      },
      data: {
        type: notification.type,
        ...notification.data,
      },
    };

    try {
      return await this.firebaseService.sendPushNotificationToTopic(topic, payload);
    } catch (error) {
      console.error('Failed to send notification to topic:', error);
      throw error;
    }
  }

  /**
   * Subscribe a device to a topic
   */
  async subscribeToTopic(deviceTokens: string | string[], topic: string) {
    return admin.messaging().subscribeToTopic(deviceTokens, topic);
  }

  /**
   * Unsubscribe a device from a topic
   */
  async unsubscribeFromTopic(deviceTokens: string | string[], topic: string) {
    return admin.messaging().unsubscribeFromTopic(deviceTokens, topic);
  }

  /**
   * Send a task reminder notification
   */
  async sendTaskReminder(userId: string, deviceToken: string, taskTitle: string, dueDate: string) {
    return this.sendToUser(userId, deviceToken, {
      title: 'Task Reminder',
      body: `Don't forget: "${taskTitle}" is due ${dueDate}`,
      type: NotificationType.TASK_REMINDER,
      data: {
        taskTitle,
        dueDate,
      },
    });
  }

  /**
   * Send a habit reminder notification
   */
  async sendHabitReminder(userId: string, deviceToken: string, habitName: string) {
    return this.sendToUser(userId, deviceToken, {
      title: 'Habit Reminder',
      body: `Time to complete your habit: "${habitName}"`,
      type: NotificationType.HABIT_REMINDER,
      data: {
        habitName,
      },
    });
  }

  /**
   * Send a streak alert notification
   */
  async sendStreakAlert(userId: string, deviceToken: string, habitName: string, streak: number) {
    return this.sendToUser(userId, deviceToken, {
      title: 'Streak Alert',
      body: `You're on a ${streak} day streak for "${habitName}"! Keep it up!`,
      type: NotificationType.HABIT_STREAK,
      data: {
        habitName,
        streak: streak.toString(),
      },
    });
  }

  /**
   * Send a milestone celebration notification
   */
  async sendMilestoneCelebration(userId: string, deviceToken: string, milestone: string) {
    return this.sendToUser(userId, deviceToken, {
      title: 'Milestone Achieved!',
      body: `Congratulations! You've achieved: ${milestone}`,
      type: NotificationType.HABIT_MILESTONE,
      data: {
        milestone,
      },
    });
  }

  /**
   * Send a podcast ready notification
   */
  async sendPodcastReady(userId: string, deviceToken: string, podcastTitle: string) {
    return this.sendToUser(userId, deviceToken, {
      title: 'New Podcast Ready',
      body: `Your daily personalized podcast "${podcastTitle}" is ready to listen!`,
      type: NotificationType.PODCAST_AVAILABLE,
      data: {
        podcastTitle,
      },
    });
  }
  
  /**
   * Send a notification to a user respecting their preferences
   */
  async sendNotificationWithPreferences(
    userId: string,
    notificationType: NotificationType,
    notificationData: NotificationData
  ) {
    try {
      // First, check if the user has enabled this notification type
      const preferences = await this.notificationPreferencesRepository.getPreferences(userId);
      
      // Get preference key using our helper method
      const preferenceKey = this.getPreferenceKey(notificationType);
      
      // Check if the user has this notification type enabled
      if (preferenceKey && preferences[preferenceKey] === false) {
        console.log(`User ${userId} has disabled ${notificationType} notifications. Skipping.`);
        return null;
      }
      
      // Create notification with provided data
      const notification = {
        ...notificationData,
        type: notificationType
      };
      
      // Send notification to all devices
      return this.sendToUserDevices(userId, notification);
    } catch (error) {
      console.error('Error sending notification with preferences:', error);
      throw error;
    }
  }

  // Map notification types to preference keys
  private getPreferenceKey(type: NotificationType): string {
    const map: Record<NotificationType, string> = {
      [NotificationType.TASK_REMINDER]: 'taskReminders',
      [NotificationType.TASK_MILESTONE]: 'milestoneCelebrations',
      [NotificationType.HABIT_REMINDER]: 'habitReminders',
      [NotificationType.HABIT_STREAK]: 'streakAlerts',
      [NotificationType.HABIT_MILESTONE]: 'milestoneCelebrations',
      [NotificationType.CHALLENGE_START]: 'challengeInvitations',
      [NotificationType.CHALLENGE_PROGRESS]: 'challengeUpdates',
      [NotificationType.CHALLENGE_LEADERBOARD]: 'challengeUpdates',
      [NotificationType.CHALLENGE_COMPLETE]: 'challengeUpdates',
      [NotificationType.CHALLENGE_ACHIEVEMENT]: 'challengeUpdates',
      [NotificationType.PLAN_PROGRESS]: 'newMessages',
      [NotificationType.PLAN_STEP_COMPLETE]: 'newMessages',
      [NotificationType.PLAN_COMPLETE]: 'newMessages',
      [NotificationType.PODCAST_AVAILABLE]: 'podcastReady',
      [NotificationType.PODCAST_RECOMMENDATION]: 'podcastReady'
    };
    return map[type];
  }

  /**
   * Register a device token for a user
   */
  async registerDeviceToken(
    userId: string, 
    token: string, 
    deviceType: string, 
    deviceName: string
  ) {
    // Use the registerDevice method from deviceTokenRepository
    return this.deviceTokensRepository.registerDevice(userId, {
      deviceToken: token,
      deviceType: deviceType as 'ios' | 'android' | 'web',
      deviceName
    });
  }
}
