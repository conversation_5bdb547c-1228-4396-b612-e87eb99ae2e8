import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedC<PERSON>umn, OneToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';

@Entity('notification_preferences')
export class NotificationPreferences {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @OneToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ name: 'task_reminders', default: true })
  taskReminders: boolean;

  @Column({ name: 'habit_reminders', default: true })
  habitReminders: boolean;

  @Column({ name: 'streak_alerts', default: true })
  streakAlerts: boolean;

  @Column({ name: 'milestone_celebrations', default: true })
  milestoneCelebrations: boolean;

  @Column({ name: 'challenge_updates', default: true })
  challengeUpdates: boolean;

  @Column({ name: 'new_messages', default: true })
  newMessages: boolean;

  @Column({ name: 'podcast_ready', default: true })
  podcastReady: boolean;

  @CreateDateColumn({ name: 'created_at', type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp' })
  updatedAt: Date;
}
