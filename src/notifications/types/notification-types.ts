export enum NotificationType {
  // Habits
  HABIT_REMINDER = 'habit_reminder',
  HABIT_STREAK = 'habit_streak',
  HABIT_MILESTONE = 'habit_milestone',

  // Tasks
  TASK_REMINDER = 'task_reminder',
  TASK_MILESTONE = 'task_milestone',

  // Skill Plans
  PLAN_PROGRESS = 'plan_progress',
  PLAN_STEP_COMPLETE = 'plan_step_complete',
  PLAN_COMPLETE = 'plan_complete',

  // Challenges
  CHALLENGE_START = 'challenge_start',
  CHALLENGE_PROGRESS = 'challenge_progress',
  CHALLENGE_LEADERBOARD = 'challenge_leaderboard',
  CHALLENGE_COMPLETE = 'challenge_complete',
  CHALLENGE_ACHIEVEMENT = 'challenge_achievement',

  // Podcasts
  PODCAST_AVAILABLE = 'podcast_available',
  PODCAST_RECOMMENDATION = 'podcast_recommendation'
}

export interface NotificationPayload {
  type: NotificationType;
  userId: string;
  title: string;
  body: string;
  data?: Record<string, any>;
}
