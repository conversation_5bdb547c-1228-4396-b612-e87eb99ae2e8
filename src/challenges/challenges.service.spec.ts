import { Test, TestingModule } from '@nestjs/testing';
import { ChallengesService } from './challenges.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Challenge } from './entities/challenge.entity';
import { NotFoundException } from '@nestjs/common';
import { User } from '../users/entities/user.entity';
import { NotificationsService } from '../notifications/notifications.service';

describe('ChallengesService', () => {
  let service: ChallengesService;
  let repository: Repository<Challenge>;
  let notificationsService: NotificationsService;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    delete: jest.fn(),
    remove: jest.fn(),
  };

  // Mock user data
  const mockUser = {
    id: '1',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    password: 'hashedPassword',
    xp: 0,
    badges: [],
    firebaseUid: undefined,
    provider: 'local',
    picture: undefined,
    habits: [],
    tasks: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: undefined,
  } as User;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ChallengesService,
        {
          provide: getRepositoryToken(Challenge),
          useValue: mockRepository,
        },
        {
          provide: NotificationsService,
          useValue: {
            sendNotificationWithPreferences: jest.fn(),
            sendNotification: jest.fn(),
            createForUser: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ChallengesService>(ChallengesService);
    repository = module.get<Repository<Challenge>>(getRepositoryToken(Challenge));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new challenge', async () => {
      const startDate = new Date();
      const endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + 30);

      const createChallengeDto = {
        name: 'Fitness Challenge',
        description: '30-day fitness challenge',
        startDate,
        endDate,
        rules: {
          goals: ['Exercise 30 minutes daily'],
          requirements: ['Log your exercise'],
          rewards: {
            xp: 1000,
            badges: ['Fitness Champion'],
          },
        },
        user: mockUser,
      };

      const challenge = {
        id: '1',
        name: createChallengeDto.name,
        description: createChallengeDto.description,
        startDate: createChallengeDto.startDate,
        endDate: createChallengeDto.endDate,
        rules: createChallengeDto.rules,
        createdBy: mockUser,
        participants: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockRepository.create.mockReturnValue(challenge);
      mockRepository.save.mockResolvedValue(challenge);

      const result = await service.create(createChallengeDto);

      expect(result).toBeDefined();
      expect(result.id).toBe(challenge.id);
      expect(result.name).toBe(createChallengeDto.name);
    });
  });

  describe('findAll', () => {
    it('should return all active challenges', async () => {
      const challenges = [
        {
          id: '1',
          name: 'Fitness Challenge',
          description: '30-day fitness challenge',
          startDate: new Date(),
          endDate: new Date(Date.now() + 86400000 * 30),
          rules: {
            goals: ['Exercise 30 minutes daily'],
            requirements: ['Log your exercise'],
            rewards: { xp: 1000, badges: ['Fitness Champion'] },
          },
          createdBy: mockUser,
          participants: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: '2',
          name: 'Reading Challenge',
          description: '30-day reading challenge',
          startDate: new Date(),
          endDate: new Date(Date.now() + 86400000 * 30),
          rules: {
            goals: ['Read for 30 minutes daily'],
            requirements: ['Log your reading time'],
            rewards: { xp: 1000, badges: ['Reading Champion'] },
          },
          createdBy: mockUser,
          participants: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockRepository.find.mockResolvedValue(challenges);

      const result = await service.findAll();

      expect(result).toBeDefined();
      expect(result).toHaveLength(2);
      expect(result[0].name).toBe('Fitness Challenge');
    });
  });

  describe('findActive', () => {
    it('should return only currently active challenges', async () => {
      const now = new Date();
      const challenges = [
        {
          id: '1',
          name: 'Active Challenge',
          description: 'Currently active challenge',
          startDate: new Date(now.getTime() - 86400000), // Yesterday
          endDate: new Date(now.getTime() + 86400000), // Tomorrow
          rules: {
            goals: ['Daily goal'],
            requirements: ['Requirement'],
            rewards: { xp: 100, badges: ['Badge'] },
          },
          createdBy: mockUser,
          participants: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: '2',
          name: 'Future Challenge',
          description: 'Future challenge',
          startDate: new Date(now.getTime() + 86400000), // Tomorrow
          endDate: new Date(now.getTime() + 86400000 * 2), // Day after tomorrow
          rules: {
            goals: ['Daily goal'],
            requirements: ['Requirement'],
            rewards: { xp: 100, badges: ['Badge'] },
          },
          createdBy: mockUser,
          participants: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockRepository.find.mockResolvedValue([challenges[0]]);

      const result = await service.findActive();

      expect(result).toBeDefined();
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('Active Challenge');
    });
  });

  describe('findOne', () => {
    it('should return a challenge if found', async () => {
      const challengeId = '1';
      const challenge = {
        id: challengeId,
        name: 'Fitness Challenge',
        description: '30-day fitness challenge',
        startDate: new Date(),
        endDate: new Date(Date.now() + 86400000 * 30),
        rules: {
          goals: ['Exercise 30 minutes daily'],
          requirements: ['Log your exercise'],
          rewards: { xp: 1000, badges: ['Fitness Champion'] },
        },
        createdBy: mockUser,
        participants: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockRepository.findOne.mockResolvedValue(challenge);

      const result = await service.findOne(challengeId);

      expect(result).toBeDefined();
      expect(result.id).toBe(challengeId);
    });

    it('should throw NotFoundException if challenge not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne('1')).rejects.toThrow(NotFoundException);
    });
  });

  describe('join', () => {
    it('should add user to challenge participants', async () => {
      const challengeId = '1';
      const challenge = {
        id: challengeId,
        name: 'Fitness Challenge',
        description: '30-day fitness challenge',
        startDate: new Date(),
        endDate: new Date(Date.now() + 86400000 * 30),
        rules: {
          goals: ['Exercise 30 minutes daily'],
          requirements: ['Log your exercise'],
          rewards: { xp: 1000, badges: ['Fitness Champion'] },
        },
        createdBy: mockUser,
        participants: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const updatedChallenge = {
        ...challenge,
        participants: [mockUser],
      };

      mockRepository.findOne.mockResolvedValue(challenge);
      mockRepository.save.mockResolvedValue(updatedChallenge);

      const result = await service.join(challengeId, mockUser);

      expect(result).toBeDefined();
      expect(result.participants).toHaveLength(1);
      expect(result.participants[0].id).toBe(mockUser.id);
    });

    it('should not add user if already participating', async () => {
      const challengeId = '1';
      const challenge = {
        id: challengeId,
        name: 'Fitness Challenge',
        description: '30-day fitness challenge',
        startDate: new Date(),
        endDate: new Date(Date.now() + 86400000 * 30),
        rules: {
          goals: ['Exercise 30 minutes daily'],
          requirements: ['Log your exercise'],
          rewards: { xp: 1000, badges: ['Fitness Champion'] },
        },
        createdBy: mockUser,
        participants: [mockUser],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockRepository.findOne.mockResolvedValue(challenge);

      const result = await service.join(challengeId, mockUser);

      expect(result).toBeDefined();
      expect(result.participants).toHaveLength(1);
    });
  });

  describe('leave', () => {
    it('should remove user from challenge participants', async () => {
      const challengeId = '1';
      const challenge = {
        id: challengeId,
        name: 'Fitness Challenge',
        description: '30-day fitness challenge',
        startDate: new Date(),
        endDate: new Date(Date.now() + 86400000 * 30),
        rules: {
          goals: ['Exercise 30 minutes daily'],
          requirements: ['Log your exercise'],
          rewards: { xp: 1000, badges: ['Fitness Champion'] },
        },
        createdBy: mockUser,
        participants: [mockUser],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const updatedChallenge = {
        ...challenge,
        participants: [],
      };

      mockRepository.findOne.mockResolvedValue(challenge);
      mockRepository.save.mockResolvedValue(updatedChallenge);

      const result = await service.leave(challengeId, mockUser.id);

      expect(result).toBeDefined();
      expect(result.participants).toHaveLength(0);
    });
  });

  describe('remove', () => {
    it('should delete a challenge', async () => {
      const challengeId = '1';
      const challenge = {
        id: challengeId,
        name: 'Fitness Challenge',
        description: '30-day fitness challenge',
        startDate: new Date(),
        endDate: new Date(Date.now() + 86400000 * 30),
        rules: {
          goals: ['Exercise 30 minutes daily'],
          requirements: ['Log your exercise'],
          rewards: { xp: 1000, badges: ['Fitness Champion'] },
        },
        createdBy: mockUser,
        participants: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockRepository.findOne.mockResolvedValue(challenge);
      mockRepository.remove.mockResolvedValue({ affected: 1 });

      await service.remove(challengeId);

      expect(mockRepository.remove).toHaveBeenCalledWith(challenge);
    });

    it('should throw NotFoundException if challenge not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.remove('1')).rejects.toThrow(NotFoundException);
    });
  });
});
