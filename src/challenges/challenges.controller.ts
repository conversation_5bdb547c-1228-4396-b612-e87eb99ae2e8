import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, Request, HttpCode } from '@nestjs/common';
import { ChallengesService } from './challenges.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiQuery, ApiCreatedResponse, ApiOkResponse, ApiBody } from '@nestjs/swagger';
import { CreateChallengeDto } from './dto/create-challenge.dto';
import { UpdateChallengeDto } from './dto/update-challenge.dto';
import { ChallengeResponseDto } from './dto/challenge-response.dto';
import { JoinResponseDto } from './dto/join-response.dto';
import { ProgressDto } from './dto/progress.dto';

@ApiTags('challenges')
@Controller('api/challenges')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ChallengesController {
  constructor(private readonly challengesService: ChallengesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new challenge' })
  @ApiBody({ type: CreateChallengeDto })
  @ApiCreatedResponse({
    description: 'The challenge has been successfully created',
    type: ChallengeResponseDto
  })
  create(@Request() req, @Body() createChallengeDto: CreateChallengeDto) {
    return this.challengesService.create({
      ...createChallengeDto,
      user: req.user,
    });
  }

  @Get()
  @ApiOperation({ summary: 'Get challenges based on filter' })
  @ApiQuery({ name: 'filter', enum: ['all', 'active'], required: false })
  @ApiOkResponse({
    description: 'Returns challenges based on filter',
    type: [ChallengeResponseDto]
  })
  findAll(@Query('filter') filter: string) {
    if (filter === 'active') {
      return this.challengesService.findActive();
    }
    return this.challengesService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific challenge' })
  @ApiOkResponse({
    description: 'Returns a specific challenge by ID',
    type: ChallengeResponseDto
  })
  findOne(@Param('id') id: string) {
    return this.challengesService.findOne(id);
  }

  @Post(':id/join')
  @ApiOperation({ summary: 'Join a challenge' })
  @ApiOkResponse({
    description: 'Returns the challenge user joined',
    type: ChallengeResponseDto
  })
  @HttpCode(200)
  async join(@Request() req, @Param('id') id: string) {
    return this.challengesService.join(id, req.user);
  }

  @Post(':id/leave')
  @ApiOperation({ summary: 'Leave a challenge' })
  @ApiOkResponse({
    description: 'Returns the challenge user left',
    type: ChallengeResponseDto
  })
  @HttpCode(200)
  leave(@Request() req, @Param('id') id: string) {
    return this.challengesService.leave(id, req.user.id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a challenge' })
  @ApiBody({ type: UpdateChallengeDto })
  @ApiOkResponse({
    description: 'Returns the updated challenge',
    type: ChallengeResponseDto
  })
  update(
    @Param('id') id: string,
    @Body() updateChallengeDto: UpdateChallengeDto,
  ) {
    return this.challengesService.update(id, updateChallengeDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a challenge' })
  @ApiOkResponse({
    description: 'The challenge has been successfully deleted',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Challenge deleted successfully' }
      }
    }
  })
  remove(@Param('id') id: string) {
    return this.challengesService.remove(id);
  }

  @Post(':id/progress')
  @ApiOperation({ summary: 'Update challenge progress' })
  @ApiBody({ type: ProgressDto })
  @ApiOkResponse({
    description: 'Returns the updated progress',
    type: ProgressDto
  })
  @HttpCode(200)
  async updateProgress(
    @Request() req,
    @Param('id') id: string,
    @Body() progressDto: ProgressDto,
  ) {
    await this.challengesService.updateProgress(id, req.user.id, progressDto.progress);
    return { progress: progressDto.progress };
  }
}
