import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LessThanOrEqual, MoreThan } from 'typeorm';
import { Challenge } from './entities/challenge.entity';
import { User } from '../users/entities/user.entity';
import { NotificationsService } from '../notifications/notifications.service';
import { NotificationType } from '../notifications/types/notification-types';
import { Cron, CronExpression } from '@nestjs/schedule';

@Injectable()
export class ChallengesService {
  constructor(
    @InjectRepository(Challenge)
    private challengesRepository: Repository<Challenge>,
    private readonly notificationsService: NotificationsService
  ) {}

  async create(createChallengeDto: {
    name: string;
    description: string;
    startDate: Date;
    endDate: Date;
    rules: {
      goals: string[];
      requirements: string[];
      rewards: {
        xp: number;
        badges: string[];
      };
    };
    user: User;
  }): Promise<Challenge> {
    if (new Date(createChallengeDto.startDate) >= new Date(createChallengeDto.endDate)) {
      throw new BadRequestException('End date must be after start date');
    }

    const challenge = this.challengesRepository.create({
      ...createChallengeDto,
      participants: [createChallengeDto.user], // Creator automatically joins
    });

    const savedChallenge = await this.challengesRepository.save(challenge);

    // Notify creator about challenge creation
    await this.notificationsService.sendNotificationWithPreferences(
      createChallengeDto.user.id,
      'milestone_celebration' as NotificationType,
      {
        title: 'Challenge Created! 🎯',
        body: `Your challenge "${savedChallenge.name}" is ready to go! Share it with others to get started.`,
        type: 'milestone_celebration' as NotificationType,
        data: {
          challengeId: savedChallenge.id,
          challengeName: savedChallenge.name,
          startDate: new Date(savedChallenge.startDate).toISOString()
        }
      }
    );

    return savedChallenge;
  }

  async findAll(): Promise<Challenge[]> {
    return this.challengesRepository.find({
      relations: ['participants'],
      order: { startDate: 'DESC' },
    });
  }

  async findActive(): Promise<Challenge[]> {
    const now = new Date();
    return this.challengesRepository.find({
      where: {
        startDate: LessThanOrEqual(now),
        endDate: MoreThan(now),
      },
      relations: ['participants'],
      order: { endDate: 'ASC' },
    });
  }

  async findOne(id: string): Promise<Challenge> {
    const challenge = await this.challengesRepository.findOne({
      where: { id },
      relations: ['participants'],
    });

    if (!challenge) {
      throw new NotFoundException('Challenge not found');
    }

    return challenge;
  }

  async join(id: string, user: User): Promise<Challenge> {
    const challenge = await this.findOne(id);
    
    // Check if user is already participating
    if (challenge.participants.some(participant => participant.id === user.id)) {
      return challenge;
    }

    // Check if challenge has already ended
    if (new Date() > new Date(challenge.endDate)) {
      throw new BadRequestException('Challenge has already ended');
    }

    challenge.participants.push(user);
    const updatedChallenge = await this.challengesRepository.save(challenge);

    // Notify other participants about new joiner
    for (const participant of challenge.participants) {
      if (participant.id !== user.id) {
        await this.notificationsService.sendNotificationWithPreferences(
          participant.id,
          'challenge_update' as NotificationType,
          {
            title: 'New Challenge Participant! 👋',
            body: `A new challenger has joined "${challenge.name}"!`,
            type: 'challenge_update' as NotificationType,
            data: {
              challengeId: challenge.id,
              challengeName: challenge.name,
              type: 'new-participant'
            }
          }
        );
      }
    }

    return updatedChallenge;
  }

  async leave(id: string, userId: string): Promise<Challenge> {
    const challenge = await this.findOne(id);
    
    challenge.participants = challenge.participants.filter(
      participant => participant.id !== userId
    );

    return this.challengesRepository.save(challenge);
  }

  async update(id: string, updateChallengeDto: Partial<Challenge>): Promise<Challenge> {
    const challenge = await this.findOne(id);
    
    if (updateChallengeDto.startDate && updateChallengeDto.endDate) {
      if (new Date(updateChallengeDto.startDate) >= new Date(updateChallengeDto.endDate)) {
        throw new BadRequestException('End date must be after start date');
      }
    }

    Object.assign(challenge, updateChallengeDto);
    return this.challengesRepository.save(challenge);
  }

  async remove(id: string): Promise<{ message: string }> {
    const challenge = await this.findOne(id);
    await this.challengesRepository.remove(challenge);
    return { message: 'Challenge deleted successfully' };
  }

  async updateProgress(id: string, userId: string, progress: number): Promise<Challenge> {
    const challenge = await this.findOne(id);

    // Check if user is participating
    if (!challenge.participants.some(participant => participant.id === userId)) {
      throw new BadRequestException('User is not participating in this challenge');
    }

    // Check if challenge is active
    const now = new Date();
    if (now < new Date(challenge.startDate) || now > new Date(challenge.endDate)) {
      throw new BadRequestException('Challenge is not active');
    }

    // Validate progress value
    if (progress < 0 || progress > 100) {
      throw new BadRequestException('Progress must be between 0 and 100');
    }

    const previousProgress = challenge.progress?.[userId]?.currentProgress || 0;
    
    // Update progress
    challenge.progress = {
      ...challenge.progress,
      [userId]: {
        currentProgress: progress,
        lastUpdated: now,
      }
    };

    const savedChallenge = await this.challengesRepository.save(challenge);

    // Send progress milestone notifications
    if (progress > previousProgress) {
      // Notify the user about their own progress at key milestones
      if (progress >= 25 && previousProgress < 25 || 
          progress >= 50 && previousProgress < 50 ||
          progress >= 75 && previousProgress < 75 ||
          progress === 100) {
        await this.notificationsService.sendNotificationWithPreferences(
          userId,
          'challenge_progress' as NotificationType,
          {
            title: 'Challenge Progress Milestone! 🎯',
            body: `You've reached ${progress}% completion in "${challenge.name}"! Keep going!`,
            type: 'challenge_progress' as NotificationType,
            data: {
              challengeId: challenge.id,
              challengeName: challenge.name,
              progress: progress.toString()
            }
          }
        );
      }

      // If the user completed the challenge, notify all participants and send achievement
      if (progress === 100) {
        // Send achievement notification to the user who completed
        await this.notificationsService.sendNotificationWithPreferences(
          userId,
          'challenge_achievement' as NotificationType,
          {
            title: 'Challenge Complete! 🏆',
            body: `Congratulations! You've successfully completed "${challenge.name}"!`,
            type: 'challenge_achievement' as NotificationType,
            data: {
              challengeId: challenge.id,
              challengeName: challenge.name,
              type: 'completion',
              rewards: JSON.stringify(challenge.rules.rewards) // Convert rewards object to string
            }
          }
        );

        // Notify other participants
        for (const participant of challenge.participants) {
          if (participant.id !== userId) {
            await this.notificationsService.sendNotificationWithPreferences(
              participant.id,
              'challenge_complete' as NotificationType,
              {
                title: 'Challenge Update! 🏆',
                body: `A participant has completed the "${challenge.name}" challenge!`,
                type: 'challenge_complete' as NotificationType,
                data: {
                  challengeId: challenge.id,
                  challengeName: challenge.name,
                  type: 'completion'
                }
              }
            );
          }
        }
      }
    }

    return savedChallenge;
  }

  @Cron(CronExpression.EVERY_DAY_AT_8AM)
  async sendChallengeReminders() {
    try {
      const now = new Date();
      const challenges = await this.challengesRepository
        .createQueryBuilder('challenge')
        .leftJoinAndSelect('challenge.participants', 'participant')
        .where('challenge.startDate <= :now', { now })
        .andWhere('challenge.endDate > :now', { now })
        .getMany();

      for (const challenge of challenges) {
        const daysLeft = Math.ceil((new Date(challenge.endDate).getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

        // Send reminder when 7 days, 3 days, or 1 day is left
        if (daysLeft === 7 || daysLeft === 3 || daysLeft === 1) {
          for (const participant of challenge.participants) {
            const progress = challenge.progress?.[participant.id]?.currentProgress || 0;
            if (progress < 100) {
              await this.notificationsService.sendNotificationWithPreferences(
                participant.id,
                'challenge_update' as NotificationType,
                {
                  title: 'Challenge Ending Soon!',
                  body: `Only ${daysLeft} day${daysLeft > 1 ? 's' : ''} left to complete "${challenge.name}"! You're at ${progress}%.`,
                  type: 'challenge_update' as NotificationType,
                  data: {
                    challengeId: challenge.id,
                    challengeName: challenge.name,
                    daysLeft: daysLeft.toString(),
                    progress: progress.toString()
                  }
                }
              );
            }
          }
        }
      }
    } catch (error) {
      console.error('Failed to send challenge reminders:', error);
    }
  }
}
