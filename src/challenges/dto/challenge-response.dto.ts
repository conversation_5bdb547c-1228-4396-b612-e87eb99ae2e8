import { ApiProperty } from '@nestjs/swagger';

class ChallengeRules {
  @ApiProperty({
    description: 'Challenge goals',
    example: ['Complete 10 workouts', 'Meditate for 20 days']
  })
  goals: string[];

  @ApiProperty({
    description: 'Challenge requirements',
    example: ['Log activity daily', 'Share progress weekly']
  })
  requirements: string[];

  @ApiProperty({
    description: 'Challenge rewards',
    example: {
      xp: 500,
      badges: ['Challenge Champion', 'Early Bird']
    }
  })
  rewards: {
    xp: number;
    badges: string[];
  };
}

export class ChallengeResponseDto {
  @ApiProperty({
    description: 'Unique challenge ID',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  id: string;

  @ApiProperty({
    description: 'Challenge name',
    example: 'Summer Fitness Challenge'
  })
  name: string;

  @ApiProperty({
    description: 'Challenge description',
    example: 'A 30-day challenge to improve your fitness and well-being'
  })
  description: string;

  @ApiProperty({
    description: 'Challenge start date',
    example: '2025-06-01T00:00:00Z'
  })
  startDate: Date;

  @ApiProperty({
    description: 'Challenge end date',
    example: '2025-06-30T23:59:59Z'
  })
  endDate: Date;

  @ApiProperty({
    description: 'Challenge rules',
    type: ChallengeRules
  })
  rules: ChallengeRules;

  @ApiProperty({
    description: 'Challenge creator user ID',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  creatorId: string;

  @ApiProperty({
    description: 'Number of participants',
    example: 125
  })
  participantCount: number;

  @ApiProperty({
    description: 'Whether the current user joined the challenge',
    example: false
  })
  joined?: boolean;

  @ApiProperty({
    description: 'User progress in the challenge (0-100)',
    example: 50,
    nullable: true
  })
  userProgress?: number;
}
