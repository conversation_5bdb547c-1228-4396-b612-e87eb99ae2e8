import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsDate, ValidateNested, IsArray, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';

class ChallengeRewardsDto {
  @ApiProperty({
    description: 'Experience points reward',
    example: 500
  })
  @IsNumber()
  xp: number;

  @ApiProperty({
    description: 'Badges to be rewarded',
    example: ['Challenge Champion', 'Early Bird']
  })
  @IsArray()
  @IsString({ each: true })
  badges: string[];
}

class ChallengeRulesDto {
  @ApiProperty({
    description: 'Challenge goals',
    example: ['Complete 10 workouts', 'Meditate for 20 days']
  })
  @IsArray()
  @IsString({ each: true })
  goals: string[];

  @ApiProperty({
    description: 'Challenge requirements',
    example: ['Log activity daily', 'Share progress weekly']
  })
  @IsArray()
  @IsString({ each: true })
  requirements: string[];

  @ApiProperty({
    description: 'Challenge rewards',
    type: ChallengeRewardsDto
  })
  @ValidateNested()
  @Type(() => ChallengeRewardsDto)
  rewards: ChallengeRewardsDto;
}

export class CreateChallengeDto {
  @ApiProperty({
    description: 'Challenge name',
    example: 'Summer Fitness Challenge'
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Challenge description',
    example: 'A 30-day challenge to improve your fitness and well-being'
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Challenge start date',
    example: '2025-06-01T00:00:00Z'
  })
  @Type(() => Date)
  @IsDate()
  startDate: Date;

  @ApiProperty({
    description: 'Challenge end date',
    example: '2025-06-30T23:59:59Z'
  })
  @Type(() => Date)
  @IsDate()
  endDate: Date;

  @ApiProperty({
    description: 'Challenge rules',
    type: ChallengeRulesDto
  })
  @ValidateNested()
  @Type(() => ChallengeRulesDto)
  rules: ChallengeRulesDto;
}
