import { Entity, PrimaryGeneratedColumn, Column, ManyToMany, JoinTable, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';

@Entity('challenges')
export class Challenge {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'timestamp' })
  startDate: Date;

  @Column({ type: 'timestamp' })
  endDate: Date;

  @Column({ type: 'jsonb', default: {} })
  rules: {
    goals: string[];
    requirements: string[];
    rewards: {
      xp: number;
      badges: string[];
    };
  };

  @Column({ type: 'jsonb', default: {} })
  progress: {
    [userId: string]: {
      currentProgress: number;
      lastUpdated: Date;
    };
  };

  @ManyToMany(() => User)
  @JoinTable({
    name: 'challenge_participants',
    joinColumn: { name: 'challenge_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'user_id', referencedColumnName: 'id' },
  })
  participants: User[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
