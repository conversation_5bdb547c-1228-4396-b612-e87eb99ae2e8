import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, Request, HttpCode } from '@nestjs/common';
import { TasksService } from './tasks.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiQuery, ApiCreatedResponse, ApiOkResponse, ApiBody } from '@nestjs/swagger';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { TaskResponseDto } from './dto/task-response.dto';

@ApiTags('tasks')
@Controller('api/tasks')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class TasksController {
  constructor(private readonly tasksService: TasksService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new task' })
  @ApiBody({ type: CreateTaskDto })
  @ApiCreatedResponse({
    description: 'The task has been successfully created',
    type: TaskResponseDto
  })
  create(@Request() req, @Body() createTaskDto: CreateTaskDto) {
    return this.tasksService.create({
      ...createTaskDto,
      user: req.user,
    });
  }

  @Get()
  @ApiOperation({ summary: 'Get tasks based on filter' })
  @ApiQuery({ name: 'filter', enum: ['all', 'upcoming', 'overdue'], required: false })
  @ApiOkResponse({
    description: 'Returns tasks based on filter',
    type: [TaskResponseDto]
  })
  findAll(@Request() req, @Query('filter') filter: string) {
    switch (filter) {
      case 'upcoming':
        return this.tasksService.findUpcoming(req.user.id);
      case 'overdue':
        return this.tasksService.findOverdue(req.user.id);
      default:
        return this.tasksService.findAll(req.user.id);
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific task' })
  @ApiOkResponse({
    description: 'Returns a specific task by ID',
    type: TaskResponseDto
  })
  findOne(@Request() req, @Param('id') id: string) {
    return this.tasksService.findOne(id, req.user.id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a task' })
  @ApiBody({ type: UpdateTaskDto })
  @ApiOkResponse({
    description: 'Returns the updated task',
    type: TaskResponseDto
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateTaskDto: UpdateTaskDto,
  ) {
    return this.tasksService.update(id, req.user.id, updateTaskDto);
  }

  @Post(':id/complete')
  @ApiOperation({ summary: 'Mark a task as complete' })
  @ApiOkResponse({
    description: 'Returns the completed task',
    type: TaskResponseDto
  })
  @HttpCode(200)
  markComplete(@Request() req, @Param('id') id: string) {
    return this.tasksService.markAsComplete(id, req.user.id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a task' })
  @ApiOkResponse({
    description: 'The task has been successfully deleted',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Task deleted successfully' }
      }
    }
  })
  remove(@Request() req, @Param('id') id: string) {
    return this.tasksService.remove(id, req.user.id);
  }
}
