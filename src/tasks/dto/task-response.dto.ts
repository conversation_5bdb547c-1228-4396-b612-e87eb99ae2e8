import { ApiProperty } from '@nestjs/swagger';

export class TaskResponseDto {
  @ApiProperty({
    description: 'Unique task ID',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  id: string;

  @ApiProperty({
    description: 'Task title',
    example: 'Complete project proposal'
  })
  title: string;
  
  @ApiProperty({
    description: 'Task description',
    example: 'Write up the proposal with timeline and budget',
    nullable: true
  })
  description: string;
  
  @ApiProperty({
    description: 'Task due date',
    example: '2025-05-25T10:00:00Z'
  })
  dueDate: Date;
  
  @ApiProperty({
    description: 'Task priority',
    enum: ['low', 'medium', 'high'],
    example: 'medium'
  })
  priority: string;
  
  @ApiProperty({
    description: 'Whether task is completed',
    example: false
  })
  completed: boolean;
  
  @ApiProperty({
    description: 'Date when task was completed',
    example: '2025-05-25T14:30:00Z',
    nullable: true
  })
  completedAt?: Date;

  @ApiProperty({
    description: 'Whether user has been notified about overdue task',
    example: false
  })
  notifiedOverdue: boolean;
  
  @ApiProperty({
    description: 'Task creation date',
    example: '2025-05-19T10:00:00Z'
  })
  createdAt: Date;
  
  @ApiProperty({
    description: 'Task last update date',
    example: '2025-05-19T10:00:00Z'
  })
  updatedAt: Date;
  
  @ApiProperty({
    description: 'Task reminder settings',
    nullable: true,
    example: {
      enabled: true,
      time: '09:00'
    }
  })
  reminderSettings?: {
    enabled: boolean;
    time: string;
  };
}
