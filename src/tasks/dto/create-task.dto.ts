import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsDate, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';

class ReminderSettingsDto {
  @ApiProperty({
    description: 'Whether reminder is enabled',
    example: true
  })
  enabled: boolean;

  @ApiProperty({
    description: 'Time for the reminder',
    example: '09:00'
  })
  @IsString()
  time: string;
}

export class CreateTaskDto {
  @ApiProperty({
    description: 'Task title',
    example: 'Complete project proposal'
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Task description',
    example: 'Write up the proposal with timeline and budget',
    required: false
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Task due date',
    example: '2025-05-25T10:00:00Z'
  })
  @Type(() => Date)
  @IsDate()
  dueDate: Date;

  @ApiProperty({
    description: 'Task priority',
    enum: ['low', 'medium', 'high'],
    example: 'medium'
  })
  @IsEnum(['low', 'medium', 'high'])
  priority: string;

  @ApiProperty({
    description: 'Task reminder settings',
    required: false,
    type: ReminderSettingsDto
  })
  @IsOptional()
  reminderSettings?: ReminderSettingsDto;
}
