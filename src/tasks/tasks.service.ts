import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { Task } from './entities/task.entity';
import { User } from '../users/entities/user.entity';
import { NotificationsService } from '../notifications/notifications.service';
import { Cron, CronExpression } from '@nestjs/schedule';
import { NotificationType } from '../notifications/types/notification-types';

@Injectable()
export class TasksService {
  constructor(
    @InjectRepository(Task)
    private tasksRepository: Repository<Task>,
    private notificationsService: NotificationsService,
  ) {}

  async create(createTaskDto: {
    title: string;
    description?: string;
    dueDate: Date;
    priority: string;
    reminderSettings?: {
      enabled: boolean;
      time: string;
    };
    user: User;
  }): Promise<Task> {
    const task = this.tasksRepository.create(createTaskDto);
    return this.tasksRepository.save(task);
  }

  async findAll(userId: string): Promise<Task[]> {
    return this.tasksRepository.find({
      where: { user: { id: userId } },
      order: { dueDate: 'ASC' },
    });
  }

  async findUpcoming(userId: string): Promise<Task[]> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    return this.tasksRepository.find({
      where: {
        user: { id: userId },
        dueDate: MoreThanOrEqual(today),
        completed: false,
      },
      order: { dueDate: 'ASC' },
    });
  }

  async findOverdue(userId: string): Promise<Task[]> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    return this.tasksRepository.find({
      where: {
        user: { id: userId },
        dueDate: LessThanOrEqual(today),
        completed: false,
      },
      order: { dueDate: 'ASC' },
    });
  }

  async findOne(id: string, userId: string): Promise<Task> {
    const task = await this.tasksRepository.findOne({
      where: { id, user: { id: userId } },
    });

    if (!task) {
      throw new NotFoundException('Task not found');
    }

    return task;
  }

  async update(id: string, userId: string, updateTaskDto: Partial<Task>): Promise<Task> {
    const task = await this.findOne(id, userId);
    Object.assign(task, updateTaskDto);
    return this.tasksRepository.save(task);
  }

  async markAsComplete(id: string, userId: string): Promise<Task> {
    const task = await this.findOne(id, userId);
    task.completed = true;
    task.completedAt = new Date();
    
    const savedTask = await this.tasksRepository.save(task);

    // Send completion milestone notification
    await this.notificationsService.sendNotificationWithPreferences(
      userId,
      NotificationType.TASK_MILESTONE,
      {
        title: 'Task Completed! 🎯',
        body: `Great job completing "${task.title}"! Keep up the momentum!`,
        type: NotificationType.TASK_MILESTONE,
        data: {
          taskId: task.id,
          taskTitle: task.title
        }
      }
    );

    return savedTask;
  }

  async remove(id: string, userId: string): Promise<{ message: string }> {
    const task = await this.findOne(id, userId);
    await this.tasksRepository.remove(task);
    return { message: 'Task deleted successfully' };
  }

  private async scheduleReminder(task: Task): Promise<void> {
    if (!task.reminderSettings?.enabled) {
      return;
    }

    const now = new Date();
    const [hours, minutes] = task.reminderSettings.time.split(':').map(Number);
    const dueDate = new Date(task.dueDate);
    const reminderTime = new Date(dueDate);
    reminderTime.setHours(hours, minutes, 0, 0);

    // If reminder time has passed, don't schedule
    if (reminderTime < now) {
      return;
    }

    // If task is already completed, don't schedule reminder
    if (task.completed) {
      return;
    }

    // Calculate delay until reminder time
    const delay = reminderTime.getTime() - now.getTime();
    
    // Schedule the reminder
    setTimeout(async () => {
      try {
        // Verify task is still not completed before sending reminder
        const currentTask = await this.findOne(task.id, task.user.id);
        if (!currentTask.completed) {
          await this.notificationsService.sendNotificationWithPreferences(
            task.user.id,
            NotificationType.TASK_REMINDER,
            {
              title: 'Task Due Soon',
              body: `"${task.title}" is due ${this.formatDueTime(task.dueDate)}`,
              type: NotificationType.TASK_REMINDER,
              data: {
                taskId: task.id,
                taskTitle: task.title,
                dueDate: task.dueDate.toISOString()
              }
            }
          );
        }
      } catch (error) {
        console.error(`Failed to send task reminder for task ${task.id}:`, error);
      }
    }, delay);
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async scheduleTaskReminders() {
    try {
      // Get all incomplete tasks with reminders enabled
      const tasks = await this.tasksRepository
        .createQueryBuilder('task')
        .leftJoinAndSelect('task.user', 'user')
        .where("task.reminderSettings->>'enabled' = :enabled", { enabled: 'true' })
        .andWhere('task.completed = :completed', { completed: false })
        .andWhere('task.dueDate > :now', { now: new Date() })
        .getMany();

      for (const task of tasks) {
        await this.scheduleReminder(task);
      }
    } catch (error) {
      console.error('Failed to schedule task reminders:', error);
    }
  }

  @Cron(CronExpression.EVERY_HOUR)
  async checkOverdueTasks() {
    try {
      const now = new Date();
      const tasks = await this.tasksRepository
        .createQueryBuilder('task')
        .leftJoinAndSelect('task.user', 'user')
        .where('task.dueDate <= :now', { now })
        .andWhere('task.completed = :completed', { completed: false })
        .andWhere("task.notifiedOverdue IS NULL OR task.notifiedOverdue = 'false'")
        .getMany();

      for (const task of tasks) {
        await this.notificationsService.sendNotificationWithPreferences(
          task.user.id,
          NotificationType.TASK_REMINDER,
          {
            title: 'Task Overdue',
            body: `"${task.title}" is overdue! Take action now.`,
            type: NotificationType.TASK_REMINDER,
            data: {
              taskId: task.id,
              taskTitle: task.title,
              dueDate: task.dueDate.toISOString(),
              status: 'overdue'
            }
          }
        );

        // Mark as notified to avoid duplicate notifications
        await this.tasksRepository.update(task.id, { notifiedOverdue: true });
      }
    } catch (error) {
      console.error('Failed to check overdue tasks:', error);
    }
  }

  private formatDueTime(date: Date): string {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    if (date.toDateString() === now.toDateString()) {
      return 'today';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'tomorrow';
    } else {
      return `on ${date.toLocaleDateString()}`;
    }
  }
}
