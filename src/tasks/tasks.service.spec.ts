import { Test, TestingModule } from '@nestjs/testing';
import { TasksService } from './tasks.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Task } from './entities/task.entity';
import { NotFoundException } from '@nestjs/common';
import { NotificationsService } from '../notifications/notifications.service';
import { User, AuthProvider } from '../users/entities/user.entity';

describe('TasksService', () => {
  let service: TasksService;
  let repository: Repository<Task>;
  let notificationsService: NotificationsService;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    delete: jest.fn(),
    remove: jest.fn(),
  };

  const mockNotificationsService = {
    sendNotificationWithPreferences: jest.fn(),
  };

  const mockUser = {
    id: '1',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    password: 'hashedPassword',
    xp: 0,
    badges: [],
    firebaseUid: '',
    provider: AuthProvider.LOCAL,
    picture: '',
    habits: [],
    tasks: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: undefined,
  } as User;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TasksService,
        {
          provide: getRepositoryToken(Task),
          useValue: mockRepository,
        },
        {
          provide: NotificationsService,
          useValue: mockNotificationsService,
        },
      ],
    }).compile();

    service = module.get<TasksService>(TasksService);
    repository = module.get<Repository<Task>>(getRepositoryToken(Task));
    notificationsService = module.get<NotificationsService>(NotificationsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new task', async () => {
      const createTaskDto = {
        title: 'Test Task',
        description: 'Test Description',
        dueDate: new Date(),
        priority: 'high',
        reminderSettings: {
          enabled: true,
          time: '09:00',
        },
        user: mockUser,
      };

      const task = {
        id: '1',
        ...createTaskDto,
        completed: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockRepository.create.mockReturnValue(task);
      mockRepository.save.mockResolvedValue(task);

      const result = await service.create(createTaskDto);

      expect(result).toBeDefined();
      expect(result.id).toBe(task.id);
      expect(result.title).toBe(createTaskDto.title);
      expect(result.completed).toBe(false);
    });
  });

  describe('findAll', () => {
    it('should return all tasks for a user', async () => {
      const userId = '1';
      const tasks = [
        {
          id: '1',
          title: 'Task 1',
          completed: false,
          user: mockUser,
        },
        {
          id: '2',
          title: 'Task 2',
          completed: true,
          user: mockUser,
        },
      ];

      mockRepository.find.mockResolvedValue(tasks);

      const result = await service.findAll(userId);

      expect(result).toBeDefined();
      expect(result).toHaveLength(2);
      expect(result[0].title).toBe('Task 1');
    });
  });

  describe('findUpcoming', () => {
    it('should return upcoming tasks', async () => {
      const userId = '1';
      const future = new Date();
      future.setDate(future.getDate() + 7);

      const tasks = [
        {
          id: '1',
          title: 'Future Task',
          dueDate: future,
          completed: false,
          user: { id: userId },
        },
      ];

      mockRepository.find.mockResolvedValue(tasks);

      const result = await service.findUpcoming(userId);

      expect(result).toBeDefined();
      expect(result).toHaveLength(1);
      expect(result[0].title).toBe('Future Task');
    });
  });

  describe('findOverdue', () => {
    it('should return overdue tasks', async () => {
      const userId = '1';
      const past = new Date();
      past.setDate(past.getDate() - 7);

      const tasks = [
        {
          id: '1',
          title: 'Overdue Task',
          dueDate: past,
          completed: false,
          user: { id: userId },
        },
      ];

      mockRepository.find.mockResolvedValue(tasks);

      const result = await service.findOverdue(userId);

      expect(result).toBeDefined();
      expect(result).toHaveLength(1);
      expect(result[0].title).toBe('Overdue Task');
    });
  });

  describe('findOne', () => {
    it('should return a task if found', async () => {
      const taskId = '1';
      const userId = '1';
      const task = {
        id: taskId,
        title: 'Test Task',
        user: { id: userId },
      };

      mockRepository.findOne.mockResolvedValue(task);

      const result = await service.findOne(taskId, userId);

      expect(result).toBeDefined();
      expect(result.id).toBe(taskId);
    });

    it('should throw NotFoundException if task not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne('1', '1')).rejects.toThrow(NotFoundException);
    });
  });

  describe('markAsComplete', () => {
    it('should mark a task as complete', async () => {
      const taskId = '1';
      const userId = '1';
      const task = {
        id: taskId,
        title: 'Test Task',
        completed: false,
        user: { id: userId },
      };

      const updatedTask = {
        ...task,
        completed: true,
      };

      mockRepository.findOne.mockResolvedValue(task);
      mockRepository.save.mockResolvedValue(updatedTask);

      const result = await service.markAsComplete(taskId, userId);

      expect(result).toBeDefined();
      expect(result.completed).toBe(true);
    });
  });

  describe('remove', () => {
    it('should delete a task', async () => {
      const taskId = '1';
      const userId = '1';
      const task = {
        id: taskId,
        title: 'Test Task',
        user: { id: userId },
      };

      mockRepository.findOne.mockResolvedValue(task);
      mockRepository.remove.mockResolvedValue(task);

      await service.remove(taskId, userId);

      expect(mockRepository.remove).toHaveBeenCalledWith(task);
    });

    it('should throw NotFoundException if task not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.remove('1', '1')).rejects.toThrow(NotFoundException);
    });
  });
});
