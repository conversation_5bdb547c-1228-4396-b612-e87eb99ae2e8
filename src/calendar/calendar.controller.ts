import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { CalendarService } from './calendar.service';
import { CreateCalendarEventDto } from './dto/create-calendar-event.dto';
import { UpdateCalendarEventDto } from './dto/update-calendar-event.dto';
import { GetCalendarEventsDto } from './dto/get-calendar-events.dto';
import { CalendarEventResponseDto } from './dto/calendar-event-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('calendar')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('api/calendar')
export class CalendarController {
  constructor(private readonly calendarService: CalendarService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new calendar event' })
  @ApiResponse({
    status: 201,
    description: 'Calendar event created successfully',
    type: CalendarEventResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async create(
    @Body() createCalendarEventDto: CreateCalendarEventDto,
    @Request() req: any,
  ) {
    return this.calendarService.create(createCalendarEventDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: 'Get all calendar events for the authenticated user' })
  @ApiResponse({
    status: 200,
    description: 'Calendar events retrieved successfully',
    type: [CalendarEventResponseDto],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findAll(
    @Query() filters: GetCalendarEventsDto,
    @Request() req: any,
  ) {
    return this.calendarService.findAll(req.user.id, filters);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific calendar event by ID' })
  @ApiResponse({
    status: 200,
    description: 'Calendar event retrieved successfully',
    type: CalendarEventResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Calendar event not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findOne(@Param('id') id: string, @Request() req: any) {
    return this.calendarService.findOne(id, req.user.id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a calendar event' })
  @ApiResponse({
    status: 200,
    description: 'Calendar event updated successfully',
    type: CalendarEventResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Calendar event not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async update(
    @Param('id') id: string,
    @Body() updateCalendarEventDto: UpdateCalendarEventDto,
    @Request() req: any,
  ) {
    return this.calendarService.update(id, updateCalendarEventDto, req.user.id);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a calendar event' })
  @ApiResponse({ status: 204, description: 'Calendar event deleted successfully' })
  @ApiResponse({ status: 404, description: 'Calendar event not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async remove(@Param('id') id: string, @Request() req: any) {
    return this.calendarService.remove(id, req.user.id);
  }

  @Patch(':id/toggle-complete')
  @ApiOperation({ summary: 'Toggle completion status of a calendar event' })
  @ApiResponse({
    status: 200,
    description: 'Calendar event completion status toggled successfully',
    type: CalendarEventResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Calendar event not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async toggleComplete(@Param('id') id: string, @Request() req: any) {
    return this.calendarService.toggleComplete(id, req.user.id);
  }
}
