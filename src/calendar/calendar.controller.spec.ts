import { Test, TestingModule } from '@nestjs/testing';
import { CalendarController } from './calendar.controller';
import { CalendarService } from './calendar.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { CalendarEvent } from './entities/calendar-event.entity';
import { HabitsService } from '../habits/habits.service';
import { TasksService } from '../tasks/tasks.service';

describe('CalendarController', () => {
  let controller: CalendarController;
  let service: CalendarService;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    remove: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
    })),
  };

  const mockHabitsService = {
    findAllByUser: jest.fn(),
  };

  const mockTasksService = {
    findAll: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CalendarController],
      providers: [
        CalendarService,
        {
          provide: getRepositoryToken(CalendarEvent),
          useValue: mockRepository,
        },
        {
          provide: HabitsService,
          useValue: mockHabitsService,
        },
        {
          provide: TasksService,
          useValue: mockTasksService,
        },
      ],
    }).compile();

    controller = module.get<CalendarController>(CalendarController);
    service = module.get<CalendarService>(CalendarService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a calendar event', async () => {
      const createDto = {
        title: 'Test Event',
        startTime: '2025-06-28T10:00:00Z',
        endTime: '2025-06-28T11:00:00Z',
      };

      const mockEvent = { id: '1', ...createDto };
      mockRepository.create.mockReturnValue(mockEvent);
      mockRepository.save.mockResolvedValue(mockEvent);

      const req = { user: { userId: 'user-1' } };
      const result = await controller.create(createDto, req);

      expect(result).toEqual(mockEvent);
      expect(mockRepository.create).toHaveBeenCalled();
      expect(mockRepository.save).toHaveBeenCalled();
    });
  });
});
