import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, Join<PERSON>olumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';

export enum CalendarEventType {
  HABIT = 'habit',
  TASK = 'task',
  CUSTOM = 'custom',
}

@Entity('calendar_events')
export class CalendarEvent {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'timestamp' })
  startTime: Date;

  @Column({ type: 'timestamp' })
  endTime: Date;

  @Column({
    type: 'enum',
    enum: CalendarEventType,
    default: CalendarEventType.CUSTOM,
  })
  type: CalendarEventType;

  @Column({ type: 'uuid', nullable: true })
  relatedId: string; // ID of the related task/habit

  @Column({ nullable: true })
  color: string; // Hex color code

  @Column({ default: false })
  isCompleted: boolean;

  @ManyToOne(() => User, user => user.calendarEvents, { nullable: false })
  @JoinColumn({ name: 'userId' })
  user: User;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
