import { IsString, <PERSON>NotEmpty, IsOptional, IsDateString, IsEnum, IsUUID, IsBoolean, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { CalendarEventType } from '../entities/calendar-event.entity';

export class CreateCalendarEventDto {
  @ApiProperty({
    description: 'Title of the calendar event',
    example: 'Team Meeting',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Description of the calendar event',
    example: 'Weekly team standup meeting',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Start time of the event',
    example: '2025-06-28T10:00:00Z',
  })
  @IsDateString()
  @IsNotEmpty()
  startTime: string;

  @ApiProperty({
    description: 'End time of the event',
    example: '2025-06-28T11:00:00Z',
  })
  @IsDateString()
  @IsNotEmpty()
  endTime: string;

  @ApiProperty({
    description: 'Type of the calendar event',
    enum: CalendarEventType,
    default: CalendarEventType.CUSTOM,
  })
  @IsEnum(CalendarEventType)
  @IsOptional()
  type?: CalendarEventType;

  @ApiProperty({
    description: 'ID of the related task or habit',
    example: 'uuid-string',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  relatedId?: string;

  @ApiProperty({
    description: 'Hex color code for the event',
    example: '#FF5722',
    required: false,
  })
  @IsString()
  @IsOptional()
  @Matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, {
    message: 'Color must be a valid hex color code (e.g., #FF5722 or #F57)',
  })
  color?: string;

  @ApiProperty({
    description: 'Whether the event is completed',
    default: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isCompleted?: boolean;
}
