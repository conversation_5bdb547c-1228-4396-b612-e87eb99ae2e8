import { IsOptional, IsDateString, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { CalendarEventType } from '../entities/calendar-event.entity';

export class GetCalendarEventsDto {
  @ApiProperty({
    description: 'Start date to filter events (ISO 8601 format)',
    example: '2025-06-01T00:00:00Z',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiProperty({
    description: 'End date to filter events (ISO 8601 format)',
    example: '2025-06-30T23:59:59Z',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  endDate?: string;

  @ApiProperty({
    description: 'Filter by event type',
    enum: CalendarEventType,
    required: false,
  })
  @IsEnum(CalendarEventType)
  @IsOptional()
  type?: CalendarEventType;
}
