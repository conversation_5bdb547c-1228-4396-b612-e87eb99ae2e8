import { ApiProperty } from '@nestjs/swagger';
import { CalendarEventType } from '../entities/calendar-event.entity';

export class CalendarEventResponseDto {
  @ApiProperty({ description: 'Unique identifier for the calendar event' })
  id: string;

  @ApiProperty({ description: 'Title of the calendar event' })
  title: string;

  @ApiProperty({ description: 'Description of the calendar event', required: false })
  description?: string;

  @ApiProperty({ description: 'Start time of the event' })
  startTime: Date;

  @ApiProperty({ description: 'End time of the event' })
  endTime: Date;

  @ApiProperty({ description: 'Type of the calendar event', enum: CalendarEventType })
  type: CalendarEventType;

  @ApiProperty({ description: 'ID of the related task or habit', required: false })
  relatedId?: string;

  @ApiProperty({ description: 'Hex color code for the event', required: false })
  color?: string;

  @ApiProperty({ description: 'Whether the event is completed' })
  isCompleted: boolean;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;
}
