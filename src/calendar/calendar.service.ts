import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, MoreThanOrEqual, LessThanOrEqual } from 'typeorm';
import { CalendarEvent, CalendarEventType } from './entities/calendar-event.entity';
import { CreateCalendarEventDto } from './dto/create-calendar-event.dto';
import { UpdateCalendarEventDto } from './dto/update-calendar-event.dto';
import { GetCalendarEventsDto } from './dto/get-calendar-events.dto';
import { HabitsService } from '../habits/habits.service';
import { TasksService } from '../tasks/tasks.service';
import { User } from '../users/entities/user.entity';

@Injectable()
export class CalendarService {
  constructor(
    @InjectRepository(CalendarEvent)
    private calendarEventRepository: Repository<CalendarEvent>,
    private habitsService: HabitsService,
    private tasksService: TasksService,
  ) {}

  async create(createCalendarEventDto: CreateCalendarEventDto, userId: string): Promise<CalendarEvent> {
    console.log(createCalendarEventDto);
    const calendarEvent = this.calendarEventRepository.create({
      ...createCalendarEventDto,
      startTime: new Date(createCalendarEventDto.startTime),
      endTime: new Date(createCalendarEventDto.endTime),
      user: { id: userId } as User,
      type: createCalendarEventDto.type || CalendarEventType.CUSTOM,
    });

    return this.calendarEventRepository.save(calendarEvent);
  }

  async findAll(userId: string, filters: GetCalendarEventsDto): Promise<CalendarEvent[]> {
    const queryBuilder = this.calendarEventRepository
      .createQueryBuilder('event')
      .leftJoinAndSelect('event.user', 'user')
      .where('user.id = :userId', { userId });

    // Add date filters
    if (filters.startDate && filters.endDate) {
      queryBuilder.andWhere('event.startTime BETWEEN :startDate AND :endDate', {
        startDate: new Date(filters.startDate),
        endDate: new Date(filters.endDate),
      });
    } else if (filters.startDate) {
      queryBuilder.andWhere('event.startTime >= :startDate', {
        startDate: new Date(filters.startDate),
      });
    } else if (filters.endDate) {
      queryBuilder.andWhere('event.startTime <= :endDate', {
        endDate: new Date(filters.endDate),
      });
    }

    // Add type filter
    if (filters.type) {
      queryBuilder.andWhere('event.type = :type', { type: filters.type });
    }

    queryBuilder.orderBy('event.startTime', 'ASC');

    const customEvents = await queryBuilder.getMany();

    // Get habit and task events
    const allEvents = [...customEvents];

    if (!filters.type || filters.type === CalendarEventType.HABIT) {
      const habitEvents = await this.getHabitEvents(userId, filters);
      allEvents.push(...habitEvents);
    }

    if (!filters.type || filters.type === CalendarEventType.TASK) {
      const taskEvents = await this.getTaskEvents(userId, filters);
      allEvents.push(...taskEvents);
    }

    // Sort all events by start time
    return allEvents.sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
  }

  async findOne(id: string, userId: string): Promise<CalendarEvent> {
    const calendarEvent = await this.calendarEventRepository.findOne({
      where: { id, user: { id: userId } },
      relations: ['user'],
    });

    if (!calendarEvent) {
      throw new NotFoundException('Calendar event not found');
    }

    return calendarEvent;
  }

  async update(id: string, updateCalendarEventDto: UpdateCalendarEventDto, userId: string): Promise<CalendarEvent> {
    const calendarEvent = await this.findOne(id, userId);

    // Update the calendar event
    Object.assign(calendarEvent, {
      ...updateCalendarEventDto,
      ...(updateCalendarEventDto.startTime && { startTime: new Date(updateCalendarEventDto.startTime) }),
      ...(updateCalendarEventDto.endTime && { endTime: new Date(updateCalendarEventDto.endTime) }),
    });

    return this.calendarEventRepository.save(calendarEvent);
  }

  async remove(id: string, userId: string): Promise<void> {
    const calendarEvent = await this.findOne(id, userId);
    await this.calendarEventRepository.remove(calendarEvent);
  }

  async toggleComplete(id: string, userId: string): Promise<CalendarEvent> {
    const calendarEvent = await this.findOne(id, userId);
    calendarEvent.isCompleted = !calendarEvent.isCompleted;
    return this.calendarEventRepository.save(calendarEvent);
  }

  private async getHabitEvents(userId: string, filters: GetCalendarEventsDto): Promise<CalendarEvent[]> {
    try {
      const user = { id: userId } as any; // Simple user object for the service call
      const habits = await this.habitsService.findAllByUser(user);
      const habitEvents: CalendarEvent[] = [];

      for (const habit of habits) {
        // Create calendar events for habits based on their schedule
        // This is a simplified implementation - you might want to expand this based on your habit scheduling logic
        if (habit.schedule && habit.schedule.length > 0) {
          const events = this.createHabitCalendarEvents(habit, filters);
          habitEvents.push(...events);
        }
      }

      return habitEvents;
    } catch (error) {
      console.warn('Error fetching habit events:', error);
      return [];
    }
  }

  private async getTaskEvents(userId: string, filters: GetCalendarEventsDto): Promise<CalendarEvent[]> {
    try {
      const tasks = await this.tasksService.findAll(userId);
      const taskEvents: CalendarEvent[] = [];

      for (const task of tasks) {
        // Create calendar events for tasks based on their due dates
        const calendarEvent = new CalendarEvent();
        calendarEvent.id = `task-${task.id}`;
        calendarEvent.title = task.title;
        calendarEvent.description = task.description;
        calendarEvent.startTime = task.dueDate;
        calendarEvent.endTime = new Date(task.dueDate.getTime() + 60 * 60 * 1000); // 1 hour duration
        calendarEvent.type = CalendarEventType.TASK;
        calendarEvent.relatedId = task.id;
        calendarEvent.color = this.getPriorityColor(task.priority);
        calendarEvent.isCompleted = task.completed;
        calendarEvent.createdAt = task.createdAt;
        calendarEvent.updatedAt = task.updatedAt;

        // Apply date filters
        if (this.eventMatchesFilters(calendarEvent, filters)) {
          taskEvents.push(calendarEvent);
        }
      }

      return taskEvents;
    } catch (error) {
      console.warn('Error fetching task events:', error);
      return [];
    }
  }

  private createHabitCalendarEvents(habit: any, filters: GetCalendarEventsDto): CalendarEvent[] {
    const events: CalendarEvent[] = [];
    const startDate = filters.startDate ? new Date(filters.startDate) : new Date();
    const endDate = filters.endDate ? new Date(filters.endDate) : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days ahead

    // This is a simplified implementation
    // You might want to expand this based on your habit scheduling logic
    for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
      const dayName = date.toLocaleDateString('en-US', { weekday: 'long' });
      
      if (habit.schedule.includes(dayName)) {
        const calendarEvent = new CalendarEvent();
        calendarEvent.id = `habit-${habit.id}-${date.toISOString().split('T')[0]}`;
        calendarEvent.title = habit.name;
        calendarEvent.description = habit.description;
        calendarEvent.startTime = new Date(date);
        calendarEvent.endTime = new Date(date.getTime() + 30 * 60 * 1000); // 30 minutes duration
        calendarEvent.type = CalendarEventType.HABIT;
        calendarEvent.relatedId = habit.id;
        calendarEvent.color = '#4CAF50'; // Green for habits
        calendarEvent.isCompleted = habit.completion?.[date.toISOString().split('T')[0]] || false;
        calendarEvent.createdAt = habit.createdAt;
        calendarEvent.updatedAt = habit.updatedAt;

        events.push(calendarEvent);
      }
    }

    return events;
  }

  private getPriorityColor(priority: string): string {
    const colors = {
      low: '#4CAF50',    // Green
      medium: '#FF9800', // Orange
      high: '#F44336',   // Red
    };
    return colors[priority] || '#2196F3'; // Default blue
  }

  private eventMatchesFilters(event: CalendarEvent, filters: GetCalendarEventsDto): boolean {
    if (filters.startDate && event.startTime < new Date(filters.startDate)) {
      return false;
    }
    if (filters.endDate && event.startTime > new Date(filters.endDate)) {
      return false;
    }
    return true;
  }
}
