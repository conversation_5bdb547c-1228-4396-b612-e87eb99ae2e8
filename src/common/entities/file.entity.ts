import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CreateDateColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';

export enum FileType {
  PROFILE_PICTURE = 'profile_picture',
  PODCAST = 'podcast',
  SKILL_PLAN = 'skill_plan',
  CHALLENGE = 'challenge',
  MESSAGE_ATTACHMENT = 'message_attachment',
}

@Entity('files')
export class File {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  filename: string;

  @Column()
  originalFilename: string;

  @Column()
  mimeType: string;

  @Column({ type: 'int' })
  size: number;

  @Column()
  path: string;

  @Column({ name: 'public_url', nullable: true })
  publicUrl: string;

  @Column({
    type: 'enum',
    enum: FileType,
    default: FileType.MESSAGE_ATTACHMENT,
  })
  type: FileType;

  @ManyToOne(() => User, { onDelete: 'CASCADE', nullable: true })
  @JoinColumn({ name: 'uploaded_by' })
  uploadedBy: User;

  @Column({ name: 'uploaded_by', nullable: true })
  uploadedById: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;
}
