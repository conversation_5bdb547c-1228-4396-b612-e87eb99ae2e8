# Local Podcast Storage

This project stores podcast files locally instead of using Google Cloud Storage.

## File Storage Structure

- Podcast files are stored in the `uploads/podcasts` directory by default (configurable)
- File naming convention: `podcast_[uuid].mp3`
- Files are served through a dedicated endpoint: `/podcasts/:filename`

## Configuration

The upload directory can be configured in your `.env` file:

```
PODCASTS_UPLOAD_DIR=uploads/podcasts
```

## Implementation Details

- `PodcastGenerationProcessor`: Handles the storage of podcast audio files locally
- `StaticFilesModule`: Provides endpoints for serving static files
- `StaticFilesController`: Controller for handling file streaming with proper MIME types

## Usage

The podcast files are served through URLs like:
`http://localhost:3000/podcasts/podcast_[uuid].mp3`

These URLs are stored in the podcast entity's `audioUrl` field.
