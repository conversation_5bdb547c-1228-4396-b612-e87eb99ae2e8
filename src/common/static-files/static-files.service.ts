import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import { getAbsolutePath } from '../utils/file-utils';

@Injectable()
export class StaticFilesService {
  constructor(private configService: ConfigService) {}

  /**
   * Gets the full path to a podcast file
   * @param filename The name of the podcast file
   * @returns The full path to the file
   */
  getPodcastFilePath(filename: string): string {
    const uploadsDir = getAbsolutePath(
      this.configService.get<string>('uploads.podcastsDir') || 'uploads/podcasts'
    );
    return path.join(uploadsDir, filename);
  }

  /**
   * Check if a file exists
   * @param filepath The full path to the file
   * @returns boolean indicating if the file exists
   */
  fileExists(filepath: string): boolean {
    return fs.existsSync(filepath);
  }

  /**
   * Get a file as a stream
   * @param filepath The full path to the file
   * @returns A readable stream of the file
   */
  getFileStream(filepath: string): fs.ReadStream {
    return fs.createReadStream(filepath);
  }
}
