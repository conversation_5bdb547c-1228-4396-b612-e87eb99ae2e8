import { <PERSON>, <PERSON>, Param, Res, NotFoundException, Header, StreamableFile } from '@nestjs/common';
import type { Response } from 'express';
import { StaticFilesService } from './static-files.service';
import * as path from 'path';
import * as fs from 'fs';

@Controller()
export class StaticFilesController {
  constructor(private readonly staticFilesService: StaticFilesService) {}

  @Get('podcasts/:filename')
  async servePodcastFile(
    @Param('filename') filename: string, 
    @Res({ passthrough: true }) response: Response
  ): Promise<StreamableFile> {
    const filepath = this.staticFilesService.getPodcastFilePath(filename);
    
    if (!this.staticFilesService.fileExists(filepath)) {
      throw new NotFoundException(`Podcast file ${filename} not found`);
    }

    const ext = path.extname(filename).toLowerCase();
    let contentType = 'application/octet-stream'; 

    // Set proper content type based on file extension
    if (ext === '.mp3') {
      contentType = 'audio/mpeg';
    } else if (ext === '.wav') {
      contentType = 'audio/wav';
    } else if (ext === '.ogg') {
      contentType = 'audio/ogg';
    }

    response.set({
      'Content-Type': contentType,
      'Content-Disposition': `inline; filename="${filename}"`
    });

    const fileStream = this.staticFilesService.getFileStream(filepath);
    return new StreamableFile(fileStream);
  }
}
