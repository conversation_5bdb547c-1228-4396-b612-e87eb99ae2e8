import { Injectable } from '@nestjs/common';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';
import { HelpModule } from '../../help/help.module';
import { AdminModule } from '../../admin/admin.module';

@Injectable()
export class ViewEngineService {
  async configureHandlebars(app: NestExpressApplication) {
    const { create } = await import('express-handlebars');
    
    // Create common helper functions to be shared across all view engines
    const commonHelpers = {
      eq: (a: any, b: any) => a === b,
      ne: (a: any, b: any) => a !== b,
      json: (context: any) => JSON.stringify(context),
      replace: (str: string, search: string, replace: string) => {
        if (!str || typeof str !== 'string') return str || '';
        if (!search || typeof search !== 'string') return str;
        if (typeof replace !== 'string') replace = '';
        return str.replace(new RegExp(search, 'g'), replace);
      },
      formatDate: (date: any, format?: string) => {
        if (!date) return '';
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';
        
        // Default format if none provided or if format is not a string
        if (!format || typeof format !== 'string') format = 'MMM DD, YYYY';
        
        const options: Intl.DateTimeFormatOptions = {};
        
        // Simple format parsing
        if (format.includes('YYYY')) {
          options.year = 'numeric';
        } else if (format.includes('YY')) {
          options.year = '2-digit';
        }
        
        if (format.includes('MMMM')) {
          options.month = 'long';
        } else if (format.includes('MMM')) {
          options.month = 'short';
        } else if (format.includes('MM')) {
          options.month = '2-digit';
        }
        
        if (format.includes('DD')) {
          options.day = '2-digit';
        } else if (format.includes('D')) {
          options.day = 'numeric';
        }
        
        if (format.includes('HH')) {
          options.hour = '2-digit';
          options.hour12 = false;
        } else if (format.includes('hh')) {
          options.hour = '2-digit';
          options.hour12 = true;
        }
        
        if (format.includes('mm')) {
          options.minute = '2-digit';
        }
        
        if (format.includes('ss')) {
          options.second = '2-digit';
        }
        
        try {
          return d.toLocaleDateString('en-US', options);
        } catch (error) {
          return d.toLocaleDateString();
        }
      },
      truncate: (str: any, length?: number) => {
        if (!str || typeof str !== 'string') return str || '';
        const maxLength = length || 100;
        if (str.length <= maxLength) return str;
        return str.substring(0, maxLength) + '...';
      },
      formatDateForInput: (date: any) => {
        if (!date) return '';
        const d = new Date(date);
        if (isNaN(d.getTime())) return '';
        
        // Format as YYYY-MM-DD for HTML input[type="date"]
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      },
      // Admin-specific helpers
      substring: (str: string, start: number, end: number) => {
        if (!str || typeof str !== 'string') return str || '';
        return str.substring(start, end);
      },
      gt: (a: any, b: any) => a > b,
      lt: (a: any, b: any) => a < b,
      and: (a: any, b: any) => a && b,
      or: (a: any, b: any) => a || b,
      join: (arr: any[], separator: string) => {
        if (!Array.isArray(arr)) return '';
        return arr.join(separator || ', ');
      },
      // Blog-specific helpers
      categoryDisplayName: (category: string) => {
        if (!category) return '';
        return category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
      },
      statusColor: (status: string) => {
        switch (status) {
          case 'published': return 'green';
          case 'draft': return 'yellow';
          case 'archived': return 'gray';
          default: return 'gray';
        }
      },
      formatDateShort: (dateString: string) => {
        if (!dateString) return '';
        try {
          const date = new Date(dateString);
          if (isNaN(date.getTime())) return '';
          return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
          });
        } catch (error) {
          return '';
        }
      },
      capitalize: (str: string) => {
        if (!str || typeof str !== 'string') return str || '';
        return str.charAt(0).toUpperCase() + str.slice(1);
      },
      markdownToHtml: (markdown: string) => {
        if (!markdown || typeof markdown !== 'string') return '';
        // Basic markdown to HTML conversion (you might want to use a proper markdown library)
        return markdown
          .replace(/^### (.*$)/gm, '<h3>$1</h3>')
          .replace(/^## (.*$)/gm, '<h2>$1</h2>')
          .replace(/^# (.*$)/gm, '<h1>$1</h1>')
          .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
          .replace(/\*(.*?)\*/g, '<em>$1</em>')
          .replace(/\n/g, '<br>');
      },
      editorJsToHtml: (editorData: string) => {
        // Import and use the EditorJsToHtmlService
        const { EditorJsToHtmlService } = require('../../blog/services/editorjs-to-html.service');
        const editorJsService = new EditorJsToHtmlService();
        return editorJsService.convertToHtml(editorData);
      },
      times: (n: number, block: any) => {
        let result = '';
        for (let i = 0; i < n; i++) {
          result += block.fn(i + 1);
        }
        return result;
      },
      add: (a: number, b: number) => a + b,
      subtract: (a: number, b: number) => a - b,
      multiply: (a: number, b: number) => a * b,
      divide: (a: number, b: number) => Math.floor(a / b),
      paginate: (currentPage: number, totalPages: number, limit: number = 5) => {
        const pages: Array<{ number: number; isCurrent: boolean }> = [];
        const start = Math.max(1, currentPage - Math.floor(limit / 2));
        const end = Math.min(totalPages, start + limit - 1);
        
        for (let i = start; i <= end; i++) {
          pages.push({
            number: i,
            isCurrent: i === currentPage
          });
        }
        
        return pages;
      },
    };

    // Create a custom view engine that can handle multiple template directories
    const customViewEngine = (filePath: string, options: any, callback: any) => {
      // Determine which handlebars instance to use based on the file path
      let hbsInstance;
      let layoutsDir;
      let defaultLayout;
      
      if (filePath.includes('src/admin/templates') || filePath.includes('admin/templates')) {
        layoutsDir = join(process.cwd(), 'src/admin/templates');
        defaultLayout = 'layout';
      } else if (filePath.includes('src/help/templates') || filePath.includes('help/templates')) {
        layoutsDir = join(process.cwd(), 'src/help/templates');
        defaultLayout = 'layout';
      } else {
        layoutsDir = join(process.cwd(), 'src/templates');
        defaultLayout = 'public-layout';
      }

      // Create handlebars instance for this request
      hbsInstance = create({
        layoutsDir: layoutsDir,
        defaultLayout: options.layout !== false ? (options.layout || defaultLayout) : false,
        extname: '.hbs',
        helpers: commonHelpers,
      });

      // Use the handlebars instance to render
      hbsInstance.engine(filePath, options, callback);
    };

    // Register the custom view engine
    app.engine('.hbs', customViewEngine);
    app.set('view engine', '.hbs');
    
    // Set up views directories - Express will search in order
    app.set('views', [
      join(process.cwd(), 'src/admin/templates'), // Admin templates
      join(process.cwd(), 'src/help/templates'),  // Help templates
      join(process.cwd(), 'src/templates'),       // Public templates
    ]);
    
    // Serve static files from public directory (for CSS, JS, images, etc.)
    app.useStaticAssets(join(process.cwd(), 'public'));
  }
}
