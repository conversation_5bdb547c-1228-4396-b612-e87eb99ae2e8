import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';

const mkdirAsync = promisify(fs.mkdir);
const writeFileAsync = promisify(fs.writeFile);

/**
 * Ensures a directory exists, creating it and any necessary parent directories if they don't
 */
export async function ensureDirectoryExists(dirPath: string): Promise<void> {
  try {
    await mkdirAsync(dirPath, { recursive: true });
  } catch (error) {
    if (error.code !== 'EEXIST') {
      throw error;
    }
  }
}

/**
 * Saves binary data to a file, creating the directory if it doesn't exist
 */
export async function saveBufferToFile(
  buffer: Buffer,
  filePath: string,
): Promise<void> {
  const directory = path.dirname(filePath);
  await ensureDirectoryExists(directory);
  await writeFileAsync(filePath, buffer);
}

/**
 * Gets the absolute path for a file in a relative directory
 */
export function getAbsolutePath(relativePath: string): string {
  return path.resolve(process.cwd(), relativePath);
}
