import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import { randomUUID } from 'crypto';
import { promisify } from 'util';
import { File, FileType } from '../entities/file.entity';
import { ensureDirectoryExists, getAbsolutePath } from '../utils/file-utils';

const unlinkAsync = promisify(fs.unlink);
const copyFileAsync = promisify(fs.copyFile);

@Injectable()
export class FileService {
  private readonly logger = new Logger(FileService.name);
  private readonly uploadsDir: string;

  constructor(
    @InjectRepository(File)
    private fileRepository: Repository<File>,
    private configService: ConfigService,
  ) {
    this.uploadsDir = getAbsolutePath(
      this.configService.get<string>('uploads.dir') || 'uploads'
    );
    ensureDirectoryExists(this.uploadsDir);
  }

  /**
   * Store a file in the local filesystem and database
   * @param fileBuffer The file buffer
   * @param filename Original filename
   * @param mimeType MIME type of the file
   * @param type File type category
   * @param userId ID of the user uploading the file
   * @returns The stored file entity
   */
  async storeFile(
    fileBuffer: Buffer,
    filename: string,
    mimeType: string,
    type: FileType,
    userId?: string,
  ): Promise<File> {
    // Create a unique filename
    const fileExtension = path.extname(filename);
    const uniqueFilename = `${randomUUID()}${fileExtension}`;
    const typeDir = path.join(this.uploadsDir, type.toString());
    
    // Ensure directory exists
    await ensureDirectoryExists(typeDir);
    
    // Path where file will be stored
    const filePath = path.join(typeDir, uniqueFilename);
    
    // Write file to disk
    fs.writeFileSync(filePath, fileBuffer);
    
    // Create file entity
    const file = this.fileRepository.create({
      filename: uniqueFilename,
      originalFilename: filename,
      mimeType,
      size: fileBuffer.length,
      path: `${type.toString()}/${uniqueFilename}`,
      publicUrl: `${this.configService.get('app.url')}/files/${type.toString()}/${uniqueFilename}`,
      type,
      uploadedById: userId,
    });
    
    // Save to database
    return this.fileRepository.save(file);
  }

  /**
   * Get a file by ID
   * @param id The file ID
   * @returns The file entity
   */
  async getFile(id: string): Promise<File> {
    const file = await this.fileRepository.findOne({
      where: { id },
      relations: ['uploadedBy'],
    });
    
    if (!file) {
      throw new NotFoundException(`File with ID ${id} not found`);
    }
    
    return file;
  }

  /**
   * Get a file by its path
   * @param filePath The file path
   * @returns The file entity
   */
  async getFileByPath(filePath: string): Promise<File> {
    const file = await this.fileRepository.findOne({
      where: { path: filePath },
    });
    
    if (!file) {
      throw new NotFoundException(`File with path ${filePath} not found`);
    }
    
    return file;
  }

  /**
   * Delete a file by ID
   * @param id The file ID
   */
  async deleteFile(id: string): Promise<void> {
    const file = await this.getFile(id);
    
    try {
      // Delete from filesystem
      const filePath = path.join(this.uploadsDir, file.path);
      await unlinkAsync(filePath);
      
      // Delete from database
      await this.fileRepository.remove(file);
    } catch (error) {
      this.logger.error(`Failed to delete file ${id}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get the full filesystem path for a file
   * @param file The file entity
   * @returns The full path to the file
   */
  getFullPath(file: File): string {
    return path.join(this.uploadsDir, file.path);
  }
}
