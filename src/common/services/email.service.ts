import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import { Transporter } from 'nodemailer';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private transporter: Transporter;

  constructor(private configService: ConfigService) {
    this.createTransporter();
  }

  private createTransporter() {
    const host = this.configService.get('email.host') || 'smtp.gmail.com';
    const port = this.configService.get('email.port') || 587;
    const secure = this.configService.get('email.secure') || false;
    
    const smtpConfig: any = {
      host,
      port,
      secure, // true for 465, false for other ports
      auth: {
        user: this.configService.get('email.user'),
        pass: this.configService.get('email.password'),
      },
    };

    // Only add TLS config for non-secure connections (typically port 587)
    if (!secure) {
      smtpConfig.requireTLS = true;
      smtpConfig.tls = {
        rejectUnauthorized: false,
        minVersion: 'TLSv1.2'
      };
    }

    this.transporter = nodemailer.createTransport(smtpConfig);
  }

  async sendPasswordResetEmail(to: string, resetCode: string): Promise<void> {
    const resetUrl = `${this.configService.get('app.frontendUrl') || 'http://localhost:3000'}/reset-password`;
    
    const mailOptions = {
      from: this.configService.get('email.from') || '"Power Up" <<EMAIL>>',
      to,
      subject: 'Password Reset - Power Up',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Password Reset Request</h2>
          <p>Hello,</p>
          <p>We received a request to reset your password for your Power Up account.</p>
          
          <div style="text-align: center; margin: 30px 0; padding: 20px; background-color: #f8f9fa; border-radius: 8px;">
            <h3 style="color: #333; margin-bottom: 10px;">Your Reset Code</h3>
            <div style="font-size: 32px; font-weight: bold; letter-spacing: 4px; color: #007bff; font-family: 'Courier New', monospace; margin: 15px 0;">
              ${resetCode}
            </div>
            <p style="margin: 10px 0; color: #666; font-size: 14px;">Enter this code on the password reset page</p>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" 
               style="background-color: #007bff; color: white; padding: 12px 30px; 
                      text-decoration: none; border-radius: 5px; font-weight: bold; 
                      display: inline-block;">
              Reset Password
            </a>
          </div>
          
          <p>You can also go to the following link and enter your reset code:</p>
          <p style="word-break: break-all; color: #666;">${resetUrl}</p>
          <p><strong>This code will expire in 1 hour.</strong></p>
          <p>If you didn't request this password reset, please ignore this email.</p>
          <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
          <p style="color: #666; font-size: 12px;">
            This email was sent by Power Up. If you have any questions, please contact our support team.
          </p>
        </div>
      `,
    };

    try {
      await this.transporter.sendMail(mailOptions);
      this.logger.log(`Password reset email sent to ${to}`);
    } catch (error) {
      this.logger.error(`Failed to send password reset email to ${to}:`, error);
      throw new Error('Failed to send password reset email');
    }
  }

  async verifyTransporter(): Promise<boolean> {
    try {
      await this.transporter.verify();
      this.logger.log('Email transporter verified successfully');
      return true;
    } catch (error) {
      this.logger.error('Email transporter verification failed:', error);
      return false;
    }
  }
}
