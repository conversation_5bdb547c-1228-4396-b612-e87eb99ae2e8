import { Controller, Get, Render } from '@nestjs/common';
import { AppService } from './app.service';
import { AdminService } from './admin/admin.service';
import { ApiTags, ApiOperation, ApiOkResponse, ApiExcludeEndpoint } from '@nestjs/swagger';

@ApiTags('app')
@Controller()
export class AppController {
  constructor(
    private readonly appService: AppService,
    private readonly adminService: AdminService
  ) {}

  @Get()
  @ApiExcludeEndpoint() // Exclude from Swagger documentation
  @Render('index')
  async serveRoot(): Promise<any> {
    try {
      // Get landing page content from admin module (full content, no section specified)
      const landingContent = await this.adminService.getLandingPageContent();
      
      return {
        title: (landingContent as any)?.heroTitle ? `Power Up - ${(landingContent as any).heroTitle}` : 'Power Up - AI-Driven Personal Wellness Coach',
        description: (landingContent as any)?.heroSubtitle || 'Transform your life with Power Up - the ultimate AI-driven wellness app featuring personalized coaching, habit tracking, daily podcasts, and community challenges.',
        content: landingContent || {
          heroTitle: 'Transform Your Life with AI-Powered Wellness',
          heroSubtitle: 'Experience personalized coaching, habit tracking, daily motivational podcasts, and community challenges - all powered by advanced AI technology.',
          ctaText: 'Get Started Now',
          ctaLink: '#download',
          heroImage: '/assets/images/logo.png'
        },
        layout: 'public-layout'
      };
    } catch (error) {
      // Fallback data if admin service fails
      return {
        title: 'Power Up - AI-Driven Personal Wellness Coach',
        description: 'Transform your life with Power Up - the ultimate AI-driven wellness app featuring personalized coaching, habit tracking, daily podcasts, and community challenges.',
        content: {
          heroTitle: 'Transform Your Life with AI-Powered Wellness',
          heroSubtitle: 'Experience personalized coaching, habit tracking, daily motivational podcasts, and community challenges - all powered by advanced AI technology.',
          ctaText: 'Get Started Now',
          ctaLink: '#download',
          heroImage: '/assets/images/logo.png'
        },
        layout: 'public-layout'
      };
    }
  }

  @Get('api/health')
  @ApiOperation({ summary: 'Check API health status' })
  @ApiOkResponse({
    description: 'Returns the health status of the API',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        timestamp: { type: 'string', example: '2025-01-09T12:00:00.000Z' },
        uptime: { type: 'number', example: 123456 }
      }
    }
  })
  getHealth() {
    return this.appService.getHealth();
  }

  @Get('api/status')
  @ApiOperation({ summary: 'Get API status information' })
  @ApiOkResponse({
    description: 'Returns detailed API status information',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'operational' },
        version: { type: 'string', example: '1.0.0' },
        features: {
          type: 'array',
          items: { type: 'string' }
        }
      }
    }
  })
  getStatus() {
    return this.appService.getStatus();
  }
}
