export default () => ({
  port: process.env.PORT ? parseInt(process.env.PORT, 10) : 3000,
  app: {
    frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000',
    baseUrl: process.env.BASE_URL || 'https://powerup.apperx.com',
  },
  database: {
    host: process.env.DATABASE_HOST || 'localhost',
    port: process.env.DATABASE_PORT ? parseInt(process.env.DATABASE_PORT, 10) : 5432,
    username: process.env.DATABASE_USERNAME || 'postgres',
    password: process.env.DATABASE_PASSWORD || 'postgres',
    database: process.env.DATABASE_NAME || 'power_up',
  },
  jwt: {
    secret: process.env.JWT_SECRET || 'secretKey',
    expiresIn: process.env.JWT_EXPIRES_IN || '1d',
  },
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT ? parseInt(process.env.REDIS_PORT, 10) : 6379,
    username: process.env.REDIS_USERNAME,
    password: process.env.REDIS_PASSWORD,
  },
  gemini: {
    apiKey: process.env.GEMINI_API_KEY || '',
  },
  openrouter: {
    apiKey: process.env.OPENROUTER_API_KEY || '',
  },
  openai: {
    apiKey: process.env.OPENAI_API_KEY || '',
  },
  uploads: {
    podcastsDir: process.env.PODCASTS_UPLOAD_DIR || 'uploads/podcasts',
    dir: process.env.FILE_UPLOADS_DIR || 'uploads/files',
  },
  firebase: {
    serviceAccountPath: process.env.FIREBASE_SERVICE_ACCOUNT_PATH || 'firebase-service-account.json',
    databaseUrl: process.env.FIREBASE_DATABASE_URL || 'https://powerup-e51d2.firebaseio.com',
    projectId: process.env.FIREBASE_PROJECT_ID || 'powerup-e51d2',
  },
  email: {
    host: process.env.EMAIL_HOST || 'smtp.gmail.com',
    port: process.env.EMAIL_PORT ? parseInt(process.env.EMAIL_PORT, 10) : 587,
    secure: process.env.EMAIL_SECURE === 'true' || false,
    user: process.env.EMAIL_USER || '',
    password: process.env.EMAIL_PASSWORD || '',
    from: process.env.EMAIL_FROM || '<EMAIL>',
  },
});
