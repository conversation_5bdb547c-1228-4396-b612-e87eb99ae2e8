import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { BullModule } from '@nestjs/bull';
import configuration from './config/configuration';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import { HabitsModule } from './habits/habits.module';
import { TasksModule } from './tasks/tasks.module';
import { ChallengesModule } from './challenges/challenges.module';
import { SkillPlansModule } from './skill-plans/skill-plans.module';
import { PodcastsModule } from './podcasts/podcasts.module';
import { AnalyticsModule } from './analytics/analytics.module';
import { StaticFilesModule } from './common/static-files/static-files.module';
import { FirebaseModule } from './firebase/firebase.module';
import { NotificationsModule } from './notifications/notifications.module';
import { MessagingModule } from './messaging/messaging.module';
import { WebhooksModule } from './webhooks/webhooks.module';
import { HelpModule } from './help/help.module';
import { AdminModule } from './admin/admin.module';
import { BlogModule } from './blog/blog.module';
import { MonetizationModule } from './monetization/monetization.module';
import { CalendarModule } from './calendar/calendar.module';
import { FeedbackModule } from './feedback/feedback.module';
import { ViewEngineModule } from './common/view-engine';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('database.host'),
        port: configService.get('database.port'),
        username: configService.get('database.username'),
        password: configService.get('database.password'),
        database: configService.get('database.database'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: false, // Set to false in production and now using migrations
        migrations: [__dirname + '/migrations/*{.ts,.js}'],
        migrationsRun: false, // Set to true to auto-run migrations on startup if needed
      }),
      inject: [ConfigService],
    }),
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        redis: {
          host: configService.get('redis.host'),
          port: configService.get('redis.port'),
          username: configService.get('redis.username'),
          password: configService.get('redis.password'),
        },
      }),
      inject: [ConfigService],
    }),
    ScheduleModule.forRoot(),
    UsersModule,
    AuthModule,
    HabitsModule,
    TasksModule,
    ChallengesModule,
    SkillPlansModule,
    PodcastsModule,
    AnalyticsModule,
    StaticFilesModule,
    FirebaseModule,
    NotificationsModule,
    MessagingModule,
    WebhooksModule,
    HelpModule,
    AdminModule,
    BlogModule,
    MonetizationModule,
    CalendarModule,
    FeedbackModule,
    ViewEngineModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
