import { Injectable } from '@nestjs/common';
import { FirebaseService } from '../firebase/firebase.service';
import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class FirebaseAuthService {
  constructor(
    private readonly firebaseService: FirebaseService,
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
  ) {}

  /**
   * Verify Firebase ID token and create/update user in our system
   */
  async verifyFirebaseToken(idToken: string) {
    try {
      // Verify the ID token with Firebase
      const decodedToken = await this.firebaseService.verifyIdToken(idToken);
      
      // Ensure we have an email from the token
      if (!decodedToken.email) {
        throw new Error('Firebase token does not contain an email address');
      }
      
      // If verified, check if the user exists in our database
      let user = await this.usersService.findOneByEmail(decodedToken.email);
      
      if (!user) {
        // Create a new user if they don't exist
        user = await this.usersService.create({
          email: decodedToken.email,
          name: decodedToken.name || decodedToken.email.split('@')[0],
          firebaseUid: decodedToken.uid,
          picture: decodedToken.picture || undefined,
        });
      } else if (!user.firebaseUid) {
        // Update the Firebase UID if the user exists but doesn't have one
        user = await this.usersService.update(user.id, {
          firebaseUid: decodedToken.uid,
          picture: decodedToken.picture || user.picture,
        });
      }
      
      // Generate JWT token for our application
      const payload = { sub: user.id, email: user.email };
      const token = this.jwtService.sign(payload);
      
      return {
        user,
        token,
      };
    } catch (error) {
      throw error;
    }
  }
}
