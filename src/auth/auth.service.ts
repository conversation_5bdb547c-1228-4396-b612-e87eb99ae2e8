import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { EmailService } from '../common/services/email.service';
import { FirebaseService } from '../firebase/firebase.service';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcrypt';
import { AuthResponseDto } from './dto/auth-response.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private emailService: EmailService,
    private firebaseService: FirebaseService,
    private configService: ConfigService,
  ) {}

  async validateUser(email: string, password: string): Promise<any> {
    const user = await this.usersService.findByEmail(email);
    if (user && (await bcrypt.compare(password, user.password))) {
      // @ts-ignore: Exclude password from response
      const { password, ...result } = user;
      return result;
    }
    return null;
  }

  async login(user: any): Promise<AuthResponseDto> {
    const payload = { email: user.email, sub: user.id };
    return {
      access_token: this.jwtService.sign(payload),
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
      },
    };
  }

  async register(registerDto: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
  }): Promise<AuthResponseDto> {
    const user = await this.usersService.create(registerDto);
    
    return {
      access_token: this.jwtService.sign({ email: user.email, sub: user.id }),
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
      },
    };
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<{ message: string }> {
    const { email } = forgotPasswordDto;
    
    // Check if user exists
    const user = await this.usersService.findByEmail(email);
    if (!user) {
      // Return success message even if user doesn't exist for security
      return { message: 'If an account with that email exists, a password reset link has been sent.' };
    }

    try {
      // Generate random 8-character reset code
      const resetCode = this.generateResetCode();
      
      // Set expiration time (1 hour from now)
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 1);
      
      // Save reset code to user
      await this.usersService.setResetCode(user.id, resetCode, expiresAt);

      // Send reset email with code
      await this.emailService.sendPasswordResetEmail(email, resetCode);

      return { message: 'If an account with that email exists, a password reset link has been sent.' };
    } catch (error) {
      // Log error but don't expose it to user
      console.error('Error sending password reset email:', error);
      return { message: 'If an account with that email exists, a password reset link has been sent.' };
    }
  }

  private generateResetCode(): string {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<{ message: string }> {
    const { code, newPassword } = resetPasswordDto;

    try {
      // Find user by reset code
      const user = await this.usersService.findByResetCode(code);
      if (!user) {
        throw new Error('Invalid reset code.');
      }

      // Check if reset code has expired
      if (!user.resetCodeExpiresAt || user.resetCodeExpiresAt < new Date()) {
        // Clear expired reset code
        await this.usersService.clearResetCode(user.id);
        throw new Error('Reset code has expired. Please request a new password reset.');
      }

      // Hash the new password
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      // Update user's password (this will also clear the reset code)
      await this.usersService.updatePassword(user.id, hashedPassword);

      return { message: 'Password has been reset successfully.' };
    } catch (error) {
      if (error.message.includes('expired')) {
        throw new Error('Reset code has expired. Please request a new password reset.');
      }
      if (error.message.includes('Invalid')) {
        throw new Error('Invalid reset code.');
      }
      throw new Error('Failed to reset password. Please try again.');
    }
  }

  async resendResetCode(email: string): Promise<{ message: string }> {
    try {
      console.log('Resend reset code request for email:', email);
      
      // Check if user exists
      const user = await this.usersService.findByEmail(email);
      if (!user) {
        console.log('User not found for email:', email);
        // Return success message even if user doesn't exist for security
        return { message: 'If an account with that email exists, a new reset code has been sent.' };
      }

      console.log('User found:', { 
        id: user.id, 
        email: user.email, 
        hasResetCode: !!user.resetCode,
        resetCodeExpiresAt: user.resetCodeExpiresAt 
      });

      // Check if user has an active reset code
      if (!user.resetCode || !user.resetCodeExpiresAt || user.resetCodeExpiresAt < new Date()) {
        console.log('No active reset code found for user');
        // No active reset code, treat like a new forgot password request
        return { message: 'No active reset code found. Please request a new password reset first.' };
      }

      // Generate new 8-character reset code
      const newResetCode = this.generateResetCode();
      console.log('Generated new reset code:', newResetCode);
      
      // Set new expiration time (1 hour from now)
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 1);
      
      // Update reset code for user
      await this.usersService.setResetCode(user.id, newResetCode, expiresAt);
      console.log('Updated reset code in database');

      // Send new reset email with code
      console.log('Attempting to send email...');
      await this.emailService.sendPasswordResetEmail(user.email, newResetCode);
      console.log('Email sent successfully');

      return { message: 'A new reset code has been sent to your email address.' };
    } catch (error) {
      console.error('Error resending reset code:', error);
      return { message: 'If an account with that email exists, a new reset code has been sent.' };
    }
  }

  async forgotPasswordWithFirebase(forgotPasswordDto: ForgotPasswordDto): Promise<{ message: string }> {
    const { email } = forgotPasswordDto;
    
    try {
      // Check if user exists in our system
      const user = await this.usersService.findByEmail(email);
      if (!user) {
        return { message: 'If an account with that email exists, a password reset link has been sent.' };
      }

      // Generate Firebase password reset link
      await this.firebaseService.generatePasswordResetLink(email);

      return { message: 'If an account with that email exists, a password reset link has been sent.' };
    } catch (error) {
      console.error('Error with Firebase password reset:', error);
      return { message: 'If an account with that email exists, a password reset link has been sent.' };
    }
  }
}
