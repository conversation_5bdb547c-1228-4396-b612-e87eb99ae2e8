import { Controller, Post, UseGuards, Request, Body, HttpCode } from '@nestjs/common';
import { AuthService } from './auth.service';
import { FirebaseAuthService } from './firebase-auth.service';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { ApiTags, ApiOperation, ApiBody, ApiCreatedResponse, ApiOkResponse } from '@nestjs/swagger';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { SocialLoginDto } from './dto/social-login.dto';
import { AuthResponseDto } from './dto/auth-response.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';

@ApiTags('auth')
@Controller('api/auth')
export class AuthController {
  constructor(
    private authService: AuthService,
    private firebaseAuthService: FirebaseAuthService,
  ) {}

  @Post('register')
  @ApiOperation({ summary: 'Register a new user' })
  @ApiBody({ type: RegisterDto })
  @ApiCreatedResponse({ 
    description: 'User has been successfully registered', 
    type: AuthResponseDto 
  })
  async register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto);
  }

  @UseGuards(LocalAuthGuard)
  @Post('login')
  @ApiOperation({ summary: 'Login with email and password' })
  @ApiBody({ type: LoginDto })
  @ApiOkResponse({ 
    description: 'User has been successfully logged in', 
    type: AuthResponseDto 
  })
  @HttpCode(200)
  async login(@Request() req) {
    return this.authService.login(req.user);
  }

  @Post('social-login')
  @ApiOperation({ summary: 'Login with social providers (Google, Apple) via Firebase' })
  @ApiBody({ type: SocialLoginDto })
  @ApiOkResponse({
    description: 'User has been successfully logged in',
    type: AuthResponseDto 
  })
  @HttpCode(200)
  async socialLogin(@Body() socialLoginDto: SocialLoginDto) {
    return this.firebaseAuthService.verifyFirebaseToken(socialLoginDto.idToken);
  }

  @Post('forgot-password')
  @ApiOperation({ summary: 'Request a password reset email' })
  @ApiBody({ type: ForgotPasswordDto })
  @ApiOkResponse({
    description: 'Password reset email sent if account exists',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'If an account with that email exists, a password reset link has been sent.'
        }
      }
    }
  })
  @HttpCode(200)
  async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    return this.authService.forgotPassword(forgotPasswordDto);
  }

  @Post('reset-password')
  @ApiOperation({ summary: 'Reset password using 8-character code from email' })
  @ApiBody({ type: ResetPasswordDto })
  @ApiOkResponse({
    description: 'Password has been reset successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Password has been reset successfully.'
        }
      }
    }
  })
  @HttpCode(200)
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authService.resetPassword(resetPasswordDto);
  }

  @Post('resend-reset-code')
  @ApiOperation({ summary: 'Resend password reset code using email address' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        email: {
          type: 'string',
          format: 'email',
          description: 'Email address to resend reset code to',
          example: '<EMAIL>'
        }
      },
      required: ['email']
    }
  })
  @ApiOkResponse({
    description: 'New reset code sent if account exists and has active reset request',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'A new reset code has been sent to your email address.'
        }
      }
    }
  })
  @HttpCode(200)
  async resendResetCode(@Body() body: { email: string }) {
    return this.authService.resendResetCode(body.email);
  }

  @Post('forgot-password-firebase')
  @ApiOperation({ summary: 'Request a password reset using Firebase (alternative method)' })
  @ApiBody({ type: ForgotPasswordDto })
  @ApiOkResponse({
    description: 'Firebase password reset email sent if account exists',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'If an account with that email exists, a password reset link has been sent.'
        }
      }
    }
  })
  @HttpCode(200)
  async forgotPasswordFirebase(@Body() forgotPasswordDto: ForgotPasswordDto) {
    return this.authService.forgotPasswordWithFirebase(forgotPasswordDto);
  }
}
