import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, ForbiddenException } from '@nestjs/common';
import { SubscriptionGuard } from './subscription.guard';
import { SubscriptionService } from '../../monetization/services/subscription.service';
import { SubscriptionStatus } from '../../monetization/entities/subscription.entity';

describe('SubscriptionGuard', () => {
  let guard: SubscriptionGuard;
  let subscriptionService: jest.Mocked<SubscriptionService>;

  const mockSubscriptionService = {
    findActiveByUser: jest.fn(),
    findByUser: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SubscriptionGuard,
        {
          provide: SubscriptionService,
          useValue: mockSubscriptionService,
        },
      ],
    }).compile();

    guard = module.get<SubscriptionGuard>(SubscriptionGuard);
    subscriptionService = module.get(SubscriptionService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const createMockExecutionContext = (user: any): ExecutionContext => {
    return {
      switchToHttp: () => ({
        getRequest: () => ({ user }),
      }),
    } as ExecutionContext;
  };

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  it('should allow access if user has active subscription', async () => {
    const user = { id: '1' };
    const mockSubscription = {
      id: '1',
      status: SubscriptionStatus.ACTIVE,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    };

    subscriptionService.findActiveByUser.mockResolvedValue(mockSubscription as any);

    const context = createMockExecutionContext(user);
    const result = await guard.canActivate(context);

    expect(result).toBe(true);
    expect(subscriptionService.findActiveByUser).toHaveBeenCalledWith('1');
  });

  it('should allow access if user has active free trial', async () => {
    const user = { id: '1' };
    const mockFreeTrialSubscription = {
      id: '1',
      status: SubscriptionStatus.FREE_TRIAL,
      freeTrialEndsAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    };

    subscriptionService.findActiveByUser.mockResolvedValue(null);
    subscriptionService.findByUser.mockResolvedValue([mockFreeTrialSubscription as any]);

    const context = createMockExecutionContext(user);
    const result = await guard.canActivate(context);

    expect(result).toBe(true);
    expect(subscriptionService.findActiveByUser).toHaveBeenCalledWith('1');
    expect(subscriptionService.findByUser).toHaveBeenCalledWith('1');
  });

  it('should deny access if user has no active subscription or trial', async () => {
    const user = { id: '1' };

    subscriptionService.findActiveByUser.mockResolvedValue(null);
    subscriptionService.findByUser.mockResolvedValue([]);

    const context = createMockExecutionContext(user);

    await expect(guard.canActivate(context)).rejects.toThrow(ForbiddenException);
    expect(subscriptionService.findActiveByUser).toHaveBeenCalledWith('1');
    expect(subscriptionService.findByUser).toHaveBeenCalledWith('1');
  });

  it('should deny access if user has expired free trial', async () => {
    const user = { id: '1' };
    const mockExpiredTrialSubscription = {
      id: '1',
      status: SubscriptionStatus.FREE_TRIAL,
      freeTrialEndsAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
    };

    subscriptionService.findActiveByUser.mockResolvedValue(null);
    subscriptionService.findByUser.mockResolvedValue([mockExpiredTrialSubscription as any]);

    const context = createMockExecutionContext(user);

    await expect(guard.canActivate(context)).rejects.toThrow(ForbiddenException);
  });

  it('should throw ForbiddenException if user is not authenticated', async () => {
    const context = createMockExecutionContext(null);

    await expect(guard.canActivate(context)).rejects.toThrow(ForbiddenException);
  });
});
