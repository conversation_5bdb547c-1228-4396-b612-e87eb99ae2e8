import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { SubscriptionService } from '../../monetization/services/subscription.service';
import { SubscriptionStatus } from '../../monetization/entities/subscription.entity';

@Injectable()
export class SubscriptionGuard implements CanActivate {
  constructor(private subscriptionService: SubscriptionService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    // Check if user has an active subscription
    const activeSubscription = await this.subscriptionService.findActiveByUser(user.id);
    
    if (activeSubscription) {
      // User has active subscription
      return true;
    }

    // Check if user has an active free trial
    const subscriptions = await this.subscriptionService.findByUser(user.id);
    const freeTrialSubscription = subscriptions.find(
      sub => sub.status === SubscriptionStatus.FREE_TRIAL && 
             sub.freeTrialEndsAt && 
             new Date() < sub.freeTrialEndsAt
    );

    if (freeTrialSubscription) {
      // User has active free trial
      return true;
    }

    throw new ForbiddenException(
      'Access denied. Premium features require an active subscription or free trial.'
    );
  }
}
