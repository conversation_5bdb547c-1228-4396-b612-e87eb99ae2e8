import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigService } from '@nestjs/config';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { AuthWebController } from './auth-web.controller';
import { UsersModule } from '../users/users.module';
import { JwtStrategy } from './strategies/jwt.strategy';
import { LocalStrategy } from './strategies/local.strategy';
import { FirebaseAuthService } from './firebase-auth.service';
import { FirebaseModule } from '../firebase/firebase.module';
import { CommonModule } from '../common/common.module';

@Module({
  imports: [
    UsersModule,
    PassportModule,
    FirebaseModule,
    CommonModule,
    JwtModule.registerAsync({
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('jwt.secret'),
        signOptions: { expiresIn: configService.get('jwt.expiresIn') },
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [AuthService, JwtStrategy, LocalStrategy, FirebaseAuthService],
  controllers: [AuthController, AuthWebController],
  exports: [AuthService, FirebaseAuthService],
})
export class AuthModule {}
