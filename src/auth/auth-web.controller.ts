import { Controller, Get, Post, Body, Res, Query, HttpStatus } from '@nestjs/common';
import type { Response } from 'express';
import { AuthService } from './auth.service';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { layout } from 'pdfkit/js/page';

@Controller()
export class AuthWebController {
  constructor(private readonly authService: AuthService) {}

  @Get('/reset-password')
  async showResetPasswordForm(@Query('code') code: string, @Res() res: Response) {
    return res.render('reset-password', { 
      title: 'Reset Password - Power Up',
      code: code || '',
      message: '',
      error: '',
      layout: false,
    });
  }

  @Post('/reset-password')
  async handleResetPassword(
    @Body() resetPasswordDto: ResetPasswordDto,
    @Res() res: Response
  ) {
    try {
      const result = await this.authService.resetPassword(resetPasswordDto);
      
      return res.render('reset-password', {
        title: 'Reset Password - Power Up',
        code: '',
        message: result.message,
        error: '',
        success: true
      });
    } catch (error) {
      return res.render('reset-password', {
        title: 'Reset Password - Power Up',
        code: resetPasswordDto.code || '',
        message: '',
        error: error.message,
        success: false
      });
    }
  }
}
