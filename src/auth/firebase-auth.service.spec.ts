import { Test, TestingModule } from '@nestjs/testing';
import { FirebaseAuthService } from './firebase-auth.service';
import { FirebaseService } from '../firebase/firebase.service';
import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';

describe('FirebaseAuthService', () => {
  let service: FirebaseAuthService;
  let firebaseService: FirebaseService;
  let usersService: UsersService;
  let jwtService: JwtService;

  const mockFirebaseService = {
    verifyIdToken: jest.fn(),
  };

  const mockUsersService = {
    findOneByEmail: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();
    
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FirebaseAuthService,
        {
          provide: FirebaseService,
          useValue: mockFirebaseService,
        },
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
      ],
    }).compile();

    service = module.get<FirebaseAuthService>(FirebaseAuthService);
    firebaseService = module.get<FirebaseService>(FirebaseService);
    usersService = module.get<UsersService>(UsersService);
    jwtService = module.get<JwtService>(JwtService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('verifyFirebaseToken', () => {
    const mockToken = 'mock-firebase-id-token';
    const mockDecodedToken = {
      uid: 'firebase-user-id',
      email: '<EMAIL>',
      name: 'Test User',
      picture: 'https://example.com/profile.jpg',
    };

    it('should create a new user if they do not exist', async () => {
      mockFirebaseService.verifyIdToken.mockResolvedValue(mockDecodedToken);
      mockUsersService.findOneByEmail.mockResolvedValue(null);
      
      const mockCreatedUser = {
        id: 'user-id',
        email: mockDecodedToken.email,
        name: mockDecodedToken.name,
        firebaseUid: mockDecodedToken.uid,
        picture: mockDecodedToken.picture,
      };
      mockUsersService.create.mockResolvedValue(mockCreatedUser);
      
      const mockToken = 'app-jwt-token';
      mockJwtService.sign.mockReturnValue(mockToken);

      const result = await service.verifyFirebaseToken(mockToken);

      expect(mockFirebaseService.verifyIdToken).toHaveBeenCalledWith(mockToken);
      expect(mockUsersService.findOneByEmail).toHaveBeenCalledWith(mockDecodedToken.email);
      expect(mockUsersService.create).toHaveBeenCalledWith({
        email: mockDecodedToken.email,
        name: mockDecodedToken.name,
        firebaseUid: mockDecodedToken.uid,
        picture: mockDecodedToken.picture,
      });
      expect(mockJwtService.sign).toHaveBeenCalledWith({
        sub: mockCreatedUser.id,
        email: mockCreatedUser.email
      });
      expect(result).toEqual({
        user: mockCreatedUser,
        token: mockToken,
      });
    });

    it('should update an existing user if they have no Firebase UID', async () => {
      mockFirebaseService.verifyIdToken.mockResolvedValue(mockDecodedToken);
      
      const mockExistingUser = {
        id: 'user-id',
        email: mockDecodedToken.email,
        name: 'Existing Name',
        firebaseUid: null,
        picture: null,
      };
      mockUsersService.findOneByEmail.mockResolvedValue(mockExistingUser);
      
      const mockUpdatedUser = {
        ...mockExistingUser,
        firebaseUid: mockDecodedToken.uid,
        picture: mockDecodedToken.picture,
      };
      mockUsersService.update.mockResolvedValue(mockUpdatedUser);
      
      const mockToken = 'app-jwt-token';
      mockJwtService.sign.mockReturnValue(mockToken);

      const result = await service.verifyFirebaseToken(mockToken);

      expect(mockFirebaseService.verifyIdToken).toHaveBeenCalledWith(mockToken);
      expect(mockUsersService.findOneByEmail).toHaveBeenCalledWith(mockDecodedToken.email);
      expect(mockUsersService.update).toHaveBeenCalledWith(mockExistingUser.id, {
        firebaseUid: mockDecodedToken.uid,
        picture: mockDecodedToken.picture,
      });
      expect(mockJwtService.sign).toHaveBeenCalledWith({
        sub: mockUpdatedUser.id,
        email: mockUpdatedUser.email
      });
      expect(result).toEqual({
        user: mockUpdatedUser,
        token: mockToken,
      });
    });

    it('should use existing user if they already have Firebase UID', async () => {
      mockFirebaseService.verifyIdToken.mockResolvedValue(mockDecodedToken);
      
      const mockExistingUser = {
        id: 'user-id',
        email: mockDecodedToken.email,
        name: 'Existing Name',
        firebaseUid: mockDecodedToken.uid,
        picture: 'existing-picture.jpg',
      };
      mockUsersService.findOneByEmail.mockResolvedValue(mockExistingUser);
      
      const mockToken = 'app-jwt-token';
      mockJwtService.sign.mockReturnValue(mockToken);

      const result = await service.verifyFirebaseToken(mockToken);

      expect(mockFirebaseService.verifyIdToken).toHaveBeenCalledWith(mockToken);
      expect(mockUsersService.findOneByEmail).toHaveBeenCalledWith(mockDecodedToken.email);
      expect(mockUsersService.update).not.toHaveBeenCalled();
      expect(mockJwtService.sign).toHaveBeenCalledWith({
        sub: mockExistingUser.id,
        email: mockExistingUser.email
      });
      expect(result).toEqual({
        user: mockExistingUser,
        token: mockToken,
      });
    });

    it('should propagate errors when token verification fails', async () => {
      const error = new Error('Invalid token');
      mockFirebaseService.verifyIdToken.mockRejectedValue(error);

      await expect(service.verifyFirebaseToken(mockToken)).rejects.toThrow(error);
      expect(mockFirebaseService.verifyIdToken).toHaveBeenCalledWith(mockToken);
      expect(mockUsersService.findOneByEmail).not.toHaveBeenCalled();
    });
  });
});
