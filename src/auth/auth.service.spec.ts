import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';
import { EmailService } from '../common/services/email.service';
import { FirebaseService } from '../firebase/firebase.service';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcrypt';

describe('AuthService', () => {
  let service: AuthService;
  let usersService: UsersService;
  let jwtService: JwtService;
  let emailService: EmailService;
  let firebaseService: FirebaseService;

  const mockUsersService = {
    findByEmail: jest.fn(),
    create: jest.fn(),
    updatePassword: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn(),
    verify: jest.fn(),
  };

  const mockEmailService = {
    sendPasswordResetEmail: jest.fn(),
  };

  const mockFirebaseService = {
    generatePasswordResetLink: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: EmailService,
          useValue: mockEmailService,
        },
        {
          provide: FirebaseService,
          useValue: mockFirebaseService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    usersService = module.get<UsersService>(UsersService);
    jwtService = module.get<JwtService>(JwtService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('validateUser', () => {
    it('should return user object when credentials are valid', async () => {
      const testUser = {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        password: await bcrypt.hash('testpass', 10),
      };

      mockUsersService.findByEmail.mockResolvedValue(testUser);

      const result = await service.validateUser('<EMAIL>', 'testpass');
      expect(result).toBeDefined();
      expect(result.id).toBe(testUser.id);
      expect(result.email).toBe(testUser.email);
      expect(result.password).toBeUndefined();
    });

    it('should return null when user is not found', async () => {
      mockUsersService.findByEmail.mockResolvedValue(null);

      const result = await service.validateUser('<EMAIL>', 'testpass');
      expect(result).toBeNull();
    });

    it('should return null when password is invalid', async () => {
      const testUser = {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        password: await bcrypt.hash('testpass', 10),
      };

      mockUsersService.findByEmail.mockResolvedValue(testUser);

      const result = await service.validateUser('<EMAIL>', 'wrongpass');
      expect(result).toBeNull();
    });
  });

  describe('login', () => {
    it('should return JWT token when login is successful', async () => {
      const testUser = {
        id: '1',
        email: '<EMAIL>',
      };

      const testToken = 'test-jwt-token';
      mockJwtService.sign.mockReturnValue(testToken);

      const result = await service.login(testUser);
      expect(result.access_token).toBe(testToken);
      expect(mockJwtService.sign).toHaveBeenCalledWith({ 
        email: testUser.email, 
        sub: testUser.id 
      });
    });
  });

  describe('forgotPassword', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should send password reset email when user exists', async () => {
      const testUser = {
        id: '1',
        email: '<EMAIL>',
      };
      const resetToken = 'reset-token';

      mockUsersService.findByEmail.mockResolvedValue(testUser);
      mockJwtService.sign.mockReturnValue(resetToken);
      mockEmailService.sendPasswordResetEmail.mockResolvedValue(undefined);

      const result = await service.forgotPassword({ email: testUser.email });

      expect(mockUsersService.findByEmail).toHaveBeenCalledWith(testUser.email);
      expect(mockJwtService.sign).toHaveBeenCalledWith(
        { email: testUser.email, sub: testUser.id, type: 'password-reset' },
        { expiresIn: '1h' }
      );
      expect(mockEmailService.sendPasswordResetEmail).toHaveBeenCalledWith(testUser.email, resetToken);
      expect(result.message).toBe('If an account with that email exists, a password reset link has been sent.');
    });

    it('should return success message even when user does not exist', async () => {
      mockUsersService.findByEmail.mockResolvedValue(null);

      const result = await service.forgotPassword({ email: '<EMAIL>' });

      expect(mockUsersService.findByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(mockJwtService.sign).not.toHaveBeenCalled();
      expect(mockEmailService.sendPasswordResetEmail).not.toHaveBeenCalled();
      expect(result.message).toBe('If an account with that email exists, a password reset link has been sent.');
    });
  });

  describe('resetPassword', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should reset password successfully with valid token', async () => {
      const testUser = {
        id: '1',
        email: '<EMAIL>',
      };
      const resetDto = {
        token: 'valid-reset-token',
        newPassword: 'newpassword123',
      };

      mockJwtService.verify.mockReturnValue({
        email: testUser.email,
        sub: testUser.id,
        type: 'password-reset',
      });
      mockUsersService.findByEmail.mockResolvedValue(testUser);
      mockUsersService.updatePassword.mockResolvedValue(undefined);

      const result = await service.resetPassword(resetDto);

      expect(mockJwtService.verify).toHaveBeenCalledWith(resetDto.token);
      expect(mockUsersService.findByEmail).toHaveBeenCalledWith(testUser.email);
      expect(mockUsersService.updatePassword).toHaveBeenCalledWith(
        testUser.id,
        expect.any(String) // bcrypt hashed password
      );
      expect(result.message).toBe('Password has been reset successfully.');
    });
  });
});
