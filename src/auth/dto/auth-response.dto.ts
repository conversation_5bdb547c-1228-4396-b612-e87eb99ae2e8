import { ApiProperty } from '@nestjs/swagger';

export class AuthResponseDto {
  @ApiProperty({
    description: 'JWT access token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  })
  access_token: string;

  @ApiProperty({
    description: 'User info',
    example: {
      id: '123e4567-e89b-12d3-a456-426614174000',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe'
    }
  })
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
}
