import { Command, CommandRunner } from 'nest-commander';
import { FirebaseMigrationService } from '../firebase/firebase-migration.service';

@Command({
  name: 'init-firebase',
  description: 'Initialize Firebase collections and indexes',
})
export class InitFirebaseCommand extends CommandRunner {
  constructor(private readonly firebaseMigrationService: FirebaseMigrationService) {
    super();
  }

  async run(): Promise<void> {
    try {
      console.log('Starting Firebase initialization...');
      const result = await this.firebaseMigrationService.initializeFirebase();
      console.log(result);
      console.log('Firebase initialization completed successfully!');
    } catch (error) {
      console.error('Firebase initialization failed:');
      console.error(error);
      process.exit(1);
    }
  }
}
