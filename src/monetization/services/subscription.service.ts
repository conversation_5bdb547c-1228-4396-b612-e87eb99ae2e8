import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, MoreThan, LessThan } from 'typeorm';
import { Subscription, SubscriptionStatus } from '../entities/subscription.entity';
import { Transaction, TransactionType, TransactionStatus } from '../entities/transaction.entity';
import { SubscriptionPlatform } from '../entities/subscription-plan.entity';
import { CreateSubscriptionDto, UpdateSubscriptionDto, VerifyReceiptDto, VerifyPurchaseTokenDto } from '../dto/subscription.dto';
import { SubscriptionPlanService } from './subscription-plan.service';
import { CouponCodeService } from './coupon-code.service';
import { AppStoreReceiptService } from './app-store-receipt.service';
import { GooglePlayReceiptService } from './google-play-receipt.service';

@Injectable()
export class SubscriptionService {
  private readonly logger = new Logger(SubscriptionService.name);

  constructor(
    @InjectRepository(Subscription)
    private subscriptionRepository: Repository<Subscription>,
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
    private subscriptionPlanService: SubscriptionPlanService,
    private couponCodeService: CouponCodeService,
    private appStoreReceiptService: AppStoreReceiptService,
    private googlePlayReceiptService: GooglePlayReceiptService,
  ) {}

  async create(userId: string, createDto: CreateSubscriptionDto): Promise<Subscription> {
    // Validate plan exists
    await this.subscriptionPlanService.findOne(createDto.planId);

    // Validate coupon if provided
    if (createDto.couponId) {
      const couponValidation = await this.couponCodeService.validateCoupon(
        { code: '', planId: createDto.planId },
        userId,
      );
      if (!couponValidation.valid) {
        throw new BadRequestException(couponValidation.message);
      }
    }

    const subscription = this.subscriptionRepository.create({
      ...createDto,
      userId,
    });

    return this.subscriptionRepository.save(subscription);
  }

  async findAll(): Promise<Subscription[]> {
    return this.subscriptionRepository.find({
      relations: ['user', 'plan', 'coupon'],
      order: { createdAt: 'DESC' },
    });
  }

  async findByUser(userId: string): Promise<Subscription[]> {
    return this.subscriptionRepository.find({
      where: { userId },
      relations: ['plan', 'coupon'],
      order: { createdAt: 'DESC' },
    });
  }

  async findActiveByUser(userId: string): Promise<Subscription | null> {
    const now = new Date();
    return this.subscriptionRepository.findOne({
      where: {
        userId,
        status: SubscriptionStatus.ACTIVE,
        expiresAt: MoreThan(now),
      },
      relations: ['plan', 'coupon'],
    });
  }

  async findOne(id: string): Promise<Subscription> {
    const subscription = await this.subscriptionRepository.findOne({
      where: { id },
      relations: ['user', 'plan', 'coupon'],
    });

    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }

    return subscription;
  }

  async update(id: string, updateDto: UpdateSubscriptionDto): Promise<Subscription> {
    const subscription = await this.findOne(id);
    Object.assign(subscription, updateDto);
    return this.subscriptionRepository.save(subscription);
  }

  async cancel(id: string): Promise<Subscription> {
    const subscription = await this.findOne(id);
    subscription.status = SubscriptionStatus.CANCELLED;
    subscription.cancelledAt = new Date();
    subscription.autoRenew = false;
    return this.subscriptionRepository.save(subscription);
  }

  async verifyAppleReceipt(userId: string, verifyDto: VerifyReceiptDto): Promise<Subscription> {
    this.logger.log(`Verifying Apple receipt for user ${userId}`);
    
    const result = await this.appStoreReceiptService.verifyReceipt(verifyDto.receipt, userId);
    
    if (!result.valid) {
      throw new BadRequestException(result.message);
    }

    if (!result.subscription) {
      throw new BadRequestException('No subscription created from receipt verification');
    }

    return result.subscription;
  }

  async verifyGooglePlayPurchase(userId: string, verifyDto: VerifyPurchaseTokenDto): Promise<Subscription> {
    this.logger.log(`Verifying Google Play purchase for user ${userId}`);
    
    const result = await this.googlePlayReceiptService.verifyPurchase(
      verifyDto.packageName,
      verifyDto.productId,
      verifyDto.purchaseToken,
      userId,
    );
    
    if (!result.valid) {
      throw new BadRequestException(result.message);
    }

    if (!result.subscription) {
      throw new BadRequestException('No subscription created from purchase verification');
    }

    return result.subscription;
  }

  async createTransaction(transactionData: Partial<Transaction>): Promise<Transaction> {
    const transaction = this.transactionRepository.create(transactionData);
    return this.transactionRepository.save(transaction);
  }

  async getSubscriptionStatus(userId: string): Promise<{
    hasActiveSubscription: boolean;
    subscription?: Subscription;
    daysUntilExpiry?: number;
  }> {
    const activeSubscription = await this.findActiveByUser(userId);
    
    if (!activeSubscription) {
      return { hasActiveSubscription: false };
    }

    const now = new Date();
    const daysUntilExpiry = Math.ceil(
      (activeSubscription.expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60 * 24),
    );

    return {
      hasActiveSubscription: true,
      subscription: activeSubscription,
      daysUntilExpiry,
    };
  }

  async renewSubscription(subscriptionId: string): Promise<Subscription> {
    const subscription = await this.findOne(subscriptionId);
    const plan = await this.subscriptionPlanService.findOne(subscription.planId);

    // Calculate new expiry date based on plan type
    const currentExpiry = subscription.expiresAt;
    let newExpiry: Date;

    switch (plan.type) {
      case 'weekly':
        newExpiry = new Date(currentExpiry.getTime() + 7 * 24 * 60 * 60 * 1000);
        break;
      case 'monthly':
        newExpiry = new Date(currentExpiry);
        newExpiry.setMonth(newExpiry.getMonth() + 1);
        break;
      case 'yearly':
        newExpiry = new Date(currentExpiry);
        newExpiry.setFullYear(newExpiry.getFullYear() + 1);
        break;
      default:
        throw new BadRequestException('Invalid subscription plan type');
    }

    subscription.expiresAt = newExpiry;
    subscription.status = SubscriptionStatus.ACTIVE;

    return this.subscriptionRepository.save(subscription);
  }

  async pauseSubscription(id: string): Promise<Subscription> {
    const subscription = await this.findOne(id);
    
    if (subscription.status !== SubscriptionStatus.ACTIVE) {
      throw new BadRequestException('Only active subscriptions can be paused');
    }

    subscription.status = SubscriptionStatus.GRACE_PERIOD;
    return this.subscriptionRepository.save(subscription);
  }

  async resumeSubscription(id: string): Promise<Subscription> {
    const subscription = await this.findOne(id);
    
    if (subscription.status !== SubscriptionStatus.GRACE_PERIOD) {
      throw new BadRequestException('Only paused subscriptions can be resumed');
    }

    const now = new Date();
    if (subscription.expiresAt > now) {
      subscription.status = SubscriptionStatus.ACTIVE;
    } else {
      subscription.status = SubscriptionStatus.EXPIRED;
    }

    return this.subscriptionRepository.save(subscription);
  }

  async extendSubscription(id: string, days: number): Promise<Subscription> {
    const subscription = await this.findOne(id);
    
    const newExpiryDate = new Date(subscription.expiresAt);
    newExpiryDate.setDate(newExpiryDate.getDate() + days);
    
    subscription.expiresAt = newExpiryDate;
    
    // Update status if needed
    const now = new Date();
    if (newExpiryDate > now && subscription.status === SubscriptionStatus.EXPIRED) {
      subscription.status = SubscriptionStatus.ACTIVE;
    }

    return this.subscriptionRepository.save(subscription);
  }

  async getSubscriptionAnalytics(userId?: string): Promise<{
    totalSubscriptions: number;
    activeSubscriptions: number;
    expiredSubscriptions: number;
    cancelledSubscriptions: number;
    totalRevenue: number;
    averageSubscriptionLength: number;
  }> {
    const queryBuilder = this.subscriptionRepository.createQueryBuilder('subscription')
      .leftJoin('subscription.plan', 'plan');

    if (userId) {
      queryBuilder.where('subscription.userId = :userId', { userId });
    }

    const subscriptions = await queryBuilder.getMany();

    const analytics = {
      totalSubscriptions: subscriptions.length,
      activeSubscriptions: subscriptions.filter(s => s.status === SubscriptionStatus.ACTIVE).length,
      expiredSubscriptions: subscriptions.filter(s => s.status === SubscriptionStatus.EXPIRED).length,
      cancelledSubscriptions: subscriptions.filter(s => s.status === SubscriptionStatus.CANCELLED).length,
      totalRevenue: subscriptions.reduce((sum, s) => sum + (s.price || 0), 0),
      averageSubscriptionLength: 0,
    };

    // Calculate average subscription length
    const completedSubscriptions = subscriptions.filter(s => 
      s.status === SubscriptionStatus.EXPIRED || s.status === SubscriptionStatus.CANCELLED
    );

    if (completedSubscriptions.length > 0) {
      const totalDays = completedSubscriptions.reduce((sum, s) => {
        const endDate = s.cancelledAt || s.expiresAt;
        const startDate = s.startsAt;
        const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
        return sum + days;
      }, 0);

      analytics.averageSubscriptionLength = Math.round(totalDays / completedSubscriptions.length);
    }

    return analytics;
  }

  async getExpiringSubscriptions(daysBefore: number = 7): Promise<Subscription[]> {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + daysBefore);

    return this.subscriptionRepository.find({
      where: {
        status: SubscriptionStatus.ACTIVE,
        expiresAt: LessThan(futureDate),
        autoRenew: false,
      },
      relations: ['user', 'plan'],
      order: { expiresAt: 'ASC' },
    });
  }

  async getUserSubscriptionHistory(userId: string): Promise<{
    subscriptions: Subscription[];
    transactions: Transaction[];
    totalSpent: number;
  }> {
    const subscriptions = await this.findByUser(userId);
    
    const transactions = await this.transactionRepository.find({
      where: { 
        userId,
        status: TransactionStatus.COMPLETED,
      },
      order: { createdAt: 'DESC' },
    });

    const totalSpent = transactions.reduce((sum, t) => sum + t.amount, 0);

    return {
      subscriptions,
      transactions,
      totalSpent,
    };
  }

  async validateSubscriptionAccess(userId: string, requiredFeatures?: string[]): Promise<{
    hasAccess: boolean;
    subscription?: Subscription;
    missingFeatures?: string[];
  }> {
    const activeSubscription = await this.findActiveByUser(userId);

    if (!activeSubscription) {
      return {
        hasAccess: false,
        missingFeatures: requiredFeatures || [],
      };
    }

    // If no specific features required, just check for active subscription
    if (!requiredFeatures || requiredFeatures.length === 0) {
      return {
        hasAccess: true,
        subscription: activeSubscription,
      };
    }

    // Check if plan includes required features
    const planFeatures = activeSubscription.plan?.features || [];
    const missingFeatures = requiredFeatures.filter(
      feature => !planFeatures.includes(feature)
    );

    return {
      hasAccess: missingFeatures.length === 0,
      subscription: activeSubscription,
      missingFeatures: missingFeatures.length > 0 ? missingFeatures : undefined,
    };
  }

  async checkExpiredSubscriptions(): Promise<void> {
    const now = new Date();
    const expiredSubscriptions = await this.subscriptionRepository.find({
      where: {
        status: SubscriptionStatus.ACTIVE,
        expiresAt: LessThan(now),
      },
    });

    for (const subscription of expiredSubscriptions) {
      subscription.status = SubscriptionStatus.EXPIRED;
      await this.subscriptionRepository.save(subscription);
      this.logger.log(`Subscription ${subscription.id} marked as expired`);
    }
  }
}
