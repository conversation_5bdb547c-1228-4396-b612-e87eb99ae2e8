import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Subscription, SubscriptionStatus } from '../entities/subscription.entity';
import { Transaction, TransactionType, TransactionStatus } from '../entities/transaction.entity';
import { SubscriptionService } from './subscription.service';

interface AppStoreWebhookPayload {
  notification_type: string;
  password: string;
  transaction_info?: {
    original_transaction_id: string;
    transaction_id: string;
    product_id: string;
    purchase_date: string;
    expires_date?: string;
    cancellation_date?: string;
    is_trial_period?: string;
  };
  auto_renew_status_change_date?: string;
  auto_renew_status?: string;
}

interface GooglePlayWebhookPayload {
  message: {
    data: string; // Base64 encoded JSON
    messageId: string;
    publishTime: string;
  };
  subscription?: string;
}

interface GooglePlayNotificationData {
  version: string;
  packageName: string;
  eventTimeMillis: string;
  subscriptionNotification?: {
    version: string;
    notificationType: number;
    purchaseToken: string;
    subscriptionId: string;
  };
}

@Injectable()
export class SubscriptionWebhookService {
  private readonly logger = new Logger(SubscriptionWebhookService.name);

  constructor(
    @InjectRepository(Subscription)
    private subscriptionRepository: Repository<Subscription>,
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
    private subscriptionService: SubscriptionService,
  ) {}

  async handleAppStoreWebhook(payload: AppStoreWebhookPayload): Promise<void> {
    try {
      this.logger.log(`Processing App Store webhook: ${payload.notification_type}`);

      if (!payload.transaction_info) {
        this.logger.warn('App Store webhook missing transaction info');
        return;
      }

      const transactionInfo = payload.transaction_info;
      
      // Find subscription by original transaction ID
      const subscription = await this.subscriptionRepository.findOne({
        where: {
          originalTransactionId: transactionInfo.original_transaction_id,
        },
        relations: ['user', 'plan'],
      });

      if (!subscription) {
        this.logger.warn(`Subscription not found for transaction ID: ${transactionInfo.original_transaction_id}`);
        return;
      }

      switch (payload.notification_type) {
        case 'RENEWAL':
          await this.handleAppStoreRenewal(subscription, transactionInfo);
          break;

        case 'CANCEL':
        case 'DID_FAIL_TO_RENEW':
          await this.handleAppStoreCancellation(subscription, transactionInfo);
          break;

        case 'REFUND':
          await this.handleAppStoreRefund(subscription, transactionInfo);
          break;

        case 'DID_CHANGE_RENEWAL_PREF':
          await this.handleAppStoreRenewalPreferenceChange(subscription, payload);
          break;

        case 'DID_RECOVER':
          await this.handleAppStoreRecovery(subscription, transactionInfo);
          break;

        default:
          this.logger.log(`Unhandled App Store notification type: ${payload.notification_type}`);
      }
    } catch (error) {
      this.logger.error('Error processing App Store webhook', error);
      throw error;
    }
  }

  async handleGooglePlayWebhook(payload: GooglePlayWebhookPayload): Promise<void> {
    try {
      this.logger.log('Processing Google Play webhook');

      if (!payload.message?.data) {
        throw new BadRequestException('Invalid Google Play webhook payload');
      }

      // Decode base64 data
      const decodedData = Buffer.from(payload.message.data, 'base64').toString();
      const notificationData: GooglePlayNotificationData = JSON.parse(decodedData);

      if (!notificationData.subscriptionNotification) {
        this.logger.log('Non-subscription notification received');
        return;
      }

      const subscriptionNotification = notificationData.subscriptionNotification;

      // Find subscription by purchase token
      const subscription = await this.subscriptionRepository.findOne({
        where: {
          purchaseToken: subscriptionNotification.purchaseToken,
        },
        relations: ['user', 'plan'],
      });

      if (!subscription) {
        this.logger.warn(`Subscription not found for purchase token: ${subscriptionNotification.purchaseToken}`);
        return;
      }

      switch (subscriptionNotification.notificationType) {
        case 1: // SUBSCRIPTION_RECOVERED
          await this.handleGooglePlayRecovery(subscription);
          break;

        case 2: // SUBSCRIPTION_RENEWED
          await this.handleGooglePlayRenewal(subscription);
          break;

        case 3: // SUBSCRIPTION_CANCELED
          await this.handleGooglePlayCancellation(subscription);
          break;

        case 4: // SUBSCRIPTION_PURCHASED
          this.logger.log('Subscription purchased notification (already handled)');
          break;

        case 5: // SUBSCRIPTION_ON_HOLD
          await this.handleGooglePlayOnHold(subscription);
          break;

        case 6: // SUBSCRIPTION_IN_GRACE_PERIOD
          await this.handleGooglePlayGracePeriod(subscription);
          break;

        case 7: // SUBSCRIPTION_RESTARTED
          await this.handleGooglePlayRestart(subscription);
          break;

        case 8: // SUBSCRIPTION_PRICE_CHANGE_CONFIRMED
          this.logger.log('Price change confirmed');
          break;

        case 9: // SUBSCRIPTION_DEFERRED
          await this.handleGooglePlayDeferred(subscription);
          break;

        case 10: // SUBSCRIPTION_PAUSED
          await this.handleGooglePlayPaused(subscription);
          break;

        case 11: // SUBSCRIPTION_PAUSE_SCHEDULE_CHANGED
          this.logger.log('Pause schedule changed');
          break;

        case 12: // SUBSCRIPTION_REVOKED
          await this.handleGooglePlayRevoked(subscription);
          break;

        case 13: // SUBSCRIPTION_EXPIRED
          await this.handleGooglePlayExpired(subscription);
          break;

        default:
          this.logger.log(`Unhandled Google Play notification type: ${subscriptionNotification.notificationType}`);
      }
    } catch (error) {
      this.logger.error('Error processing Google Play webhook', error);
      throw error;
    }
  }

  private async handleAppStoreRenewal(subscription: Subscription, transactionInfo: any): Promise<void> {
    const newExpiryDate = new Date(transactionInfo.expires_date);
    
    subscription.expiresAt = newExpiryDate;
    subscription.status = SubscriptionStatus.ACTIVE;
    await this.subscriptionRepository.save(subscription);

    // Create renewal transaction
    await this.createTransaction({
      userId: subscription.userId,
      subscriptionId: subscription.id,
      type: TransactionType.RENEWAL,
      status: TransactionStatus.COMPLETED,
      amount: subscription.price || 0,
      currency: subscription.currency || 'USD',
      platformTransactionId: transactionInfo.transaction_id,
      originalTransactionId: transactionInfo.original_transaction_id,
      completedAt: new Date(transactionInfo.purchase_date),
    });

    this.logger.log(`Subscription renewed: ${subscription.id}`);
  }

  private async handleAppStoreCancellation(subscription: Subscription, transactionInfo: any): Promise<void> {
    subscription.status = SubscriptionStatus.CANCELLED;
    subscription.cancelledAt = transactionInfo.cancellation_date 
      ? new Date(transactionInfo.cancellation_date)
      : new Date();
    subscription.autoRenew = false;
    
    await this.subscriptionRepository.save(subscription);
    this.logger.log(`Subscription cancelled: ${subscription.id}`);
  }

  private async handleAppStoreRefund(subscription: Subscription, transactionInfo: any): Promise<void> {
    // Create refund transaction
    await this.createTransaction({
      userId: subscription.userId,
      subscriptionId: subscription.id,
      type: TransactionType.REFUND,
      status: TransactionStatus.COMPLETED,
      amount: -(subscription.price || 0),
      currency: subscription.currency || 'USD',
      platformTransactionId: transactionInfo.transaction_id,
      originalTransactionId: transactionInfo.original_transaction_id,
      completedAt: new Date(),
    });

    // Cancel subscription
    subscription.status = SubscriptionStatus.CANCELLED;
    subscription.cancelledAt = new Date();
    await this.subscriptionRepository.save(subscription);

    this.logger.log(`Subscription refunded: ${subscription.id}`);
  }

  private async handleAppStoreRenewalPreferenceChange(subscription: Subscription, payload: AppStoreWebhookPayload): Promise<void> {
    subscription.autoRenew = payload.auto_renew_status === '1';
    await this.subscriptionRepository.save(subscription);
    this.logger.log(`Auto-renew preference changed: ${subscription.id}`);
  }

  private async handleAppStoreRecovery(subscription: Subscription, transactionInfo: any): Promise<void> {
    subscription.status = SubscriptionStatus.ACTIVE;
    if (transactionInfo.expires_date) {
      subscription.expiresAt = new Date(transactionInfo.expires_date);
    }
    await this.subscriptionRepository.save(subscription);
    this.logger.log(`Subscription recovered: ${subscription.id}`);
  }

  private async handleGooglePlayRenewal(subscription: Subscription): Promise<void> {
    // Renew subscription - expiry date will be updated through purchase verification
    subscription.status = SubscriptionStatus.ACTIVE;
    await this.subscriptionRepository.save(subscription);
    this.logger.log(`Google Play subscription renewed: ${subscription.id}`);
  }

  private async handleGooglePlayCancellation(subscription: Subscription): Promise<void> {
    subscription.status = SubscriptionStatus.CANCELLED;
    subscription.cancelledAt = new Date();
    subscription.autoRenew = false;
    await this.subscriptionRepository.save(subscription);
    this.logger.log(`Google Play subscription cancelled: ${subscription.id}`);
  }

  private async handleGooglePlayRecovery(subscription: Subscription): Promise<void> {
    subscription.status = SubscriptionStatus.ACTIVE;
    await this.subscriptionRepository.save(subscription);
    this.logger.log(`Google Play subscription recovered: ${subscription.id}`);
  }

  private async handleGooglePlayOnHold(subscription: Subscription): Promise<void> {
    subscription.status = SubscriptionStatus.GRACE_PERIOD;
    await this.subscriptionRepository.save(subscription);
    this.logger.log(`Google Play subscription on hold: ${subscription.id}`);
  }

  private async handleGooglePlayGracePeriod(subscription: Subscription): Promise<void> {
    subscription.status = SubscriptionStatus.GRACE_PERIOD;
    await this.subscriptionRepository.save(subscription);
    this.logger.log(`Google Play subscription in grace period: ${subscription.id}`);
  }

  private async handleGooglePlayRestart(subscription: Subscription): Promise<void> {
    subscription.status = SubscriptionStatus.ACTIVE;
    await this.subscriptionRepository.save(subscription);
    this.logger.log(`Google Play subscription restarted: ${subscription.id}`);
  }

  private async handleGooglePlayDeferred(subscription: Subscription): Promise<void> {
    // Subscription payment deferred - keep current status
    this.logger.log(`Google Play subscription payment deferred: ${subscription.id}`);
  }

  private async handleGooglePlayPaused(subscription: Subscription): Promise<void> {
    subscription.status = SubscriptionStatus.GRACE_PERIOD;
    await this.subscriptionRepository.save(subscription);
    this.logger.log(`Google Play subscription paused: ${subscription.id}`);
  }

  private async handleGooglePlayRevoked(subscription: Subscription): Promise<void> {
    subscription.status = SubscriptionStatus.CANCELLED;
    subscription.cancelledAt = new Date();
    await this.subscriptionRepository.save(subscription);
    this.logger.log(`Google Play subscription revoked: ${subscription.id}`);
  }

  private async handleGooglePlayExpired(subscription: Subscription): Promise<void> {
    subscription.status = SubscriptionStatus.EXPIRED;
    await this.subscriptionRepository.save(subscription);
    this.logger.log(`Google Play subscription expired: ${subscription.id}`);
  }

  private async createTransaction(transactionData: Partial<Transaction>): Promise<Transaction> {
    const transaction = this.transactionRepository.create(transactionData);
    return this.transactionRepository.save(transaction);
  }
}
