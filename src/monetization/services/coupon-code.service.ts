import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, MoreThan } from 'typeorm';
import { CouponCode, CouponType } from '../entities/coupon-code.entity';
import { CreateCouponCodeDto, UpdateCouponCodeDto, ValidateCouponDto } from '../dto/coupon-code.dto';
import { User } from '../../users/entities/user.entity';
import { Subscription } from '../entities/subscription.entity';

@Injectable()
export class CouponCodeService {
  constructor(
    @InjectRepository(CouponCode)
    private couponCodeRepository: Repository<CouponCode>,
    @InjectRepository(Subscription)
    private subscriptionRepository: Repository<Subscription>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async create(createDto: CreateCouponCodeDto): Promise<CouponCode> {
    // Check if coupon code already exists
    const existingCoupon = await this.couponCodeRepository.findOne({
      where: { code: createDto.code.toUpperCase() },
    });

    if (existingCoupon) {
      throw new BadRequestException('Coupon code already exists');
    }

    const coupon = this.couponCodeRepository.create({
      ...createDto,
      code: createDto.code.toUpperCase(),
    });

    return this.couponCodeRepository.save(coupon);
  }

  async findAll(): Promise<CouponCode[]> {
    return this.couponCodeRepository.find({
      order: { createdAt: 'DESC' },
    });
  }

  async findActive(): Promise<CouponCode[]> {
    const now = new Date();
    return this.couponCodeRepository.find({
      where: {
        active: true,
        validFrom: MoreThan(now),
        validUntil: MoreThan(now),
      },
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<CouponCode> {
    const coupon = await this.couponCodeRepository.findOne({
      where: { id },
    });

    if (!coupon) {
      throw new NotFoundException('Coupon code not found');
    }

    return coupon;
  }

  async findByCode(code: string): Promise<CouponCode> {
    const coupon = await this.couponCodeRepository.findOne({
      where: { code: code.toUpperCase() },
    });

    if (!coupon) {
      throw new NotFoundException('Coupon code not found');
    }

    return coupon;
  }

  async validateCoupon(validateDto: ValidateCouponDto, userId: string): Promise<{
    valid: boolean;
    coupon?: CouponCode;
    discount?: number;
    message?: string;
  }> {
    try {
      const coupon = await this.findByCode(validateDto.code);
      const now = new Date();

      // Check if coupon is active
      if (!coupon.active) {
        return { valid: false, message: 'Coupon code is not active' };
      }

      // Check if coupon is within validity period
      if (now < coupon.validFrom || now > coupon.validUntil) {
        return { valid: false, message: 'Coupon code has expired or is not yet valid' };
      }

      // Check if coupon has reached maximum uses
      if (coupon.maxUses && coupon.usedCount >= coupon.maxUses) {
        return { valid: false, message: 'Coupon code has reached maximum usage limit' };
      }

      // Check if coupon is for first-time users only
      if (coupon.firstTimeOnly) {
        const existingSubscription = await this.subscriptionRepository.findOne({
          where: { userId },
        });

        if (existingSubscription) {
          return { valid: false, message: 'This coupon is only valid for first-time subscribers' };
        }
      }

      // Check if coupon applies to specific plans
      if (coupon.applicablePlans && coupon.applicablePlans.length > 0 && validateDto.planId) {
        if (!coupon.applicablePlans.includes(validateDto.planId)) {
          return { valid: false, message: 'This coupon is not applicable to the selected plan' };
        }
      }

      let discount = 0;
      if (coupon.type === CouponType.PERCENTAGE) {
        discount = coupon.value;
      } else if (coupon.type === CouponType.FIXED_AMOUNT) {
        discount = coupon.value;
      }

      return {
        valid: true,
        coupon,
        discount,
        message: 'Coupon code is valid',
      };
    } catch (error) {
      return { valid: false, message: 'Invalid coupon code' };
    }
  }

  async applyCoupon(couponId: string): Promise<CouponCode> {
    const coupon = await this.findOne(couponId);
    coupon.usedCount += 1;
    return this.couponCodeRepository.save(coupon);
  }

  async update(id: string, updateDto: UpdateCouponCodeDto): Promise<CouponCode> {
    const coupon = await this.findOne(id);
    Object.assign(coupon, updateDto);
    return this.couponCodeRepository.save(coupon);
  }

  async remove(id: string): Promise<void> {
    const coupon = await this.findOne(id);
    await this.couponCodeRepository.remove(coupon);
  }

  async deactivate(id: string): Promise<CouponCode> {
    return this.update(id, { active: false });
  }

  async activate(id: string): Promise<CouponCode> {
    return this.update(id, { active: true });
  }

  async getUsageStats(id: string): Promise<{
    coupon: CouponCode;
    usagePercentage: number;
    remainingUses: number | null;
  }> {
    const coupon = await this.findOne(id);
    const usagePercentage = coupon.maxUses ? (coupon.usedCount / coupon.maxUses) * 100 : 0;
    const remainingUses = coupon.maxUses ? coupon.maxUses - coupon.usedCount : null;

    return {
      coupon,
      usagePercentage,
      remainingUses,
    };
  }
}
