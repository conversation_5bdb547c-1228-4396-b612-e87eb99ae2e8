import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Subscription, SubscriptionStatus } from '../entities/subscription.entity';
import { Transaction, TransactionType, TransactionStatus } from '../entities/transaction.entity';
import { SubscriptionPlatform } from '../entities/subscription-plan.entity';
import { SubscriptionPlanService } from './subscription-plan.service';

interface GooglePlayPurchaseResponse {
  purchaseTimeMillis: string;
  purchaseState: number;
  consumptionState: number;
  developerPayload: string;
  orderId: string;
  purchaseType?: number;
  acknowledgmentState?: number;
  kind: string;
  regionCode?: string;
  startTimeMillis?: string;
  expiryTimeMillis?: string;
  autoRenewing?: boolean;
  priceCurrencyCode?: string;
  priceAmountMicros?: string;
  introductoryPriceInfo?: {
    introductoryPriceCurrencyCode: string;
    introductoryPriceAmountMicros: string;
    introductoryPricePeriod: string;
    introductoryPriceCycles: string;
  };
  trialInfo?: {
    trialPeriod: string;
  };
}

@Injectable()
export class GooglePlayReceiptService {
  private readonly logger = new Logger(GooglePlayReceiptService.name);
  private readonly baseUrl = 'https://androidpublisher.googleapis.com/androidpublisher/v3';

  constructor(
    private configService: ConfigService,
    @InjectRepository(Subscription)
    private subscriptionRepository: Repository<Subscription>,
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
    private subscriptionPlanService: SubscriptionPlanService,
  ) {}

  async verifyPurchase(
    packageName: string,
    productId: string,
    purchaseToken: string,
    userId: string,
  ): Promise<{
    valid: boolean;
    subscription?: Subscription;
    transaction?: Transaction;
    message: string;
  }> {
    try {
      const accessToken = await this.getAccessToken();
      if (!accessToken) {
        throw new BadRequestException('Google Play access token not available');
      }

      // Verify the purchase with Google Play API
      const purchaseResponse = await this.makePurchaseVerificationRequest(
        packageName,
        productId,
        purchaseToken,
        accessToken,
      );

      // Check purchase state (0 = purchased, 1 = canceled)
      if (purchaseResponse.purchaseState !== 0) {
        return {
          valid: false,
          message: 'Purchase is not in valid state',
        };
      }

      // Find the subscription plan by product ID
      const plan = await this.subscriptionPlanService.findByProductId(
        productId,
        SubscriptionPlatform.ANDROID,
      );
      if (!plan) {
        return {
          valid: false,
          message: `No subscription plan found for product ID: ${productId}`,
        };
      }

      // Check if subscription already exists
      let subscription = await this.subscriptionRepository.findOne({
        where: {
          userId,
          purchaseToken,
        },
      });

      const purchaseDate = new Date(parseInt(purchaseResponse.purchaseTimeMillis));
      const expiresDate = purchaseResponse.expiryTimeMillis
        ? new Date(parseInt(purchaseResponse.expiryTimeMillis))
        : null;

      if (!subscription) {
        // Create new subscription
        subscription = this.subscriptionRepository.create({
          userId,
          planId: plan.id,
          purchaseToken,
          startsAt: purchaseDate,
          expiresAt: expiresDate || this.calculateExpiryDate(purchaseDate, plan.type),
          status: this.determineSubscriptionStatus(expiresDate),
          price: plan.price,
          currency: plan.currency,
          autoRenew: purchaseResponse.autoRenewing || false,
        });

        subscription = await this.subscriptionRepository.save(subscription);
      } else {
        // Update existing subscription
        subscription.expiresAt = expiresDate || this.calculateExpiryDate(purchaseDate, plan.type);
        subscription.status = this.determineSubscriptionStatus(expiresDate);
        subscription.autoRenew = purchaseResponse.autoRenewing || false;
        subscription = await this.subscriptionRepository.save(subscription);
      }

      // Create transaction record
      const transaction = this.transactionRepository.create({
        userId,
        subscriptionId: subscription.id,
        type: TransactionType.SUBSCRIPTION_PURCHASE,
        status: TransactionStatus.COMPLETED,
        amount: plan.price,
        currency: plan.currency,
        platformTransactionId: purchaseResponse.orderId,
        platformPurchaseToken: purchaseToken,
        completedAt: purchaseDate,
        metadata: {
          packageName,
          productId,
          purchaseState: purchaseResponse.purchaseState,
          consumptionState: purchaseResponse.consumptionState,
        },
      });

      const savedTransaction = await this.transactionRepository.save(transaction);

      return {
        valid: true,
        subscription,
        transaction: savedTransaction,
        message: 'Purchase verified successfully',
      };
    } catch (error) {
      this.logger.error('Google Play purchase verification failed', error);
      return {
        valid: false,
        message: 'Purchase verification failed: ' + error.message,
      };
    }
  }

  private async getAccessToken(): Promise<string | null> {
    // In a real implementation, you would:
    // 1. Use Google Cloud service account credentials
    // 2. Generate JWT token
    // 3. Exchange for access token
    // For now, return a placeholder
    
    const serviceAccountKey = this.configService.get<string>('GOOGLE_PLAY_SERVICE_ACCOUNT_KEY');
    if (!serviceAccountKey) {
      this.logger.warn('Google Play service account key not configured');
      return null;
    }

    // TODO: Implement proper Google OAuth2 flow
    // This would typically involve:
    // - Creating a JWT with service account credentials
    // - Exchanging JWT for access token
    // - Caching the access token until expiry
    
    throw new BadRequestException('Google Play authentication not fully implemented');
  }

  private async makePurchaseVerificationRequest(
    packageName: string,
    productId: string,
    purchaseToken: string,
    accessToken: string,
  ): Promise<GooglePlayPurchaseResponse> {
    const url = `${this.baseUrl}/applications/${packageName}/purchases/subscriptions/${productId}/tokens/${purchaseToken}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  private determineSubscriptionStatus(expiresDate: Date | null): SubscriptionStatus {
    if (!expiresDate) {
      return SubscriptionStatus.ACTIVE;
    }

    const now = new Date();
    if (expiresDate > now) {
      return SubscriptionStatus.ACTIVE;
    } else {
      return SubscriptionStatus.EXPIRED;
    }
  }

  private calculateExpiryDate(startDate: Date, planType: string): Date {
    const expiry = new Date(startDate);
    
    switch (planType) {
      case 'weekly':
        expiry.setDate(expiry.getDate() + 7);
        break;
      case 'monthly':
        expiry.setMonth(expiry.getMonth() + 1);
        break;
      case 'yearly':
        expiry.setFullYear(expiry.getFullYear() + 1);
        break;
      default:
        expiry.setMonth(expiry.getMonth() + 1); // Default to monthly
    }

    return expiry;
  }
}
