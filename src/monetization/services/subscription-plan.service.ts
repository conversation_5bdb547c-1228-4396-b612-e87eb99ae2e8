import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SubscriptionPlan, SubscriptionPlatform } from '../entities/subscription-plan.entity';
import { CreateSubscriptionPlanDto, UpdateSubscriptionPlanDto } from '../dto/subscription-plan.dto';

@Injectable()
export class SubscriptionPlanService {
  constructor(
    @InjectRepository(SubscriptionPlan)
    private subscriptionPlanRepository: Repository<SubscriptionPlan>,
  ) {}

  async create(createDto: CreateSubscriptionPlanDto): Promise<SubscriptionPlan> {
    // Check if product ID already exists for the platform
    const existingPlan = await this.subscriptionPlanRepository.findOne({
      where: {
        productId: createDto.productId,
        platform: createDto.platform,
      },
    });

    if (existingPlan) {
      throw new BadRequestException('Product ID already exists for this platform');
    }

    const plan = this.subscriptionPlanRepository.create(createDto);
    return this.subscriptionPlanRepository.save(plan);
  }

  async findAll(): Promise<SubscriptionPlan[]> {
    return this.subscriptionPlanRepository.find({
      order: { sortOrder: 'ASC', createdAt: 'DESC' },
    });
  }

  async findActive(): Promise<SubscriptionPlan[]> {
    return this.subscriptionPlanRepository.find({
      where: { active: true },
      order: { sortOrder: 'ASC', createdAt: 'DESC' },
    });
  }

  async findByPlatform(platform: SubscriptionPlatform): Promise<SubscriptionPlan[]> {
    return this.subscriptionPlanRepository.find({
      where: { platform, active: true },
      order: { sortOrder: 'ASC', createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<SubscriptionPlan> {
    const plan = await this.subscriptionPlanRepository.findOne({
      where: { id },
    });

    if (!plan) {
      throw new NotFoundException('Subscription plan not found');
    }

    return plan;
  }

  async findByProductId(productId: string, platform: SubscriptionPlatform): Promise<SubscriptionPlan> {
    const plan = await this.subscriptionPlanRepository.findOne({
      where: { productId, platform },
    });

    if (!plan) {
      throw new NotFoundException('Subscription plan not found');
    }

    return plan;
  }

  async update(id: string, updateDto: UpdateSubscriptionPlanDto): Promise<SubscriptionPlan> {
    const plan = await this.findOne(id);

    // If updating product ID, check for conflicts
    if (updateDto.productId && updateDto.productId !== plan.productId) {
      const existingPlan = await this.subscriptionPlanRepository.findOne({
        where: {
          productId: updateDto.productId,
          platform: plan.platform,
        },
      });

      if (existingPlan && existingPlan.id !== id) {
        throw new BadRequestException('Product ID already exists for this platform');
      }
    }

    Object.assign(plan, updateDto);
    return this.subscriptionPlanRepository.save(plan);
  }

  async remove(id: string): Promise<void> {
    const plan = await this.findOne(id);
    await this.subscriptionPlanRepository.remove(plan);
  }

  async deactivate(id: string): Promise<SubscriptionPlan> {
    return this.update(id, { active: false });
  }

  async activate(id: string): Promise<SubscriptionPlan> {
    return this.update(id, { active: true });
  }
}
