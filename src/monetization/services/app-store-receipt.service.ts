import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Subscription, SubscriptionStatus } from '../entities/subscription.entity';
import { Transaction, TransactionType, TransactionStatus } from '../entities/transaction.entity';
import { SubscriptionPlatform } from '../entities/subscription-plan.entity';
import { SubscriptionPlanService } from './subscription-plan.service';

interface AppStoreReceiptResponse {
  status: number;
  receipt?: {
    in_app: Array<{
      original_transaction_id: string;
      product_id: string;
      transaction_id: string;
      purchase_date: string;
      expires_date?: string;
      is_trial_period?: string;
      is_in_intro_offer_period?: string;
    }>;
  };
  latest_receipt_info?: Array<{
    original_transaction_id: string;
    product_id: string;
    transaction_id: string;
    purchase_date: string;
    expires_date?: string;
    is_trial_period?: string;
    is_in_intro_offer_period?: string;
  }>;
  pending_renewal_info?: Array<{
    original_transaction_id: string;
    product_id: string;
    auto_renew_status: string;
    auto_renew_product_id: string;
  }>;
}

@Injectable()
export class AppStoreReceiptService {
  private readonly logger = new Logger(AppStoreReceiptService.name);
  private readonly sandboxUrl = 'https://sandbox.itunes.apple.com/verifyReceipt';
  private readonly productionUrl = 'https://buy.itunes.apple.com/verifyReceipt';

  constructor(
    private configService: ConfigService,
    @InjectRepository(Subscription)
    private subscriptionRepository: Repository<Subscription>,
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
    private subscriptionPlanService: SubscriptionPlanService,
  ) {}

  async verifyReceipt(receiptData: string, userId: string): Promise<{
    valid: boolean;
    subscription?: Subscription;
    transaction?: Transaction;
    message: string;
  }> {
    try {
      const sharedSecret = this.configService.get<string>('APP_STORE_SHARED_SECRET');
      if (!sharedSecret) {
        throw new BadRequestException('App Store shared secret not configured');
      }

      // First try production endpoint
      let response = await this.makeReceiptVerificationRequest(
        this.productionUrl,
        receiptData,
        sharedSecret,
      );

      // If status is 21007, receipt is for sandbox - try sandbox endpoint
      if (response.status === 21007) {
        response = await this.makeReceiptVerificationRequest(
          this.sandboxUrl,
          receiptData,
          sharedSecret,
        );
      }

      if (response.status !== 0) {
        return {
          valid: false,
          message: this.getErrorMessage(response.status),
        };
      }

      // Process the receipt
      const latestReceiptInfo = response.latest_receipt_info || response.receipt?.in_app || [];
      if (latestReceiptInfo.length === 0) {
        return {
          valid: false,
          message: 'No purchase information found in receipt',
        };
      }

      // Get the latest transaction
      const latestTransaction = latestReceiptInfo.sort(
        (a, b) => new Date(b.purchase_date).getTime() - new Date(a.purchase_date).getTime(),
      )[0];

      // Find the subscription plan by product ID
      const plan = await this.subscriptionPlanService.findByProductId(
        latestTransaction.product_id,
        SubscriptionPlatform.IOS
      );
      if (!plan) {
        return {
          valid: false,
          message: `No subscription plan found for product ID: ${latestTransaction.product_id}`,
        };
      }

      // Check if subscription already exists
      let subscription = await this.subscriptionRepository.findOne({
        where: {
          userId,
          originalTransactionId: latestTransaction.original_transaction_id,
        },
      });

      const purchaseDate = new Date(latestTransaction.purchase_date);
      const expiresDate = latestTransaction.expires_date ? new Date(latestTransaction.expires_date) : null;
      const isTrialPeriod = latestTransaction.is_trial_period === 'true';

      if (!subscription) {
        // Create new subscription
        subscription = this.subscriptionRepository.create({
          userId,
          planId: plan.id,
          originalTransactionId: latestTransaction.original_transaction_id,
          latestReceipt: receiptData,
          startsAt: purchaseDate,
          expiresAt: expiresDate || this.calculateExpiryDate(purchaseDate, plan.type),
          status: this.determineSubscriptionStatus(expiresDate),
          isFreeTrialTier: isTrialPeriod,
          price: plan.price,
          currency: plan.currency,
          autoRenew: true,
        });

        if (isTrialPeriod && expiresDate) {
          subscription.freeTrialEndsAt = expiresDate;
        }

        subscription = await this.subscriptionRepository.save(subscription);
      } else {
        // Update existing subscription
        subscription.latestReceipt = receiptData;
        subscription.expiresAt = expiresDate || this.calculateExpiryDate(purchaseDate, plan.type);
        subscription.status = this.determineSubscriptionStatus(expiresDate);
        subscription = await this.subscriptionRepository.save(subscription);
      }

      // Create transaction record
      const transaction = this.transactionRepository.create({
        userId,
        subscriptionId: subscription.id,
        type: TransactionType.SUBSCRIPTION_PURCHASE,
        status: TransactionStatus.COMPLETED,
        amount: plan.price,
        currency: plan.currency,
        platformTransactionId: latestTransaction.transaction_id,
        originalTransactionId: latestTransaction.original_transaction_id,
        receiptData,
        completedAt: purchaseDate,
      });

      const savedTransaction = await this.transactionRepository.save(transaction);

      return {
        valid: true,
        subscription,
        transaction: savedTransaction,
        message: 'Receipt verified successfully',
      };
    } catch (error) {
      this.logger.error('App Store receipt verification failed', error);
      return {
        valid: false,
        message: 'Receipt verification failed: ' + error.message,
      };
    }
  }

  private async makeReceiptVerificationRequest(
    url: string,
    receiptData: string,
    password: string,
  ): Promise<AppStoreReceiptResponse> {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        'receipt-data': receiptData,
        password,
        'exclude-old-transactions': false,
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  private determineSubscriptionStatus(expiresDate: Date | null): SubscriptionStatus {
    if (!expiresDate) {
      return SubscriptionStatus.ACTIVE;
    }

    const now = new Date();
    if (expiresDate > now) {
      return SubscriptionStatus.ACTIVE;
    } else {
      return SubscriptionStatus.EXPIRED;
    }
  }

  private calculateExpiryDate(startDate: Date, planType: string): Date {
    const expiry = new Date(startDate);
    
    switch (planType) {
      case 'weekly':
        expiry.setDate(expiry.getDate() + 7);
        break;
      case 'monthly':
        expiry.setMonth(expiry.getMonth() + 1);
        break;
      case 'yearly':
        expiry.setFullYear(expiry.getFullYear() + 1);
        break;
      default:
        expiry.setMonth(expiry.getMonth() + 1); // Default to monthly
    }

    return expiry;
  }

  private getErrorMessage(status: number): string {
    const errorMessages: { [key: number]: string } = {
      21000: 'The App Store could not read the JSON object you provided.',
      21002: 'The data in the receipt-data property was malformed or missing.',
      21003: 'The receipt could not be authenticated.',
      21004: 'The shared secret you provided does not match the shared secret on file for your account.',
      21005: 'The receipt server is not currently available.',
      21006: 'This receipt is valid but the subscription has expired.',
      21007: 'This receipt is from the sandbox environment.',
      21008: 'This receipt is from the production environment.',
      21009: 'Internal data access error.',
      21010: 'The user account cannot be found or has been deleted.',
    };

    return errorMessages[status] || `Unknown error status: ${status}`;
  }
}
