import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, MoreThan, LessThan } from 'typeorm';
import { Subscription, SubscriptionStatus } from '../entities/subscription.entity';
import { Transaction, TransactionType, TransactionStatus } from '../entities/transaction.entity';
import { CreateSubscriptionDto, UpdateSubscriptionDto, VerifyReceiptDto, VerifyPurchaseTokenDto } from '../dto/subscription.dto';
import { SubscriptionPlanService } from './subscription-plan.service';
import { CouponCodeService } from './coupon-code.service';
import { SubscriptionPlatform } from '../entities/subscription-plan.entity';

@Injectable()
export class SubscriptionService {
  private readonly logger = new Logger(SubscriptionService.name);

  constructor(
    @InjectRepository(Subscription)
    private subscriptionRepository: Repository<Subscription>,
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
    private subscriptionPlanService: SubscriptionPlanService,
    private couponCodeService: CouponCodeService,
  ) {}

  async create(userId: string, createDto: CreateSubscriptionDto): Promise<Subscription> {
    // Validate plan exists
    await this.subscriptionPlanService.findOne(createDto.planId);

    // Validate coupon if provided
    if (createDto.couponId) {
      const couponValidation = await this.couponCodeService.validateCoupon(
        { code: '', planId: createDto.planId },
        userId,
      );
      if (!couponValidation.valid) {
        throw new BadRequestException(couponValidation.message);
      }
    }

    const subscription = this.subscriptionRepository.create({
      ...createDto,
      userId,
    });

    return this.subscriptionRepository.save(subscription);
  }

  async findAll(): Promise<Subscription[]> {
    return this.subscriptionRepository.find({
      relations: ['user', 'plan', 'coupon'],
      order: { createdAt: 'DESC' },
    });
  }

  async findByUser(userId: string): Promise<Subscription[]> {
    return this.subscriptionRepository.find({
      where: { userId },
      relations: ['plan', 'coupon'],
      order: { createdAt: 'DESC' },
    });
  }

  async findActiveByUser(userId: string): Promise<Subscription | null> {
    const now = new Date();
    return this.subscriptionRepository.findOne({
      where: {
        userId,
        status: SubscriptionStatus.ACTIVE,
        expiresAt: MoreThan(now),
      },
      relations: ['plan', 'coupon'],
    });
  }

  async findOne(id: string): Promise<Subscription> {
    const subscription = await this.subscriptionRepository.findOne({
      where: { id },
      relations: ['user', 'plan', 'coupon'],
    });

    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }

    return subscription;
  }

  async update(id: string, updateDto: UpdateSubscriptionDto): Promise<Subscription> {
    const subscription = await this.findOne(id);
    Object.assign(subscription, updateDto);
    return this.subscriptionRepository.save(subscription);
  }

  async cancel(id: string): Promise<Subscription> {
    const subscription = await this.findOne(id);
    subscription.status = SubscriptionStatus.CANCELLED;
    subscription.cancelledAt = new Date();
    subscription.autoRenew = false;
    return this.subscriptionRepository.save(subscription);
  }

  async createTransaction(transactionData: Partial<Transaction>): Promise<Transaction> {
    const transaction = this.transactionRepository.create(transactionData);
    return this.transactionRepository.save(transaction);
  }

  async getSubscriptionStatus(userId: string): Promise<{
    hasActiveSubscription: boolean;
    subscription?: Subscription;
    daysUntilExpiry?: number;
  }> {
    const activeSubscription = await this.findActiveByUser(userId);
    
    if (!activeSubscription) {
      return { hasActiveSubscription: false };
    }

    const now = new Date();
    const daysUntilExpiry = Math.ceil(
      (activeSubscription.expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60 * 24),
    );

    return {
      hasActiveSubscription: true,
      subscription: activeSubscription,
      daysUntilExpiry,
    };
  }

  async renewSubscription(subscriptionId: string): Promise<Subscription> {
    const subscription = await this.findOne(subscriptionId);
    const plan = await this.subscriptionPlanService.findOne(subscription.planId);

    // Calculate new expiry date based on plan type
    const currentExpiry = subscription.expiresAt;
    let newExpiry: Date;

    switch (plan.type) {
      case 'weekly':
        newExpiry = new Date(currentExpiry.getTime() + 7 * 24 * 60 * 60 * 1000);
        break;
      case 'monthly':
        newExpiry = new Date(currentExpiry);
        newExpiry.setMonth(newExpiry.getMonth() + 1);
        break;
      case 'yearly':
        newExpiry = new Date(currentExpiry);
        newExpiry.setFullYear(newExpiry.getFullYear() + 1);
        break;
      default:
        throw new BadRequestException('Invalid subscription plan type');
    }

    subscription.expiresAt = newExpiry;
    subscription.status = SubscriptionStatus.ACTIVE;

    return this.subscriptionRepository.save(subscription);
  }

  async checkExpiredSubscriptions(): Promise<void> {
    const now = new Date();
    const expiredSubscriptions = await this.subscriptionRepository.find({
      where: {
        status: SubscriptionStatus.ACTIVE,
        expiresAt: LessThan(now),
      },
    });

    for (const subscription of expiredSubscriptions) {
      subscription.status = SubscriptionStatus.EXPIRED;
      await this.subscriptionRepository.save(subscription);
      this.logger.log(`Subscription ${subscription.id} marked as expired`);
    }
  }

  // Basic receipt verification - simplified version
  async verifyAppleReceipt(userId: string, receipt: string): Promise<{ valid: boolean; message: string }> {
    // This is a simplified version - in production you'd integrate with App Store Server API
    this.logger.log(`Verifying App Store receipt for user ${userId}`);
    
    // For now, return a basic validation
    if (!receipt || receipt.length < 10) {
      return { valid: false, message: 'Invalid receipt data' };
    }

    return { valid: true, message: 'Receipt verified successfully' };
  }

  async verifyGooglePlayPurchase(userId: string, purchaseToken: string): Promise<{ valid: boolean; message: string }> {
    // This is a simplified version - in production you'd integrate with Google Play Developer API
    this.logger.log(`Verifying Google Play purchase for user ${userId}`);
    
    // For now, return a basic validation
    if (!purchaseToken || purchaseToken.length < 10) {
      return { valid: false, message: 'Invalid purchase token' };
    }

    return { valid: true, message: 'Purchase verified successfully' };
  }
}
