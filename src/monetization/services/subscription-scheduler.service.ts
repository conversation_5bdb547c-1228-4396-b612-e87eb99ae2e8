import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { SubscriptionService } from './subscription.service';
import { SubscriptionNotificationService } from './subscription-notification.service';

@Injectable()
export class SubscriptionSchedulerService {
  private readonly logger = new Logger(SubscriptionSchedulerService.name);

  constructor(
    private subscriptionService: SubscriptionService,
    private subscriptionNotificationService: SubscriptionNotificationService,
  ) {}

  /**
   * Check for expired subscriptions every hour
   */
  @Cron(CronExpression.EVERY_HOUR)
  async handleExpiredSubscriptions(): Promise<void> {
    try {
      this.logger.log('Checking for expired subscriptions...');
      await this.subscriptionService.checkExpiredSubscriptions();
      this.logger.log('Expired subscriptions check completed');
    } catch (error) {
      this.logger.error('Error checking expired subscriptions', error);
    }
  }

  /**
   * Send renewal reminders daily at 9 AM
   */
  @Cron('0 9 * * *')
  async sendRenewalReminders(): Promise<void> {
    try {
      this.logger.log('Sending renewal reminders...');
      await this.subscriptionNotificationService.processExpiringSubscriptions();
      this.logger.log('Renewal reminders sent');
    } catch (error) {
      this.logger.error('Error sending renewal reminders', error);
    }
  }

  /**
   * Process expired subscription notifications daily at 10 AM
   */
  @Cron('0 10 * * *')
  async processExpiredNotifications(): Promise<void> {
    try {
      this.logger.log('Processing expired subscription notifications...');
      await this.subscriptionNotificationService.processExpiredSubscriptions();
      this.logger.log('Expired subscription notifications processed');
    } catch (error) {
      this.logger.error('Error processing expired notifications', error);
    }
  }

  /**
   * Generate subscription analytics weekly on Monday at 8 AM
   */
  @Cron('0 8 * * 1')
  async generateWeeklyAnalytics(): Promise<void> {
    try {
      this.logger.log('Generating weekly subscription analytics...');
      
      const analytics = await this.subscriptionService.getSubscriptionAnalytics();
      
      this.logger.log('Weekly Analytics:', {
        totalSubscriptions: analytics.totalSubscriptions,
        activeSubscriptions: analytics.activeSubscriptions,
        expiredSubscriptions: analytics.expiredSubscriptions,
        cancelledSubscriptions: analytics.cancelledSubscriptions,
        totalRevenue: analytics.totalRevenue,
        averageSubscriptionLength: analytics.averageSubscriptionLength,
      });

      // Here you could send this data to an analytics service, email report, etc.
      
      this.logger.log('Weekly analytics generated');
    } catch (error) {
      this.logger.error('Error generating weekly analytics', error);
    }
  }

  /**
   * Clean up old transaction records monthly on the 1st at 2 AM
   */
  @Cron('0 2 1 * *')
  async cleanupOldRecords(): Promise<void> {
    try {
      this.logger.log('Starting monthly cleanup of old records...');
      
      // This is a placeholder - implement actual cleanup logic
      // For example, archive transactions older than 2 years
      
      this.logger.log('Monthly cleanup completed');
    } catch (error) {
      this.logger.error('Error during monthly cleanup', error);
    }
  }

  /**
   * Health check for subscription system every 30 minutes
   */
  @Cron('*/30 * * * *')
  async healthCheck(): Promise<void> {
    try {
      // Perform basic health checks
      const analytics = await this.subscriptionService.getSubscriptionAnalytics();
      
      // Log system health
      this.logger.debug('Subscription system health check:', {
        totalSubscriptions: analytics.totalSubscriptions,
        activeSubscriptions: analytics.activeSubscriptions,
        timestamp: new Date().toISOString(),
      });

      // You could add more sophisticated health checks here:
      // - Database connectivity
      // - External service availability (App Store, Google Play)
      // - Queue health
      // - Cache health
      
    } catch (error) {
      this.logger.error('Health check failed', error);
      // You might want to send alerts here
    }
  }
}
