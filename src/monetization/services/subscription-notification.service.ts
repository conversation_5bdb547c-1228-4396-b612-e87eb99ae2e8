import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Subscription, SubscriptionStatus } from '../entities/subscription.entity';
import { SubscriptionService } from './subscription.service';
// import { MailerService } from '@nestjs-modules/mailer'; // Uncomment if using mailer

interface NotificationPreferences {
  emailEnabled: boolean;
  pushEnabled: boolean;
  smsEnabled: boolean;
}

@Injectable()
export class SubscriptionNotificationService {
  private readonly logger = new Logger(SubscriptionNotificationService.name);

  constructor(
    @InjectRepository(Subscription)
    private subscriptionRepository: Repository<Subscription>,
    private subscriptionService: SubscriptionService,
    // private mailerService: MailerService, // Uncomment if using mailer
  ) {}

  async sendRenewalReminder(subscription: Subscription): Promise<void> {
    try {
      const daysUntilExpiry = Math.ceil(
        (subscription.expiresAt.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
      );

      this.logger.log(`Sending renewal reminder for subscription ${subscription.id}, expires in ${daysUntilExpiry} days`);

      // Email notification
      await this.sendRenewalEmail(subscription, daysUntilExpiry);

      // Push notification
      await this.sendRenewalPushNotification(subscription, daysUntilExpiry);

      // Log the notification
      this.logger.log(`Renewal reminder sent for subscription ${subscription.id}`);
    } catch (error) {
      this.logger.error(`Failed to send renewal reminder for subscription ${subscription.id}`, error);
    }
  }

  async sendExpirationNotification(subscription: Subscription): Promise<void> {
    try {
      this.logger.log(`Sending expiration notification for subscription ${subscription.id}`);

      // Email notification
      await this.sendExpirationEmail(subscription);

      // Push notification
      await this.sendExpirationPushNotification(subscription);

      this.logger.log(`Expiration notification sent for subscription ${subscription.id}`);
    } catch (error) {
      this.logger.error(`Failed to send expiration notification for subscription ${subscription.id}`, error);
    }
  }

  async sendCancellationConfirmation(subscription: Subscription): Promise<void> {
    try {
      this.logger.log(`Sending cancellation confirmation for subscription ${subscription.id}`);

      // Email notification
      await this.sendCancellationEmail(subscription);

      this.logger.log(`Cancellation confirmation sent for subscription ${subscription.id}`);
    } catch (error) {
      this.logger.error(`Failed to send cancellation confirmation for subscription ${subscription.id}`, error);
    }
  }

  async sendRenewalSuccessNotification(subscription: Subscription): Promise<void> {
    try {
      this.logger.log(`Sending renewal success notification for subscription ${subscription.id}`);

      // Email notification
      await this.sendRenewalSuccessEmail(subscription);

      // Push notification
      await this.sendRenewalSuccessPushNotification(subscription);

      this.logger.log(`Renewal success notification sent for subscription ${subscription.id}`);
    } catch (error) {
      this.logger.error(`Failed to send renewal success notification for subscription ${subscription.id}`, error);
    }
  }

  async sendPaymentFailedNotification(subscription: Subscription): Promise<void> {
    try {
      this.logger.log(`Sending payment failed notification for subscription ${subscription.id}`);

      // Email notification
      await this.sendPaymentFailedEmail(subscription);

      // Push notification
      await this.sendPaymentFailedPushNotification(subscription);

      this.logger.log(`Payment failed notification sent for subscription ${subscription.id}`);
    } catch (error) {
      this.logger.error(`Failed to send payment failed notification for subscription ${subscription.id}`, error);
    }
  }

  async processExpiringSubscriptions(): Promise<void> {
    const expiringSubscriptions = await this.subscriptionService.getExpiringSubscriptions(7);

    for (const subscription of expiringSubscriptions) {
      await this.sendRenewalReminder(subscription);
    }

    this.logger.log(`Processed ${expiringSubscriptions.length} expiring subscriptions`);
  }

  async processExpiredSubscriptions(): Promise<void> {
    const expiredSubscriptions = await this.subscriptionRepository.find({
      where: {
        status: SubscriptionStatus.EXPIRED,
      },
      relations: ['user', 'plan'],
    });

    for (const subscription of expiredSubscriptions) {
      await this.sendExpirationNotification(subscription);
    }

    this.logger.log(`Processed ${expiredSubscriptions.length} expired subscriptions`);
  }

  private async sendRenewalEmail(subscription: Subscription, daysUntilExpiry: number): Promise<void> {
    // Implementation would depend on your email service
    // Example with @nestjs-modules/mailer:
    /*
    await this.mailerService.sendMail({
      to: subscription.user.email,
      subject: `Your ${subscription.plan.name} subscription expires in ${daysUntilExpiry} days`,
      template: 'subscription-renewal-reminder',
      context: {
        userName: subscription.user.name,
        planName: subscription.plan.name,
        expiryDate: subscription.expiresAt.toLocaleDateString(),
        daysUntilExpiry,
        renewalUrl: `${process.env.APP_URL}/subscription/renew/${subscription.id}`,
      },
    });
    */

    this.logger.log(`Renewal email would be sent to user ${subscription.userId}`);
  }

  private async sendRenewalPushNotification(subscription: Subscription, daysUntilExpiry: number): Promise<void> {
    // Implementation would depend on your push notification service
    // Example with Firebase Cloud Messaging:
    /*
    const message = {
      notification: {
        title: 'Subscription Renewal Reminder',
        body: `Your ${subscription.plan.name} subscription expires in ${daysUntilExpiry} days`,
      },
      data: {
        subscriptionId: subscription.id,
        type: 'renewal_reminder',
      },
      token: subscription.user.fcmToken,
    };

    await this.firebaseService.sendMessage(message);
    */

    this.logger.log(`Renewal push notification would be sent to user ${subscription.userId}`);
  }

  private async sendExpirationEmail(subscription: Subscription): Promise<void> {
    /*
    await this.mailerService.sendMail({
      to: subscription.user.email,
      subject: `Your ${subscription.plan.name} subscription has expired`,
      template: 'subscription-expired',
      context: {
        userName: subscription.user.name,
        planName: subscription.plan.name,
        expiryDate: subscription.expiresAt.toLocaleDateString(),
        reactivateUrl: `${process.env.APP_URL}/subscription/reactivate`,
      },
    });
    */

    this.logger.log(`Expiration email would be sent to user ${subscription.userId}`);
  }

  private async sendExpirationPushNotification(subscription: Subscription): Promise<void> {
    /*
    const message = {
      notification: {
        title: 'Subscription Expired',
        body: `Your ${subscription.plan.name} subscription has expired`,
      },
      data: {
        subscriptionId: subscription.id,
        type: 'subscription_expired',
      },
      token: subscription.user.fcmToken,
    };

    await this.firebaseService.sendMessage(message);
    */

    this.logger.log(`Expiration push notification would be sent to user ${subscription.userId}`);
  }

  private async sendCancellationEmail(subscription: Subscription): Promise<void> {
    /*
    await this.mailerService.sendMail({
      to: subscription.user.email,
      subject: `Your ${subscription.plan.name} subscription has been cancelled`,
      template: 'subscription-cancelled',
      context: {
        userName: subscription.user.name,
        planName: subscription.plan.name,
        cancellationDate: subscription.cancelledAt?.toLocaleDateString(),
        reactivateUrl: `${process.env.APP_URL}/subscription/reactivate`,
      },
    });
    */

    this.logger.log(`Cancellation email would be sent to user ${subscription.userId}`);
  }

  private async sendRenewalSuccessEmail(subscription: Subscription): Promise<void> {
    /*
    await this.mailerService.sendMail({
      to: subscription.user.email,
      subject: `Your ${subscription.plan.name} subscription has been renewed`,
      template: 'subscription-renewed',
      context: {
        userName: subscription.user.name,
        planName: subscription.plan.name,
        renewalDate: new Date().toLocaleDateString(),
        nextBillingDate: subscription.expiresAt.toLocaleDateString(),
        amount: subscription.price,
        currency: subscription.currency,
      },
    });
    */

    this.logger.log(`Renewal success email would be sent to user ${subscription.userId}`);
  }

  private async sendRenewalSuccessPushNotification(subscription: Subscription): Promise<void> {
    /*
    const message = {
      notification: {
        title: 'Subscription Renewed',
        body: `Your ${subscription.plan.name} subscription has been renewed successfully`,
      },
      data: {
        subscriptionId: subscription.id,
        type: 'subscription_renewed',
      },
      token: subscription.user.fcmToken,
    };

    await this.firebaseService.sendMessage(message);
    */

    this.logger.log(`Renewal success push notification would be sent to user ${subscription.userId}`);
  }

  private async sendPaymentFailedEmail(subscription: Subscription): Promise<void> {
    /*
    await this.mailerService.sendMail({
      to: subscription.user.email,
      subject: `Payment failed for your ${subscription.plan.name} subscription`,
      template: 'payment-failed',
      context: {
        userName: subscription.user.name,
        planName: subscription.plan.name,
        updatePaymentUrl: `${process.env.APP_URL}/subscription/payment-method`,
      },
    });
    */

    this.logger.log(`Payment failed email would be sent to user ${subscription.userId}`);
  }

  private async sendPaymentFailedPushNotification(subscription: Subscription): Promise<void> {
    /*
    const message = {
      notification: {
        title: 'Payment Failed',
        body: `Payment failed for your ${subscription.plan.name} subscription`,
      },
      data: {
        subscriptionId: subscription.id,
        type: 'payment_failed',
      },
      token: subscription.user.fcmToken,
    };

    await this.firebaseService.sendMessage(message);
    */

    this.logger.log(`Payment failed push notification would be sent to user ${subscription.userId}`);
  }
}
