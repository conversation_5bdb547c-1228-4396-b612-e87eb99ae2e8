import { IsString, IsEnum, IsO<PERSON>al, IsN<PERSON>ber, IsBoolean, IsDateString, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { SubscriptionStatus } from '../entities/subscription.entity';

export class CreateSubscriptionDto {
  @ApiProperty()
  @IsUUID()
  planId: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsUUID()
  couponId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  originalTransactionId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  latestReceipt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  purchaseToken?: string;

  @ApiProperty()
  @IsDateString()
  startsAt: string;

  @ApiProperty()
  @IsDateString()
  expiresAt: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isFreeTrialTier?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  freeTrialEndsAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  price?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  metadata?: any;
}

export class UpdateSubscriptionDto {
  @ApiProperty({ required: false, enum: SubscriptionStatus })
  @IsOptional()
  @IsEnum(SubscriptionStatus)
  status?: SubscriptionStatus;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  latestReceipt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  purchaseToken?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  cancelledAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  autoRenew?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  metadata?: any;
}

export class VerifyReceiptDto {
  @ApiProperty()
  @IsString()
  receipt: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  productId?: string;
}

export class VerifyPurchaseTokenDto {
  @ApiProperty()
  @IsString()
  purchaseToken: string;

  @ApiProperty()
  @IsString()
  productId: string;

  @ApiProperty()
  @IsString()
  packageName: string;
}
