import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { SubscriptionPlanService } from './services/subscription-plan.service';
import { SubscriptionService } from './services/subscription.service';
import { CouponCodeService } from './services/coupon-code.service';
import { CreateSubscriptionPlanDto, UpdateSubscriptionPlanDto } from './dto/subscription-plan.dto';
import { CreateCouponCodeDto, UpdateCouponCodeDto } from './dto/coupon-code.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('Admin - Monetization')
@Controller('api/admin/monetization')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class MonetizationAdminController {
  constructor(
    private readonly subscriptionPlanService: SubscriptionPlanService,
    private readonly subscriptionService: SubscriptionService,
    private readonly couponCodeService: CouponCodeService,
  ) {}

  // Subscription Plans Management
  @Get('plans')
  @ApiOperation({ summary: 'Get all subscription plans (admin)' })
  @ApiResponse({ status: 200, description: 'List of all subscription plans' })
  async getAllPlans() {
    return this.subscriptionPlanService.findAll();
  }

  @Post('plans')
  @ApiOperation({ summary: 'Create new subscription plan' })
  @ApiResponse({ status: 201, description: 'Subscription plan created successfully' })
  async createPlan(@Body() createPlanDto: CreateSubscriptionPlanDto) {
    return this.subscriptionPlanService.create(createPlanDto);
  }

  @Patch('plans/:id')
  @ApiOperation({ summary: 'Update subscription plan' })
  @ApiResponse({ status: 200, description: 'Subscription plan updated successfully' })
  async updatePlan(@Param('id') id: string, @Body() updatePlanDto: UpdateSubscriptionPlanDto) {
    return this.subscriptionPlanService.update(id, updatePlanDto);
  }

  @Delete('plans/:id')
  @ApiOperation({ summary: 'Delete subscription plan' })
  @ApiResponse({ status: 200, description: 'Subscription plan deleted successfully' })
  async deletePlan(@Param('id') id: string) {
    return this.subscriptionPlanService.remove(id);
  }

  @Post('plans/:id/activate')
  @ApiOperation({ summary: 'Activate subscription plan' })
  @ApiResponse({ status: 200, description: 'Subscription plan activated successfully' })
  async activatePlan(@Param('id') id: string) {
    return this.subscriptionPlanService.activate(id);
  }

  @Post('plans/:id/deactivate')
  @ApiOperation({ summary: 'Deactivate subscription plan' })
  @ApiResponse({ status: 200, description: 'Subscription plan deactivated successfully' })
  async deactivatePlan(@Param('id') id: string) {
    return this.subscriptionPlanService.deactivate(id);
  }

  // Subscriptions Management
  @Get('subscriptions')
  @ApiOperation({ summary: 'Get all subscriptions (admin)' })
  @ApiResponse({ status: 200, description: 'List of all subscriptions' })
  async getAllSubscriptions() {
    return this.subscriptionService.findAll();
  }

  @Get('subscriptions/:id')
  @ApiOperation({ summary: 'Get subscription by ID (admin)' })
  @ApiResponse({ status: 200, description: 'Subscription details' })
  async getSubscription(@Param('id') id: string) {
    return this.subscriptionService.findOne(id);
  }

  @Post('subscriptions/:id/cancel')
  @ApiOperation({ summary: 'Cancel subscription (admin)' })
  @ApiResponse({ status: 200, description: 'Subscription cancelled successfully' })
  async cancelSubscription(@Param('id') id: string) {
    return this.subscriptionService.cancel(id);
  }

  // Coupon Codes Management
  @Get('coupons')
  @ApiOperation({ summary: 'Get all coupon codes (admin)' })
  @ApiResponse({ status: 200, description: 'List of all coupon codes' })
  async getAllCoupons() {
    return this.couponCodeService.findAll();
  }

  @Post('coupons')
  @ApiOperation({ summary: 'Create new coupon code' })
  @ApiResponse({ status: 201, description: 'Coupon code created successfully' })
  async createCoupon(@Body() createCouponDto: CreateCouponCodeDto) {
    return this.couponCodeService.create(createCouponDto);
  }

  @Get('coupons/:id')
  @ApiOperation({ summary: 'Get coupon code by ID (admin)' })
  @ApiResponse({ status: 200, description: 'Coupon code details' })
  async getCoupon(@Param('id') id: string) {
    return this.couponCodeService.findOne(id);
  }

  @Patch('coupons/:id')
  @ApiOperation({ summary: 'Update coupon code' })
  @ApiResponse({ status: 200, description: 'Coupon code updated successfully' })
  async updateCoupon(@Param('id') id: string, @Body() updateCouponDto: UpdateCouponCodeDto) {
    return this.couponCodeService.update(id, updateCouponDto);
  }

  @Delete('coupons/:id')
  @ApiOperation({ summary: 'Delete coupon code' })
  @ApiResponse({ status: 200, description: 'Coupon code deleted successfully' })
  async deleteCoupon(@Param('id') id: string) {
    return this.couponCodeService.remove(id);
  }

  @Post('coupons/:id/activate')
  @ApiOperation({ summary: 'Activate coupon code' })
  @ApiResponse({ status: 200, description: 'Coupon code activated successfully' })
  async activateCoupon(@Param('id') id: string) {
    return this.couponCodeService.activate(id);
  }

  @Post('coupons/:id/deactivate')
  @ApiOperation({ summary: 'Deactivate coupon code' })
  @ApiResponse({ status: 200, description: 'Coupon code deactivated successfully' })
  async deactivateCoupon(@Param('id') id: string) {
    return this.couponCodeService.deactivate(id);
  }

  @Get('coupons/:id/stats')
  @ApiOperation({ summary: 'Get coupon code usage statistics' })
  @ApiResponse({ status: 200, description: 'Coupon code usage statistics' })
  async getCouponStats(@Param('id') id: string) {
    return this.couponCodeService.getUsageStats(id);
  }

  // Analytics and Reports
  @Get('analytics/overview')
  @ApiOperation({ summary: 'Get monetization overview analytics' })
  @ApiResponse({ status: 200, description: 'Monetization analytics overview' })
  async getAnalyticsOverview() {
    // This would typically return revenue, active subscriptions, etc.
    return {
      totalActiveSubscriptions: 0,
      monthlyRevenue: 0,
      yearlyRevenue: 0,
      conversionRate: 0,
    };
  }
}
