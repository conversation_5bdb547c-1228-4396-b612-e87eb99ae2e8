import { Controller, Get, Post, Body, Param, Render, Redirect, UseGuards, Request, Response } from '@nestjs/common';
import { SubscriptionPlanService } from './services/subscription-plan.service';
import { SubscriptionService } from './services/subscription.service';
import { CouponCodeService } from './services/coupon-code.service';
import { CreateSubscriptionPlanDto, UpdateSubscriptionPlanDto } from './dto/subscription-plan.dto';
import { CreateCouponCodeDto, UpdateCouponCodeDto } from './dto/coupon-code.dto';
import { AdminSessionGuard } from '../admin/guards/admin-session.guard';

@Controller('admin/monetization')
@UseGuards(AdminSessionGuard)
export class MonetizationAdminWebController {
  constructor(
    private readonly subscriptionPlanService: SubscriptionPlanService,
    private readonly subscriptionService: SubscriptionService,
    private readonly couponCodeService: CouponCodeService,
  ) {}

  @Get()
  @Render('admin/monetization/index')
  async index() {
    const [plans, subscriptions, coupons] = await Promise.all([
      this.subscriptionPlanService.findAll(),
      this.subscriptionService.findAll(),
      this.couponCodeService.findAll(),
    ]);

    return {
      title: 'Monetization Dashboard',
      plans: plans.slice(0, 5), // Show only first 5 for overview
      subscriptions: subscriptions.slice(0, 10), // Show recent 10
      coupons: coupons.slice(0, 5), // Show only first 5 for overview
      totalPlans: plans.length,
      totalSubscriptions: subscriptions.length,
      totalCoupons: coupons.length,
    };
  }

  // Subscription Plans
  @Get('plans')
  @Render('admin/monetization/plans/index')
  async plansIndex() {
    const plans = await this.subscriptionPlanService.findAll();
    return {
      title: 'Subscription Plans',
      plans,
    };
  }

  @Get('plans/new')
  @Render('admin/monetization/plans/new')
  async newPlan() {
    return {
      title: 'Create New Subscription Plan',
    };
  }

  @Post('plans')
  async createPlan(@Body() createPlanDto: CreateSubscriptionPlanDto, @Response() res) {
    try {
      await this.subscriptionPlanService.create(createPlanDto);
      res.redirect('/admin/monetization/plans?success=Plan created successfully');
    } catch (error) {
      res.redirect('/admin/monetization/plans/new?error=' + encodeURIComponent(error.message));
    }
  }

  @Get('plans/:id/edit')
  @Render('admin/monetization/plans/edit')
  async editPlan(@Param('id') id: string) {
    const plan = await this.subscriptionPlanService.findOne(id);
    return {
      title: 'Edit Subscription Plan',
      plan,
    };
  }

  @Post('plans/:id')
  async updatePlan(@Param('id') id: string, @Body() updatePlanDto: UpdateSubscriptionPlanDto, @Response() res) {
    try {
      await this.subscriptionPlanService.update(id, updatePlanDto);
      res.redirect('/admin/monetization/plans?success=Plan updated successfully');
    } catch (error) {
      res.redirect(`/admin/monetization/plans/${id}/edit?error=` + encodeURIComponent(error.message));
    }
  }

  @Post('plans/:id/delete')
  async deletePlan(@Param('id') id: string, @Response() res) {
    try {
      await this.subscriptionPlanService.remove(id);
      res.redirect('/admin/monetization/plans?success=Plan deleted successfully');
    } catch (error) {
      res.redirect('/admin/monetization/plans?error=' + encodeURIComponent(error.message));
    }
  }

  // Subscriptions
  @Get('subscriptions')
  @Render('admin/monetization/subscriptions/index')
  async subscriptionsIndex() {
    const subscriptions = await this.subscriptionService.findAll();
    return {
      title: 'Subscriptions',
      subscriptions,
    };
  }

  @Get('subscriptions/:id')
  @Render('admin/monetization/subscriptions/show')
  async showSubscription(@Param('id') id: string) {
    const subscription = await this.subscriptionService.findOne(id);
    return {
      title: 'Subscription Details',
      subscription,
    };
  }

  @Post('subscriptions/:id/cancel')
  async cancelSubscription(@Param('id') id: string, @Response() res) {
    try {
      await this.subscriptionService.cancel(id);
      res.redirect('/admin/monetization/subscriptions?success=Subscription cancelled successfully');
    } catch (error) {
      res.redirect('/admin/monetization/subscriptions?error=' + encodeURIComponent(error.message));
    }
  }

  // Coupon Codes
  @Get('coupons')
  @Render('admin/monetization/coupons/index')
  async couponsIndex() {
    const coupons = await this.couponCodeService.findAll();
    return {
      title: 'Coupon Codes',
      coupons,
    };
  }

  @Get('coupons/new')
  @Render('admin/monetization/coupons/new')
  async newCoupon() {
    return {
      title: 'Create New Coupon Code',
    };
  }

  @Post('coupons')
  async createCoupon(@Body() createCouponDto: CreateCouponCodeDto, @Response() res) {
    try {
      await this.couponCodeService.create(createCouponDto);
      res.redirect('/admin/monetization/coupons?success=Coupon created successfully');
    } catch (error) {
      res.redirect('/admin/monetization/coupons/new?error=' + encodeURIComponent(error.message));
    }
  }

  @Get('coupons/:id/edit')
  @Render('admin/monetization/coupons/edit')
  async editCoupon(@Param('id') id: string) {
    const coupon = await this.couponCodeService.findOne(id);
    return {
      title: 'Edit Coupon Code',
      coupon,
    };
  }

  @Post('coupons/:id')
  async updateCoupon(@Param('id') id: string, @Body() updateCouponDto: UpdateCouponCodeDto, @Response() res) {
    try {
      await this.couponCodeService.update(id, updateCouponDto);
      res.redirect('/admin/monetization/coupons?success=Coupon updated successfully');
    } catch (error) {
      res.redirect(`/admin/monetization/coupons/${id}/edit?error=` + encodeURIComponent(error.message));
    }
  }

  @Post('coupons/:id/delete')
  async deleteCoupon(@Param('id') id: string, @Response() res) {
    try {
      await this.couponCodeService.remove(id);
      res.redirect('/admin/monetization/coupons?success=Coupon deleted successfully');
    } catch (error) {
      res.redirect('/admin/monetization/coupons?error=' + encodeURIComponent(error.message));
    }
  }

  @Get('analytics')
  @Render('admin/monetization/analytics')
  async analytics() {
    const [plans, subscriptions, coupons] = await Promise.all([
      this.subscriptionPlanService.findAll(),
      this.subscriptionService.findAll(),
      this.couponCodeService.findAll(),
    ]);

    // Calculate basic analytics
    const activeSubscriptions = subscriptions.filter(s => s.status === 'active');
    const totalRevenue = activeSubscriptions.reduce((sum, s) => sum + (s.price || 0), 0);
    const activeCoupons = coupons.filter(c => c.active);

    return {
      title: 'Monetization Analytics',
      stats: {
        totalPlans: plans.length,
        activePlans: plans.filter(p => p.active).length,
        totalSubscriptions: subscriptions.length,
        activeSubscriptions: activeSubscriptions.length,
        totalCoupons: coupons.length,
        activeCoupons: activeCoupons.length,
        totalRevenue,
      },
    };
  }
}
