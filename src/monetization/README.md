# Monetization Module

The Monetization Module provides comprehensive subscription management and in-app purchase functionality for the Power Up API. It supports multiple platforms (iOS, Android, Web) and includes advanced features like coupon codes, free trials, and analytics.

## Features

### 🔥 Core Features
- **In-App Purchase Support**: Full integration with App Store and Google Play
- **Subscription Management**: Weekly, monthly, and yearly subscription plans
- **Multi-Platform Support**: iOS, Android, and Web platforms
- **Free Trial Tiers**: Configurable free trial periods for each plan
- **Coupon Code System**: Percentage, fixed amount, and free trial extension coupons
- **Admin Dashboard**: Complete web interface for managing subscriptions and coupons
- **API Endpoints**: RESTful API for mobile app integration
- **Receipt Verification**: Secure verification of App Store and Google Play receipts

### 📊 Analytics & Reporting
- Subscription status tracking
- Revenue analytics
- Coupon usage statistics
- Transaction history
- User subscription lifecycle

## Database Schema

### Tables Created
- `subscription_plans` - Available subscription plans
- `subscriptions` - User subscriptions
- `coupon_codes` - Discount and promotional codes
- `transactions` - Payment transaction records

### Enums
- **Subscription Plan Types**: `weekly`, `monthly`, `yearly`
- **Platforms**: `ios`, `android`, `web`
- **Subscription Status**: `active`, `expired`, `cancelled`, `pending`, `grace_period`, `free_trial`
- **Coupon Types**: `percentage`, `fixed_amount`, `free_trial_extension`
- **Transaction Types**: `purchase`, `renewal`, `refund`, `cancellation`
- **Transaction Status**: `pending`, `completed`, `failed`, `refunded`

## API Endpoints

### Public API (for mobile apps)

#### Subscription Plans
- `GET /monetization/plans` - Get all active subscription plans
- `GET /monetization/plans?platform=ios` - Get plans for specific platform
- `GET /monetization/plans/:id` - Get plan details

#### Subscriptions
- `POST /monetization/subscribe` - Create new subscription
- `GET /monetization/my-subscriptions` - Get user's subscriptions
- `GET /monetization/subscription-status` - Get current subscription status
- `PATCH /monetization/subscriptions/:id` - Update subscription
- `POST /monetization/subscriptions/:id/cancel` - Cancel subscription

#### Receipt Verification
- `POST /monetization/verify/apple-receipt` - Verify Apple App Store receipt
- `POST /monetization/verify/google-purchase` - Verify Google Play purchase

#### Coupons
- `POST /monetization/validate-coupon` - Validate coupon code

### Admin API

#### Plans Management
- `GET /admin/monetization/plans` - Get all plans
- `POST /admin/monetization/plans` - Create new plan
- `PATCH /admin/monetization/plans/:id` - Update plan
- `DELETE /admin/monetization/plans/:id` - Delete plan
- `POST /admin/monetization/plans/:id/activate` - Activate plan
- `POST /admin/monetization/plans/:id/deactivate` - Deactivate plan

#### Subscriptions Management
- `GET /admin/monetization/subscriptions` - Get all subscriptions
- `GET /admin/monetization/subscriptions/:id` - Get subscription details
- `POST /admin/monetization/subscriptions/:id/cancel` - Cancel subscription

#### Coupons Management
- `GET /admin/monetization/coupons` - Get all coupons
- `POST /admin/monetization/coupons` - Create new coupon
- `GET /admin/monetization/coupons/:id` - Get coupon details
- `PATCH /admin/monetization/coupons/:id` - Update coupon
- `DELETE /admin/monetization/coupons/:id` - Delete coupon
- `POST /admin/monetization/coupons/:id/activate` - Activate coupon
- `POST /admin/monetization/coupons/:id/deactivate` - Deactivate coupon
- `GET /admin/monetization/coupons/:id/stats` - Get coupon usage stats

### Admin Web Interface

#### Pages Available
- `/admin/monetization` - Dashboard overview
- `/admin/monetization/plans` - Subscription plans management
- `/admin/monetization/plans/new` - Create new plan
- `/admin/monetization/plans/:id/edit` - Edit plan
- `/admin/monetization/subscriptions` - View all subscriptions
- `/admin/monetization/subscriptions/:id` - Subscription details
- `/admin/monetization/coupons` - Coupon codes management
- `/admin/monetization/coupons/new` - Create new coupon
- `/admin/monetization/coupons/:id/edit` - Edit coupon
- `/admin/monetization/analytics` - Analytics dashboard

## Usage Examples

### Creating a Subscription Plan

```typescript
const planData = {
  name: 'Power Up Monthly - iOS',
  description: 'Monthly subscription with premium features',
  type: 'monthly',
  platform: 'ios',
  price: 14.99,
  currency: 'USD',
  productId: 'com.powerup.monthly',
  hasFreeTrialTier: true,
  freeTrialDays: 14,
  features: ['AI tracking', 'Analytics', 'Premium themes']
};

const plan = await subscriptionPlanService.create(planData);
```

### Creating a Coupon Code

```typescript
const couponData = {
  code: 'WELCOME20',
  name: 'Welcome Discount',
  description: '20% off for new users',
  type: 'percentage',
  value: 20,
  maxUses: 1000,
  validFrom: new Date('2025-01-01'),
  validUntil: new Date('2025-12-31'),
  firstTimeOnly: true
};

const coupon = await couponCodeService.create(couponData);
```

### Validating a Coupon

```typescript
const validation = await couponCodeService.validateCoupon(
  { code: 'WELCOME20', planId: 'plan-uuid' },
  'user-uuid'
);

if (validation.valid) {
  console.log('Discount:', validation.discount);
} else {
  console.log('Error:', validation.message);
}
```

### Checking Subscription Status

```typescript
const status = await subscriptionService.getSubscriptionStatus('user-uuid');

if (status.hasActiveSubscription) {
  console.log('Days until expiry:', status.daysUntilExpiry);
  console.log('Plan:', status.subscription.plan.name);
}
```

## Configuration

### Environment Variables
Add these to your `.env` file:

```env
# Apple App Store (optional - for production receipt verification)
APPLE_SHARED_SECRET=your_shared_secret

# Google Play (optional - for production receipt verification)
GOOGLE_PLAY_SERVICE_ACCOUNT_KEY_PATH=path/to/service-account.json
```

### Database Migration
The migration creates all necessary tables and relationships:

```bash
npm run migration:run
```

### Seeding Sample Data
To populate the database with sample subscription plans and coupon codes:

```bash
# Run the seeding script (you'll need to create a command for this)
npm run seed:monetization
```

## Security Considerations

1. **Receipt Verification**: Always verify receipts server-side
2. **Coupon Validation**: Validate coupons before applying discounts
3. **Admin Access**: Ensure proper authentication for admin endpoints
4. **Rate Limiting**: Implement rate limiting on verification endpoints
5. **Data Encryption**: Sensitive payment data should be encrypted

## Testing

### Unit Tests
```bash
npm run test -- monetization
```

### E2E Tests
```bash
npm run test:e2e -- monetization
```

## Troubleshooting

### Common Issues

1. **Migration Failures**: Ensure database is up and migrations are run in order
2. **Receipt Verification**: Check that platform credentials are properly configured
3. **Coupon Not Working**: Verify coupon is active and within validity period
4. **Admin Access**: Ensure admin guards are properly configured

### Debug Logs
Enable debug logging for the monetization module:

```typescript
// In your logger configuration
logger.setLevel('debug');
```

## Future Enhancements

- [ ] Apple App Store Server API integration
- [ ] Google Play Developer API integration
- [ ] Webhook support for automatic subscription updates
- [ ] Advanced analytics dashboard
- [ ] Subscription pause/resume functionality
- [ ] Family sharing support
- [ ] Promotional offer codes
- [ ] A/B testing for pricing
- [ ] Revenue forecasting
- [ ] Churn prediction analytics

## Support

For questions or issues related to the monetization module, please:

1. Check this documentation
2. Review the API endpoints and their responses
3. Check the admin dashboard for subscription and coupon management
4. Contact the development team for integration support

---

**Note**: This module is designed to be extensible and can be easily adapted for different business models and pricing strategies.
