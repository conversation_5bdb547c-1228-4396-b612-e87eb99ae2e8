import { Controller, Post, Body, Headers, HttpCode, HttpStatus, Logger, BadRequestException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { SubscriptionWebhookService } from './services/subscription-webhook.service';

@ApiTags('webhooks')
@Controller('api/webhooks')
export class WebhookController {
  private readonly logger = new Logger(WebhookController.name);

  constructor(
    private subscriptionWebhookService: SubscriptionWebhookService,
  ) {}

  @Post('app-store')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Handle App Store webhook notifications' })
  @ApiResponse({ status: 200, description: 'Webhook processed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid webhook payload' })
  async handleAppStoreWebhook(
    @Body() payload: any,
    @Headers() headers: Record<string, string>,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.log('Received App Store webhook notification');
      
      // Validate the webhook (in production, you should verify the signature)
      if (!payload || !payload.notification_type) {
        throw new BadRequestException('Invalid App Store webhook payload');
      }

      // Process the webhook
      await this.subscriptionWebhookService.handleAppStoreWebhook(payload);

      return { success: true };
    } catch (error) {
      this.logger.error('Error processing App Store webhook', error);
      throw error;
    }
  }

  @Post('google-play')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Handle Google Play webhook notifications' })
  @ApiResponse({ status: 200, description: 'Webhook processed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid webhook payload' })
  async handleGooglePlayWebhook(
    @Body() payload: any,
    @Headers() headers: Record<string, string>,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.log('Received Google Play webhook notification');
      
      // Validate the webhook (in production, you should verify the signature)
      if (!payload || !payload.message) {
        throw new BadRequestException('Invalid Google Play webhook payload');
      }

      // Process the webhook
      await this.subscriptionWebhookService.handleGooglePlayWebhook(payload);

      return { success: true };
    } catch (error) {
      this.logger.error('Error processing Google Play webhook', error);
      throw error;
    }
  }
}
