import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MonetizationController } from './monetization.controller';
import { MonetizationAdminController } from './monetization-admin.controller';
import { <PERSON>hookController } from './webhook.controller';
import { SubscriptionPlanService } from './services/subscription-plan.service';
import { SubscriptionService } from './services/subscription.service';
import { CouponCodeService } from './services/coupon-code.service';
import { AppStoreReceiptService } from './services/app-store-receipt.service';
import { GooglePlayReceiptService } from './services/google-play-receipt.service';
import { SubscriptionWebhookService } from './services/subscription-webhook.service';
import { SubscriptionNotificationService } from './services/subscription-notification.service';
import { SubscriptionSchedulerService } from './services/subscription-scheduler.service';
import { SubscriptionPlan } from './entities/subscription-plan.entity';
import { Subscription } from './entities/subscription.entity';
import { CouponCode } from './entities/coupon-code.entity';
import { Transaction } from './entities/transaction.entity';
import { User } from '../users/entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SubscriptionPlan,
      Subscription,
      CouponCode,
      Transaction,
      User,
    ]),
  ],
  controllers: [
    MonetizationController, 
    MonetizationAdminController,
    WebhookController,
  ],
  providers: [
    SubscriptionPlanService,
    SubscriptionService,
    CouponCodeService,
    AppStoreReceiptService,
    GooglePlayReceiptService,
    SubscriptionWebhookService,
    SubscriptionNotificationService,
    SubscriptionSchedulerService,
  ],
  exports: [
    SubscriptionPlanService,
    SubscriptionService,
    CouponCodeService,
    AppStoreReceiptService,
    GooglePlayReceiptService,
    SubscriptionWebhookService,
    SubscriptionNotificationService,
    SubscriptionSchedulerService,
  ],
})
export class MonetizationModule {}
