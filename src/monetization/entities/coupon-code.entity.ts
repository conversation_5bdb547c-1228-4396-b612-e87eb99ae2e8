import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

export enum CouponType {
  PERCENTAGE = 'percentage',
  FIXED_AMOUNT = 'fixed_amount',
  FREE_TRIAL_EXTENSION = 'free_trial_extension',
}

@Entity('coupon_codes')
export class CouponCode {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  code: string;

  @Column()
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: CouponType,
  })
  type: CouponType;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  value: number; // Percentage (0-100) or fixed amount

  @Column({ name: 'max_uses', nullable: true })
  maxUses?: number; // null means unlimited

  @Column({ name: 'used_count', default: 0 })
  usedCount: number;

  @Column({ name: 'valid_from' })
  validFrom: Date;

  @Column({ name: 'valid_until' })
  validUntil: Date;

  @Column({ name: 'first_time_only', default: false })
  firstTimeOnly: boolean; // Only for first-time subscribers

  @Column('simple-array', { nullable: true })
  applicablePlans: string[]; // Plan IDs this coupon applies to (null means all plans)

  @Column({ default: true })
  active: boolean;

  @Column({ name: 'minimum_purchase_amount', nullable: true, type: 'decimal', precision: 10, scale: 2 })
  minimumPurchaseAmount?: number;

  @Column({ name: 'free_trial_days', nullable: true })
  freeTrialDays?: number; // For FREE_TRIAL_EXTENSION type

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
