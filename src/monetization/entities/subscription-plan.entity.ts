import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';

export enum SubscriptionPlanType {
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
}

export enum SubscriptionPlatform {
  IOS = 'ios',
  ANDROID = 'android',
  WEB = 'web',
}

@Entity('subscription_plans')
export class SubscriptionPlan {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: SubscriptionPlanType,
  })
  type: SubscriptionPlanType;

  @Column({
    type: 'enum',
    enum: SubscriptionPlatform,
  })
  platform: SubscriptionPlatform;

  @Column('decimal', { precision: 10, scale: 2 })
  price: number;

  @Column({ length: 3, default: 'USD' })
  currency: string;

  @Column({ name: 'product_id' })
  productId: string; // App Store/Play Store product ID

  @Column({ name: 'has_free_trial', default: false })
  hasFreeTrialTier: boolean;

  @Column({ name: 'free_trial_days', default: 0 })
  freeTrialDays: number;

  @Column({ default: true })
  active: boolean;

  @Column('simple-array', { nullable: true })
  features: string[];

  @Column({ name: 'sort_order', default: 0 })
  sortOrder: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
