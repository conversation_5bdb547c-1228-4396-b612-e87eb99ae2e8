import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { SubscriptionPlan } from './subscription-plan.entity';
import { CouponCode } from './coupon-code.entity';

export enum SubscriptionStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled',
  PENDING = 'pending',
  GRACE_PERIOD = 'grace_period',
  FREE_TRIAL = 'free_trial',
}

@Entity('subscriptions')
export class Subscription {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @Column({ name: 'plan_id' })
  planId: string;

  @Column({ name: 'coupon_id', nullable: true })
  couponId?: string;

  @Column({
    type: 'enum',
    enum: SubscriptionStatus,
    default: SubscriptionStatus.PENDING,
  })
  status: SubscriptionStatus;

  @Column({ name: 'original_transaction_id', nullable: true })
  originalTransactionId?: string; // App Store original transaction ID

  @Column({ name: 'latest_receipt', nullable: true, type: 'text' })
  latestReceipt?: string; // App Store receipt

  @Column({ name: 'purchase_token', nullable: true })
  purchaseToken?: string; // Google Play purchase token

  @Column({ name: 'starts_at' })
  startsAt: Date;

  @Column({ name: 'expires_at' })
  expiresAt: Date;

  @Column({ name: 'cancelled_at', nullable: true })
  cancelledAt?: Date;

  @Column({ name: 'is_free_trial', default: false })
  isFreeTrialTier: boolean;

  @Column({ name: 'free_trial_ends_at', nullable: true })
  freeTrialEndsAt?: Date;

  @Column({ name: 'auto_renew', default: true })
  autoRenew: boolean;

  @Column('decimal', { precision: 10, scale: 2, nullable: true })
  price?: number;

  @Column({ length: 3, nullable: true })
  currency?: string;

  @Column('json', { nullable: true })
  metadata: any;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => SubscriptionPlan)
  @JoinColumn({ name: 'plan_id' })
  plan: SubscriptionPlan;

  @ManyToOne(() => CouponCode, { nullable: true })
  @JoinColumn({ name: 'coupon_id' })
  coupon?: CouponCode;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
