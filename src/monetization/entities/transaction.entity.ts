import { Entity, PrimaryGeneratedColumn, Column, CreateDateC<PERSON>umn, ManyToOne, JoinC<PERSON>um<PERSON> } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Subscription } from './subscription.entity';

export enum TransactionType {
  PURCHASE = 'purchase',
  SUBSCRIPTION_PURCHASE = 'subscription_purchase',
  RENEWAL = 'renewal',
  REFUND = 'refund',
  CANCELLATION = 'cancellation',
}

export enum TransactionStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  FAILED = 'failed',
  REFUNDED = 'refunded',
}

@Entity('transactions')
export class Transaction {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @Column({ name: 'subscription_id', nullable: true })
  subscriptionId?: string;

  @Column({
    type: 'enum',
    enum: TransactionType,
  })
  type: TransactionType;

  @Column({
    type: 'enum',
    enum: TransactionStatus,
    default: TransactionStatus.PENDING,
  })
  status: TransactionStatus;

  @Column('decimal', { precision: 10, scale: 2 })
  amount: number;

  @Column({ length: 3 })
  currency: string;

  @Column({ name: 'platform_transaction_id', nullable: true })
  platformTransactionId?: string; // App Store/Play Store transaction ID

  @Column({ name: 'platform_receipt', nullable: true, type: 'text' })
  platformReceipt?: string;

  @Column({ name: 'platform_purchase_token', nullable: true })
  platformPurchaseToken?: string;

  @Column({ name: 'original_transaction_id', nullable: true })
  originalTransactionId?: string;

  @Column({ name: 'receipt_data', nullable: true, type: 'text' })
  receiptData?: string;

  @Column({ name: 'completed_at', nullable: true })
  completedAt?: Date;

  @Column('json', { nullable: true })
  metadata: any;

  @Column({ name: 'processed_at', nullable: true })
  processedAt?: Date;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Subscription, { nullable: true })
  @JoinColumn({ name: 'subscription_id' })
  subscription?: Subscription;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;
}
