import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { SubscriptionPlanService } from './services/subscription-plan.service';
import { SubscriptionService } from './services/subscription.service';
import { CouponCodeService } from './services/coupon-code.service';
import { CreateSubscriptionDto, UpdateSubscriptionDto, VerifyReceiptDto, VerifyPurchaseTokenDto } from './dto/subscription.dto';
import { ValidateCouponDto } from './dto/coupon-code.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { SubscriptionPlatform } from './entities/subscription-plan.entity';

@ApiTags('Monetization')
@Controller('api/monetization')
export class MonetizationController {
  constructor(
    private readonly subscriptionPlanService: SubscriptionPlanService,
    private readonly subscriptionService: SubscriptionService,
    private readonly couponCodeService: CouponCodeService,
  ) {}

  @Get('plans')
  @ApiOperation({ summary: 'Get all active subscription plans' })
  @ApiResponse({ status: 200, description: 'List of active subscription plans' })
  async getPlans(@Query('platform') platform?: SubscriptionPlatform) {
    if (platform) {
      return this.subscriptionPlanService.findByPlatform(platform);
    }
    return this.subscriptionPlanService.findActive();
  }

  @Get('plans/:id')
  @ApiOperation({ summary: 'Get subscription plan by ID' })
  @ApiResponse({ status: 200, description: 'Subscription plan details' })
  async getPlan(@Param('id') id: string) {
    return this.subscriptionPlanService.findOne(id);
  }

  @Post('subscribe')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new subscription' })
  @ApiResponse({ status: 201, description: 'Subscription created successfully' })
  async subscribe(@Request() req, @Body() createSubscriptionDto: CreateSubscriptionDto) {
    return this.subscriptionService.create(req.user.id, createSubscriptionDto);
  }

  @Get('my-subscriptions')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user subscriptions' })
  @ApiResponse({ status: 200, description: 'List of user subscriptions' })
  async getMySubscriptions(@Request() req) {
    return this.subscriptionService.findByUser(req.user.id);
  }

  @Get('subscription-status')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get current subscription status' })
  @ApiResponse({ status: 200, description: 'Current subscription status' })
  async getSubscriptionStatus(@Request() req) {
    return this.subscriptionService.getSubscriptionStatus(req.user.id);
  }

  @Patch('subscriptions/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update subscription' })
  @ApiResponse({ status: 200, description: 'Subscription updated successfully' })
  async updateSubscription(@Param('id') id: string, @Body() updateSubscriptionDto: UpdateSubscriptionDto) {
    return this.subscriptionService.update(id, updateSubscriptionDto);
  }

  @Post('subscriptions/:id/cancel')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Cancel subscription' })
  @ApiResponse({ status: 200, description: 'Subscription cancelled successfully' })
  async cancelSubscription(@Param('id') id: string) {
    return this.subscriptionService.cancel(id);
  }

  @Post('verify/apple-receipt')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Verify Apple App Store receipt' })
  @ApiResponse({ status: 200, description: 'Receipt verified successfully' })
  async verifyAppleReceipt(@Request() req, @Body() verifyReceiptDto: VerifyReceiptDto) {
    return this.subscriptionService.verifyAppleReceipt(req.user.id, verifyReceiptDto);
  }

  @Post('verify/google-purchase')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Verify Google Play purchase' })
  @ApiResponse({ status: 200, description: 'Purchase verified successfully' })
  async verifyGooglePurchase(@Request() req, @Body() verifyPurchaseDto: VerifyPurchaseTokenDto) {
    return this.subscriptionService.verifyGooglePlayPurchase(req.user.id, verifyPurchaseDto);
  }

  @Post('validate-coupon')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Validate coupon code' })
  @ApiResponse({ status: 200, description: 'Coupon validation result' })
  async validateCoupon(@Request() req, @Body() validateCouponDto: ValidateCouponDto) {
    return this.couponCodeService.validateCoupon(validateCouponDto, req.user.id);
  }
}
