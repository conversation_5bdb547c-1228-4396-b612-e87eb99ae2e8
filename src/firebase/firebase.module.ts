import { Module } from '@nestjs/common';
import { FirebaseService } from './firebase.service';
import { FirebaseMigrationService } from './firebase-migration.service';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DeviceToken } from '../notifications/entities/device-token.entity';
import { Message } from '../messaging/entities/message.entity';
import { Conversation } from '../messaging/entities/conversation.entity';
import { User } from '../users/entities/user.entity';
import { File } from '../common/entities/file.entity';

@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([
      DeviceToken, 
      Message, 
      Conversation, 
      User, 
      File
    ])
  ],
  providers: [FirebaseService, FirebaseMigrationService],
  exports: [FirebaseService, FirebaseMigrationService],
})
export class FirebaseModule {}
