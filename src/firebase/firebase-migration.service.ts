import { Injectable, Logger } from '@nestjs/common';
import { FirebaseService } from '../firebase/firebase.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Message } from '../messaging/entities/message.entity';
import { Conversation } from '../messaging/entities/conversation.entity';
import { User, AuthProvider } from '../users/entities/user.entity';

/**
 * This service handles data migration from Firebase to PostgreSQL
 * It's been updated to use the PostgreSQL database instead of Firestore
 */
@Injectable()
export class FirebaseMigrationService {
  private readonly logger = new Logger(FirebaseMigrationService.name);

  constructor(
    private readonly firebaseService: FirebaseService,
    @InjectRepository(Message)
    private messagesRepository: Repository<Message>,
    @InjectRepository(Conversation)
    private conversationsRepository: Repository<Conversation>,
    @InjectRepository(User)
    private usersRepository: Repository<User>
  ) {}

  /**
   * Initialize database tables and sample data
   * This method replaces the original Firebase Firestore initialization
   */
  async initializeDatabase() {
    try {
      this.logger.log('Initializing PostgreSQL database tables');
      
      // Check if we already have conversations
      const existingConversations = await this.conversationsRepository.count();
      
      if (existingConversations === 0) {
        await this.createSampleData();
      }

      return { success: true, message: 'PostgreSQL database initialized successfully' };
    } catch (error) {
      this.logger.error(`Failed to initialize database: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * This method is maintained for backward compatibility
   * Now it calls initializeDatabase() instead of creating Firebase collections
   */
  async initializeFirebase() {
    return this.initializeDatabase();
  }

  /**
   * Create sample data in the PostgreSQL database
   */
  private async createSampleData() {
    try {
      let systemUser = await this.usersRepository.findOne({ 
        where: { email: '<EMAIL>' } 
      });
      
      // If no system user exists, create one
      if (!systemUser) {
        // First create a user entity
        const newUser = this.usersRepository.create({
          email: '<EMAIL>',
          firstName: 'System',
          lastName: 'User',
          password: '', // Empty string instead of null
          provider: AuthProvider.LOCAL,
          xp: 0,
          badges: []
        });
        
        // Then save it
        systemUser = await this.usersRepository.save(newUser);
      }
      
      // At this point, systemUser is guaranteed to be a valid User entity

      // Create a sample conversation without participants first
      const newConversation = this.conversationsRepository.create({
        name: 'Welcome',
        isGroup: false,
        lastMessage: 'Welcome to Power Up!',
        lastMessageTime: new Date()
      });
      
      const savedConversation = await this.conversationsRepository.save(newConversation);
      
      // Now set the participants and other fields
      savedConversation.participants = [systemUser];
      savedConversation.lastMessageSenderId = systemUser.id;
      
      const conversation = await this.conversationsRepository.save(savedConversation);

      // Create a sample message without relationships first
      const newMessage = this.messagesRepository.create({
        content: 'Welcome to Power Up! This is a sample message.',
        type: 'system-message',
        read: false,
      });
      
      // Now set the relationships
      newMessage.conversation = conversation;
      newMessage.conversationId = conversation.id;
      newMessage.sender = systemUser;
      newMessage.senderId = systemUser.id;
      
      await this.messagesRepository.save(newMessage);

      this.logger.log('Sample data created successfully');
    } catch (error) {
      this.logger.error(`Failed to create sample data: ${error.message}`);
      throw error;
    }
  }
  /**
   * Run database maintenance tasks
   * Replaces the original createIndexes method
   */
  private async runMaintenance() {
    // Could add maintenance tasks like creating database indexes, etc.
    this.logger.log('Running database maintenance tasks');
    this.logger.log('No maintenance tasks needed at this time');
  }
}
