import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import * as admin from 'firebase-admin';
import { ConfigService } from '@nestjs/config';
import * as path from 'path';
import * as fs from 'fs';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DeviceToken } from '../notifications/entities/device-token.entity';

@Injectable()
export class FirebaseService implements OnModuleInit {
  private readonly logger = new Logger(FirebaseService.name);
  
  constructor(
    private configService: ConfigService,
    @InjectRepository(DeviceToken)
    private deviceTokenRepository: Repository<DeviceToken>
  ) {}

  onModuleInit() {
    // We still need Firebase Auth, but not Firestore
    this.initializeFirebase();
  }
  
  private initializeFirebase() {
    try {
      // Check if Firebase is already initialized
      if (admin.apps.length > 0) {
        this.logger.log('Firebase already initialized');
        return;
      }
      
      // First try to use environment variables for service account
      const projectId = this.configService.get<string>('firebase.projectId');
      const clientEmail = this.configService.get<string>('FIREBASE_CLIENT_EMAIL');
      const privateKey = this.configService.get<string>('FIREBASE_PRIVATE_KEY');
      const databaseUrl = this.configService.get<string>('firebase.databaseUrl');
      
      // If environment variables are available, use them
      if (projectId && clientEmail && privateKey) {
        this.logger.log('Initializing Firebase with environment variables');
        
        admin.initializeApp({
          credential: admin.credential.cert({
            projectId,
            clientEmail,
            privateKey: privateKey.replace(/\\n/g, '\n'),
          }),
          databaseURL: databaseUrl,
        });
      } 
      // Otherwise, use the service account file
      else {
        const serviceAccountPath = path.resolve(
          process.cwd(),
          this.configService.get<string>('firebase.serviceAccountPath') || 
          'firebase-service-account.json',
        );
        
        if (!fs.existsSync(serviceAccountPath)) {
          throw new Error(`Firebase service account file not found at ${serviceAccountPath}`);
        }
        
        this.logger.log(`Initializing Firebase with service account file: ${serviceAccountPath}`);
        
        admin.initializeApp({
          credential: admin.credential.cert(serviceAccountPath),
          databaseURL: databaseUrl,
        });
      }
      
      this.logger.log('Firebase initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Firebase', error.stack);
      throw error;
    }
  }

  getAuth() {
    return admin.auth();
  }

  getMessaging() {
    return admin.messaging();
  }

  /**
   * Verify a Firebase ID token
   * @param token Firebase ID token
   * @returns Decoded token
   */
  async verifyIdToken(token: string) {
    try {
      return await this.getAuth().verifyIdToken(token);
    } catch (error) {
      this.logger.error(`Failed to verify Firebase ID token: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create a custom token for a user
   * @param uid User ID
   * @param claims Custom claims
   * @returns Custom token
   */
  async createCustomToken(uid: string, claims?: Record<string, any>) {
    try {
      return await this.getAuth().createCustomToken(uid, claims);
    } catch (error) {
      this.logger.error(`Failed to create custom token: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get user by email
   * @param email User email
   * @returns Firebase user record
   */
  async getUserByEmail(email: string) {
    try {
      return await this.getAuth().getUserByEmail(email);
    } catch (error) {
      if (error.code === 'auth/user-not-found') {
        return null;
      }
      this.logger.error(`Error getting user by email: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get user by UID
   * @param uid User ID
   * @returns Firebase user record
   */
  async getUserByUid(uid: string) {
    try {
      return await this.getAuth().getUser(uid);
    } catch (error) {
      if (error.code === 'auth/user-not-found') {
        return null;
      }
      this.logger.error(`Error getting user by UID: ${error.message}`);
      throw error;
    }
  }

  /**
   * Send push notification to a specific device
   * @param token Device token
   * @param payload Notification payload
   * @returns Messaging response
   */
  async sendPushNotification(token: string, payload: admin.messaging.MessagingPayload) {
    try {
      return await this.getMessaging().send({
        token: token,
        ...payload,
      });
    } catch (error) {
      this.logger.error(`Failed to send push notification: ${error.message}`);
      throw error;
    }
  }

  /**
   * Send push notification to multiple devices
   * @param tokens Device tokens
   * @param payload Notification payload
   * @returns Messaging response
   */
  async sendPushNotificationToMultipleDevices(
    tokens: string[],
    payload: admin.messaging.MessagingPayload,
  ) {
    try {
      // Send to each token individually as the newer API doesn't support multiple tokens in one call
      const results = await Promise.all(
        tokens.map(token => 
          this.getMessaging().send({
            token: token,
            notification: payload.notification,
            data: payload.data
          })
        )
      );
      return results;
    } catch (error) {
      this.logger.error(`Failed to send push notifications to multiple devices: ${error.message}`);
      throw error;
    }
  }

  /**
   * Send push notification to a topic
   * @param topic Topic name
   * @param payload Notification payload
   * @returns Messaging response
   */
  async sendPushNotificationToTopic(
    topic: string,
    payload: admin.messaging.MessagingPayload,
  ) {
    try {
      return await this.getMessaging().send({
        topic: topic,
        notification: payload.notification,
        data: payload.data
      });
    } catch (error) {
      this.logger.error(`Failed to send push notification to topic: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Create a new user in Firebase Authentication
   * @param email User email
   * @param password User password
   * @param displayName User's display name
   * @returns Firebase user record
   */
  async createUser(email: string, password: string, displayName?: string) {
    try {
      return await this.getAuth().createUser({
        email,
        password,
        displayName,
        emailVerified: false,
      });
    } catch (error) {
      this.logger.error(`Failed to create Firebase user: ${error.message}`);
      throw error;
    }
  }

  /**
   * Set custom claims for a user
   * @param uid User ID
   * @param claims Custom claims
   */
  async setCustomUserClaims(uid: string, claims: object) {
    try {
      await this.getAuth().setCustomUserClaims(uid, claims);
      this.logger.log(`Custom claims set for user ${uid}`);
    } catch (error) {
      this.logger.error(`Failed to set custom claims for user ${uid}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Revoke all refresh tokens for a user
   * Useful after a password change or sensitive action
   * @param uid User ID
   */
  async revokeRefreshTokens(uid: string) {
    try {
      await this.getAuth().revokeRefreshTokens(uid);
      this.logger.log(`Refresh tokens revoked for user ${uid}`);
    } catch (error) {
      this.logger.error(`Failed to revoke refresh tokens for user ${uid}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Delete a user from Firebase Authentication
   * @param uid User ID
   */
  async deleteUser(uid: string) {
    try {
      await this.getAuth().deleteUser(uid);
      this.logger.log(`User ${uid} deleted from Firebase`);
    } catch (error) {
      this.logger.error(`Failed to delete user ${uid}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Generate a password reset link for a user
   * @param email User email
   * @returns Password reset link
   */
  async generatePasswordResetLink(email: string) {
    try {
      return await this.getAuth().generatePasswordResetLink(email);
    } catch (error) {
      this.logger.error(`Failed to generate password reset link for ${email}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Generate an email verification link for a user
   * @param email User email
   * @returns Email verification link
   */
  async generateEmailVerificationLink(email: string) {
    try {
      return await this.getAuth().generateEmailVerificationLink(email);
    } catch (error) {
      this.logger.error(`Failed to generate email verification link for ${email}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update a user's profile in Firebase Authentication
   * @param uid User ID
   * @param properties Properties to update
   * @returns Updated user record
   */
  async updateUser(uid: string, properties: admin.auth.UpdateRequest) {
    try {
      return await this.getAuth().updateUser(uid, properties);
    } catch (error) {
      this.logger.error(`Failed to update user ${uid}: ${error.message}`);
      throw error;
    }
  }
}
