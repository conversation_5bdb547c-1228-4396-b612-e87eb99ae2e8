import { Test, TestingModule } from '@nestjs/testing';
import { FirebaseService } from './firebase.service';
import { ConfigService } from '@nestjs/config';
import * as admin from 'firebase-admin';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DeviceToken } from '../notifications/entities/device-token.entity';
import { Repository } from 'typeorm';

jest.mock('firebase-admin', () => {
  const mockMessaging = {
    send: jest.fn().mockResolvedValue('message-id'),
  };

  const mockAuth = {
    verifyIdToken: jest.fn(),
    getUserByEmail: jest.fn(),
    getUser: jest.fn(),
    createCustomToken: jest.fn(),
    createUser: jest.fn(),
    setCustomUserClaims: jest.fn(),
    revokeRefreshTokens: jest.fn(),
    deleteUser: jest.fn(),
    generatePasswordResetLink: jest.fn(),
    generateEmailVerificationLink: jest.fn(),
    updateUser: jest.fn(),
  };

  return {
    apps: [],
    initializeApp: jest.fn(),
    credential: {
      cert: jest.fn(),
    },
    messaging: jest.fn().mockReturnValue(mockMessaging),
    auth: jest.fn().mockReturnValue(mockAuth),
  };
});

describe('FirebaseService', () => {
  let service: FirebaseService;
  let configService: ConfigService;
  let deviceTokenRepository: Repository<DeviceToken>;

  const mockConfigService = {
    get: jest.fn().mockImplementation((key) => {
      const config = {
        'firebase.projectId': 'test-project',
        'FIREBASE_CLIENT_EMAIL': '<EMAIL>',
        'FIREBASE_PRIVATE_KEY': '-----BEGIN PRIVATE KEY-----\nMockKey\n-----END PRIVATE KEY-----',
        'firebase.databaseUrl': 'https://test-project.firebaseio.com',
      };
      return config[key];
    }),
  };

  const mockDeviceTokenRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    delete: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();
    
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FirebaseService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: getRepositoryToken(DeviceToken),
          useValue: mockDeviceTokenRepository,
        },
      ],
    }).compile();

    service = module.get<FirebaseService>(FirebaseService);
    configService = module.get<ConfigService>(ConfigService);
    deviceTokenRepository = module.get<Repository<DeviceToken>>(getRepositoryToken(DeviceToken));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('onModuleInit', () => {
    it('should initialize Firebase with environment variables when available', () => {
      // Mock the initialization for credential cert
      (admin.credential.cert as jest.Mock).mockReturnValue('mocked-credential');
      
      service.onModuleInit();
      
      expect(admin.initializeApp).toHaveBeenCalledWith({
        credential: 'mocked-credential',
        databaseURL: 'https://test-project.firebaseio.com',
      });
      expect(admin.credential.cert).toHaveBeenCalledWith({
        projectId: 'test-project',
        clientEmail: '<EMAIL>',
        privateKey: '-----BEGIN PRIVATE KEY-----\nMockKey\n-----END PRIVATE KEY-----',
      });
    });

    it('should not re-initialize Firebase if already initialized', () => {
      // Mock that Firebase is already initialized
      (admin.apps as any).length = 1;
      
      service.onModuleInit();
      
      expect(admin.initializeApp).not.toHaveBeenCalled();
    });
  });

  describe('sendPushNotification', () => {
    it('should send a push notification to a single device', async () => {
      const token = 'device-token';
      const payload = {
        notification: {
          title: 'Test',
          body: 'This is a test',
        },
        data: {},
      };

      await service.sendPushNotification(token, payload);
      
      expect(admin.messaging().send).toHaveBeenCalledWith({
        token,
        ...payload,
      });
    });
  });

  describe('sendPushNotificationToMultipleDevices', () => {
    it('should send a notification to multiple devices', async () => {
      const tokens = ['token1', 'token2'];
      const payload = {
        notification: {
          title: 'Test',
          body: 'This is a test',
        },
        data: {},
      };

      await service.sendPushNotificationToMultipleDevices(tokens, payload);
      
      // Each token should be processed individually
      tokens.forEach((token) => {
        expect(admin.messaging().send).toHaveBeenCalledWith({
          token: token,
          notification: payload.notification,
          data: payload.data,
        });
      });
    });
  });

  describe('verifyIdToken', () => {
    it('should verify a Firebase ID token', async () => {
      const token = 'fake-token';
      const decodedToken = { uid: 'user-id', email: '<EMAIL>' };
      
      (admin.auth().verifyIdToken as jest.Mock).mockResolvedValue(decodedToken);
      
      const result = await service.verifyIdToken(token);
      
      expect(admin.auth().verifyIdToken).toHaveBeenCalledWith(token);
      expect(result).toEqual(decodedToken);
    });
  });

  describe('getUserByEmail', () => {
    it('should get a Firebase user by email', async () => {
      const email = '<EMAIL>';
      const userRecord = { uid: 'user-id', email };
      
      (admin.auth().getUserByEmail as jest.Mock).mockResolvedValue(userRecord);
      
      const result = await service.getUserByEmail(email);
      
      expect(admin.auth().getUserByEmail).toHaveBeenCalledWith(email);
      expect(result).toEqual(userRecord);
    });
  });

  describe('getUserByUid', () => {
    it('should get a Firebase user by UID', async () => {
      const uid = 'user-id';
      const userRecord = { uid, email: '<EMAIL>' };
      
      (admin.auth().getUser as jest.Mock).mockResolvedValue(userRecord);
      
      const result = await service.getUserByUid(uid);
      
      expect(admin.auth().getUser).toHaveBeenCalledWith(uid);
      expect(result).toEqual(userRecord);
    });
  });

  describe('createCustomToken', () => {
    it('should create a custom Firebase token', async () => {
      const uid = 'user-id';
      const claims = { role: 'admin' };
      const customToken = 'custom-token';
      
      (admin.auth().createCustomToken as jest.Mock).mockResolvedValue(customToken);
      
      const result = await service.createCustomToken(uid, claims);
      
      expect(admin.auth().createCustomToken).toHaveBeenCalledWith(uid, claims);
      expect(result).toEqual(customToken);
    });
  });

  describe('createUser', () => {
    it('should create a Firebase user', async () => {
      const email = '<EMAIL>';
      const password = 'password123';
      const displayName = 'Test User';
      const userRecord = { uid: 'user-id', email, displayName };
      
      (admin.auth().createUser as jest.Mock).mockResolvedValue(userRecord);
      
      const result = await service.createUser(email, password, displayName);
      
      expect(admin.auth().createUser).toHaveBeenCalledWith({
        email,
        password,
        displayName,
        emailVerified: false,
      });
      expect(result).toEqual(userRecord);
    });
  });
});