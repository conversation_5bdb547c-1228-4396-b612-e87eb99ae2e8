import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateHelpTables1736537000000 implements MigrationInterface {
    name = 'CreateHelpTables1736537000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create contact_inquiries table
        await queryRunner.query(`
            CREATE TABLE "contact_inquiries" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "name" character varying(100) NOT NULL,
                "email" character varying(255) NOT NULL,
                "subject" character varying(200) NOT NULL,
                "message" text NOT NULL,
                "user_id" character varying,
                "category" character varying NOT NULL DEFAULT 'general' CHECK ("category" IN ('technical', 'billing', 'feature-request', 'bug-report', 'general')),
                "status" character varying NOT NULL DEFAULT 'pending' CHECK ("status" IN ('pending', 'in-progress', 'resolved', 'closed')),
                "assigned_to" character varying,
                "response_sent" boolean NOT NULL DEFAULT false,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "resolved_at" TIMESTAMP,
                CONSTRAINT "PK_contact_inquiries" PRIMARY KEY ("id")
            )
        `);

        // Create feedback table
        await queryRunner.query(`
            CREATE TABLE "feedback" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "type" character varying NOT NULL CHECK ("type" IN ('bug', 'feature-request', 'improvement', 'compliment', 'complaint')),
                "title" character varying(200) NOT NULL,
                "description" text NOT NULL,
                "priority" character varying NOT NULL DEFAULT 'medium' CHECK ("priority" IN ('low', 'medium', 'high', 'critical')),
                "email" character varying,
                "user_id" character varying,
                "device_info" character varying,
                "app_version" character varying,
                "status" character varying NOT NULL DEFAULT 'pending' CHECK ("status" IN ('pending', 'reviewing', 'in-progress', 'completed', 'rejected')),
                "assigned_to" character varying,
                "admin_notes" text,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "completed_at" TIMESTAMP,
                CONSTRAINT "PK_feedback" PRIMARY KEY ("id")
            )
        `);

        // Create help_articles table
        await queryRunner.query(`
            CREATE TABLE "help_articles" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "title" character varying(200) NOT NULL,
                "description" character varying(500) NOT NULL,
                "content" text NOT NULL,
                "category" character varying NOT NULL CHECK ("category" IN ('getting-started', 'account', 'features', 'billing', 'troubleshooting', 'privacy', 'technical')),
                "tags" text,
                "published" boolean NOT NULL DEFAULT true,
                "sort_order" integer NOT NULL DEFAULT 0,
                "view_count" integer NOT NULL DEFAULT 0,
                "helpful_count" integer NOT NULL DEFAULT 0,
                "not_helpful_count" integer NOT NULL DEFAULT 0,
                "author_id" character varying NOT NULL,
                "last_reviewed_at" TIMESTAMP,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_help_articles" PRIMARY KEY ("id")
            )
        `);

        // Create indexes for better performance
        await queryRunner.query(`CREATE INDEX "IDX_contact_inquiries_status" ON "contact_inquiries" ("status")`);
        await queryRunner.query(`CREATE INDEX "IDX_contact_inquiries_category" ON "contact_inquiries" ("category")`);
        await queryRunner.query(`CREATE INDEX "IDX_contact_inquiries_created_at" ON "contact_inquiries" ("created_at")`);
        
        await queryRunner.query(`CREATE INDEX "IDX_feedback_type" ON "feedback" ("type")`);
        await queryRunner.query(`CREATE INDEX "IDX_feedback_status" ON "feedback" ("status")`);
        await queryRunner.query(`CREATE INDEX "IDX_feedback_priority" ON "feedback" ("priority")`);
        await queryRunner.query(`CREATE INDEX "IDX_feedback_created_at" ON "feedback" ("created_at")`);
        
        await queryRunner.query(`CREATE INDEX "IDX_help_articles_category" ON "help_articles" ("category")`);
        await queryRunner.query(`CREATE INDEX "IDX_help_articles_published" ON "help_articles" ("published")`);
        await queryRunner.query(`CREATE INDEX "IDX_help_articles_view_count" ON "help_articles" ("view_count")`);
        await queryRunner.query(`CREATE INDEX "IDX_help_articles_sort_order" ON "help_articles" ("sort_order")`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop indexes
        await queryRunner.query(`DROP INDEX "IDX_help_articles_sort_order"`);
        await queryRunner.query(`DROP INDEX "IDX_help_articles_view_count"`);
        await queryRunner.query(`DROP INDEX "IDX_help_articles_published"`);
        await queryRunner.query(`DROP INDEX "IDX_help_articles_category"`);
        await queryRunner.query(`DROP INDEX "IDX_feedback_created_at"`);
        await queryRunner.query(`DROP INDEX "IDX_feedback_priority"`);
        await queryRunner.query(`DROP INDEX "IDX_feedback_status"`);
        await queryRunner.query(`DROP INDEX "IDX_feedback_type"`);
        await queryRunner.query(`DROP INDEX "IDX_contact_inquiries_created_at"`);
        await queryRunner.query(`DROP INDEX "IDX_contact_inquiries_category"`);
        await queryRunner.query(`DROP INDEX "IDX_contact_inquiries_status"`);
        
        // Drop tables
        await queryRunner.query(`DROP TABLE "help_articles"`);
        await queryRunner.query(`DROP TABLE "feedback"`);
        await queryRunner.query(`DROP TABLE "contact_inquiries"`);
    }
}
