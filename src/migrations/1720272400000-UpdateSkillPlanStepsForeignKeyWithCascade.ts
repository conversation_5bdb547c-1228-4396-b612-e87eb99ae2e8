import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateSkillPlanStepsForeignKeyWithCascade1720272400000 implements MigrationInterface {
    name = 'UpdateSkillPlanStepsForeignKeyWithCascade1720272400000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Drop the existing foreign key constraint
        await queryRunner.query(`ALTER TABLE "skill_plan_steps" DROP CONSTRAINT "FK_be12a0fad10f0f3a4956d9edc7b"`);
        
        // Add the foreign key constraint with CASCADE delete
        await queryRunner.query(`ALTER TABLE "skill_plan_steps" ADD CONSTRAINT "FK_be12a0fad10f0f3a4956d9edc7b" FOREIGN KEY ("skillPlanId") REFERENCES "skill_plans"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop the CASCADE foreign key constraint
        await queryRunner.query(`ALTER TABLE "skill_plan_steps" DROP CONSTRAINT "FK_be12a0fad10f0f3a4956d9edc7b"`);
        
        // Add back the original foreign key constraint without CASCADE
        await queryRunner.query(`ALTER TABLE "skill_plan_steps" ADD CONSTRAINT "FK_be12a0fad10f0f3a4956d9edc7b" FOREIGN KEY ("skillPlanId") REFERENCES "skill_plans"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
}
