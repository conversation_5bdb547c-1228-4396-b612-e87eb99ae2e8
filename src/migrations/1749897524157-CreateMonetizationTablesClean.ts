import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateMonetizationTablesClean1749897524157 implements MigrationInterface {
    name = 'CreateMonetizationTablesClean1749897524157'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create subscription plan types enum
        await queryRunner.query(`CREATE TYPE "public"."subscription_plans_type_enum" AS ENUM('weekly', 'monthly', 'yearly')`);
        await queryRunner.query(`CREATE TYPE "public"."subscription_plans_platform_enum" AS ENUM('ios', 'android', 'web')`);
        
        // Create subscription plans table
        await queryRunner.query(`CREATE TABLE "subscription_plans" (
            "id" uuid NOT NULL DEFAULT uuid_generate_v4(), 
            "name" character varying NOT NULL, 
            "description" text, 
            "type" "public"."subscription_plans_type_enum" NOT NULL, 
            "platform" "public"."subscription_plans_platform_enum" NOT NULL, 
            "price" numeric(10,2) NOT NULL, 
            "currency" character varying(3) NOT NULL DEFAULT 'USD', 
            "product_id" character varying NOT NULL, 
            "has_free_trial" boolean NOT NULL DEFAULT false, 
            "free_trial_days" integer NOT NULL DEFAULT '0', 
            "active" boolean NOT NULL DEFAULT true, 
            "features" text, 
            "sort_order" integer NOT NULL DEFAULT '0', 
            "created_at" TIMESTAMP NOT NULL DEFAULT now(), 
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(), 
            CONSTRAINT "UQ_subscription_plans_name" UNIQUE ("name"), 
            CONSTRAINT "PK_subscription_plans" PRIMARY KEY ("id")
        )`);

        // Create coupon codes types enum
        await queryRunner.query(`CREATE TYPE "public"."coupon_codes_type_enum" AS ENUM('percentage', 'fixed_amount', 'free_trial_extension')`);
        
        // Create coupon codes table
        await queryRunner.query(`CREATE TABLE "coupon_codes" (
            "id" uuid NOT NULL DEFAULT uuid_generate_v4(), 
            "code" character varying NOT NULL, 
            "name" character varying NOT NULL, 
            "description" text, 
            "type" "public"."coupon_codes_type_enum" NOT NULL, 
            "value" numeric(10,2), 
            "max_uses" integer, 
            "used_count" integer NOT NULL DEFAULT '0', 
            "valid_from" TIMESTAMP NOT NULL, 
            "valid_until" TIMESTAMP NOT NULL, 
            "first_time_only" boolean NOT NULL DEFAULT false, 
            "applicablePlans" text, 
            "active" boolean NOT NULL DEFAULT true, 
            "minimum_purchase_amount" numeric(10,2), 
            "free_trial_days" integer, 
            "created_at" TIMESTAMP NOT NULL DEFAULT now(), 
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(), 
            CONSTRAINT "UQ_coupon_codes_code" UNIQUE ("code"), 
            CONSTRAINT "PK_coupon_codes" PRIMARY KEY ("id")
        )`);

        // Create subscriptions status enum
        await queryRunner.query(`CREATE TYPE "public"."subscriptions_status_enum" AS ENUM('active', 'expired', 'cancelled', 'pending', 'grace_period', 'free_trial')`);
        
        // Create subscriptions table
        await queryRunner.query(`CREATE TABLE "subscriptions" (
            "id" uuid NOT NULL DEFAULT uuid_generate_v4(), 
            "user_id" uuid NOT NULL, 
            "plan_id" uuid NOT NULL, 
            "coupon_id" uuid, 
            "status" "public"."subscriptions_status_enum" NOT NULL DEFAULT 'pending', 
            "original_transaction_id" character varying, 
            "latest_receipt" text, 
            "purchase_token" character varying, 
            "starts_at" TIMESTAMP NOT NULL, 
            "expires_at" TIMESTAMP NOT NULL, 
            "cancelled_at" TIMESTAMP, 
            "is_free_trial" boolean NOT NULL DEFAULT false, 
            "free_trial_ends_at" TIMESTAMP, 
            "auto_renew" boolean NOT NULL DEFAULT true, 
            "price" numeric(10,2), 
            "currency" character varying(3), 
            "metadata" json, 
            "created_at" TIMESTAMP NOT NULL DEFAULT now(), 
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(), 
            CONSTRAINT "PK_subscriptions" PRIMARY KEY ("id")
        )`);

        // Create transactions enums
        await queryRunner.query(`CREATE TYPE "public"."transactions_type_enum" AS ENUM('purchase', 'renewal', 'refund', 'cancellation')`);
        await queryRunner.query(`CREATE TYPE "public"."transactions_status_enum" AS ENUM('pending', 'completed', 'failed', 'refunded')`);
        
        // Create transactions table
        await queryRunner.query(`CREATE TABLE "transactions" (
            "id" uuid NOT NULL DEFAULT uuid_generate_v4(), 
            "user_id" uuid NOT NULL, 
            "subscription_id" uuid, 
            "type" "public"."transactions_type_enum" NOT NULL, 
            "status" "public"."transactions_status_enum" NOT NULL DEFAULT 'pending', 
            "amount" numeric(10,2) NOT NULL, 
            "currency" character varying(3) NOT NULL, 
            "platform_transaction_id" character varying, 
            "platform_receipt" text, 
            "platform_purchase_token" character varying, 
            "metadata" json, 
            "processed_at" TIMESTAMP, 
            "created_at" TIMESTAMP NOT NULL DEFAULT now(), 
            CONSTRAINT "PK_transactions" PRIMARY KEY ("id")
        )`);

        // Add foreign key constraints
        await queryRunner.query(`ALTER TABLE "subscriptions" ADD CONSTRAINT "FK_subscriptions_user_id" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "subscriptions" ADD CONSTRAINT "FK_subscriptions_plan_id" FOREIGN KEY ("plan_id") REFERENCES "subscription_plans"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "subscriptions" ADD CONSTRAINT "FK_subscriptions_coupon_id" FOREIGN KEY ("coupon_id") REFERENCES "coupon_codes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "transactions" ADD CONSTRAINT "FK_transactions_user_id" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "transactions" ADD CONSTRAINT "FK_transactions_subscription_id" FOREIGN KEY ("subscription_id") REFERENCES "subscriptions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop foreign key constraints
        await queryRunner.query(`ALTER TABLE "transactions" DROP CONSTRAINT "FK_transactions_subscription_id"`);
        await queryRunner.query(`ALTER TABLE "transactions" DROP CONSTRAINT "FK_transactions_user_id"`);
        await queryRunner.query(`ALTER TABLE "subscriptions" DROP CONSTRAINT "FK_subscriptions_coupon_id"`);
        await queryRunner.query(`ALTER TABLE "subscriptions" DROP CONSTRAINT "FK_subscriptions_plan_id"`);
        await queryRunner.query(`ALTER TABLE "subscriptions" DROP CONSTRAINT "FK_subscriptions_user_id"`);
        
        // Drop tables
        await queryRunner.query(`DROP TABLE "transactions"`);
        await queryRunner.query(`DROP TABLE "subscriptions"`);
        await queryRunner.query(`DROP TABLE "coupon_codes"`);
        await queryRunner.query(`DROP TABLE "subscription_plans"`);
        
        // Drop enums
        await queryRunner.query(`DROP TYPE "public"."transactions_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."transactions_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."subscriptions_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."coupon_codes_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."subscription_plans_platform_enum"`);
        await queryRunner.query(`DROP TYPE "public"."subscription_plans_type_enum"`);
    }
}
