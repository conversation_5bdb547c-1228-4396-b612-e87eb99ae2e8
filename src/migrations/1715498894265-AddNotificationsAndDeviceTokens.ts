import { MigrationInterface, QueryRunner, Table, TableForeignKey } from "typeorm";

export class AddNotificationsAndDeviceTokens1715498894265 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create DeviceTokens table only if it doesn't exist
        const deviceTokensExists = await queryRunner.hasTable('device_tokens');
        if (!deviceTokensExists) {
            await queryRunner.createTable(new Table({
                name: 'device_tokens',
                columns: [
                    {
                        name: 'id',
                        type: 'uuid',
                        isPrimary: true,
                        default: 'uuid_generate_v4()',
                    },
                    {
                        name: 'user_id',
                        type: 'uuid',
                    },
                    {
                        name: 'device_token',
                        type: 'varchar',
                        isNullable: false,
                    },
                    {
                        name: 'device_type',
                        type: 'varchar',
                        isNullable: false,
                    },
                    {
                        name: 'device_name',
                        type: 'varchar',
                    isNullable: true,
                },
                {
                    name: 'is_active',
                    type: 'boolean',
                    default: true,
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'now()',
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'now()',
                },
                {
                    name: 'last_used_at',
                    type: 'timestamp',
                    isNullable: true,
                },
            ],
        }), true);
        }

        // Create NotificationPreferences table only if it doesn't exist
        const notificationPreferencesExists = await queryRunner.hasTable('notification_preferences');
        if (!notificationPreferencesExists) {
            await queryRunner.createTable(new Table({
            name: 'notification_preferences',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    default: 'uuid_generate_v4()',
                },
                {
                    name: 'user_id',
                    type: 'uuid',
                    isUnique: true,
                },
                {
                    name: 'task_reminders',
                    type: 'boolean',
                    default: true,
                },
                {
                    name: 'habit_reminders',
                    type: 'boolean',
                    default: true,
                },
                {
                    name: 'streak_alerts',
                    type: 'boolean',
                    default: true,
                },
                {
                    name: 'milestone_celebrations',
                    type: 'boolean',
                    default: true,
                },
                {
                    name: 'challenge_updates',
                    type: 'boolean',
                    default: true,
                },
                {
                    name: 'new_messages',
                    type: 'boolean',
                    default: true,
                },
                {
                    name: 'podcast_ready',
                    type: 'boolean',
                    default: true,
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'now()',
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'now()',
                },
            ],
        }), true);
        }

        // Add foreign key constraints with error handling
        await queryRunner.query(`
            DO $$
            BEGIN
                -- Add foreign key for device_tokens if it doesn't exist
                IF NOT EXISTS (
                    SELECT 1 FROM information_schema.table_constraints 
                    WHERE constraint_name = 'FK_17e1f528b993c6d55def4cf5bea' 
                    AND table_name = 'device_tokens'
                ) THEN
                    ALTER TABLE device_tokens 
                    ADD CONSTRAINT FK_17e1f528b993c6d55def4cf5bea 
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
                END IF;
                
                -- Add foreign key for notification_preferences if it doesn't exist
                IF NOT EXISTS (
                    SELECT 1 FROM information_schema.table_constraints 
                    WHERE constraint_type = 'FOREIGN KEY' 
                    AND table_name = 'notification_preferences'
                    AND constraint_name LIKE 'FK_%'
                ) THEN
                    ALTER TABLE notification_preferences 
                    ADD CONSTRAINT FK_notification_preferences_user_id 
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
                END IF;
            END
            $$;
        `);

        // Update Users table with new columns for Firebase auth
        await queryRunner.query(`
            ALTER TABLE users 
            ADD COLUMN IF NOT EXISTS firebase_uid VARCHAR,
            ADD COLUMN IF NOT EXISTS provider VARCHAR DEFAULT 'local',
            ADD COLUMN IF NOT EXISTS picture VARCHAR;

            -- Make password field nullable for social logins
            ALTER TABLE users ALTER COLUMN password DROP NOT NULL;
        `);

        // Create enum type for auth providers if it doesn't exist
        await queryRunner.query(`
            DO $$
            BEGIN
                -- Handle auth_provider enum creation and casting
                IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'auth_provider') THEN
                    -- Check if users_provider_enum already exists
                    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'users_provider_enum') THEN
                        -- Rename the existing enum
                        ALTER TYPE users_provider_enum RENAME TO auth_provider;
                    ELSE
                        -- Create new enum if neither exists
                        CREATE TYPE auth_provider AS ENUM ('local', 'google', 'apple');
                    END IF;
                END IF;
                
                -- Update provider column to use the correct enum type
                IF EXISTS (SELECT 1 FROM information_schema.columns 
                         WHERE table_name = 'users' 
                         AND column_name = 'provider' 
                         AND udt_name = 'users_provider_enum') THEN
                    -- Column is using old enum name, update it safely
                    ALTER TABLE users ALTER COLUMN provider TYPE auth_provider USING provider::text::auth_provider;
                ELSIF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                                WHERE table_name = 'users' 
                                AND column_name = 'provider' 
                                AND udt_name = 'auth_provider') THEN
                    -- Column is not using enum yet, convert it
                    ALTER TABLE users ALTER COLUMN provider TYPE auth_provider USING provider::auth_provider;
                END IF;
            END
            $$;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop foreign keys first
        const deviceTokensTable = await queryRunner.getTable('device_tokens');
        if (deviceTokensTable) {
            const deviceTokensForeignKey = deviceTokensTable.foreignKeys.find(
                fk => fk.columnNames.indexOf('user_id') !== -1,
            );
            if (deviceTokensForeignKey) {
                await queryRunner.dropForeignKey('device_tokens', deviceTokensForeignKey);
            }
        }

        const notificationPreferencesTable = await queryRunner.getTable('notification_preferences');
        if (notificationPreferencesTable) {
            const notificationPreferencesForeignKey = notificationPreferencesTable.foreignKeys.find(
                fk => fk.columnNames.indexOf('user_id') !== -1,
            );
            if (notificationPreferencesForeignKey) {
                await queryRunner.dropForeignKey('notification_preferences', notificationPreferencesForeignKey);
            }
        }

        // Drop tables
        await queryRunner.dropTable('device_tokens');
        await queryRunner.dropTable('notification_preferences');

        // Remove columns from users table
        await queryRunner.query(`
            ALTER TABLE users 
            DROP COLUMN IF EXISTS firebase_uid,
            DROP COLUMN IF EXISTS provider,
            DROP COLUMN IF EXISTS picture;

            -- Make password required again
            ALTER TABLE users ALTER COLUMN password SET NOT NULL;
        `);

        // Drop enum type
        await queryRunner.query(`DROP TYPE IF EXISTS auth_provider;`);
    }
}
