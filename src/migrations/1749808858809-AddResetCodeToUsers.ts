import { MigrationInterface, QueryRunner } from "typeorm";

export class AddResetCodeToUsers1749808858809 implements MigrationInterface {
    name = 'AddResetCodeToUsers1749808858809'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "notification_preferences" DROP CONSTRAINT "fk_notification_preferences_user_id"`);
        await queryRunner.query(`ALTER TABLE "blog_posts" DROP CONSTRAINT "FK_blog_posts_author"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_help_articles_category"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_help_articles_published"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_help_articles_view_count"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_help_articles_sort_order"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_feedback_type"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_feedback_status"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_feedback_priority"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_feedback_created_at"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_contact_inquiries_status"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_contact_inquiries_category"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_contact_inquiries_created_at"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_blog_posts_slug"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_blog_posts_category"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_blog_posts_status"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_blog_posts_featured"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_blog_posts_published_at"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_blog_posts_author"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_blog_posts_created_at"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_blog_posts_deleted_at"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_blog_posts_published_status"`);
        await queryRunner.query(`ALTER TABLE "help_articles" DROP CONSTRAINT "help_articles_category_check"`);
        await queryRunner.query(`ALTER TABLE "feedback" DROP CONSTRAINT "feedback_type_check"`);
        await queryRunner.query(`ALTER TABLE "feedback" DROP CONSTRAINT "feedback_priority_check"`);
        await queryRunner.query(`ALTER TABLE "feedback" DROP CONSTRAINT "feedback_status_check"`);
        await queryRunner.query(`ALTER TABLE "contact_inquiries" DROP CONSTRAINT "contact_inquiries_category_check"`);
        await queryRunner.query(`ALTER TABLE "contact_inquiries" DROP CONSTRAINT "contact_inquiries_status_check"`);
        await queryRunner.query(`ALTER TABLE "blog_posts" DROP CONSTRAINT "blog_posts_status_check"`);
        await queryRunner.query(`ALTER TABLE "blog_posts" DROP CONSTRAINT "blog_posts_category_check"`);
        await queryRunner.query(`ALTER TABLE "blog_posts" DROP COLUMN "readingTime"`);
        await queryRunner.query(`ALTER TABLE "blog_posts" DROP COLUMN "deletedAt"`);
        await queryRunner.query(`ALTER TABLE "blog_posts" DROP COLUMN "metaTitle"`);
        // Check if reset_code column exists before adding it
        const resetCodeColumnExists = await queryRunner.hasColumn("users", "reset_code");
        if (!resetCodeColumnExists) {
            await queryRunner.query(`ALTER TABLE "users" ADD "reset_code" character varying`);
        }
        
        // Check if reset_code_expires_at column exists before adding it
        const resetCodeExpiresColumnExists = await queryRunner.hasColumn("users", "reset_code_expires_at");
        if (!resetCodeExpiresColumnExists) {
            await queryRunner.query(`ALTER TABLE "users" ADD "reset_code_expires_at" TIMESTAMP`);
        }
        await queryRunner.query(`ALTER TYPE "public"."auth_provider" RENAME TO "auth_provider_old"`);
        await queryRunner.query(`CREATE TYPE "public"."users_provider_enum" AS ENUM('local', 'google', 'apple')`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "provider" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "provider" TYPE "public"."users_provider_enum" USING "provider"::"text"::"public"."users_provider_enum"`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "provider" SET DEFAULT 'local'`);
        await queryRunner.query(`DROP TYPE "public"."auth_provider_old"`);
        await queryRunner.query(`ALTER TABLE "help_articles" DROP COLUMN "category"`);
        await queryRunner.query(`CREATE TYPE "public"."help_articles_category_enum" AS ENUM('getting-started', 'account', 'features', 'billing', 'troubleshooting', 'privacy', 'technical')`);
        await queryRunner.query(`ALTER TABLE "help_articles" ADD "category" "public"."help_articles_category_enum" NOT NULL DEFAULT 'getting-started'`);
        await queryRunner.query(`ALTER TABLE "feedback" DROP COLUMN "type"`);
        await queryRunner.query(`CREATE TYPE "public"."feedback_type_enum" AS ENUM('bug', 'feature-request', 'improvement', 'compliment', 'complaint')`);
        await queryRunner.query(`ALTER TABLE "feedback" ADD "type" "public"."feedback_type_enum" NOT NULL DEFAULT 'bug'`);
        await queryRunner.query(`ALTER TABLE "feedback" DROP COLUMN "priority"`);
        await queryRunner.query(`CREATE TYPE "public"."feedback_priority_enum" AS ENUM('low', 'medium', 'high', 'critical')`);
        await queryRunner.query(`ALTER TABLE "feedback" ADD "priority" "public"."feedback_priority_enum" NOT NULL DEFAULT 'medium'`);
        await queryRunner.query(`ALTER TABLE "feedback" DROP COLUMN "status"`);
        await queryRunner.query(`CREATE TYPE "public"."feedback_status_enum" AS ENUM('pending', 'reviewing', 'in-progress', 'completed', 'rejected')`);
        await queryRunner.query(`ALTER TABLE "feedback" ADD "status" "public"."feedback_status_enum" NOT NULL DEFAULT 'pending'`);
        await queryRunner.query(`ALTER TABLE "contact_inquiries" DROP COLUMN "category"`);
        await queryRunner.query(`CREATE TYPE "public"."contact_inquiries_category_enum" AS ENUM('technical', 'billing', 'feature-request', 'bug-report', 'general')`);
        await queryRunner.query(`ALTER TABLE "contact_inquiries" ADD "category" "public"."contact_inquiries_category_enum" NOT NULL DEFAULT 'general'`);
        await queryRunner.query(`ALTER TABLE "contact_inquiries" DROP COLUMN "status"`);
        await queryRunner.query(`CREATE TYPE "public"."contact_inquiries_status_enum" AS ENUM('pending', 'in-progress', 'resolved', 'closed')`);
        await queryRunner.query(`ALTER TABLE "contact_inquiries" ADD "status" "public"."contact_inquiries_status_enum" NOT NULL DEFAULT 'pending'`);
        await queryRunner.query(`ALTER TABLE "admin_users" DROP CONSTRAINT "UQ_admin_users_email"`);
        await queryRunner.query(`ALTER TABLE "admin_users" DROP COLUMN "email"`);
        await queryRunner.query(`ALTER TABLE "admin_users" ADD "email" character varying NOT NULL DEFAULT '<EMAIL>'`);
        await queryRunner.query(`ALTER TABLE "admin_users" ADD CONSTRAINT "UQ_dcd0c8a4b10af9c986e510b9ecc" UNIQUE ("email")`);
        await queryRunner.query(`ALTER TABLE "admin_users" DROP COLUMN "firstName"`);
        await queryRunner.query(`ALTER TABLE "admin_users" ADD "firstName" character varying NOT NULL DEFAULT 'Admin'`);
        await queryRunner.query(`ALTER TABLE "admin_users" DROP COLUMN "lastName"`);
        await queryRunner.query(`ALTER TABLE "admin_users" ADD "lastName" character varying NOT NULL DEFAULT 'User'`);
        await queryRunner.query(`ALTER TABLE "blog_posts" DROP COLUMN "category"`);
        await queryRunner.query(`CREATE TYPE "public"."blog_posts_category_enum" AS ENUM('wellness', 'ai-technology', 'habits', 'productivity', 'mental-health', 'fitness', 'nutrition', 'company-news')`);
        await queryRunner.query(`ALTER TABLE "blog_posts" ADD "category" "public"."blog_posts_category_enum" NOT NULL DEFAULT 'wellness'`);
        await queryRunner.query(`ALTER TABLE "blog_posts" DROP COLUMN "status"`);
        await queryRunner.query(`CREATE TYPE "public"."blog_posts_status_enum" AS ENUM('draft', 'published', 'archived')`);
        await queryRunner.query(`ALTER TABLE "blog_posts" ADD "status" "public"."blog_posts_status_enum" NOT NULL DEFAULT 'draft'`);
        await queryRunner.query(`ALTER TABLE "blog_posts" DROP COLUMN "featuredImage"`);
        await queryRunner.query(`ALTER TABLE "blog_posts" ADD "featuredImage" character varying`);
        await queryRunner.query(`ALTER TABLE "blog_posts" DROP COLUMN "metaDescription"`);
        await queryRunner.query(`ALTER TABLE "blog_posts" ADD "metaDescription" text`);
        await queryRunner.query(`ALTER TABLE "blog_posts" DROP COLUMN "tags"`);
        await queryRunner.query(`ALTER TABLE "blog_posts" ADD "tags" text`);
        await queryRunner.query(`ALTER TABLE "blog_posts" ADD CONSTRAINT "FK_09269227c7acf3cdf47ea4051e1" FOREIGN KEY ("authorId") REFERENCES "admin_users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "blog_posts" DROP CONSTRAINT "FK_09269227c7acf3cdf47ea4051e1"`);
        await queryRunner.query(`ALTER TABLE "blog_posts" DROP COLUMN "tags"`);
        await queryRunner.query(`ALTER TABLE "blog_posts" ADD "tags" text array`);
        await queryRunner.query(`ALTER TABLE "blog_posts" DROP COLUMN "metaDescription"`);
        await queryRunner.query(`ALTER TABLE "blog_posts" ADD "metaDescription" character varying(500)`);
        await queryRunner.query(`ALTER TABLE "blog_posts" DROP COLUMN "featuredImage"`);
        await queryRunner.query(`ALTER TABLE "blog_posts" ADD "featuredImage" character varying(500)`);
        await queryRunner.query(`ALTER TABLE "blog_posts" DROP COLUMN "status"`);
        await queryRunner.query(`DROP TYPE "public"."blog_posts_status_enum"`);
        await queryRunner.query(`ALTER TABLE "blog_posts" ADD "status" character varying NOT NULL DEFAULT 'draft'`);
        await queryRunner.query(`ALTER TABLE "blog_posts" DROP COLUMN "category"`);
        await queryRunner.query(`DROP TYPE "public"."blog_posts_category_enum"`);
        await queryRunner.query(`ALTER TABLE "blog_posts" ADD "category" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "admin_users" DROP COLUMN "lastName"`);
        await queryRunner.query(`ALTER TABLE "admin_users" ADD "lastName" character varying(100) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "admin_users" DROP COLUMN "firstName"`);
        await queryRunner.query(`ALTER TABLE "admin_users" ADD "firstName" character varying(100) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "admin_users" DROP CONSTRAINT "UQ_dcd0c8a4b10af9c986e510b9ecc"`);
        await queryRunner.query(`ALTER TABLE "admin_users" DROP COLUMN "email"`);
        await queryRunner.query(`ALTER TABLE "admin_users" ADD "email" character varying(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "admin_users" ADD CONSTRAINT "UQ_admin_users_email" UNIQUE ("email")`);
        await queryRunner.query(`ALTER TABLE "contact_inquiries" DROP COLUMN "status"`);
        await queryRunner.query(`DROP TYPE "public"."contact_inquiries_status_enum"`);
        await queryRunner.query(`ALTER TABLE "contact_inquiries" ADD "status" character varying NOT NULL DEFAULT 'pending'`);
        await queryRunner.query(`ALTER TABLE "contact_inquiries" DROP COLUMN "category"`);
        await queryRunner.query(`DROP TYPE "public"."contact_inquiries_category_enum"`);
        await queryRunner.query(`ALTER TABLE "contact_inquiries" ADD "category" character varying NOT NULL DEFAULT 'general'`);
        await queryRunner.query(`ALTER TABLE "feedback" DROP COLUMN "status"`);
        await queryRunner.query(`DROP TYPE "public"."feedback_status_enum"`);
        await queryRunner.query(`ALTER TABLE "feedback" ADD "status" character varying NOT NULL DEFAULT 'pending'`);
        await queryRunner.query(`ALTER TABLE "feedback" DROP COLUMN "priority"`);
        await queryRunner.query(`DROP TYPE "public"."feedback_priority_enum"`);
        await queryRunner.query(`ALTER TABLE "feedback" ADD "priority" character varying NOT NULL DEFAULT 'medium'`);
        await queryRunner.query(`ALTER TABLE "feedback" DROP COLUMN "type"`);
        await queryRunner.query(`DROP TYPE "public"."feedback_type_enum"`);
        await queryRunner.query(`ALTER TABLE "feedback" ADD "type" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "help_articles" DROP COLUMN "category"`);
        await queryRunner.query(`DROP TYPE "public"."help_articles_category_enum"`);
        await queryRunner.query(`ALTER TABLE "help_articles" ADD "category" character varying NOT NULL`);
        await queryRunner.query(`CREATE TYPE "public"."auth_provider_old" AS ENUM('local', 'google', 'apple')`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "provider" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "provider" TYPE "public"."auth_provider_old" USING "provider"::"text"::"public"."auth_provider_old"`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "provider" SET DEFAULT 'local'`);
        await queryRunner.query(`DROP TYPE "public"."users_provider_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."auth_provider_old" RENAME TO "auth_provider"`);
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "reset_code_expires_at"`);
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "reset_code"`);
        await queryRunner.query(`ALTER TABLE "blog_posts" ADD "metaTitle" character varying(255)`);
        await queryRunner.query(`ALTER TABLE "blog_posts" ADD "deletedAt" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "blog_posts" ADD "readingTime" integer`);
        await queryRunner.query(`ALTER TABLE "blog_posts" ADD CONSTRAINT "blog_posts_category_check" CHECK (((category)::text = ANY ((ARRAY['wellness'::character varying, 'ai-technology'::character varying, 'habits'::character varying, 'productivity'::character varying, 'mental-health'::character varying, 'fitness'::character varying, 'nutrition'::character varying, 'company-news'::character varying])::text[])))`);
        await queryRunner.query(`ALTER TABLE "blog_posts" ADD CONSTRAINT "blog_posts_status_check" CHECK (((status)::text = ANY ((ARRAY['draft'::character varying, 'published'::character varying, 'archived'::character varying])::text[])))`);
        await queryRunner.query(`ALTER TABLE "contact_inquiries" ADD CONSTRAINT "contact_inquiries_status_check" CHECK (((status)::text = ANY ((ARRAY['pending'::character varying, 'in-progress'::character varying, 'resolved'::character varying, 'closed'::character varying])::text[])))`);
        await queryRunner.query(`ALTER TABLE "contact_inquiries" ADD CONSTRAINT "contact_inquiries_category_check" CHECK (((category)::text = ANY ((ARRAY['technical'::character varying, 'billing'::character varying, 'feature-request'::character varying, 'bug-report'::character varying, 'general'::character varying])::text[])))`);
        await queryRunner.query(`ALTER TABLE "feedback" ADD CONSTRAINT "feedback_status_check" CHECK (((status)::text = ANY ((ARRAY['pending'::character varying, 'reviewing'::character varying, 'in-progress'::character varying, 'completed'::character varying, 'rejected'::character varying])::text[])))`);
        await queryRunner.query(`ALTER TABLE "feedback" ADD CONSTRAINT "feedback_priority_check" CHECK (((priority)::text = ANY ((ARRAY['low'::character varying, 'medium'::character varying, 'high'::character varying, 'critical'::character varying])::text[])))`);
        await queryRunner.query(`ALTER TABLE "feedback" ADD CONSTRAINT "feedback_type_check" CHECK (((type)::text = ANY ((ARRAY['bug'::character varying, 'feature-request'::character varying, 'improvement'::character varying, 'compliment'::character varying, 'complaint'::character varying])::text[])))`);
        await queryRunner.query(`ALTER TABLE "help_articles" ADD CONSTRAINT "help_articles_category_check" CHECK (((category)::text = ANY ((ARRAY['getting-started'::character varying, 'account'::character varying, 'features'::character varying, 'billing'::character varying, 'troubleshooting'::character varying, 'privacy'::character varying, 'technical'::character varying])::text[])))`);
        await queryRunner.query(`CREATE INDEX "IDX_blog_posts_published_status" ON "blog_posts" ("publishedAt", "status") `);
        await queryRunner.query(`CREATE INDEX "IDX_blog_posts_deleted_at" ON "blog_posts" ("deletedAt") `);
        await queryRunner.query(`CREATE INDEX "IDX_blog_posts_created_at" ON "blog_posts" ("createdAt") `);
        await queryRunner.query(`CREATE INDEX "IDX_blog_posts_author" ON "blog_posts" ("authorId") `);
        await queryRunner.query(`CREATE INDEX "IDX_blog_posts_published_at" ON "blog_posts" ("publishedAt") `);
        await queryRunner.query(`CREATE INDEX "IDX_blog_posts_featured" ON "blog_posts" ("isFeatured") `);
        await queryRunner.query(`CREATE INDEX "IDX_blog_posts_status" ON "blog_posts" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_blog_posts_category" ON "blog_posts" ("category") `);
        await queryRunner.query(`CREATE INDEX "IDX_blog_posts_slug" ON "blog_posts" ("slug") `);
        await queryRunner.query(`CREATE INDEX "IDX_contact_inquiries_created_at" ON "contact_inquiries" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_contact_inquiries_category" ON "contact_inquiries" ("category") `);
        await queryRunner.query(`CREATE INDEX "IDX_contact_inquiries_status" ON "contact_inquiries" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_feedback_created_at" ON "feedback" ("created_at") `);
        await queryRunner.query(`CREATE INDEX "IDX_feedback_priority" ON "feedback" ("priority") `);
        await queryRunner.query(`CREATE INDEX "IDX_feedback_status" ON "feedback" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_feedback_type" ON "feedback" ("type") `);
        await queryRunner.query(`CREATE INDEX "IDX_help_articles_sort_order" ON "help_articles" ("sort_order") `);
        await queryRunner.query(`CREATE INDEX "IDX_help_articles_view_count" ON "help_articles" ("view_count") `);
        await queryRunner.query(`CREATE INDEX "IDX_help_articles_published" ON "help_articles" ("published") `);
        await queryRunner.query(`CREATE INDEX "IDX_help_articles_category" ON "help_articles" ("category") `);
        await queryRunner.query(`ALTER TABLE "blog_posts" ADD CONSTRAINT "FK_blog_posts_author" FOREIGN KEY ("authorId") REFERENCES "admin_users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "notification_preferences" ADD CONSTRAINT "fk_notification_preferences_user_id" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

}
