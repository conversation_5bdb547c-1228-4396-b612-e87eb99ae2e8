import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateBlogCategoryConstraint1749738931604 implements MigrationInterface {
    name = 'UpdateBlogCategoryConstraint1749738931604'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Drop the existing constraint
        await queryRunner.query(`ALTER TABLE "blog_posts" DROP CONSTRAINT "blog_posts_category_check"`);
        
        // Add the new constraint with updated categories to match our TypeScript enum
        await queryRunner.query(`
            ALTER TABLE "blog_posts" 
            ADD CONSTRAINT "blog_posts_category_check" 
            CHECK ("category" IN (
                'wellness', 
                'ai-technology', 
                'habits', 
                'productivity', 
                'mental-health', 
                'fitness', 
                'nutrition', 
                'company-news'
            ))
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop the updated constraint
        await queryRunner.query(`ALTER TABLE "blog_posts" DROP CONSTRAINT "blog_posts_category_check"`);
        
        // Restore the original constraint
        await queryRunner.query(`
            ALTER TABLE "blog_posts" 
            ADD CONSTRAINT "blog_posts_category_check" 
            CHECK ("category" IN (
                'wellness', 
                'ai-technology', 
                'habits', 
                'productivity', 
                'motivation', 
                'health', 
                'fitness', 
                'nutrition', 
                'mental-health', 
                'lifestyle', 
                'technology', 
                'tutorials', 
                'guides', 
                'news', 
                'updates', 
                'announcements'
            ))
        `);
    }
}
