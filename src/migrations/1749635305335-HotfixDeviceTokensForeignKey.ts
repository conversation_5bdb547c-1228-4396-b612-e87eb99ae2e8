import { MigrationInterface, QueryRunner } from "typeorm";

export class HotfixDeviceTokensForeignKey1749635305335 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // This hotfix migration addresses the issue where the foreign key constraint
        // FK_17e1f528b993c6d55def4cf5bea already exists in production
        
        await queryRunner.query(`
            DO $$
            BEGIN
                -- Check if device_tokens table exists, if not create it
                IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'device_tokens') THEN
                    CREATE TABLE device_tokens (
                        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                        user_id UUID NOT NULL,
                        device_token VARCHAR NOT NULL,
                        device_type VARCHAR NOT NULL,
                        device_name VARCHAR,
                        is_active BOOLEAN DEFAULT true,
                        created_at TIMESTAMP DEFAULT now(),
                        updated_at TIMESTAMP DEFAULT now(),
                        last_used_at TIMESTAMP
                    );
                END IF;

                -- Check if notification_preferences table exists, if not create it
                IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notification_preferences') THEN
                    CREATE TABLE notification_preferences (
                        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                        user_id UUID UNIQUE NOT NULL,
                        task_reminders BOOLEAN DEFAULT true,
                        habit_reminders BOOLEAN DEFAULT true,
                        streak_alerts BOOLEAN DEFAULT true,
                        milestone_celebrations BOOLEAN DEFAULT true,
                        challenge_updates BOOLEAN DEFAULT true,
                        new_messages BOOLEAN DEFAULT true,
                        podcast_ready BOOLEAN DEFAULT true,
                        created_at TIMESTAMP DEFAULT now(),
                        updated_at TIMESTAMP DEFAULT now()
                    );
                END IF;

                -- Add foreign key constraints only if they don't exist
                IF NOT EXISTS (
                    SELECT 1 FROM information_schema.table_constraints 
                    WHERE constraint_name = 'FK_17e1f528b993c6d55def4cf5bea' 
                    AND table_name = 'device_tokens'
                ) THEN
                    ALTER TABLE device_tokens 
                    ADD CONSTRAINT FK_17e1f528b993c6d55def4cf5bea 
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
                END IF;
                
                -- Add foreign key for notification_preferences if it doesn't exist
                IF NOT EXISTS (
                    SELECT 1 FROM information_schema.table_constraints 
                    WHERE constraint_type = 'FOREIGN KEY' 
                    AND table_name = 'notification_preferences'
                    AND constraint_name LIKE '%user_id%'
                ) THEN
                    ALTER TABLE notification_preferences 
                    ADD CONSTRAINT FK_notification_preferences_user_id 
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
                END IF;

                -- Add missing columns to users table if they don't exist
                IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'firebase_uid') THEN
                    ALTER TABLE users ADD COLUMN firebase_uid VARCHAR;
                END IF;

                IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'provider') THEN
                    ALTER TABLE users ADD COLUMN provider VARCHAR DEFAULT 'local';
                END IF;

                IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'picture') THEN
                    ALTER TABLE users ADD COLUMN picture VARCHAR;
                END IF;

                -- Make password nullable if it's not already
                IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'password' AND is_nullable = 'NO') THEN
                    ALTER TABLE users ALTER COLUMN password DROP NOT NULL;
                END IF;

                -- Handle enum types carefully
                -- First, check if users_provider_enum exists and rename it
                IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'users_provider_enum') THEN
                    -- If auth_provider doesn't exist, rename the old one
                    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'auth_provider') THEN
                        ALTER TYPE users_provider_enum RENAME TO auth_provider;
                    ELSE
                        -- Both exist, need to handle this carefully
                        -- Drop the old one if it's not being used
                        BEGIN
                            DROP TYPE users_provider_enum CASCADE;
                        EXCEPTION WHEN OTHERS THEN
                            -- If it fails, it's probably in use, which is fine
                            NULL;
                        END;
                    END IF;
                ELSIF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'auth_provider') THEN
                    -- Neither exists, create auth_provider
                    CREATE TYPE auth_provider AS ENUM ('local', 'google', 'apple');
                END IF;

                -- Now safely update the column type if needed
                IF EXISTS (SELECT 1 FROM information_schema.columns 
                         WHERE table_name = 'users' 
                         AND column_name = 'provider' 
                         AND data_type = 'character varying') THEN
                    -- Column is varchar, convert to enum
                    ALTER TABLE users ALTER COLUMN provider TYPE auth_provider USING 
                        CASE 
                            WHEN provider = 'local' THEN 'local'::auth_provider
                            WHEN provider = 'google' THEN 'google'::auth_provider
                            WHEN provider = 'apple' THEN 'apple'::auth_provider
                            ELSE 'local'::auth_provider
                        END;
                END IF;

            END
            $$;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // This is a hotfix migration, rollback should be handled carefully in production
        // For safety, we won't automatically drop anything in the down migration
        console.log('Hotfix migration rollback - manual intervention may be required');
    }

}
