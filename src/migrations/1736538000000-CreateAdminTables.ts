import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateAdminTables1736538000000 implements MigrationInterface {
    name = 'CreateAdminTables1736538000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create admin_users table
        await queryRunner.query(`
            CREATE TABLE "admin_users" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "email" character varying(255) NOT NULL,
                "password" character varying NOT NULL,
                "firstName" character varying(100) NOT NULL,
                "lastName" character varying(100) NOT NULL,
                "isActive" boolean NOT NULL DEFAULT true,
                "lastLoginAt" TIMESTAMP,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_admin_users" PRIMARY KEY ("id"),
                CONSTRAINT "UQ_admin_users_email" UNIQUE ("email")
            )
        `);

        // Create landing_page_content table
        await queryRunner.query(`
            CREATE TABLE "landing_page_content" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "heroTitle" character varying(200),
                "heroSubtitle" text,
                "heroImage" character varying(500),
                "ctaText" character varying(100),
                "ctaLink" character varying(500),
                "feature1Title" character varying(100),
                "feature1Description" text,
                "feature2Title" character varying(100),
                "feature2Description" text,
                "feature3Title" character varying(100),
                "feature3Description" text,
                "aboutTitle" character varying(200),
                "aboutContent" text,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_landing_page_content" PRIMARY KEY ("id")
            )
        `);

        // Create app_constants table
        await queryRunner.query(`
            CREATE TABLE "app_constants" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "appVersion" character varying(20) NOT NULL,
                "buildNumber" integer NOT NULL,
                "minSupportedVersion" character varying(20) NOT NULL,
                "forceUpdateVersion" character varying(20),
                "appName" character varying(100) NOT NULL,
                "appDescription" text,
                "playStoreUrl" character varying(500),
                "appStoreUrl" character varying(500),
                "apiBaseUrl" character varying(500) NOT NULL,
                "apiVersion" character varying(10) NOT NULL,
                "requestTimeout" integer NOT NULL DEFAULT 30000,
                "maxRetries" integer NOT NULL DEFAULT 3,
                "enablePushNotifications" boolean NOT NULL DEFAULT true,
                "enableAnalytics" boolean NOT NULL DEFAULT true,
                "enableCrashReporting" boolean NOT NULL DEFAULT true,
                "enableBetaFeatures" boolean NOT NULL DEFAULT false,
                "enableOfflineMode" boolean NOT NULL DEFAULT true,
                "enableDarkMode" boolean NOT NULL DEFAULT true,
                "supportEmail" character varying(255),
                "websiteUrl" character varying(500),
                "privacyPolicyUrl" character varying(500),
                "termsOfServiceUrl" character varying(500),
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_app_constants" PRIMARY KEY ("id")
            )
        `);

        // Insert default admin user (password: "AhmSal1996;" - will be hashed by the service)
        await queryRunner.query(`
            INSERT INTO "admin_users" ("email", "password", "firstName", "lastName", "isActive", "createdAt", "updatedAt")
            VALUES ('<EMAIL>', '$2b$10$9.LdQxWjV5mV3KMq6R8eQO5FJ7v9ZQ2Q8H8A5z5mZ8bZ9cQ1W8Y1u', 'Ahmed', 'Salah', true, now(), now())
        `);

        // Insert default landing page content
        await queryRunner.query(`
            INSERT INTO "landing_page_content" (
                "heroTitle", "heroSubtitle", "heroImage", "ctaText", "ctaLink",
                "feature1Title", "feature1Description",
                "feature2Title", "feature2Description", 
                "feature3Title", "feature3Description",
                "aboutTitle", "aboutContent", "createdAt", "updatedAt"
            ) VALUES (
                'Welcome to Power Up',
                'Transform your life with our powerful habit tracking app. Build better habits, achieve your goals, and unlock your potential.',
                'https://images.unsplash.com/photo-1551632811-561732d1e306?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
                'Get Started Today',
                'https://play.google.com/store/apps/details?id=com.powerup',
                'Habit Tracking',
                'Track your daily habits and build consistency with our intuitive interface and smart reminders.',
                'Goal Setting',
                'Set ambitious goals and break them down into manageable steps with our goal management system.',
                'Progress Analytics',
                'Visualize your progress with detailed analytics and insights to stay motivated on your journey.',
                'About Power Up',
                'Power Up is more than just a habit tracker - it''s your personal growth companion. Our app combines the latest behavioral science with intuitive design to help you build lasting positive changes in your life. Whether you''re looking to develop new skills, maintain healthy routines, or achieve ambitious goals, Power Up provides the tools and motivation you need to succeed.',
                now(), now()
            )
        `);

        // Insert default app constants
        await queryRunner.query(`
            INSERT INTO "app_constants" (
                "appVersion", "buildNumber", "minSupportedVersion", "forceUpdateVersion",
                "appName", "appDescription", "playStoreUrl", "appStoreUrl",
                "apiBaseUrl", "apiVersion", "requestTimeout", "maxRetries",
                "enablePushNotifications", "enableAnalytics", "enableCrashReporting",
                "enableBetaFeatures", "enableOfflineMode", "enableDarkMode",
                "supportEmail", "websiteUrl", "privacyPolicyUrl", "termsOfServiceUrl",
                "createdAt", "updatedAt"
            ) VALUES (
                '1.0.0', 1, '1.0.0', null,
                'Power Up', 'Transform your life with powerful habit tracking', 
                'https://play.google.com/store/apps/details?id=com.powerup',
                'https://apps.apple.com/app/power-up/id123456789',
                'https://api.powerup.com', 'v1', 30000, 3,
                true, true, true, false, true, true,
                '<EMAIL>', 'https://powerup.com',
                'https://powerup.com/privacy', 'https://powerup.com/terms',
                now(), now()
            )
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "app_constants"`);
        await queryRunner.query(`DROP TABLE "landing_page_content"`);
        await queryRunner.query(`DROP TABLE "admin_users"`);
    }
}
