import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateCalendarEventsTable1735401600000 implements MigrationInterface {
    name = 'CreateCalendarEventsTable1735401600000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create calendar event type enum
        await queryRunner.query(`CREATE TYPE "public"."calendar_events_type_enum" AS ENUM('habit', 'task', 'custom')`);
        
        // Create calendar events table
        await queryRunner.query(`CREATE TABLE "calendar_events" (
            "id" uuid NOT NULL DEFAULT uuid_generate_v4(), 
            "title" character varying NOT NULL, 
            "description" text, 
            "startTime" TIMESTAMP NOT NULL, 
            "endTime" TIMESTAMP NOT NULL, 
            "type" "public"."calendar_events_type_enum" NOT NULL DEFAULT 'custom', 
            "relatedId" uuid, 
            "color" character varying, 
            "isCompleted" boolean NOT NULL DEFAULT false, 
            "userId" uuid NOT NULL, 
            "createdAt" TIMESTAMP NOT NULL DEFAULT now(), 
            "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), 
            CONSTRAINT "PK_calendar_events" PRIMARY KEY ("id")
        )`);

        // Create indexes
        await queryRunner.query(`CREATE INDEX "IDX_CALENDAR_EVENTS_USER_ID" ON "calendar_events" ("userId")`);
        await queryRunner.query(`CREATE INDEX "IDX_CALENDAR_EVENTS_START_TIME" ON "calendar_events" ("startTime")`);
        await queryRunner.query(`CREATE INDEX "IDX_CALENDAR_EVENTS_TYPE" ON "calendar_events" ("type")`);

        // Create foreign key
        await queryRunner.query(`ALTER TABLE "calendar_events" ADD CONSTRAINT "FK_calendar_events_user" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "calendar_events" DROP CONSTRAINT "FK_calendar_events_user"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_CALENDAR_EVENTS_TYPE"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_CALENDAR_EVENTS_START_TIME"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_CALENDAR_EVENTS_USER_ID"`);
        await queryRunner.query(`DROP TABLE "calendar_events"`);
        await queryRunner.query(`DROP TYPE "public"."calendar_events_type_enum"`);
    }
}
