import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateSkillPlansTables1749745400000 implements MigrationInterface {
    name = 'CreateSkillPlansTables1749745400000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if skill_plans table exists, if not create it
        const skillPlansExists = await queryRunner.hasTable("skill_plans");
        if (!skillPlansExists) {
            await queryRunner.query(`
                CREATE TABLE "skill_plans" (
                    "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                    "name" character varying NOT NULL,
                    "description" text NOT NULL,
                    "category" character varying NOT NULL,
                    "difficulty" character varying NOT NULL DEFAULT 'beginner',
                    "estimatedDuration" character varying NOT NULL,
                    "tags" text,
                    "isCompleted" boolean NOT NULL DEFAULT false,
                    "completedAt" TIMESTAMP,
                    "progress" integer NOT NULL DEFAULT 0,
                    "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                    "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                    "userId" uuid NOT NULL,
                    CONSTRAINT "PK_skill_plans" PRIMARY KEY ("id")
                )
            `);
        }

        // Check if skill_plan_steps table exists, if not create it
        const skillPlanStepsExists = await queryRunner.hasTable("skill_plan_steps");
        if (!skillPlanStepsExists) {
            await queryRunner.query(`
                CREATE TABLE "skill_plan_steps" (
                    "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                    "title" character varying NOT NULL,
                    "description" text NOT NULL,
                    "order" integer NOT NULL,
                    "resources" text,
                    "tasks" text,
                    "isCompleted" boolean NOT NULL DEFAULT false,
                    "completedAt" TIMESTAMP,
                    "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                    "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                    "skillPlanId" uuid NOT NULL,
                    CONSTRAINT "PK_skill_plan_steps" PRIMARY KEY ("id")
                )
            `);
        }

        // Add foreign key constraints if they don't exist
        if (!skillPlansExists) {
            await queryRunner.query(`
                ALTER TABLE "skill_plans" 
                ADD CONSTRAINT "FK_skill_plans_user" 
                FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION
            `);
        }

        if (!skillPlanStepsExists) {
            await queryRunner.query(`
                ALTER TABLE "skill_plan_steps" 
                ADD CONSTRAINT "FK_skill_plan_steps_skill_plan" 
                FOREIGN KEY ("skillPlanId") REFERENCES "skill_plans"("id") ON DELETE CASCADE ON UPDATE NO ACTION
            `);
        }

        // Add indexes for better performance (only if tables were just created)
        if (!skillPlansExists) {
            await queryRunner.query(`CREATE INDEX "IDX_skill_plans_user" ON "skill_plans" ("userId")`);
            await queryRunner.query(`CREATE INDEX "IDX_skill_plans_category" ON "skill_plans" ("category")`);
            await queryRunner.query(`CREATE INDEX "IDX_skill_plans_difficulty" ON "skill_plans" ("difficulty")`);
            await queryRunner.query(`CREATE INDEX "IDX_skill_plans_completed" ON "skill_plans" ("isCompleted")`);
        }
        
        if (!skillPlanStepsExists) {
            await queryRunner.query(`CREATE INDEX "IDX_skill_plan_steps_skill_plan" ON "skill_plan_steps" ("skillPlanId")`);
            await queryRunner.query(`CREATE INDEX "IDX_skill_plan_steps_order" ON "skill_plan_steps" ("skillPlanId", "order")`);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop indexes
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_skill_plan_steps_order"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_skill_plan_steps_skill_plan"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_skill_plans_completed"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_skill_plans_difficulty"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_skill_plans_category"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_skill_plans_user"`);

        // Drop foreign key constraints
        await queryRunner.query(`ALTER TABLE "skill_plan_steps" DROP CONSTRAINT IF EXISTS "FK_skill_plan_steps_skill_plan"`);
        await queryRunner.query(`ALTER TABLE "skill_plans" DROP CONSTRAINT IF EXISTS "FK_skill_plans_user"`);

        // Drop tables
        await queryRunner.query(`DROP TABLE IF EXISTS "skill_plan_steps"`);
        await queryRunner.query(`DROP TABLE IF EXISTS "skill_plans"`);
    }
}