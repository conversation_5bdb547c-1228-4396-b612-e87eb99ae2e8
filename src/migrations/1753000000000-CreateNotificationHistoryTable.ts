import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateNotificationHistoryTable1753000000000 implements MigrationInterface {
  name = 'CreateNotificationHistoryTable1753000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create notification_history table
    await queryRunner.query(`
      CREATE TABLE notification_history (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL,
        type VARCHAR NOT NULL CHECK (type IN (
          'habit_reminder',
          'habit_streak',
          'habit_milestone',
          'task_reminder',
          'task_milestone',
          'plan_progress',
          'plan_step_complete',
          'plan_complete',
          'challenge_start',
          'challenge_progress',
          'challenge_leaderboard',
          'challenge_complete',
          'challenge_achievement',
          'podcast_available',
          'podcast_recommendation'
        )),
        title VARCHAR(255) NOT NULL,
        body TEXT NOT NULL,
        status VARCHAR NOT NULL DEFAULT 'pending' CHECK (status IN (
          'pending',
          'sent',
          'delivered',
          'failed',
          'read'
        )),
        data JSONB,
        device_token VARCHAR(500),
        firebase_message_id VARCHAR(255),
        error_message TEXT,
        sent_at TIMESTAMP,
        delivered_at TIMESTAMP,
        read_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
      );
    `);

    // Create foreign key constraint to users table
    await queryRunner.query(`
      ALTER TABLE notification_history
      ADD CONSTRAINT FK_notification_history_user_id
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
    `);

    // Create indexes for better query performance
    await queryRunner.query(`
      CREATE INDEX IDX_notification_history_user_id ON notification_history(user_id);
      CREATE INDEX IDX_notification_history_type ON notification_history(type);
      CREATE INDEX IDX_notification_history_status ON notification_history(status);
      CREATE INDEX IDX_notification_history_created_at ON notification_history(created_at);
      CREATE INDEX IDX_notification_history_user_created ON notification_history(user_id, created_at);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop table (this will also drop indexes and foreign keys)
    await queryRunner.query(`DROP TABLE IF EXISTS notification_history;`);
  }
}
