import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateBlogTables1749800000000 implements MigrationInterface {
  name = 'CreateBlogTables1749800000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create blog_posts table
    await queryRunner.query(`
      CREATE TABLE "blog_posts" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "title" character varying(255) NOT NULL,
        "slug" character varying(255) NOT NULL,
        "content" text NOT NULL,
        "excerpt" character varying(500),
        "featuredImage" character varying(500),
        "category" character varying NOT NULL CHECK ("category" IN (
          'wellness', 'ai-technology', 'habits', 'productivity', 'motivation',
          'health', 'fitness', 'nutrition', 'mental-health', 'lifestyle',
          'technology', 'tutorials', 'guides', 'news', 'updates', 'announcements'
        )),
        "status" character varying NOT NULL DEFAULT 'draft' CHECK ("status" IN ('draft', 'published', 'archived')),
        "tags" text[],
        "views" integer NOT NULL DEFAULT 0,
        "likes" integer NOT NULL DEFAULT 0,
        "isFeatured" boolean NOT NULL DEFAULT false,
        "metaTitle" character varying(255),
        "metaDescription" character varying(500),
        "readingTime" integer,
        "publishedAt" TIMESTAMP,
        "authorId" uuid NOT NULL,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "deletedAt" TIMESTAMP,
        CONSTRAINT "PK_blog_posts" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_blog_posts_slug" UNIQUE ("slug"),
        CONSTRAINT "FK_blog_posts_author" FOREIGN KEY ("authorId") REFERENCES "admin_users"("id") ON DELETE CASCADE
      )
    `);

    // Create indexes for better performance
    await queryRunner.query(`CREATE INDEX "IDX_blog_posts_slug" ON "blog_posts" ("slug")`);
    await queryRunner.query(`CREATE INDEX "IDX_blog_posts_category" ON "blog_posts" ("category")`);
    await queryRunner.query(`CREATE INDEX "IDX_blog_posts_status" ON "blog_posts" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_blog_posts_featured" ON "blog_posts" ("isFeatured")`);
    await queryRunner.query(`CREATE INDEX "IDX_blog_posts_published_at" ON "blog_posts" ("publishedAt")`);
    await queryRunner.query(`CREATE INDEX "IDX_blog_posts_author" ON "blog_posts" ("authorId")`);
    await queryRunner.query(`CREATE INDEX "IDX_blog_posts_created_at" ON "blog_posts" ("createdAt")`);
    await queryRunner.query(`CREATE INDEX "IDX_blog_posts_deleted_at" ON "blog_posts" ("deletedAt")`);
    await queryRunner.query(`CREATE INDEX "IDX_blog_posts_published_status" ON "blog_posts" ("status", "publishedAt")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes first
    await queryRunner.query(`DROP INDEX "IDX_blog_posts_published_status"`);
    await queryRunner.query(`DROP INDEX "IDX_blog_posts_deleted_at"`);
    await queryRunner.query(`DROP INDEX "IDX_blog_posts_created_at"`);
    await queryRunner.query(`DROP INDEX "IDX_blog_posts_author"`);
    await queryRunner.query(`DROP INDEX "IDX_blog_posts_published_at"`);
    await queryRunner.query(`DROP INDEX "IDX_blog_posts_featured"`);
    await queryRunner.query(`DROP INDEX "IDX_blog_posts_status"`);
    await queryRunner.query(`DROP INDEX "IDX_blog_posts_category"`);
    await queryRunner.query(`DROP INDEX "IDX_blog_posts_slug"`);

    // Drop table
    await queryRunner.query(`DROP TABLE "blog_posts"`);
  }
}
