import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateMobileFeedbackTable1752649213597 implements MigrationInterface {
    name = 'CreateMobileFeedbackTable1752649213597'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "calendar_events" DROP CONSTRAINT "FK_calendar_events_user"`);
        await queryRunner.query(`ALTER TABLE "subscriptions" DROP CONSTRAINT "FK_subscriptions_coupon_id"`);
        await queryRunner.query(`ALTER TABLE "subscriptions" DROP CONSTRAINT "FK_subscriptions_plan_id"`);
        await queryRunner.query(`ALTER TABLE "subscriptions" DROP CONSTRAINT "FK_subscriptions_user_id"`);
        await queryRunner.query(`ALTER TABLE "transactions" DROP CONSTRAINT "FK_transactions_subscription_id"`);
        await queryRunner.query(`ALTER TABLE "transactions" DROP CONSTRAINT "FK_transactions_user_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_CALENDAR_EVENTS_USER_ID"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_CALENDAR_EVENTS_START_TIME"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_CALENDAR_EVENTS_TYPE"`);
        await queryRunner.query(`CREATE TYPE "public"."mobile_feedback_type_enum" AS ENUM('bug', 'feature-request', 'improvement', 'ui-ux', 'performance', 'content', 'general')`);
        await queryRunner.query(`CREATE TYPE "public"."mobile_feedback_priority_enum" AS ENUM('low', 'medium', 'high', 'critical')`);
        await queryRunner.query(`CREATE TYPE "public"."mobile_feedback_status_enum" AS ENUM('pending', 'reviewing', 'in-progress', 'completed', 'rejected', 'duplicate')`);
        await queryRunner.query(`CREATE TYPE "public"."mobile_feedback_platform_enum" AS ENUM('ios', 'android', 'web')`);
        await queryRunner.query(`CREATE TABLE "mobile_feedback" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "type" "public"."mobile_feedback_type_enum" NOT NULL DEFAULT 'general', "title" character varying(200) NOT NULL, "description" text NOT NULL, "priority" "public"."mobile_feedback_priority_enum" NOT NULL DEFAULT 'medium', "status" "public"."mobile_feedback_status_enum" NOT NULL DEFAULT 'pending', "platform" "public"."mobile_feedback_platform_enum", "app_version" character varying(20), "device_model" character varying(100), "os_version" character varying(50), "screen_resolution" character varying(20), "user_id" uuid, "email" character varying(100), "user_name" character varying(100), "contact_back" boolean NOT NULL DEFAULT false, "anonymous" boolean NOT NULL DEFAULT false, "rating" integer NOT NULL DEFAULT '0', "feature_context" character varying(200), "reproduction_steps" text, "expected_behavior" text, "actual_behavior" text, "screenshot_urls" json, "video_url" character varying, "admin_notes" text, "assigned_to" character varying, "resolution_notes" text, "estimated_hours" integer, "actual_hours" integer, "tags" json, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "resolved_at" TIMESTAMP, "due_date" TIMESTAMP, CONSTRAINT "PK_ccffaadf1eb8a96b3b9f42a2aa0" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "transactions" ADD "original_transaction_id" character varying`);
        await queryRunner.query(`ALTER TABLE "transactions" ADD "receipt_data" text`);
        await queryRunner.query(`ALTER TABLE "transactions" ADD "completed_at" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "reset_code"`);
        await queryRunner.query(`ALTER TABLE "users" ADD "reset_code" character varying`);
        await queryRunner.query(`ALTER TYPE "public"."transactions_type_enum" RENAME TO "transactions_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."transactions_type_enum" AS ENUM('purchase', 'subscription_purchase', 'renewal', 'refund', 'cancellation')`);
        await queryRunner.query(`ALTER TABLE "transactions" ALTER COLUMN "type" TYPE "public"."transactions_type_enum" USING "type"::"text"::"public"."transactions_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."transactions_type_enum_old"`);
        await queryRunner.query(`ALTER TABLE "feedback" ALTER COLUMN "type" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "help_articles" ALTER COLUMN "category" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "admin_users" ALTER COLUMN "email" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "admin_users" ALTER COLUMN "firstName" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "admin_users" ALTER COLUMN "lastName" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "calendar_events" ADD CONSTRAINT "FK_1c7bc3511809b48395c3eec5484" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "subscriptions" ADD CONSTRAINT "FK_d0a95ef8a28188364c546eb65c1" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "subscriptions" ADD CONSTRAINT "FK_e45fca5d912c3a2fab512ac25dc" FOREIGN KEY ("plan_id") REFERENCES "subscription_plans"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "subscriptions" ADD CONSTRAINT "FK_5759fd98f5e56940654ba474041" FOREIGN KEY ("coupon_id") REFERENCES "coupon_codes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "transactions" ADD CONSTRAINT "FK_e9acc6efa76de013e8c1553ed2b" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "transactions" ADD CONSTRAINT "FK_082a872afa121a69b68fbf3e121" FOREIGN KEY ("subscription_id") REFERENCES "subscriptions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "mobile_feedback" ADD CONSTRAINT "FK_35bce1bec8090ab5c04080a0803" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "mobile_feedback" DROP CONSTRAINT "FK_35bce1bec8090ab5c04080a0803"`);
        await queryRunner.query(`ALTER TABLE "transactions" DROP CONSTRAINT "FK_082a872afa121a69b68fbf3e121"`);
        await queryRunner.query(`ALTER TABLE "transactions" DROP CONSTRAINT "FK_e9acc6efa76de013e8c1553ed2b"`);
        await queryRunner.query(`ALTER TABLE "subscriptions" DROP CONSTRAINT "FK_5759fd98f5e56940654ba474041"`);
        await queryRunner.query(`ALTER TABLE "subscriptions" DROP CONSTRAINT "FK_e45fca5d912c3a2fab512ac25dc"`);
        await queryRunner.query(`ALTER TABLE "subscriptions" DROP CONSTRAINT "FK_d0a95ef8a28188364c546eb65c1"`);
        await queryRunner.query(`ALTER TABLE "calendar_events" DROP CONSTRAINT "FK_1c7bc3511809b48395c3eec5484"`);
        await queryRunner.query(`ALTER TABLE "admin_users" ALTER COLUMN "lastName" SET DEFAULT 'User'`);
        await queryRunner.query(`ALTER TABLE "admin_users" ALTER COLUMN "firstName" SET DEFAULT 'Admin'`);
        await queryRunner.query(`ALTER TABLE "admin_users" ALTER COLUMN "email" SET DEFAULT '<EMAIL>'`);
        await queryRunner.query(`ALTER TABLE "help_articles" ALTER COLUMN "category" SET DEFAULT 'getting-started'`);
        await queryRunner.query(`ALTER TABLE "feedback" ALTER COLUMN "type" SET DEFAULT 'bug'`);
        await queryRunner.query(`CREATE TYPE "public"."transactions_type_enum_old" AS ENUM('purchase', 'renewal', 'refund', 'cancellation')`);
        await queryRunner.query(`ALTER TABLE "transactions" ALTER COLUMN "type" TYPE "public"."transactions_type_enum_old" USING "type"::"text"::"public"."transactions_type_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."transactions_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."transactions_type_enum_old" RENAME TO "transactions_type_enum"`);
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "reset_code"`);
        await queryRunner.query(`ALTER TABLE "users" ADD "reset_code" character varying(8)`);
        await queryRunner.query(`ALTER TABLE "transactions" DROP COLUMN "completed_at"`);
        await queryRunner.query(`ALTER TABLE "transactions" DROP COLUMN "receipt_data"`);
        await queryRunner.query(`ALTER TABLE "transactions" DROP COLUMN "original_transaction_id"`);
        await queryRunner.query(`DROP TABLE "mobile_feedback"`);
        await queryRunner.query(`DROP TYPE "public"."mobile_feedback_platform_enum"`);
        await queryRunner.query(`DROP TYPE "public"."mobile_feedback_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."mobile_feedback_priority_enum"`);
        await queryRunner.query(`DROP TYPE "public"."mobile_feedback_type_enum"`);
        await queryRunner.query(`CREATE INDEX "IDX_CALENDAR_EVENTS_TYPE" ON "calendar_events" ("type") `);
        await queryRunner.query(`CREATE INDEX "IDX_CALENDAR_EVENTS_START_TIME" ON "calendar_events" ("startTime") `);
        await queryRunner.query(`CREATE INDEX "IDX_CALENDAR_EVENTS_USER_ID" ON "calendar_events" ("userId") `);
        await queryRunner.query(`ALTER TABLE "transactions" ADD CONSTRAINT "FK_transactions_user_id" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "transactions" ADD CONSTRAINT "FK_transactions_subscription_id" FOREIGN KEY ("subscription_id") REFERENCES "subscriptions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "subscriptions" ADD CONSTRAINT "FK_subscriptions_user_id" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "subscriptions" ADD CONSTRAINT "FK_subscriptions_plan_id" FOREIGN KEY ("plan_id") REFERENCES "subscription_plans"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "subscriptions" ADD CONSTRAINT "FK_subscriptions_coupon_id" FOREIGN KEY ("coupon_id") REFERENCES "coupon_codes"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "calendar_events" ADD CONSTRAINT "FK_calendar_events_user" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

}
