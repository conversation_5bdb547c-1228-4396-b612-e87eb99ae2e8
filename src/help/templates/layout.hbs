<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}} - Power Up</title>
    <meta name="description" content="{{description}}">
    
    <!-- Web App Manifest -->
    <link rel="manifest" href="/manifest.json">
    
    <!-- Favicons -->
    <link rel="icon" type="image/x-icon" href="/assets/images/logo.png">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                        dark: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <style>
        * {
            scroll-behavior: smooth;
        }
        
        body {
            background: linear-gradient(135deg, #020617 0%, #0f172a 100%);
            overflow-x: hidden;
        }
        
        .gradient-purple {
            background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%);
        }
        
        .gradient-text {
            background: linear-gradient(135deg, #60a5fa, #3b82f6, #2563eb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        /* Navbar */
        .navbar-transparent {
            background: transparent;
            backdrop-filter: none;
            transition: all 0.3s ease;
        }
        
        .navbar-scrolled {
            background: rgba(30, 41, 59, 0.95);
            backdrop-filter: blur(20px);
            box-shadow: 0 4px 20px rgba(96, 165, 250, 0.2);
        }
        
        /* Animations */
        .fade-in-up {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .fade-in-up.animate {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* Feature Cards */
        .feature-card {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(96, 165, 250, 0.2);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .feature-card:hover {
            transform: translateY(-10px) scale(1.02);
            border-color: rgba(96, 165, 250, 0.5);
            box-shadow: 0 25px 50px rgba(96, 165, 250, 0.2);
        }
        
        /* Glowing effects */
        .glow-purple {
            box-shadow: 0 0 30px rgba(96, 165, 250, 0.3);
        }
        
        .glow-purple:hover {
            box-shadow: 0 0 40px rgba(96, 165, 250, 0.5);
        }
        
        /* Dark mode enforcement */
        body {
            background-color: #111827 !important;
            color: #f9fafb !important;
        }
        
        .content-card {
            background-color: #1f2937 !important;
            color: #f9fafb !important;
        }
        
        /* Animated background */
        .animated-bg {
            background: radial-gradient(circle at 20% 80%, rgba(96, 165, 250, 0.1) 0%, transparent 50%),
                       radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                       radial-gradient(circle at 40% 40%, rgba(37, 99, 235, 0.1) 0%, transparent 50%);
            animation: backgroundShift 20s ease-in-out infinite;
        }
        
        @keyframes backgroundShift {
            0%, 100% { 
                background: radial-gradient(circle at 20% 80%, rgba(96, 165, 250, 0.1) 0%, transparent 50%),
                           radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                           radial-gradient(circle at 40% 40%, rgba(37, 99, 235, 0.1) 0%, transparent 50%);
            }
            50% { 
                background: radial-gradient(circle at 80% 20%, rgba(96, 165, 250, 0.15) 0%, transparent 50%),
                           radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
                           radial-gradient(circle at 60% 60%, rgba(37, 99, 235, 0.15) 0%, transparent 50%);
            }
        }
        
        /* SVG animations */
        .pulse-glow {
            animation: pulseGlow 2s ease-in-out infinite alternate;
        }
        
        @keyframes pulseGlow {
            from {
                filter: drop-shadow(0 0 10px rgba(96, 165, 250, 0.5));
            }
            to {
                filter: drop-shadow(0 0 20px rgba(96, 165, 250, 0.8));
            }
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #1e293b;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #60a5fa;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #3b82f6;
        }
        
        /* Page specific styles */
        .content-card {
            background: rgba(30, 41, 59, 0.9);
            border: 1px solid rgba(96, 165, 250, 0.2);
            backdrop-filter: blur(20px);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #60a5fa, #3b82f6);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(168, 85, 247, 0.3);
        }
        
        /* Mobile responsive */
        @media (max-width: 768px) {
            .container {
                padding-left: 1rem;
                padding-right: 1rem;
            }
        }
    </style>
</head>
<body class="bg-gray-900 text-white animated-bg min-h-screen">
    <!-- Transparent Navbar -->
    <nav class="navbar-transparent fixed top-0 w-full z-50" id="navbar">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <a href="/">
                    <div class="flex items-center space-x-3">
                        <img src="/assets/images/logo.png" alt="Power Up Logo" class="w-10 h-10 pulse-glow rounded-lg">
                        <span class="text-2xl font-bold gradient-text">Power Up</span>
                    </div>
                </a>
                <div class="hidden md:flex space-x-8">
                    <a href="/" class="hover:text-blue-400 transition-colors duration-300">Home</a>
                    <a href="/help" class="hover:text-blue-400 transition-colors duration-300">Help Center</a>
                    <a href="/help/contact" class="hover:text-blue-400 transition-colors duration-300">Contact</a>
                </div>
                <!-- Mobile menu button -->
                <button class="md:hidden text-white" id="mobile-menu-button">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" id="menu-icon">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                    <svg class="w-6 h-6 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24" id="close-icon">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Mobile menu -->
        <div class="md:hidden hidden bg-gray-900 bg-opacity-95 backdrop-filter backdrop-blur-lg border-t border-blue-500 border-opacity-20" id="mobile-menu">
            <div class="px-6 py-4 space-y-4">
                <a href="/" class="block text-white hover:text-blue-400 transition-colors duration-300 py-2">Home</a>
                <a href="/help" class="block text-white hover:text-blue-400 transition-colors duration-300 py-2">Help Center</a>
                <a href="/help/contact" class="block text-white hover:text-blue-400 transition-colors duration-300 py-2">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Page Content -->
    <main>
        {{{body}}}
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 backdrop-filter backdrop-blur-lg border-t border-blue-500 border-opacity-20">
        <div class="container mx-auto px-6 py-12">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-3 mb-6">
                        <img src="/assets/images/logo.png" alt="Power Up Logo" class="w-8 h-8 pulse-glow rounded-lg">
                        <span class="text-xl font-bold gradient-text">Power Up</span>
                    </div>
                    <p class="text-gray-400">Transform your life with AI-powered wellness coaching.</p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-blue-400">Help & Support</h3>
                    <ul class="space-y-2">
                        <li><a href="/help" class="text-gray-400 hover:text-blue-400 transition-colors">Help Center</a></li>
                        <li><a href="/help/contact" class="text-gray-400 hover:text-blue-400 transition-colors">Contact Us</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-blue-400">Legal</h3>
                    <ul class="space-y-2">
                        <li><a href="/help/privacy" class="text-gray-400 hover:text-blue-400 transition-colors">Privacy Policy</a></li>
                        <li><a href="/help/terms" class="text-gray-400 hover:text-blue-400 transition-colors">Terms & Conditions</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-blue-400">Connect</h3>
                    <p class="text-gray-400"><EMAIL></p>
                    <p class="text-gray-400">1-800-POWERUP</p>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 Power Up. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            const menuIcon = document.getElementById('menu-icon');
            const closeIcon = document.getElementById('close-icon');
            
            mobileMenu.classList.toggle('hidden');
            menuIcon.classList.toggle('hidden');
            closeIcon.classList.toggle('hidden');
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 10) {
                navbar.classList.add('navbar-scrolled');
            } else {
                navbar.classList.remove('navbar-scrolled');
            }
        });

        // Animate elements on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.fade-in-up').forEach(el => {
            observer.observe(el);
        });
    </script>
</body>
</html>
