<!-- Help Center Hero -->
<section class="bg-gradient-to-br from-blue-900 via-gray-900 to-slate-900 min-h-screen flex items-center">
    <div class="container mx-auto px-6 max-w-7xl text-center">
        <div class="fade-in-up">
            <h1 class="text-5xl md:text-6xl font-bold mb-6 gradient-text">Help Center</h1>
            <p class="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto mb-8">
                Find answers to your questions and get support for your wellness journey
            </p>
            
            <!-- Search Box -->
            <div class="max-w-2xl mx-auto">
                <div class="relative">
                    <input type="text" id="searchInput" placeholder="Search for help articles..." 
                        class="w-full px-6 py-4 pr-12 bg-gray-800/80 backdrop-filter backdrop-blur-lg border border-gray-600 rounded-full text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300">
                    <div class="absolute right-4 top-1/2 transform -translate-y-1/2">
                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Main Content -->
<section class="py-20 bg-gray-900">
    <div class="container mx-auto px-6 max-w-7xl">
        <!-- Categories -->
        <div class="mb-16">
            <h2 class="text-3xl font-bold mb-8 text-center gradient-text">Browse by Category</h2>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {{#each categories}}
                <div class="feature-card rounded-2xl p-8 text-center group cursor-pointer" onclick="filterByCategory('{{this}}')">
                    <div class="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-blue-500/30 transition-colors">
                        {{#if (eq this 'getting-started')}}
                        <svg class="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        {{else if (eq this 'account')}}
                        <svg class="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        {{else if (eq this 'features')}}
                        <svg class="w-8 h-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                        </svg>
                        {{else if (eq this 'billing')}}
                        <svg class="w-8 h-8 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        {{else if (eq this 'troubleshooting')}}
                        <svg class="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        {{else if (eq this 'privacy')}}
                        <svg class="w-8 h-8 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                        {{else if (eq this 'technical')}}
                        <svg class="w-8 h-8 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        {{else}}
                        <svg class="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        {{/if}}
                    </div>
                    <h3 class="text-xl font-bold mb-3 text-white capitalize group-hover:text-blue-400 transition-colors">
                        {{replace this '-' ' '}}
                    </h3>
                    <p class="text-gray-400 group-hover:text-gray-300 transition-colors">
                        Get help with {{replace this '-' ' '}} topics
                    </p>
                </div>
                {{/each}}
            </div>
        </div>
        
        <!-- Recent Articles -->
        <div class="mb-16">
            <h2 class="text-3xl font-bold mb-8 text-center gradient-text">Recent Articles</h2>
            <div class="content-card rounded-2xl p-8">
                <div id="articlesContainer" class="space-y-6">
                    {{#each articles}}
                    <div class="article-item border-b border-gray-700 last:border-b-0 pb-6 last:pb-0">
                        <a href="/help/article/{{this.id}}" class="block group">
                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center flex-shrink-0 group-hover:bg-blue-500/30 transition-colors">
                                    <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h4 class="text-lg font-semibold text-white group-hover:text-blue-400 transition-colors mb-2">
                                        {{this.title}}
                                    </h4>
                                    <div class="text-gray-400 line-clamp-2">
                                        {{{truncate this.content 150}}}...
                                    </div>
                                    <div class="flex items-center space-x-4 mt-3 text-sm text-gray-500">
                                        <span class="capitalize">{{this.category}}</span>
                                        <span>•</span>
                                        <span>{{formatDate this.createdAt}}</span>
                                    </div>
                                </div>
                                <div class="flex-shrink-0">
                                    <svg class="w-5 h-5 text-gray-500 group-hover:text-blue-400 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </div>
                            </div>
                        </a>
                    </div>
                    {{/each}}
                </div>
                
                {{#unless articles.length}}
                <div class="text-center py-12">
                    <svg class="w-16 h-16 text-gray-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="text-xl font-semibold text-gray-400 mb-2">No articles found</h3>
                    <p class="text-gray-500">Try adjusting your search or browse our categories.</p>
                </div>
                {{/unless}}
            </div>
        </div>
        
        <!-- Quick Links -->
        <div>
            <h2 class="text-3xl font-bold mb-8 text-center gradient-text">Quick Links</h2>
            <div class="grid md:grid-cols-3 gap-8">
                <div class="content-card rounded-2xl p-8 text-center">
                    <div class="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3 text-white">Contact Support</h3>
                    <p class="text-gray-400 mb-4">Get in touch with our support team for personalized help</p>
                    <a href="/help/contact" class="btn-primary text-white font-semibold py-2 px-6 rounded-lg inline-block transition-all duration-300 hover:scale-105">
                        Contact Us
                    </a>
                </div>
                
                <div class="content-card rounded-2xl p-8 text-center">
                    <div class="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3 text-white">Privacy Policy</h3>
                    <p class="text-gray-400 mb-4">Learn how we protect and handle your personal information</p>
                    <a href="/help/privacy" class="btn-primary text-white font-semibold py-2 px-6 rounded-lg inline-block transition-all duration-300 hover:scale-105">
                        Read Policy
                    </a>
                </div>
                
                <div class="content-card rounded-2xl p-8 text-center">
                    <div class="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3 text-white">Terms & Conditions</h3>
                    <p class="text-gray-400 mb-4">Review the terms and conditions for using Power Up</p>
                    <a href="/help/terms" class="btn-primary text-white font-semibold py-2 px-6 rounded-lg inline-block transition-all duration-300 hover:scale-105">
                        View Terms
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
let debounceTimer;
let currentCategory = null;

// Initialize animations
document.addEventListener('DOMContentLoaded', function() {
    // Trigger fade-in animation
    setTimeout(() => {
        document.querySelectorAll('.fade-in-up').forEach(el => {
            el.classList.add('animate');
        });
    }, 100);
});

// Search functionality
document.getElementById('searchInput').addEventListener('input', function(e) {
    clearTimeout(debounceTimer);
    const query = e.target.value.trim();
    
    debounceTimer = setTimeout(() => {
        if (query.length > 2) {
            searchArticles(query);
        } else if (query.length === 0) {
            // Reset to show all articles or current category
            if (currentCategory) {
                filterByCategory(currentCategory);
            } else {
                showAllArticles();
            }
        }
    }, 300);
});

async function searchArticles(query) {
    try {
        const response = await fetch(`/api/help/articles/search?query=${encodeURIComponent(query)}`);
        const data = await response.json();
        updateArticlesDisplay(data.articles || []);
    } catch (error) {
        console.error('Search error:', error);
        showErrorMessage('Failed to search articles. Please try again.');
    }
}

async function filterByCategory(category) {
    currentCategory = category;
    try {
        const response = await fetch(`/api/help/articles/category/${encodeURIComponent(category)}`);
        const articles = await response.json();
        // Category endpoint returns array directly, not wrapped in {articles: []}
        updateArticlesDisplay(Array.isArray(articles) ? articles : []);
        
        // Update search placeholder
        const searchInput = document.getElementById('searchInput');
        searchInput.placeholder = `Search in ${category.replace('-', ' ')}...`;
    } catch (error) {
        console.error('Filter error:', error);
        showErrorMessage('Failed to filter articles. Please try again.');
    }
}

function showAllArticles() {
    currentCategory = null;
    // Reset to original articles
    location.reload();
}

function updateArticlesDisplay(articles) {
    const container = document.getElementById('articlesContainer');
    
    if (articles.length === 0) {
        container.innerHTML = `
            <div class="text-center py-12">
                <svg class="w-16 h-16 text-gray-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h3 class="text-xl font-semibold text-gray-400 mb-2">No articles found</h3>
                <p class="text-gray-500">Try adjusting your search or browse our categories.</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = articles.map(article => `
        <div class="article-item border-b border-gray-700 last:border-b-0 pb-6 last:pb-0">
            <a href="/help/article/${article.id}" class="block group">
                <div class="flex items-start space-x-4">
                    <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center flex-shrink-0 group-hover:bg-blue-500/30 transition-colors">
                        <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-semibold text-white group-hover:text-blue-400 transition-colors mb-2">
                            ${article.title}
                        </h4>
                        <div class="text-gray-400 line-clamp-2">
                            ${article.content.substring(0, 150)}...
                        </div>
                        <div class="flex items-center space-x-4 mt-3 text-sm text-gray-500">
                            <span class="capitalize">${article.category}</span>
                            <span>•</span>
                            <span>${formatDate(article.createdAt)}</span>
                        </div>
                    </div>
                    <div class="flex-shrink-0">
                        <svg class="w-5 h-5 text-gray-500 group-hover:text-blue-400 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </div>
                </div>
            </a>
        </div>
    `).join('');
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
    });
}

function showErrorMessage(message) {
    const errorAlert = document.createElement('div');
    errorAlert.className = 'fixed top-20 right-4 bg-red-600 text-white px-6 py-4 rounded-lg shadow-lg z-50 transform transition-all duration-300';
    errorAlert.innerHTML = `
        <div class="flex items-center space-x-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span>${message}</span>
        </div>
    `;
    document.body.appendChild(errorAlert);
    
    setTimeout(() => {
        errorAlert.remove();
    }, 5000);
}
</script>
