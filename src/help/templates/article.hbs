<!-- Article Hero -->
<section class="bg-gradient-to-br from-blue-900 via-gray-900 to-blue-900 py-24 md:py-32">
    <div class="container mx-auto px-6 max-w-7xl">
        <div class="fade-in-up max-w-5xl mx-auto">
            <!-- Breadcrumb -->
            <nav class="mb-8">
                <ol class="flex items-center space-x-2 text-sm text-gray-400">
                    <li><a href="/help" class="hover:text-blue-400 transition-colors">Help Center</a></li>
                    <li>
                        <svg class="w-4 h-4 mx-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </li>
                    <li class="capitalize text-blue-400">{{article.category}}</li>
                    <li>
                        <svg class="w-4 h-4 mx-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </li>
                    <li class="text-white">{{article.title}}</li>
                </ol>
            </nav>
            
            <h1 class="text-4xl md:text-5xl font-bold mb-6 gradient-text">{{article.title}}</h1>
            
            <div class="flex items-center space-x-6 text-gray-400">
                <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                    <span class="capitalize">{{article.category}}</span>
                </div>
                <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>{{formatDate article.createdAt}}</span>
                </div>
                {{#if article.estimatedReadTime}}
                <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                    <span>{{article.estimatedReadTime}} min read</span>
                </div>
                {{/if}}
            </div>
        </div>
    </div>
</section>

<!-- Main Content -->
<section class="py-20 bg-gray-900">
    <div class="container mx-auto px-6 max-w-7xl">
        <div class="max-w-7xl mx-auto">
            <div class="grid lg:grid-cols-4 gap-16">
                <!-- Article Content -->
                <div class="lg:col-span-3">
                    <div class="content-card rounded-2xl p-10 md:p-16">
                        <!-- Article Content -->
                        <div class="prose prose-invert prose-lg max-w-none">
                            {{{article.content}}}
                        </div>
                        
                        <!-- Article Footer -->
                        <div class="border-t border-gray-700 mt-16 pt-12">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-semibold text-white mb-2">Was this article helpful?</h3>
                                    <div class="flex items-center space-x-4">
                                        <button onclick="rateArticle(true)" class="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
                                            </svg>
                                            <span>Yes</span>
                                        </button>
                                        <button onclick="rateArticle(false)" class="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14H5.236a2 2 0 01-1.789-2.894l3.5-7A2 2 0 018.736 3h4.018c.163 0 .326.02.485.06L17 4m-7 10v2a2 2 0 002 2h.095c.5 0 .905-.405.905-.905 0-.714.211-1.412.608-2.006L17 13V4m-7 10h2M17 4H19a2 2 0 012 2v6a2 2 0 01-2 2h-2.5"></path>
                                            </svg>
                                            <span>No</span>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="text-right">
                                    <h3 class="text-lg font-semibold text-white mb-2">Share this article</h3>
                                    <div class="flex items-center space-x-3">
                                        <button onclick="shareArticle('twitter')" class="p-2 bg-black  text-white rounded-lg transition-colors">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M13.6823 10.6218L20.2391 3H18.6854L12.9921 9.61788L8.44486 3H3.2002L10.0765 13.0074L3.2002 21H4.75404L10.7663 14.0113L15.5685 21H20.8131L13.6819 10.6218H13.6823ZM11.5541 13.0956L10.8574 12.0991L5.31391 4.16971H7.70053L12.1742 10.5689L12.8709 11.5655L18.6861 19.8835H16.2995L11.5541 13.096V13.0956Z"/>
                                            </svg>
                                        </button>
                                        <button onclick="shareArticle('facebook')" class="p-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                            </svg>
                                        </button>
                                        <button onclick="copyLink()" class="p-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <div class="sticky top-24 space-y-10">
                        <!-- Table of Contents -->
                        {{#if article.tableOfContents}}
                        <div class="content-card rounded-2xl p-8">
                            <h3 class="text-lg font-semibold mb-6 gradient-text">Table of Contents</h3>
                            <nav class="space-y-3">
                                {{#each article.tableOfContents}}
                                <a href="#{{this.id}}" class="block text-gray-400 hover:text-blue-400 transition-colors py-1 text-sm">
                                    {{this.title}}
                                </a>
                                {{/each}}
                            </nav>
                        </div>
                        {{/if}}
                        
                        <!-- Related Articles -->
                        {{#if relatedArticles}}
                        <div class="content-card rounded-2xl p-8">
                            <h3 class="text-lg font-semibold mb-6 gradient-text">Related Articles</h3>
                            <div class="space-y-5">
                                {{#each relatedArticles}}
                                <a href="/help/article/{{this.id}}" class="block group">
                                    <div class="p-3 rounded-lg bg-gray-800 hover:bg-gray-700 transition-colors">
                                        <h4 class="text-sm font-medium text-white group-hover:text-blue-400 transition-colors mb-1">
                                            {{this.title}}
                                        </h4>
                                        <p class="text-xs text-gray-400 line-clamp-2">
                                            {{truncate this.content 80}}...
                                        </p>
                                    </div>
                                </a>
                                {{/each}}
                            </div>
                        </div>
                        {{/if}}
                        
                        <!-- Quick Help -->
                        <div class="content-card rounded-2xl p-8">
                            <h3 class="text-lg font-semibold mb-6 gradient-text">Still Need Help?</h3>
                            <div class="space-y-5">
                                <a href="/help/contact" class="block btn-primary text-white font-semibold py-3 px-4 rounded-lg text-center transition-all duration-300 hover:scale-105">
                                    Contact Support
                                </a>
                                <a href="/help" class="block text-center text-blue-400 hover:text-blue-300 transition-colors">
                                    ← Back to Help Center
                                </a>
                            </div>
                        </div>
                        
                        <!-- Article Info -->
                        <div class="content-card rounded-2xl p-8">
                            <h3 class="text-lg font-semibold mb-6 gradient-text">Article Info</h3>
                            <div class="space-y-4 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Category:</span>
                                    <span class="text-white capitalize">{{article.category}}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Published:</span>
                                    <span class="text-white">{{formatDate article.createdAt}}</span>
                                </div>
                                {{#if article.updatedAt}}
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Updated:</span>
                                    <span class="text-white">{{formatDate article.updatedAt}}</span>
                                </div>
                                {{/if}}
                                {{#if article.views}}
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Views:</span>
                                    <span class="text-white">{{article.views}}</span>
                                </div>
                                {{/if}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
function rateArticle(isHelpful) {
    fetch(`/api/help/articles/{{article.id}}/helpful`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ helpful: isHelpful })
    })
    .then(response => response.json())
    .then(data => {
        showSuccessMessage(isHelpful ? 'Thank you for your feedback!' : 'Thank you for letting us know. We\'ll work to improve this article.');
    })
    .catch(error => {
        console.error('Error rating article:', error);
        showErrorMessage('Failed to submit rating. Please try again.');
    });
}

function shareArticle(platform) {
    const url = window.location.href;
    const title = document.title;
    
    let shareUrl;
    switch (platform) {
        case 'twitter':
            shareUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`;
            break;
        case 'facebook':
            shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
            break;
        default:
            return;
    }
    
    window.open(shareUrl, '_blank', 'width=600,height=400');
}

function copyLink() {
    navigator.clipboard.writeText(window.location.href)
        .then(() => {
            showSuccessMessage('Link copied to clipboard!');
        })
        .catch(err => {
            console.error('Failed to copy link:', err);
            showErrorMessage('Failed to copy link. Please try again.');
        });
}

function showSuccessMessage(message) {
    const successAlert = document.createElement('div');
    successAlert.className = 'fixed top-20 right-4 bg-green-600 text-white px-6 py-4 rounded-lg shadow-lg z-50 transform transition-all duration-300';
    successAlert.innerHTML = `
        <div class="flex items-center space-x-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span>${message}</span>
        </div>
    `;
    document.body.appendChild(successAlert);
    
    setTimeout(() => {
        successAlert.remove();
    }, 3000);
}

function showErrorMessage(message) {
    const errorAlert = document.createElement('div');
    errorAlert.className = 'fixed top-20 right-4 bg-red-600 text-white px-6 py-4 rounded-lg shadow-lg z-50 transform transition-all duration-300';
    errorAlert.innerHTML = `
        <div class="flex items-center space-x-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span>${message}</span>
        </div>
    `;
    document.body.appendChild(errorAlert);
    
    setTimeout(() => {
        errorAlert.remove();
    }, 5000);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    });
}

// Smooth scrolling for table of contents
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});
</script>
