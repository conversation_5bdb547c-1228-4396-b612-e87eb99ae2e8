<!-- Privacy Policy Hero -->
<section class="bg-gradient-to-br from-blue-900 via-gray-900 to-blue-900 py-20">
    <div class="container mx-auto px-6 text-center">
        <div class="fade-in-up">
            <h1 class="text-5xl md:text-6xl font-bold mb-6 gradient-text">Privacy Policy</h1>
            <p class="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto">
                Your privacy is important to us. Learn how we collect, use, and protect your information.
            </p>
        </div>
    </div>
</section>

<!-- Main Content -->
<section class="py-16 bg-gray-900">
    <div class="container mx-auto px-6">
        <div class="max-w-4xl mx-auto">
            <div class="content-card bg-gray-800 rounded-2xl p-8 md:p-12">
                <div class="mb-8">
                    <p class="text-gray-400 italic">Last updated: January 1, 2024</p>
                </div>
                
                <div class="prose prose-invert max-w-none">
                    <!-- Information We Collect -->
                    <div class="mb-12">
                        <h2 class="text-3xl font-bold mb-6 gradient-text">Information We Collect</h2>
                        <p class="text-gray-300 mb-6 leading-relaxed">
                            We collect information you provide directly to us, such as when you create an account, 
                            make a purchase, or contact us for support. This helps us provide you with the best 
                            possible experience using Power Up.
                        </p>
                        
                        <div class="grid md:grid-cols-2 gap-8">
                            <div class="bg-gray-800 rounded-lg p-6 border border-blue-500/20">
                                <h3 class="text-xl font-semibold mb-4 text-blue-400">Personal Information</h3>
                                <ul class="text-gray-300 space-y-2">
                                    <li class="flex items-start space-x-2">
                                        <svg class="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span>Name and email address</span>
                                    </li>
                                    <li class="flex items-start space-x-2">
                                        <svg class="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span>Phone number (optional)</span>
                                    </li>
                                    <li class="flex items-start space-x-2">
                                        <svg class="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span>Payment information</span>
                                    </li>
                                    <li class="flex items-start space-x-2">
                                        <svg class="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span>Profile preferences</span>
                                    </li>
                                </ul>
                            </div>
                            
                            <div class="bg-gray-800 rounded-lg p-6 border border-blue-500/20">
                                <h3 class="text-xl font-semibold mb-4 text-blue-400">Usage Information</h3>
                                <ul class="text-gray-300 space-y-2">
                                    <li class="flex items-start space-x-2">
                                        <svg class="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span>IP address and location data</span>
                                    </li>
                                    <li class="flex items-start space-x-2">
                                        <svg class="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span>Browser type and version</span>
                                    </li>
                                    <li class="flex items-start space-x-2">
                                        <svg class="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span>App usage patterns</span>
                                    </li>
                                    <li class="flex items-start space-x-2">
                                        <svg class="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span>Device information</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <!-- How We Use Your Information -->
                    <div class="mb-12">
                        <h2 class="text-3xl font-bold mb-6 gradient-text">How We Use Your Information</h2>
                        <p class="text-gray-300 mb-6 leading-relaxed">
                            We use the information we collect to provide, maintain, and improve our services. 
                            Here's how your data helps us serve you better:
                        </p>
                        
                        <div class="grid gap-4">
                            <div class="feature-card rounded-lg p-6">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center">
                                        <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-white">Provide and maintain our services</h3>
                                        <p class="text-gray-400">Ensure our app functions properly and deliver personalized content</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="feature-card rounded-lg p-6">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center">
                                        <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-white">Process transactions</h3>
                                        <p class="text-gray-400">Handle payments and send related information securely</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="feature-card rounded-lg p-6">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-green-500/20 rounded-full flex items-center justify-center">
                                        <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-white">Provide customer support</h3>
                                        <p class="text-gray-400">Respond to your comments, questions, and support requests</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="feature-card rounded-lg p-6">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-yellow-500/20 rounded-full flex items-center justify-center">
                                        <svg class="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-white">Improve our services</h3>
                                        <p class="text-gray-400">Analyze usage patterns to enhance user experience</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Information Sharing -->
                    <div class="mb-12">
                        <h2 class="text-3xl font-bold mb-6 gradient-text">Information Sharing</h2>
                        <div class="bg-red-500/10 border border-red-500/20 rounded-lg p-6">
                            <div class="flex items-start space-x-3">
                                <svg class="w-6 h-6 text-red-400 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                                <div>
                                    <h3 class="text-lg font-semibold text-red-400 mb-2">We do not sell your data</h3>
                                    <p class="text-gray-300 leading-relaxed">
                                        We do not sell, trade, or otherwise transfer your personal information to third parties 
                                        without your consent, except as described in this policy. Your trust is paramount to us.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Data Security -->
                    <div class="mb-12">
                        <h2 class="text-3xl font-bold mb-6 gradient-text">Data Security</h2>
                        <p class="text-gray-300 mb-6 leading-relaxed">
                            We implement appropriate security measures to protect your personal information against 
                            unauthorized access, alteration, disclosure, or destruction.
                        </p>
                        
                        <div class="grid md:grid-cols-3 gap-6">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                    </svg>
                                </div>
                                <h3 class="text-lg font-semibold text-white mb-2">Encryption</h3>
                                <p class="text-gray-400 text-sm">All data is encrypted in transit and at rest</p>
                            </div>
                            
                            <div class="text-center">
                                <div class="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                    </svg>
                                </div>
                                <h3 class="text-lg font-semibold text-white mb-2">Compliance</h3>
                                <p class="text-gray-400 text-sm">GDPR and CCPA compliant practices</p>
                            </div>
                            
                            <div class="text-center">
                                <div class="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-8 h-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <h3 class="text-lg font-semibold text-white mb-2">Monitoring</h3>
                                <p class="text-gray-400 text-sm">24/7 security monitoring and alerts</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Your Rights -->
                    <div class="mb-12">
                        <h2 class="text-3xl font-bold mb-6 gradient-text">Your Rights</h2>
                        <p class="text-gray-300 mb-6 leading-relaxed">
                            You have the right to access, update, or delete your personal information. 
                            You can exercise these rights by logging into your account or contacting us directly.
                        </p>
                        
                        <div class="bg-blue-500/10 border border-blue-500/20 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-blue-400 mb-4">What you can do:</h3>
                            <ul class="space-y-2 text-gray-300">
                                <li class="flex items-center space-x-2">
                                    <svg class="w-5 h-5 text-blue-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span>Access and download your data</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <svg class="w-5 h-5 text-blue-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span>Update or correct your information</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <svg class="w-5 h-5 text-blue-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span>Delete your account and data</span>
                                </li>
                                <li class="flex items-center space-x-2">
                                    <svg class="w-5 h-5 text-blue-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span>Opt out of marketing communications</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- Contact Us -->
                    <div class="mb-8">
                        <h2 class="text-3xl font-bold mb-6 gradient-text">Contact Us</h2>
                        <p class="text-gray-300 mb-6 leading-relaxed">
                            If you have any questions about this Privacy Policy, please contact us at our 
                            <a href="/help/contact" class="text-blue-400 hover:text-blue-300 underline">contact page</a> 
                            or email us directly at 
                            <a href="mailto:<EMAIL>" class="text-blue-400 hover:text-blue-300 underline"><EMAIL></a>.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
