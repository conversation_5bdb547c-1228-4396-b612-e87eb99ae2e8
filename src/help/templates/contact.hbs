<style>
/* Ensure dark theme for contact page */
body { 
    background: linear-gradient(135deg, #020617 0%, #0f172a 100%) !important; 
    color: #ffffff !important; 
}
.content-card {
    background-color: #1e293b !important;
}
</style>

<!-- Contact Page Hero -->
<section class="min-h-screen flex items-center" style="background: linear-gradient(135deg, #020617 0%, #0f172a 100%)">
    <div class="container mx-auto px-6 max-w-7xl text-center">
        <div class="fade-in-up">
            <h1 class="text-5xl md:text-6xl font-bold mb-6 gradient-text">Contact Us</h1>
            <p class="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto mb-8">
                We're here to help! Get in touch with our support team and we'll get back to you as soon as possible.
            </p>
        </div>
    </div>
</section>

<!-- Main Content -->
<section class="py-20" style="background: linear-gradient(135deg, #020617 0%, #0f172a 100%)">
    <div class="container mx-auto px-6 max-w-7xl">
        <div class="grid lg:grid-cols-3 gap-16">
            <!-- Contact Form -->
            <div class="lg:col-span-2">
                <div class="content-card rounded-2xl p-10 md:p-16" style="background-color: #1e293b; border: 1px solid #334155;"}
                    <h2 class="text-3xl font-bold mb-8 gradient-text">Send us a message</h2>
                    <form id="contactForm" class="space-y-6">
                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-300 mb-2">Name *</label>
                                <input type="text" id="name" name="name" required
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400 transition-all duration-300">
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-300 mb-2">Email *</label>
                                <input type="email" id="email" name="email" required
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400 transition-all duration-300">
                            </div>
                        </div>
                        
                        <div>
                            <label for="subject" class="block text-sm font-medium text-gray-300 mb-2">Subject *</label>
                            <select id="subject" name="subject" required
                                class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white transition-all duration-300">
                                <option value="">Please select a subject</option>
                                <option value="general">General Inquiry</option>
                                <option value="technical">Technical Support</option>
                                <option value="billing">Billing Question</option>
                                <option value="feature">Feature Request</option>
                                <option value="bug">Bug Report</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="message" class="block text-sm font-medium text-gray-300 mb-2">Message *</label>
                            <textarea id="message" name="message" rows="6" required
                                placeholder="Please describe your question or issue in detail..."
                                class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-gray-400 resize-vertical transition-all duration-300"></textarea>
                        </div>
                        
                        <button type="submit" class="btn-primary text-white font-semibold py-3 px-8 rounded-lg transform transition-all duration-300 hover:scale-105">
                            <span class="flex items-center justify-center space-x-2">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                </svg>
                                <span>Send Message</span>
                            </span>
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="space-y-10">
                <!-- Quick Help -->
                <div class="content-card rounded-2xl p-8 bg-gray-800">
                    <h3 class="text-xl font-bold mb-4 gradient-text">Quick Help</h3>
                    <div class="space-y-3">
                        {{#each articles}}
                        <a href="/help/article/{{this.id}}" class="block p-3 rounded-lg bg-gray-800 hover:bg-gray-700 transition-colors group">
                            <span class="text-gray-300 group-hover:text-blue-400 transition-colors">{{this.title}}</span>
                        </a>
                        {{/each}}
                    </div>
                </div>
                
                <!-- Contact Information -->
                <div class="content-card rounded-2xl p-8 bg-gray-800">
                    <h3 class="text-xl font-bold mb-4 gradient-text">Other Ways to Reach Us</h3>
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            <div>
                                <p class="text-sm text-gray-400">Email</p>
                                <p class="text-white"><EMAIL></p>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                            <div>
                                <p class="text-sm text-gray-400">Phone</p>
                                <p class="text-white">1-800-POWERUP</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <p class="text-sm text-gray-400">Hours</p>
                                <p class="text-white">Mon-Fri 9AM-6PM EST</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Response Time -->
                <div class="content-card rounded-2xl p-8 bg-gray-800">
                    <h3 class="text-xl font-bold mb-4 gradient-text">Response Time</h3>
                    <div class="space-y-3 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-400">General Inquiries</span>
                            <span class="text-green-400">24 hours</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Technical Support</span>
                            <span class="text-yellow-400">4-8 hours</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Urgent Issues</span>
                            <span class="text-red-400">1-2 hours</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
document.getElementById('contactForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    // Show loading state
    submitBtn.innerHTML = `
        <span class="flex items-center justify-center space-x-2">
            <svg class="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            <span>Sending...</span>
        </span>
    `;
    submitBtn.disabled = true;
    
    const formData = new FormData(e.target);
    const data = Object.fromEntries(formData.entries());
    
    try {
        const response = await fetch('/api/help/contact', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        });
        
        if (response.ok) {
            // Show success message
            const successAlert = document.createElement('div');
            successAlert.className = 'fixed top-20 right-4 bg-green-600 text-white px-6 py-4 rounded-lg shadow-lg z-50 transform transition-all duration-300';
            successAlert.innerHTML = `
                <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span>Thank you for your message! We'll get back to you soon.</span>
                </div>
            `;
            document.body.appendChild(successAlert);
            
            // Reset form
            e.target.reset();
            
            // Remove success message after 5 seconds
            setTimeout(() => {
                successAlert.remove();
            }, 5000);
        } else {
            throw new Error('Failed to send message');
        }
    } catch (error) {
        // Show error message
        const errorAlert = document.createElement('div');
        errorAlert.className = 'fixed top-20 right-4 bg-red-600 text-white px-6 py-4 rounded-lg shadow-lg z-50 transform transition-all duration-300';
        errorAlert.innerHTML = `
            <div class="flex items-center space-x-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>There was an error sending your message. Please try again.</span>
            </div>
        `;
        document.body.appendChild(errorAlert);
        
        // Remove error message after 5 seconds
        setTimeout(() => {
            errorAlert.remove();
        }, 5000);
    } finally {
        // Restore button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
});

// Ensure dark theme
document.body.style.backgroundColor = '#111827';
document.body.style.color = '#ffffff';
</script>
