import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NestExpressApplication } from '@nestjs/platform-express';
import { HelpController } from './help.controller';
import { HelpWebController } from './help-web.controller';
import { HelpService } from './help.service';
import { HelpSeedService } from './help-seed.service';
import { ContactInquiry, Feedback, HelpArticle } from './entities';
import { join } from 'path';
import { create } from 'express-handlebars';

@Module({
  imports: [
    TypeOrmModule.forFeature([ContactInquiry, Feedback, HelpArticle])
  ],
  controllers: [HelpController, HelpWebController],
  providers: [HelpService, HelpSeedService],
  exports: [HelpService]
})
export class HelpModule {
  static configureHandlebars(app: NestExpressApplication) {
    // Configure Handlebars view engine
    const hbs = create({
      layoutsDir: join(process.cwd(), 'src/help/templates'),
      defaultLayout: 'layout',
      extname: '.hbs',
      helpers: {
        eq: (a: any, b: any) => a === b,
        replace: (str: string, search: string, replace: string) => {
          if (!str || typeof str !== 'string') return str || '';
          if (!search || typeof search !== 'string') return str;
          if (typeof replace !== 'string') replace = '';
          return str.replace(new RegExp(search, 'g'), replace);
        },
        truncate: (str: string, length: number) => {
          if (!str || typeof str !== 'string') return str || '';
          if (!length || length <= 0) return str;
          return str.length > length ? str.substring(0, length) + '...' : str;
        },
        formatDate: (dateString: string) => {
          if (!dateString) return '';
          try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return dateString;
            return date.toLocaleDateString('en-US', { 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            });
          } catch (error) {
            return dateString;
          }
        }
      }
    });

    app.engine('hbs', hbs.engine);
    app.set('view engine', 'hbs');
    app.set('views', join(process.cwd(), 'src/help/templates'));
  }
}
