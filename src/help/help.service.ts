import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, ILike } from 'typeorm';
import { ContactInquiry, Feedback, HelpArticle } from './entities';
import { ContactUsDto, FeedbackDto, SearchHelpDto, HelpCategory } from './dto';

@Injectable()
export class HelpService {
  constructor(
    @InjectRepository(ContactInquiry)
    private contactInquiryRepository: Repository<ContactInquiry>,
    @InjectRepository(Feedback)
    private feedbackRepository: Repository<Feedback>,
    @InjectRepository(HelpArticle)
    private helpArticleRepository: Repository<HelpArticle>,
  ) {}

  // Contact Us Methods
  async submitContactInquiry(contactUsDto: ContactUsDto): Promise<ContactInquiry> {
    const inquiry = this.contactInquiryRepository.create(contactUsDto);
    return await this.contactInquiryRepository.save(inquiry);
  }

  async getContactInquiries(status?: string): Promise<ContactInquiry[]> {
    const where = status ? { status } : {};
    return await this.contactInquiryRepository.find({
      where,
      order: { createdAt: 'DESC' }
    });
  }

  async getContactInquiry(id: string): Promise<ContactInquiry> {
    const inquiry = await this.contactInquiryRepository.findOne({ where: { id } });
    if (!inquiry) {
      throw new NotFoundException('Contact inquiry not found');
    }
    return inquiry;
  }

  async updateContactInquiryStatus(id: string, status: string, assignedTo?: string): Promise<ContactInquiry> {
    const inquiry = await this.getContactInquiry(id);
    inquiry.status = status;
    if (assignedTo) {
      inquiry.assignedTo = assignedTo;
    }
    if (status === 'resolved') {
      inquiry.resolvedAt = new Date();
    }
    return await this.contactInquiryRepository.save(inquiry);
  }

  // Feedback Methods
  async submitFeedback(feedbackDto: FeedbackDto): Promise<Feedback> {
    const feedback = this.feedbackRepository.create(feedbackDto);
    return await this.feedbackRepository.save(feedback);
  }

  async getFeedback(type?: string, status?: string): Promise<Feedback[]> {
    const where: any = {};
    if (type) where.type = type;
    if (status) where.status = status;

    return await this.feedbackRepository.find({
      where,
      order: { createdAt: 'DESC' }
    });
  }

  async getFeedbackById(id: string): Promise<Feedback> {
    const feedback = await this.feedbackRepository.findOne({ where: { id } });
    if (!feedback) {
      throw new NotFoundException('Feedback not found');
    }
    return feedback;
  }

  async updateFeedbackStatus(id: string, status: string, adminNotes?: string): Promise<Feedback> {
    const feedback = await this.getFeedbackById(id);
    feedback.status = status;
    if (adminNotes) {
      feedback.adminNotes = adminNotes;
    }
    if (status === 'completed') {
      feedback.completedAt = new Date();
    }
    return await this.feedbackRepository.save(feedback);
  }

  // Help Articles Methods
  async searchHelpArticles(searchDto: SearchHelpDto): Promise<{ articles: HelpArticle[], total: number }> {
    const { query, category, limit = 10, offset = 0 } = searchDto;
    
    const queryBuilder = this.helpArticleRepository.createQueryBuilder('article')
      .where('article.published = :published', { published: true });

    if (query) {
      queryBuilder.andWhere(
        '(article.title ILIKE :query OR article.description ILIKE :query OR article.content ILIKE :query)',
        { query: `%${query}%` }
      );
    }

    if (category) {
      queryBuilder.andWhere('article.category = :category', { category });
    }

    queryBuilder
      .orderBy('article.sortOrder', 'ASC')
      .addOrderBy('article.viewCount', 'DESC')
      .take(limit)
      .skip(offset);

    const [articles, total] = await queryBuilder.getManyAndCount();
    
    return { articles, total };
  }

  async getHelpArticle(id: string): Promise<HelpArticle> {
    const article = await this.helpArticleRepository.findOne({ 
      where: { id, published: true } 
    });
    
    if (!article) {
      throw new NotFoundException('Help article not found');
    }

    // Increment view count
    article.viewCount++;
    await this.helpArticleRepository.save(article);

    return article;
  }

  async getHelpArticlesByCategory(category: HelpCategory): Promise<HelpArticle[]> {
    return await this.helpArticleRepository.find({
      where: { category, published: true },
      order: { sortOrder: 'ASC', viewCount: 'DESC' }
    });
  }

  async markArticleHelpful(id: string, helpful: boolean): Promise<HelpArticle> {
    const article = await this.getHelpArticle(id);
    
    if (helpful) {
      article.helpfulCount++;
    } else {
      article.notHelpfulCount++;
    }

    return await this.helpArticleRepository.save(article);
  }

  async getPopularArticles(limit: number = 10): Promise<HelpArticle[]> {
    return await this.helpArticleRepository.find({
      where: { published: true },
      order: { viewCount: 'DESC' },
      take: limit
    });
  }

  async getFAQs(): Promise<HelpArticle[]> {
    return await this.helpArticleRepository.find({
      where: { 
        published: true,
        tags: Like('%faq%')
      },
      order: { sortOrder: 'ASC' },
      take: 20
    });
  }

  // Admin Methods for Help Articles Management
  async getAllHelpArticlesForAdmin(): Promise<HelpArticle[]> {
    // Return all articles including unpublished ones for admin
    return await this.helpArticleRepository.find({
      order: { 
        sortOrder: 'ASC',
        createdAt: 'DESC' 
      }
    });
  }

  async getHelpArticleForAdmin(id: string): Promise<HelpArticle> {
    // Return article regardless of published status for admin
    const article = await this.helpArticleRepository.findOne({ where: { id } });
    if (!article) {
      throw new NotFoundException('Help article not found');
    }
    return article;
  }

  async createHelpArticle(data: {
    title: string;
    description: string;
    content: string;
    category: HelpCategory;
    tags?: string[];
    published?: boolean;
    sortOrder?: number;
    authorId?: string;
  }): Promise<HelpArticle> {
    const article = this.helpArticleRepository.create({
      ...data,
      published: data.published ?? true,
      sortOrder: data.sortOrder ?? 0,
      authorId: data.authorId ?? 'admin',
      viewCount: 0,
      helpfulCount: 0,
      notHelpfulCount: 0,
    });
    return await this.helpArticleRepository.save(article);
  }

  async updateHelpArticle(id: string, data: Partial<{
    title: string;
    description: string;
    content: string;
    category: HelpCategory;
    tags: string[];
    published: boolean;
    sortOrder: number;
  }>): Promise<HelpArticle> {
    const article = await this.getHelpArticleForAdmin(id);
    Object.assign(article, data);
    return await this.helpArticleRepository.save(article);
  }

  async deleteHelpArticle(id: string): Promise<void> {
    const article = await this.getHelpArticleForAdmin(id);
    await this.helpArticleRepository.remove(article);
  }

  // Static Content Methods
  getPrivacyPolicy(): any {
    return {
      title: "Privacy Policy",
      lastUpdated: "2025-01-01",
      content: {
        introduction: "Power Up is committed to protecting your privacy. This Privacy Policy explains how we collect, use, and protect your information when you use our wellness coaching application.",
        dataCollection: {
          title: "Information We Collect",
          items: [
            "Personal information (name, email, age)",
            "Health and wellness data (habits, goals, progress)",
            "Usage data (app interactions, feature usage)",
            "Device information (device type, OS version)",
            "Location data (if enabled for local features)"
          ]
        },
        dataUsage: {
          title: "How We Use Your Information",
          items: [
            "Provide personalized coaching and recommendations",
            "Generate custom podcasts and content",
            "Track your progress and habits",
            "Improve our services and app functionality",
            "Send notifications and updates (with your consent)"
          ]
        },
        dataProtection: {
          title: "Data Protection",
          items: [
            "End-to-end encryption for sensitive data",
            "Secure cloud storage with industry-standard security",
            "Regular security audits and updates",
            "Limited access to authorized personnel only",
            "Automatic data backup and recovery systems"
          ]
        },
        userRights: {
          title: "Your Rights",
          items: [
            "Access your personal data",
            "Correct inaccurate information",
            "Delete your account and data",
            "Export your data",
            "Opt-out of marketing communications"
          ]
        },
        contact: "For privacy-related questions, contact <NAME_EMAIL>"
      }
    };
  }

  getTermsAndConditions(): any {
    return {
      title: "Terms and Conditions",
      lastUpdated: "2025-01-01",
      content: {
        introduction: "These Terms and Conditions govern your use of the Power Up application and services. By using our app, you agree to these terms.",
        serviceDescription: {
          title: "Service Description",
          content: "Power Up is an AI-driven personal wellness coaching application that provides personalized recommendations, habit tracking, task management, and community features to help improve your overall wellness."
        },
        userResponsibilities: {
          title: "User Responsibilities",
          items: [
            "Provide accurate information",
            "Use the service lawfully and responsibly",
            "Maintain the security of your account",
            "Respect other users and community guidelines",
            "Not misuse or attempt to hack the service"
          ]
        },
        subscriptionTerms: {
          title: "Subscription and Payment",
          items: [
            "Premium features require a subscription",
            "Billing occurs automatically unless cancelled",
            "Refunds subject to our refund policy",
            "Prices may change with 30-day notice",
            "Free trial terms apply to first-time users"
          ]
        },
        limitation: {
          title: "Limitation of Liability",
          content: "Power Up is not a medical service. Always consult healthcare professionals for medical advice. We are not liable for any health-related decisions made based on app recommendations."
        },
        termination: {
          title: "Account Termination",
          content: "You may terminate your account at any time. We may suspend or terminate accounts that violate these terms or for legal compliance."
        },
        contact: "For legal questions, contact <NAME_EMAIL>"
      }
    };
  }

  getAboutUs(): any {
    return {
      title: "About Power Up",
      content: {
        mission: "Our mission is to empower individuals to achieve their wellness goals through personalized AI-driven coaching and community support.",
        story: "Power Up was founded by a team of wellness experts, AI researchers, and mobile developers who recognized the need for truly personalized wellness guidance.",
        team: [
          {
            name: "Dr. Sarah Johnson",
            role: "Chief Wellness Officer",
            bio: "Licensed psychologist with 15+ years in behavioral wellness"
          },
          {
            name: "Michael Chen",
            role: "Chief Technology Officer",
            bio: "AI researcher specializing in personalization algorithms"
          },
          {
            name: "Emma Davis",
            role: "Head of Product",
            bio: "Product manager with expertise in health and wellness apps"
          }
        ],
        values: [
          "Privacy-first approach to personal data",
          "Evidence-based wellness recommendations",
          "Inclusive and supportive community",
          "Continuous innovation and improvement"
        ],
        contact: {
          email: "<EMAIL>",
          address: "123 Wellness Street, Health City, HC 12345",
          phone: "+****************"
        }
      }
    };
  }
}
