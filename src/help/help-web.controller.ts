import { <PERSON>, Get, Res, Param } from '@nestjs/common';
import { ApiExcludeController } from '@nestjs/swagger';
import type { Response } from 'express';
import { HelpService } from './help.service';
import { HelpCategory } from './dto/search-help.dto';

@ApiExcludeController()
@Controller()
export class HelpWebController {
  constructor(private readonly helpService: HelpService) {}

  @Get('help/contact')
  async getContactPage(@Res() res: Response) {
    const articles = await this.helpService.searchHelpArticles({ category: HelpCategory.GETTING_STARTED, limit: 5 });
    
    res.render('contact', {
      title: 'Contact Us',
      description: 'Get in touch with our support team for help with Power Up',
      articles: articles.articles
    });
  }

  @Get('help/privacy')
  async getPrivacyPage(@Res() res: Response) {
    res.render('privacy', {
      title: 'Privacy Policy - Power Up',
      description: 'Your privacy is important to us',
      lastUpdated: 'January 1, 2024'
    });
  }

  @Get('help/terms')
  async getTermsPage(@Res() res: Response) {
    res.render('terms', {
      title: 'Terms and Conditions - Power Up',
      description: 'Please read these terms carefully before using our services',
      lastUpdated: 'January 1, 2024'
    });
  }

  @Get('help')
  async getHelpCenter(@Res() res: Response) {
    const articles = await this.helpService.searchHelpArticles({ limit: 10 });
    const categories = ['getting-started', 'account', 'features', 'billing', 'troubleshooting', 'privacy', 'technical'];
    
    res.render('help-center', {
      title: 'Help Center - Power Up',
      description: 'Find answers to your questions and get support',
      articles: articles.articles,
      categories: categories
    });
  }

  @Get('help/article/:id')
  async getArticlePage(@Param('id') id: string, @Res() res: Response) {
    try {
      const article = await this.helpService.getHelpArticle(id);
      
      res.render('article', {
        title: `${article.title} - Power Up Help`,
        description: article.content.substring(0, 150),
        article: article
      });
    } catch (error) {
      res.status(404).render('article', {
        title: 'Article Not Found - Power Up Help',
        description: 'The help article you\'re looking for could not be found.',
        error: {
          title: 'Article Not Found',
          message: 'The help article you\'re looking for could not be found.',
          backLink: '/help'
        }
      });
    }
  }
}