import { IsOptional, IsString, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum HelpCategory {
  GETTING_STARTED = 'getting-started',
  ACCOUNT = 'account',
  FEATURES = 'features',
  BILLING = 'billing',
  TROUBLESHOOTING = 'troubleshooting',
  PRIVACY = 'privacy',
  TECHNICAL = 'technical'
}

export class SearchHelpDto {
  @ApiProperty({
    description: 'Search query for help articles',
    example: 'how to reset password',
    required: false
  })
  @IsOptional()
  @IsString()
  query?: string;

  @ApiProperty({
    description: 'Category to filter help articles',
    enum: HelpCategory,
    example: HelpCategory.ACCOUNT,
    required: false
  })
  @IsOptional()
  @IsEnum(HelpCategory)
  category?: HelpCategory;

  @ApiProperty({
    description: 'Number of results to return',
    example: 10,
    required: false,
    default: 10
  })
  @IsOptional()
  limit?: number;

  @ApiProperty({
    description: 'Number of results to skip',
    example: 0,
    required: false,
    default: 0
  })
  @IsOptional()
  offset?: number;
}
