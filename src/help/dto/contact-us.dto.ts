import { Is<PERSON><PERSON>, <PERSON><PERSON>otE<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>S<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ContactUsDto {
  @ApiProperty({
    description: 'Full name of the person contacting',
    example: '<PERSON>'
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(2)
  @MaxLength(100)
  name: string;

  @ApiProperty({
    description: 'Email address for response',
    example: '<EMAIL>'
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Subject of the inquiry',
    example: 'Question about premium features'
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(5)
  @MaxLength(200)
  subject: string;

  @ApiProperty({
    description: 'Message content',
    example: 'I would like to know more about the premium features available in the app.'
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(10)
  @MaxLength(2000)
  message: string;

  @ApiProperty({
    description: 'User ID if logged in',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: false
  })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiProperty({
    description: 'Category of the inquiry',
    example: 'technical',
    enum: ['technical', 'billing', 'feature-request', 'bug-report', 'general'],
    required: false
  })
  @IsOptional()
  @IsString()
  category?: 'technical' | 'billing' | 'feature-request' | 'bug-report' | 'general';
}
