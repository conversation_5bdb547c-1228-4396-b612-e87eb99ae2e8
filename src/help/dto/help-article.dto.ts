import { IsString, IsO<PERSON>al, IsBoolean, IsArray, IsEnum, IsNumber, MinLength, MaxLength } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { HelpCategory } from './search-help.dto';

export class CreateHelpArticleDto {
  @ApiProperty({ description: 'Article title', example: 'Getting Started with Power Up' })
  @IsString()
  @MinLength(5)
  @MaxLength(200)
  title: string;

  @ApiProperty({ description: 'Article description', example: 'Learn how to get started with the app' })
  @IsString()
  @MinLength(10)
  @MaxLength(500)
  description: string;

  @ApiProperty({ description: 'Article content in HTML format' })
  @IsString()
  @MinLength(50)
  content: string;

  @ApiProperty({ description: 'Article category', enum: HelpCategory })
  @IsEnum(HelpCategory)
  category: HelpCategory;

  @ApiPropertyOptional({ description: 'Article tags', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ description: 'Whether the article is published', default: true })
  @IsOptional()
  @IsBoolean()
  published?: boolean;

  @ApiPropertyOptional({ description: 'Display order (lower numbers appear first)', default: 0 })
  @IsOptional()
  @IsNumber()
  sortOrder?: number;
}

export class UpdateHelpArticleDto {
  @ApiPropertyOptional({ description: 'Article title' })
  @IsOptional()
  @IsString()
  @MinLength(5)
  @MaxLength(200)
  title?: string;

  @ApiPropertyOptional({ description: 'Article description' })
  @IsOptional()
  @IsString()
  @MinLength(10)
  @MaxLength(500)
  description?: string;

  @ApiPropertyOptional({ description: 'Article content in HTML format' })
  @IsOptional()
  @IsString()
  @MinLength(50)
  content?: string;

  @ApiPropertyOptional({ description: 'Article category', enum: HelpCategory })
  @IsOptional()
  @IsEnum(HelpCategory)
  category?: HelpCategory;

  @ApiPropertyOptional({ description: 'Article tags', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ description: 'Whether the article is published' })
  @IsOptional()
  @IsBoolean()
  published?: boolean;

  @ApiPropertyOptional({ description: 'Display order (lower numbers appear first)' })
  @IsOptional()
  @IsNumber()
  sortOrder?: number;
}

export class HelpArticleResponseDto {
  @ApiProperty({ description: 'Article ID' })
  id: string;

  @ApiProperty({ description: 'Article title' })
  title: string;

  @ApiProperty({ description: 'Article description' })
  description: string;

  @ApiProperty({ description: 'Article content' })
  content: string;

  @ApiProperty({ description: 'Article category', enum: HelpCategory })
  category: HelpCategory;

  @ApiProperty({ description: 'Article tags', type: [String] })
  tags: string[];

  @ApiProperty({ description: 'Whether the article is published' })
  published: boolean;

  @ApiProperty({ description: 'Display order' })
  sortOrder: number;

  @ApiProperty({ description: 'View count' })
  viewCount: number;

  @ApiProperty({ description: 'Helpful count' })
  helpfulCount: number;

  @ApiProperty({ description: 'Not helpful count' })
  notHelpfulCount: number;

  @ApiProperty({ description: 'Author ID' })
  authorId: string;

  @ApiProperty({ description: 'Created date' })
  createdAt: Date;

  @ApiProperty({ description: 'Updated date' })
  updatedAt: Date;
}
