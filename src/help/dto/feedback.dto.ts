import { IsE<PERSON>, <PERSON>NotEmpty, <PERSON>Optional, IsString, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum FeedbackType {
  BUG = 'bug',
  FEATURE_REQUEST = 'feature-request',
  IMPROVEMENT = 'improvement',
  COMPLIMENT = 'compliment',
  COMPLAINT = 'complaint'
}

export enum FeedbackPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export class FeedbackDto {
  @ApiProperty({
    description: 'Type of feedback',
    enum: FeedbackType,
    example: FeedbackType.FEATURE_REQUEST
  })
  @IsEnum(FeedbackType)
  @IsNotEmpty()
  type: FeedbackType;

  @ApiProperty({
    description: 'Title of the feedback',
    example: 'Add dark mode support'
  })
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Detailed description of the feedback',
    example: 'It would be great to have a dark mode option for better usability during night time.'
  })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Priority level of the feedback',
    enum: FeedbackPriority,
    example: FeedbackPriority.MEDIUM,
    required: false
  })
  @IsOptional()
  @IsEnum(FeedbackPriority)
  priority?: FeedbackPriority;

  @ApiProperty({
    description: 'User email for follow-up',
    example: '<EMAIL>',
    required: false
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({
    description: 'User ID if authenticated',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: false
  })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiProperty({
    description: 'Device information for bug reports',
    example: 'iOS 17.0, iPhone 14 Pro',
    required: false
  })
  @IsOptional()
  @IsString()
  deviceInfo?: string;

  @ApiProperty({
    description: 'App version when feedback was submitted',
    example: '1.2.3',
    required: false
  })
  @IsOptional()
  @IsString()
  appVersion?: string;
}
