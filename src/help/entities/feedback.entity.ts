import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('feedback')
export class Feedback {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ 
    type: 'enum', 
    enum: ['bug', 'feature-request', 'improvement', 'compliment', 'complaint']
  })
  type: string;

  @Column({ length: 200 })
  title: string;

  @Column('text')
  description: string;

  @Column({ 
    type: 'enum', 
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  })
  priority: string;

  @Column({ nullable: true })
  email?: string;

  @Column({ name: 'user_id', nullable: true })
  userId?: string;

  @Column({ name: 'device_info', nullable: true })
  deviceInfo?: string;

  @Column({ name: 'app_version', nullable: true })
  appVersion?: string;

  @Column({ 
    type: 'enum', 
    enum: ['pending', 'reviewing', 'in-progress', 'completed', 'rejected'],
    default: 'pending'
  })
  status: string;

  @Column({ name: 'assigned_to', nullable: true })
  assignedTo?: string;

  @Column({ name: 'admin_notes', type: 'text', nullable: true })
  adminNotes?: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'completed_at', nullable: true })
  completedAt?: Date;
}
