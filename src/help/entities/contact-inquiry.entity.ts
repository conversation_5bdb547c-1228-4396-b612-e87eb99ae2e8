import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('contact_inquiries')
export class ContactInquiry {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 100 })
  name: string;

  @Column({ length: 255 })
  email: string;

  @Column({ length: 200 })
  subject: string;

  @Column('text')
  message: string;

  @Column({ name: 'user_id', nullable: true })
  userId?: string;

  @Column({ 
    type: 'enum', 
    enum: ['technical', 'billing', 'feature-request', 'bug-report', 'general'],
    default: 'general'
  })
  category: string;

  @Column({ 
    type: 'enum', 
    enum: ['pending', 'in-progress', 'resolved', 'closed'],
    default: 'pending'
  })
  status: string;

  @Column({ name: 'assigned_to', nullable: true })
  assignedTo?: string;

  @Column({ name: 'response_sent', default: false })
  responseSent: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'resolved_at', nullable: true })
  resolvedAt?: Date;
}
