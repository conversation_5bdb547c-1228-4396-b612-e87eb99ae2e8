import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('help_articles')
export class HelpArticle {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 200 })
  title: string;

  @Column({ length: 500 })
  description: string;

  @Column('text')
  content: string;

  @Column({ 
    type: 'enum', 
    enum: ['getting-started', 'account', 'features', 'billing', 'troubleshooting', 'privacy', 'technical']
  })
  category: string;

  @Column('simple-array', { nullable: true })
  tags?: string[];

  @Column({ default: true })
  published: boolean;

  @Column({ name: 'sort_order', default: 0 })
  sortOrder: number;

  @Column({ name: 'view_count', default: 0 })
  viewCount: number;

  @Column({ name: 'helpful_count', default: 0 })
  helpfulCount: number;

  @Column({ name: 'not_helpful_count', default: 0 })
  notHelpfulCount: number;

  @Column({ name: 'author_id' })
  authorId: string;

  @Column({ name: 'last_reviewed_at', nullable: true })
  lastReviewedAt?: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
