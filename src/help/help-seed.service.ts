import { Injectable, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { HelpArticle } from './entities';

@Injectable()
export class HelpSeedService implements OnModuleInit {
  constructor(
    @InjectRepository(HelpArticle)
    private helpArticleRepository: Repository<HelpArticle>,
  ) {}

  async onModuleInit() {
    await this.seedHelpArticles();
  }

  private async seedHelpArticles() {
    const existingArticles = await this.helpArticleRepository.count();
    if (existingArticles > 0) {
      return; // Already seeded
    }

    const articles = [
      // Getting Started
      {
        title: 'Getting Started with Power Up',
        description: 'Learn the basics of using Power Up for your wellness journey',
        content: `
          <h2>Welcome to Power Up!</h2>
          <p>Power Up is your AI-driven personal wellness coach designed to help you achieve your health and wellness goals through personalized guidance, habit tracking, and community support.</p>
          
          <h3>First Steps</h3>
          <ol>
            <li><strong>Create Your Profile:</strong> Set up your personal information and wellness goals</li>
            <li><strong>Complete the Assessment:</strong> Answer questions about your current habits and lifestyle</li>
            <li><strong>Set Your Goals:</strong> Define what you want to achieve with Power Up</li>
            <li><strong>Start Your First Habit:</strong> Begin with one simple habit to build momentum</li>
          </ol>
          
          <h3>Key Features to Explore</h3>
          <ul>
            <li>Skill development plans</li>
          </ul>
          
          <p>Take your time exploring each feature. Your AI coach will adapt to your preferences and provide increasingly personalized recommendations as you use the app.</p>
        `,
        category: 'getting-started',
        tags: ['onboarding', 'basics', 'new-user', 'faq'],
        authorId: 'system',
        sortOrder: 1,
      },

      {
        title: 'How to Set Up Your First Habit',
        description: 'Step-by-step guide to creating and tracking your first habit in Power Up',
        content: `
          <h2>Creating Your First Habit</h2>
          <p>Habits are the foundation of lasting change. Here's how to set up your first habit for success:</p>
          
          <h3>Step 1: Choose a Simple Habit</h3>
          <p>Start small! Examples of good first habits:</p>
          <ul>
            <li>Drink one glass of water upon waking</li>
            <li>Take 5 deep breaths before bed</li>
            <li>Write down 3 things you're grateful for</li>
            <li>Do 10 push-ups or squats</li>
          </ul>
          
          <h3>Step 2: Set Your Trigger</h3>
          <p>Link your new habit to an existing routine. This helps with consistency.</p>
          
          <h3>Step 3: Track Daily</h3>
          <p>Use the habit tracker to mark completion each day. Visual progress is motivating!</p>
          
          <h3>Step 4: Be Patient</h3>
          <p>It takes an average of 66 days to form a new habit. Don't give up if you miss a day - just get back on track.</p>
        `,
        category: 'getting-started',
        tags: ['habits', 'tracking', 'beginners', 'faq'],
        authorId: 'system',
        sortOrder: 2,
      },

      // Account Management
      {
        title: 'How to Reset Your Password',
        description: 'Steps to reset your password if you forget it',
        content: `
          <h2>Resetting Your Password</h2>
          <p>If you've forgotten your password, don't worry! Here's how to reset it:</p>
          
          <h3>From the Login Screen</h3>
          <ol>
            <li>On the login screen, tap "Forgot Password?"</li>
            <li>Enter the email address associated with your account</li>
            <li>Check your email for a reset link</li>
            <li>Follow the link and create a new password</li>
            <li>Return to the app and log in with your new password</li>
          </ol>
          
          <h3>Troubleshooting</h3>
          <ul>
            <li><strong>Don't see the email?</strong> Check your spam folder</li>
            <li><strong>Email not arriving?</strong> Make sure you entered the correct email address</li>
            <li><strong>Still having issues?</strong> Contact our support team</li>
          </ul>
        `,
        category: 'account',
        tags: ['password', 'login', 'account', 'troubleshooting'],
        authorId: 'system',
        sortOrder: 10,
      },

      {
        title: 'Updating Your Profile Information',
        description: 'How to edit your personal information and preferences',
        content: `
          <h2>Managing Your Profile</h2>
          <p>Keep your profile up to date to get the most personalized experience:</p>
          
          <h3>Basic Information</h3>
          <ol>
            <li>Go to Settings > Profile</li>
            <li>Tap on the field you want to edit</li>
            <li>Make your changes</li>
            <li>Tap "Save" to confirm</li>
          </ol>
          
          <h3>Wellness Goals</h3>
          <p>Your goals can evolve over time. Update them by:</p>
          <ol>
            <li>Going to Settings > Wellness Goals</li>
            <li>Reviewing your current goals</li>
            <li>Adding new goals or modifying existing ones</li>
            <li>Setting target dates and priorities</li>
          </ol>
          
          <h3>Privacy Settings</h3>
          <p>Control what information is shared and visible to other users in the community features.</p>
        `,
        category: 'account',
        tags: ['profile', 'settings', 'goals', 'privacy'],
        authorId: 'system',
        sortOrder: 11,
      },

      // Features and Functionality
      {
        title: 'Understanding Your Daily Podcast',
        description: 'How your personalized daily podcast is created and how to get the most from it',
        content: `
          <h2>Your Personalized Daily Podcast</h2>
          <p>Every day, Power Up's AI creates a unique podcast tailored specifically to your journey, goals, and current challenges.</p>
          
          <h3>How It Works</h3>
          <p>Your podcast is generated based on:</p>
          <ul>
            <li>Your current wellness goals and progress</li>
            <li>Recent habit tracking data</li>
            <li>Challenges you're facing</li>
            <li>Your learning preferences</li>
            <li>Seasonal and trending wellness topics</li>
          </ul>
          
          <h3>Podcast Sections</h3>
          <ul>
            <li><strong>Morning Motivation:</strong> Energizing content to start your day</li>
            <li><strong>Focus Topic:</strong> Deep dive into a relevant wellness concept</li>
            <li><strong>Practical Tips:</strong> Actionable advice you can implement today</li>
            <li><strong>Reflection:</strong> Questions to help you think about your journey</li>
          </ul>
          
          <h3>Getting More Value</h3>
          <ul>
            <li>Listen actively and take notes</li>
            <li>Rate episodes to improve future recommendations</li>
            <li>Share insights with the community</li>
            <li>Revisit favorite episodes in your library</li>
          </ul>
        `,
        category: 'features',
        tags: ['podcast', 'ai', 'personalization', 'daily-content'],
        authorId: 'system',
        sortOrder: 20,
      },

      {
        title: 'Habit Tracking Best Practices',
        description: 'Tips and strategies for effective habit tracking',
        content: `
          <h2>Mastering Habit Tracking</h2>
          <p>Effective habit tracking is key to building lasting change. Here are our best practices:</p>
          
          <h3>Setting Up for Success</h3>
          <ul>
            <li><strong>Start Small:</strong> Begin with 1-3 habits maximum</li>
            <li><strong>Be Specific:</strong> "Exercise 20 minutes" not just "exercise"</li>
            <li><strong>Set Realistic Goals:</strong> Build consistency before intensity</li>
            <li><strong>Choose Your Timing:</strong> Link habits to existing routines</li>
          </ul>
          
          <h3>Daily Tracking Tips</h3>
          <ul>
            <li>Track immediately after completing the habit</li>
            <li>Don't stress about perfect streaks</li>
            <li>Focus on getting back on track quickly after a miss</li>
            <li>Celebrate small wins and milestones</li>
          </ul>
          
          <h3>Using the Analytics</h3>
          <p>Review your habit data weekly to:</p>
          <ul>
            <li>Identify patterns and trends</li>
            <li>Understand what triggers success or setbacks</li>
            <li>Adjust your approach based on what works</li>
            <li>Set new challenges and goals</li>
          </ul>
        `,
        category: 'features',
        tags: ['habits', 'tracking', 'analytics', 'best-practices'],
        authorId: 'system',
        sortOrder: 21,
      },

      // Troubleshooting
      {
        title: 'App Not Loading or Crashing',
        description: 'Solutions for common app performance issues',
        content: `
          <h2>Troubleshooting App Issues</h2>
          <p>If you're experiencing app crashes or loading problems, try these solutions:</p>
          
          <h3>Quick Fixes</h3>
          <ol>
            <li><strong>Restart the App:</strong> Close completely and reopen</li>
            <li><strong>Check Your Connection:</strong> Ensure you have stable internet</li>
            <li><strong>Update the App:</strong> Make sure you have the latest version</li>
            <li><strong>Restart Your Device:</strong> A simple reboot often helps</li>
          </ol>
          
          <h3>Still Having Issues?</h3>
          <ol>
            <li><strong>Clear App Cache:</strong> Go to device settings and clear Power Up's cache</li>
            <li><strong>Free Up Storage:</strong> Ensure you have at least 1GB free space</li>
            <li><strong>Check for OS Updates:</strong> Update your device's operating system</li>
            <li><strong>Reinstall the App:</strong> Uninstall and reinstall (your data will be saved)</li>
          </ol>
          
          <h3>Contact Support</h3>
          <p>If problems persist, contact our support team with:</p>
          <ul>
            <li>Your device model and OS version</li>
            <li>App version number</li>
            <li>Description of when the issue occurs</li>
            <li>Any error messages you see</li>
          </ul>
        `,
        category: 'troubleshooting',
        tags: ['app-crash', 'loading', 'performance', 'technical'],
        authorId: 'system',
        sortOrder: 30,
      },

      {
        title: 'Sync Issues Across Devices',
        description: 'How to resolve data synchronization problems',
        content: `
          <h2>Fixing Sync Issues</h2>
          <p>Your data should sync automatically across all your devices. If it's not working:</p>
          
          <h3>Check Your Account</h3>
          <ol>
            <li>Make sure you're logged into the same account on all devices</li>
            <li>Verify your email address is confirmed</li>
            <li>Check that sync is enabled in Settings</li>
          </ol>
          
          <h3>Force a Sync</h3>
          <ol>
            <li>Go to Settings > Account</li>
            <li>Tap "Sync Now" or pull down to refresh</li>
            <li>Wait a few minutes for sync to complete</li>
            <li>Check if your data appears on other devices</li>
          </ol>
          
          <h3>Common Solutions</h3>
          <ul>
            <li><strong>Poor Connection:</strong> Ensure stable internet on all devices</li>
            <li><strong>Outdated App:</strong> Update to the latest version on all devices</li>
            <li><strong>Storage Full:</strong> Free up space on devices with sync issues</li>
            <li><strong>Multiple Accounts:</strong> Make sure you're not accidentally using different accounts</li>
          </ul>
        `,
        category: 'troubleshooting',
        tags: ['sync', 'devices', 'data', 'account'],
        authorId: 'system',
        sortOrder: 31,
      },

      // Community
      {
        title: 'Joining Community Challenges',
        description: 'How to participate in community challenges and events',
        content: `
          <h2>Community Challenges</h2>
          <p>Join challenges to stay motivated and connect with others on similar wellness journeys!</p>
          
          <h3>Finding Challenges</h3>
          <ul>
            <li>Browse the Community tab for active challenges</li>
            <li>Filter by your interests and goals</li>
            <li>Check difficulty levels to find your match</li>
            <li>Read descriptions and requirements carefully</li>
          </ul>
          
          <h3>Participating</h3>
          <ol>
            <li>Tap "Join Challenge" on any challenge that interests you</li>
            <li>Read the rules and commitment required</li>
            <li>Track your progress daily</li>
            <li>Share updates and encourage others</li>
            <li>Celebrate milestones with the community</li>
          </ol>
          
          <h3>Challenge Types</h3>
          <ul>
            <li><strong>Habit Challenges:</strong> Build specific habits together</li>
            <li><strong>Goal Challenges:</strong> Work towards common objectives</li>
            <li><strong>Skill Challenges:</strong> Learn new wellness skills</li>
            <li><strong>Seasonal Challenges:</strong> Time-limited special events</li>
          </ul>
        `,
        category: 'features',
        tags: ['challenges', 'community', 'motivation', 'social'],
        authorId: 'system',
        sortOrder: 40,
      },

      // Privacy and Security
      {
        title: 'Your Privacy and Data Security',
        description: 'Understanding how your data is protected and used',
        content: `
          <h2>Privacy and Security</h2>
          <p>Your privacy and data security are our top priorities. Here's how we protect your information:</p>
          
          <h3>Data Encryption</h3>
          <ul>
            <li>All data is encrypted in transit and at rest</li>
            <li>We use industry-standard security protocols</li>
            <li>Your personal information is never stored in plain text</li>
          </ul>
          
          <h3>What We Collect</h3>
          <ul>
            <li>Profile information you provide</li>
            <li>Habit tracking and wellness data</li>
            <li>App usage patterns (anonymized)</li>
            <li>Device information for technical support</li>
          </ul>
          
          <h3>How We Use Your Data</h3>
          <ul>
            <li>Personalizing your wellness experience</li>
            <li>Generating your daily podcasts</li>
            <li>Improving our AI recommendations</li>
            <li>Providing customer support</li>
          </ul>
          
          <h3>Your Control</h3>
          <ul>
            <li>Export your data at any time</li>
            <li>Delete your account and all data</li>
            <li>Control what's shared in community features</li>
            <li>Opt out of non-essential data collection</li>
          </ul>
        `,
        category: 'privacy',
        tags: ['privacy', 'security', 'data', 'protection'],
        authorId: 'system',
        sortOrder: 50,
      },
    ];

    for (const articleData of articles) {
      const article = this.helpArticleRepository.create(articleData);
      await this.helpArticleRepository.save(article);
    }

    console.log(`Seeded ${articles.length} help articles`);
  }
}
