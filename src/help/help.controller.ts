import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Param, 
  Query, 
  Patch,
  ParseUUIDPipe,
  ParseBoolPipe
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiQuery, 
  ApiParam,
  ApiBody
} from '@nestjs/swagger';
import { HelpService } from './help.service';
import { ContactUsDto, FeedbackDto, SearchHelpDto, HelpCategory } from './dto';

@ApiTags('help')
@Controller('api/help')
export class HelpController {
  constructor(private readonly helpService: HelpService) {}

  // Contact Us Endpoints
  @Post('contact')
  @ApiOperation({ summary: 'Submit a contact inquiry' })
  @ApiResponse({ status: 201, description: 'Contact inquiry submitted successfully' })
  @ApiBody({ type: ContactUsDto })
  async submitContactInquiry(@Body() contactUsDto: ContactUsDto) {
    const inquiry = await this.helpService.submitContactInquiry(contactUsDto);
    return {
      message: 'Your inquiry has been submitted successfully. We will get back to you soon.',
      inquiryId: inquiry.id
    };
  }

  @Get('contacts')
  @ApiOperation({ summary: 'Get contact inquiries (Admin only)' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by status' })
  async getContactInquiries(@Query('status') status?: string) {
    return await this.helpService.getContactInquiries(status);
  }

  @Get('contact/:id')
  @ApiOperation({ summary: 'Get a specific contact inquiry (Admin only)' })
  @ApiParam({ name: 'id', description: 'Contact inquiry ID' })
  async getContactInquiry(@Param('id', ParseUUIDPipe) id: string) {
    return await this.helpService.getContactInquiry(id);
  }

  @Patch('contact/:id/status')
  @ApiOperation({ summary: 'Update contact inquiry status (Admin only)' })
  @ApiParam({ name: 'id', description: 'Contact inquiry ID' })
  async updateContactInquiryStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateData: { status: string; assignedTo?: string }
  ) {
    return await this.helpService.updateContactInquiryStatus(
      id, 
      updateData.status, 
      updateData.assignedTo
    );
  }

  // Feedback Endpoints
  @Post('feedback')
  @ApiOperation({ summary: 'Submit feedback' })
  @ApiResponse({ status: 201, description: 'Feedback submitted successfully' })
  @ApiBody({ type: FeedbackDto })
  async submitFeedback(@Body() feedbackDto: FeedbackDto) {
    const feedback = await this.helpService.submitFeedback(feedbackDto);
    return {
      message: 'Thank you for your feedback! We appreciate your input.',
      feedbackId: feedback.id
    };
  }

  @Get('feedback')
  @ApiOperation({ summary: 'Get feedback submissions (Admin only)' })
  @ApiQuery({ name: 'type', required: false, description: 'Filter by feedback type' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by status' })
  async getFeedback(
    @Query('type') type?: string,
    @Query('status') status?: string
  ) {
    return await this.helpService.getFeedback(type, status);
  }

  @Get('feedback/:id')
  @ApiOperation({ summary: 'Get specific feedback (Admin only)' })
  @ApiParam({ name: 'id', description: 'Feedback ID' })
  async getFeedbackById(@Param('id', ParseUUIDPipe) id: string) {
    return await this.helpService.getFeedbackById(id);
  }

  @Patch('feedback/:id/status')
  @ApiOperation({ summary: 'Update feedback status (Admin only)' })
  @ApiParam({ name: 'id', description: 'Feedback ID' })
  async updateFeedbackStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateData: { status: string; adminNotes?: string }
  ) {
    return await this.helpService.updateFeedbackStatus(
      id,
      updateData.status,
      updateData.adminNotes
    );
  }

  // Help Articles Endpoints
  @Get('articles/search')
  @ApiOperation({ summary: 'Search help articles' })
  @ApiQuery({ name: 'query', required: false, description: 'Search query' })
  @ApiQuery({ name: 'category', required: false, enum: HelpCategory, description: 'Filter by category' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of results' })
  @ApiQuery({ name: 'offset', required: false, description: 'Results offset' })
  async searchHelpArticles(@Query() searchDto: SearchHelpDto) {
    return await this.helpService.searchHelpArticles(searchDto);
  }

  @Get('articles/popular')
  @ApiOperation({ summary: 'Get popular help articles' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of articles to return' })
  async getPopularArticles(@Query('limit') limit?: number) {
    return await this.helpService.getPopularArticles(limit ? parseInt(limit.toString()) : 10);
  }

  @Get('articles/faq')
  @ApiOperation({ summary: 'Get frequently asked questions' })
  async getFAQs() {
    return await this.helpService.getFAQs();
  }

  @Get('articles/category/:category')
  @ApiOperation({ summary: 'Get help articles by category' })
  @ApiParam({ name: 'category', enum: HelpCategory, description: 'Article category' })
  async getHelpArticlesByCategory(@Param('category') category: HelpCategory) {
    return await this.helpService.getHelpArticlesByCategory(category);
  }

  @Get('articles/:id')
  @ApiOperation({ summary: 'Get a specific help article' })
  @ApiParam({ name: 'id', description: 'Article ID' })
  async getHelpArticle(@Param('id', ParseUUIDPipe) id: string) {
    return await this.helpService.getHelpArticle(id);
  }

  @Post('articles/:id/helpful')
  @ApiOperation({ summary: 'Mark article as helpful or not helpful' })
  @ApiParam({ name: 'id', description: 'Article ID' })
  @ApiBody({ schema: { properties: { helpful: { type: 'boolean' } } } })
  async markArticleHelpful(
    @Param('id', ParseUUIDPipe) id: string,
    @Body('helpful', ParseBoolPipe) helpful: boolean
  ) {
    await this.helpService.markArticleHelpful(id, helpful);
    return { message: 'Thank you for your feedback!' };
  }

  // Static Content API Endpoints
  @Get('privacy-policy')
  @ApiOperation({ summary: 'Get privacy policy content' })
  async getPrivacyPolicy() {
    return this.helpService.getPrivacyPolicy();
  }

  @Get('terms-and-conditions')
  @ApiOperation({ summary: 'Get terms and conditions content' })
  async getTermsAndConditions() {
    return this.helpService.getTermsAndConditions();
  }

  @Get('about-us')
  @ApiOperation({ summary: 'Get about us information' })
  async getAboutUs() {
    return this.helpService.getAboutUs();
  }
}
