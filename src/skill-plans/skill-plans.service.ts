import { Injectable, NotFoundException, BadRequestException, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SkillPlan } from './entities/skill-plan.entity';
import { SkillPlanStep } from './entities/skill-plan-step.entity';
import { User } from '../users/entities/user.entity';
import { NotificationsService } from '../notifications/notifications.service';
import { AnalyticsService } from '../analytics/analytics.service';
import { NotificationType } from '../notifications/types/notification-types';

@Injectable()
export class SkillPlansService {
  constructor(
    @InjectRepository(SkillPlan)
    private skillPlansRepository: Repository<SkillPlan>,
    @InjectRepository(SkillPlanStep)
    private skillPlanStepsRepository: Repository<SkillPlanStep>,
    private notificationsService: NotificationsService,
    @Inject(forwardRef(() => AnalyticsService))
    private analyticsService: AnalyticsService,
  ) {}

  async create(createSkillPlanDto: {
    name: string;
    description: string;
    isPublic: boolean;
    metadata: {
      category: string;
      difficulty: 'beginner' | 'intermediate' | 'advanced';
      estimatedDuration: string;
      tags: string[];
    };
    steps: {
      title: string;
      description: string;
      order: number;
      resources?: {
        type: string;
        url: string;
        title: string;
      }[];
      tasks?: {
        description: string;
        isCompleted: boolean;
      }[];
    }[];
    creator: User;
  }): Promise<SkillPlan> {
    const skillPlan = this.skillPlansRepository.create({
      name: createSkillPlanDto.name,
      description: createSkillPlanDto.description,
      isPublic: createSkillPlanDto.isPublic,
      metadata: createSkillPlanDto.metadata,
      creator: createSkillPlanDto.creator,
    });

    await this.skillPlansRepository.save(skillPlan);

    // Create and associate steps
    const steps = createSkillPlanDto.steps.map(step => {
      const skillPlanStep = this.skillPlanStepsRepository.create({
        ...step,
        skillPlan,
      });
      return skillPlanStep;
    });

    skillPlan.steps = await this.skillPlanStepsRepository.save(steps);
    return skillPlan;
  }

  async findAll(options: { isPublic?: boolean; creatorId?: string } = {}): Promise<SkillPlan[]> {
    const where: any = {};
    if (typeof options.isPublic === 'boolean') {
      where.isPublic = options.isPublic;
    }
    if (options.creatorId) {
      where.creator = { id: options.creatorId };
    }

    return this.skillPlansRepository.find({
      where,
      relations: ['creator', 'steps'],
      order: {
        createdAt: 'DESC',
        steps: {
          order: 'ASC',
        },
      },
    });
  }

  async findOne(id: string): Promise<SkillPlan> {
    const skillPlan = await this.skillPlansRepository.findOne({
      where: { id },
      relations: ['creator', 'steps'],
    });

    if (!skillPlan) {
      throw new NotFoundException('Skill plan not found');
    }

    return skillPlan;
  }

  async update(id: string, userId: string, updateSkillPlanDto: Partial<SkillPlan>): Promise<SkillPlan> {
    const skillPlan = await this.findOne(id);

    if (skillPlan.creator.id !== userId) {
      throw new BadRequestException('Only the creator can update the skill plan');
    }

    Object.assign(skillPlan, updateSkillPlanDto);
    return this.skillPlansRepository.save(skillPlan);
  }

  async updateStep(
    planId: string,
    stepId: string,
    userId: string,
    updateStepDto: Partial<SkillPlanStep>,
  ): Promise<SkillPlanStep> {
    const skillPlan = await this.findOne(planId);

    if (skillPlan.creator.id !== userId) {
      throw new BadRequestException('Only the creator can update the skill plan steps');
    }

    const step = skillPlan.steps.find(s => s.id === stepId);
    if (!step) {
      throw new NotFoundException('Step not found');
    }

    Object.assign(step, updateStepDto);
    return this.skillPlanStepsRepository.save(step);
  }

  async markStepTaskComplete(
    planId: string,
    stepId: string,
    taskIndex: number,
    userId: string,
  ): Promise<SkillPlanStep> {
    const skillPlan = await this.findOne(planId);
    const step = skillPlan.steps.find(s => s.id === stepId);
    
    if (!step) {
      throw new NotFoundException('Step not found');
    }

    if (!step.tasks || !step.tasks[taskIndex]) {
      throw new BadRequestException('Task not found');
    }

    step.tasks[taskIndex].isCompleted = true;
    return this.skillPlanStepsRepository.save(step);
  }

  /**
   * Update the progress of a skill plan
   * 
   * @param id Skill plan ID (UUID)
   * @param userId User ID who is updating the progress (must be creator)
   * @param stepId Step order number to mark as completed/incomplete
   * @param completed Whether the step is completed
   * @returns Updated skill plan with progress information
   */
  async updateProgress(
    id: string,
    userId: string,
    stepId: number,
    completed: boolean
  ): Promise<any> {
    // Early validation to prevent database errors with invalid UUIDs
    if (!id) {
      throw new BadRequestException('Skill plan ID is required');
    }
    
    try {
      const skillPlan = await this.skillPlansRepository.findOne({
        where: { id, creator: { id: userId } },
        relations: ['steps', 'creator'],
      });

      if (!skillPlan) {
        throw new NotFoundException('Skill plan not found');
      }

    const step = skillPlan.steps.find(s => s.order === stepId);
    if (!step) {
      throw new NotFoundException('Step not found');
    }

    step.isCompleted = completed;
    step.completedAt = completed ? new Date() : undefined;

    if (completed) {
      // Calculate progress
      const completedSteps = skillPlan.steps.filter(s => s.isCompleted).length;
      skillPlan.progress = Math.round((completedSteps / skillPlan.steps.length) * 100);

      // Send step completion notification
      await this.notificationsService.sendNotificationWithPreferences(
        userId,
        NotificationType.PLAN_PROGRESS,
        {
          title: 'Step Completed!',
          body: `You've completed "${step.title}" in ${skillPlan.name}`,
          type: NotificationType.PLAN_PROGRESS,
          data: {
            skillPlanId: id,
            stepId: step.id,
            progress: String(skillPlan.progress)
          }
        }
      );

      // Update analytics and award XP
      await this.analyticsService.updateUserProgress(userId, {
        skillPlans: {
          active: 1,
          completedSteps,
          totalSteps: skillPlan.steps.length
        },
        xpGained: 50 // Award XP for completing a step
      });

      // Handle plan completion
      if (skillPlan.progress === 100) {
        skillPlan.completedAt = new Date();
        
        // Send plan completion notification
        await this.notificationsService.sendNotificationWithPreferences(
          userId,
          NotificationType.PLAN_COMPLETE,
          {
            title: 'Skill Plan Completed! 🎉',
            body: `Congratulations! You've completed "${skillPlan.name}"`,
            type: NotificationType.PLAN_COMPLETE,
            data: {
              skillPlanId: id,
              category: skillPlan.metadata.category,
              difficulty: skillPlan.metadata.difficulty
            }
          }
        );

        // Award bonus XP and badge for plan completion
        await this.analyticsService.updateUserProgress(userId, {
          xpGained: 500, // Bonus XP for completing the full plan
          badgesEarned: [`${skillPlan.metadata.category}_${skillPlan.metadata.difficulty}`]
        });
      }
    }

    await this.skillPlanStepsRepository.save(step);
    await this.skillPlansRepository.save(skillPlan);
    
    return skillPlan;
    } catch (error) {
      if (error instanceof BadRequestException || error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Error updating skill plan progress: ${error.message}`);
    }
  }

  async remove(id: string, userId: string): Promise<{ message: string }> {
    const skillPlan = await this.findOne(id);

    if (skillPlan.creator.id !== userId) {
      throw new BadRequestException('Only the creator can delete the skill plan');
    }

    await this.skillPlansRepository.remove(skillPlan);
    return { message: 'Skill plan deleted successfully' };
  }
}
