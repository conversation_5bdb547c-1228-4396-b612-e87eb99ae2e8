import { <PERSON><PERSON><PERSON>, PrimaryGenerated<PERSON><PERSON>umn, Column, ManyTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON>n, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { SkillPlan } from './skill-plan.entity';

@Entity('skill_plan_steps')
export class SkillPlanStep {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column()
  order: number;

  @Column({ type: 'jsonb', nullable: true })
  resources: {
    type: string;
    url: string;
    title: string;
  }[];

  @Column({ type: 'jsonb', nullable: true })
  tasks: {
    description: string;
    isCompleted: boolean;
  }[];

  @Column({ default: false })
  isCompleted: boolean;

  @Column({ type: 'timestamp', nullable: true })
  completedAt?: Date;

  @ManyToOne(() => SkillPlan, skillPlan => skillPlan.steps, { onDelete: 'CASCADE' })
  @Join<PERSON>olumn({ name: 'skillPlanId' })
  skillPlan: SkillPlan;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
