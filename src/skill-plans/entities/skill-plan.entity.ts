import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { SkillPlanStep } from './skill-plan-step.entity';

@Entity('skill_plans')
export class SkillPlan {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ default: false })
  isPublic: boolean;

  @Column({ type: 'jsonb', default: {} })
  metadata: {
    category: string;
    difficulty: 'beginner' | 'intermediate' | 'advanced';
    estimatedDuration: string;
    tags: string[];
  };

  @Column({ type: 'int', default: 0 })
  progress: number;

  @ManyToOne(() => User)
  creator: User;

  @OneToMany(() => SkillPlanStep, step => step.skillPlan, { cascade: true })
  steps: SkillPlanStep[];

  @Column({ type: 'timestamp', nullable: true })
  completedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
