import { Test, TestingModule } from '@nestjs/testing';
import { SkillPlansService } from './skill-plans.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SkillPlan } from './entities/skill-plan.entity';
import { SkillPlanStep } from './entities/skill-plan-step.entity';
import { NotFoundException } from '@nestjs/common';
import { User } from '../users/entities/user.entity';
import { NotificationsService } from '../notifications/notifications.service';
import { AnalyticsService } from '../analytics/analytics.service';

describe('SkillPlansService', () => {
  let service: SkillPlansService;
  let planRepository: Repository<SkillPlan>;
  let stepRepository: Repository<SkillPlanStep>;
  let notificationsService: NotificationsService;
  let analyticsService: AnalyticsService;

  // Mock user for testing
  const mockUser: User = {
    id: '1',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    password: 'hashedPassword',
    xp: 0,
    badges: [],
    firebaseUid: undefined,
    provider: 'local',
    picture: undefined,
    habits: [],
    tasks: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: undefined,
  } as User;

  const mockPlanRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    delete: jest.fn(),
    remove: jest.fn(),
  };

  const mockStepRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
  };

  const mockNotificationsService = {
    sendNotificationWithPreferences: jest.fn(),
    sendNotification: jest.fn(),
    createForUser: jest.fn(),
  };

  const mockAnalyticsService = {
    trackEvent: jest.fn(),
    createEvent: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SkillPlansService,
        {
          provide: getRepositoryToken(SkillPlan),
          useValue: mockPlanRepository,
        },
        {
          provide: getRepositoryToken(SkillPlanStep),
          useValue: mockStepRepository,
        },
        {
          provide: NotificationsService,
          useValue: mockNotificationsService,
        },
        {
          provide: AnalyticsService,
          useValue: mockAnalyticsService,
        },
      ],
    }).compile();

    service = module.get<SkillPlansService>(SkillPlansService);
    planRepository = module.get<Repository<SkillPlan>>(getRepositoryToken(SkillPlan));
    stepRepository = module.get<Repository<SkillPlanStep>>(getRepositoryToken(SkillPlanStep));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new skill plan with steps', async () => {
      const createSkillPlanDto = {
        name: 'Learn TypeScript',
        description: 'Comprehensive TypeScript learning plan',
        isPublic: true,
        metadata: {
          category: 'Programming',
          difficulty: 'intermediate' as 'intermediate' | 'beginner' | 'advanced',
          estimatedDuration: '4 weeks',
          tags: ['typescript', 'programming'],
        },
        steps: [
          {
            title: 'TypeScript Basics',
            description: 'Learn basic TypeScript concepts',
            order: 1,
            resources: [
              {
                type: 'video',
                url: 'https://example.com/typescript-basics',
                title: 'TypeScript Basics Tutorial',
              },
            ],
            tasks: [
              {
                description: 'Complete basic exercises',
                isCompleted: false,
              },
            ],
          },
        ],
        creator: mockUser,
      };

      const createdPlan = {
        id: '1',
        ...createSkillPlanDto,
        createdAt: new Date(),
        updatedAt: new Date(),
        steps: [],
      };

      const createdStep = {
        id: '1',
        ...createSkillPlanDto.steps[0],
        skillPlan: createdPlan,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPlanRepository.create.mockReturnValue(createdPlan);
      mockPlanRepository.save.mockResolvedValue(createdPlan);
      
      mockStepRepository.create.mockReturnValue(createdStep);
      mockStepRepository.save.mockResolvedValue([createdStep]);

      const result = await service.create(createSkillPlanDto);

      expect(result).toBeDefined();
      expect(result.id).toBe(createdPlan.id);
      expect(result.name).toBe(createSkillPlanDto.name);
      expect(result.steps).toBeDefined();
      expect(result.steps).toHaveLength(1);
      expect(result.steps[0].title).toBe(createSkillPlanDto.steps[0].title);
    });
  });

  describe('findAll', () => {
    it('should return all public skill plans', async () => {
      const plans = [
        {
          id: '1',
          name: 'Learn TypeScript',
          isPublic: true,
          creator: { id: '1' },
        },
        {
          id: '2',
          name: 'Master React',
          isPublic: true,
          creator: { id: '2' },
        },
      ];

      mockPlanRepository.find.mockResolvedValue(plans);

      const result = await service.findAll({ isPublic: true });

      expect(result).toBeDefined();
      expect(result).toHaveLength(2);
      expect(result[0].name).toBe('Learn TypeScript');
    });

    it('should return user\'s skill plans', async () => {
      const creatorId = '1';
      const plans = [
        {
          id: '1',
          name: 'Learn TypeScript',
          isPublic: false,
          creator: { id: creatorId },
        },
      ];

      mockPlanRepository.find.mockResolvedValue(plans);

      const result = await service.findAll({ creatorId });

      expect(result).toBeDefined();
      expect(result).toHaveLength(1);
      expect(result[0].creator.id).toBe(creatorId);
    });
  });

  describe('findOne', () => {
    it('should return a skill plan if found', async () => {
      const planId = '1';
      const plan = {
        id: planId,
        name: 'Learn TypeScript',
        steps: [
          {
            id: '1',
            title: 'TypeScript Basics',
            order: 1,
          },
        ],
      };

      mockPlanRepository.findOne.mockResolvedValue(plan);

      const result = await service.findOne(planId);

      expect(result).toBeDefined();
      expect(result.id).toBe(planId);
    });

    it('should throw NotFoundException if plan not found', async () => {
      mockPlanRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne('1')).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update a skill plan', async () => {
      const planId = '1';
      const userId = '1';
      const updateDto = {
        name: 'Updated TypeScript Course',
        description: 'Updated description',
      };

      const plan = {
        id: planId,
        name: 'Learn TypeScript',
        creator: { id: userId },
      };

      const updatedPlan = {
        ...plan,
        ...updateDto,
      };

      mockPlanRepository.findOne.mockResolvedValue(plan);
      mockPlanRepository.save.mockResolvedValue(updatedPlan);

      const result = await service.update(planId, userId, updateDto);

      expect(result).toBeDefined();
      expect(result.name).toBe(updateDto.name);
    });

    it('should throw NotFoundException if plan not found', async () => {
      mockPlanRepository.findOne.mockResolvedValue(null);

      await expect(service.update('1', '1', {})).rejects.toThrow(NotFoundException);
    });
  });

  describe('updateStep', () => {
    it('should update a step in the skill plan', async () => {
      const planId = '1';
      const stepId = '1';
      const userId = '1';
      const updateStepDto = {
        title: 'Updated Step Title',
        description: 'Updated step description',
      };

      const step = {
        id: stepId,
        title: 'Original Step Title',
        plan: {
          id: planId,
          creator: { id: userId },
        },
      };

      const updatedStep = {
        ...step,
        ...updateStepDto,
      };

      mockStepRepository.findOne.mockResolvedValue(step);
      mockPlanRepository.findOne.mockResolvedValue({
        id: planId,
        creator: { id: userId },
        steps: [step]
      });
      mockStepRepository.save.mockResolvedValue(updatedStep);

      const result = await service.updateStep(planId, stepId, userId, updateStepDto);

      expect(result).toBeDefined();
      expect(result.title).toBe(updateStepDto.title);
    });
  });

  describe('markStepTaskComplete', () => {
    it('should mark a task as complete in a step', async () => {
      const planId = '1';
      const stepId = '1';
      const taskIndex = 0;
      const userId = '1';

      const step = {
        id: stepId,
        tasks: [
          {
            description: 'Complete exercise',
            isCompleted: false,
          },
        ],
        plan: {
          id: planId,
          creator: { id: userId },
        },
      };

      const updatedStep = {
        ...step,
        tasks: [
          {
            ...step.tasks[0],
            isCompleted: true,
          },
        ],
      };

      mockPlanRepository.findOne.mockResolvedValue({
        id: planId,
        creator: { id: userId },
        steps: [step]
      });
      mockStepRepository.findOne.mockResolvedValue(step);
      mockStepRepository.save.mockResolvedValue(updatedStep);

      const result = await service.markStepTaskComplete(planId, stepId, taskIndex, userId);

      expect(result).toBeDefined();
      expect(result.tasks[0].isCompleted).toBe(true);
    });
  });

  describe('remove', () => {
    it('should delete a skill plan', async () => {
      const planId = '1';
      const userId = '1';
      const plan = {
        id: planId,
        name: 'Learn TypeScript',
        creator: { id: userId },
      };

      mockPlanRepository.findOne.mockResolvedValue(plan);
      mockPlanRepository.remove.mockResolvedValue(plan);

      await service.remove(planId, userId);

      expect(mockPlanRepository.remove).toHaveBeenCalledWith(plan);
    });

    it('should throw NotFoundException if plan not found', async () => {
      mockPlanRepository.findOne.mockResolvedValue(null);

      await expect(service.remove('1', '1')).rejects.toThrow(NotFoundException);
    });
  });
});
