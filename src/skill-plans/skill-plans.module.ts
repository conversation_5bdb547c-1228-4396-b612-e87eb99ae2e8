import { Modu<PERSON>, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MessagingModule } from '../messaging/messaging.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { AnalyticsModule } from '../analytics/analytics.module';
import { SkillPlan } from './entities/skill-plan.entity';
import { SkillPlanStep } from './entities/skill-plan-step.entity';
import { SkillPlansService } from './skill-plans.service';
import { SkillPlansController } from './skill-plans.controller';
import { AiSkillPlanService } from './ai-skill-plan.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([SkillPlan, SkillPlanStep]),
    MessagingModule,
    NotificationsModule,
    forwardRef(() => AnalyticsModule),
  ],
  providers: [SkillPlansService, AiSkillPlanService],
  controllers: [SkillPlansController],
  exports: [SkillPlansService, AiSkillPlanService],
})
export class SkillPlansModule {}
