import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CreateAiSkillPlanDto } from './dto/create-ai-skill-plan.dto';
import { User } from '../users/entities/user.entity';

interface GeneratedSkillPlan {
  name: string;
  description: string;
  metadata: {
    category: string;
    difficulty: 'beginner' | 'intermediate' | 'advanced';
    estimatedDuration: string;
    tags: string[];
  };
  steps: {
    title: string;
    description: string;
    order: number;
    resources?: {
      type: string;
      url: string;
      title: string;
    }[];
    tasks?: {
      description: string;
      isCompleted: boolean;
    }[];
  }[];
}

@Injectable()
export class AiSkillPlanService {
  private openrouterApiKey: string;

  constructor(private configService: ConfigService) {
    this.openrouterApiKey = this.configService.get<string>('openrouter.apiKey') || '';
  }

  async generateSkillPlan(
    createAiSkillPlanDto: CreateAiSkillPlanDto,
    user: User
  ): Promise<GeneratedSkillPlan> {
    const prompt = this.buildPrompt(createAi<PERSON>kill<PERSON>lan<PERSON><PERSON>, user);
    const aiResponse = await this.callOpenRouter(prompt);
    
    try {
      // Parse the AI response
      const parsedResponse = JSON.parse(aiResponse);
      return this.validateAndFormatResponse(parsedResponse, createAiSkillPlanDto);
    } catch (error) {
      throw new Error(`Failed to parse AI response: ${error.message}`);
    }
  }

  private buildPrompt(dto: CreateAiSkillPlanDto, user: User): string {
    const prompt = `
You are an expert learning coach and curriculum designer. Create a comprehensive, structured skill plan for learning "${dto.topic}" at the ${dto.level} level.

User Context:
- Experience Level: ${dto.level}
- Time Commitment: ${dto.timeCommitment || 'Not specified'}
- Learning Goals: ${dto.goals?.join(', ') || 'General skill acquisition'}
- Preferred Resources: ${dto.preferredResources?.join(', ') || 'Mixed resources'}
- User Background: ${user.firstName} ${user.lastName}

Requirements:
1. Create a skill plan with 5-8 progressive steps
2. Each step should build upon previous knowledge
3. Include diverse learning resources (videos, articles, practice exercises)
4. Provide actionable tasks for each step
5. Estimate realistic timeframes
6. Choose appropriate difficulty level and tags

Respond with a valid JSON object in this exact format:
{
  "name": "Clear, engaging skill plan title",
  "description": "Comprehensive description of what the learner will achieve",
  "metadata": {
    "category": "Primary category (e.g., Programming, Design, Business)",
    "difficulty": "${dto.level}",
    "estimatedDuration": "Realistic completion time (e.g., '4 weeks', '2 months')",
    "tags": ["relevant", "searchable", "tags"]
  },
  "steps": [
    {
      "title": "Step title",
      "description": "Detailed description of what this step covers",
      "order": 1,
      "resources": [
        {
          "type": "video|article|book|course|tutorial",
          "url": "https://example.com/resource (use real, accessible URLs when possible)",
          "title": "Resource title"
        }
      ],
      "tasks": [
        {
          "description": "Specific, actionable task",
          "isCompleted": false
        }
      ]
    }
  ]
}

Ensure:
- All URLs are realistic and accessible
- Tasks are specific and measurable
- Resources are diverse and high-quality
- Steps follow logical progression
- Content matches the specified difficulty level

Generate the skill plan now:`;

    return prompt;
  }

  private async callOpenRouter(prompt: string): Promise<string> {
    try {
      const response = await fetch(
        'https://openrouter.ai/api/v1/chat/completions',
        {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${this.openrouterApiKey}`,
            'HTTP-Referer': 'https://power-up-api.com',
            'X-Title': 'Power Up API - AI Skill Plan Generator',
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: 'deepseek/deepseek-r1-0528:free',
            messages: [
              {
                role: 'user',
                content: prompt,
              },
            ],
            temperature: 0.7,
            max_tokens: 4000,
          }),
        },
      );

      if (!response.ok) {
        throw new Error(
          `OpenRouter API error: ${response.status} ${response.statusText}`,
        );
      }

      const data = await response.json();
      return data.choices[0]?.message?.content || '';
    } catch (error) {
      console.error('Error calling OpenRouter API:', error);
      throw new Error('Failed to generate AI skill plan');
    }
  }

  private validateAndFormatResponse(
    response: any,
    dto: CreateAiSkillPlanDto
  ): GeneratedSkillPlan {
    // Validate required fields
    if (!response.name || !response.description || !response.steps) {
      throw new Error('Invalid AI response: missing required fields');
    }

    // Ensure steps are properly formatted
    const formattedSteps = response.steps.map((step: any, index: number) => ({
      title: step.title || `Step ${index + 1}`,
      description: step.description || 'Step description',
      order: index + 1,
      resources: step.resources || [],
      tasks: step.tasks || [],
    }));

    // Ensure metadata is properly formatted
    const metadata = {
      category: response.metadata?.category || 'General',
      difficulty: dto.level,
      estimatedDuration: response.metadata?.estimatedDuration || '4 weeks',
      tags: response.metadata?.tags || [dto.topic.toLowerCase()],
    };

    return {
      name: response.name,
      description: response.description,
      metadata,
      steps: formattedSteps,
    };
  }
}
