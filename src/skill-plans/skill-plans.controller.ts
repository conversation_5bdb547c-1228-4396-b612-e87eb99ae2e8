import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, Request, HttpCode, BadRequestException, NotFoundException } from '@nestjs/common';
import { SkillPlansService } from './skill-plans.service';
import { AiSkillPlanService } from './ai-skill-plan.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiQuery, ApiCreatedResponse, ApiOkResponse, ApiBody } from '@nestjs/swagger';
import { CreateSkillPlanDto } from './dto/create-skill-plan.dto';
import { CreateAiSkillPlanDto } from './dto/create-ai-skill-plan.dto';
import { UpdateSkillPlanDto } from './dto/update-skill-plan.dto';
import { UpdateStepDto } from './dto/update-step.dto';
import { UpdateStepCompletionDto } from './dto/update-step-completion.dto';
import { SkillPlanResponseDto } from './dto/skill-plan-response.dto';
import { SkillPlan } from './entities/skill-plan.entity';
import { SkillPlanStep } from './entities/skill-plan-step.entity';

@ApiTags('skill-plans')
@Controller('api/skill-plans')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class SkillPlansController {
  constructor(
    private readonly skillPlansService: SkillPlansService,
    private readonly aiSkillPlanService: AiSkillPlanService
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new skill plan' })
  @ApiBody({ type: CreateSkillPlanDto })
  @ApiCreatedResponse({
    description: 'The skill plan has been successfully created',
    type: SkillPlanResponseDto
  })
  create(@Request() req, @Body() createSkillPlanDto: CreateSkillPlanDto) {
    return this.skillPlansService.create({
      ...createSkillPlanDto,
      creator: req.user,
    });
  }

  @Post('ai-generate')
  @ApiOperation({ summary: 'Generate a skill plan using AI' })
  @ApiBody({ type: CreateAiSkillPlanDto })
  @ApiCreatedResponse({
    description: 'AI-generated skill plan has been successfully created',
    type: SkillPlanResponseDto
  })
  async createWithAi(@Request() req, @Body() createAiSkillPlanDto: CreateAiSkillPlanDto) {
    try {
      // Generate skill plan using AI
      const generatedPlan = await this.aiSkillPlanService.generateSkillPlan(
        createAiSkillPlanDto,
        req.user
      );

      // Create the skill plan using the existing service
      return this.skillPlansService.create({
        ...generatedPlan,
        creator: req.user,
        isPublic: createAiSkillPlanDto.isPublic || false,
      });
    } catch (error) {
      throw new BadRequestException(`Failed to generate AI skill plan: ${error.message}`);
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get skill plans based on filters' })
  @ApiQuery({ name: 'isPublic', type: 'boolean', required: false })
  @ApiQuery({ name: 'creatorId', type: 'string', required: false })
  @ApiOkResponse({
    description: 'Returns skill plans based on filters',
    type: [SkillPlanResponseDto]
  })
  findAll(
    @Query('isPublic') isPublic?: boolean,
    @Query('creatorId') creatorId?: string,
  ) {
    return this.skillPlansService.findAll({ isPublic, creatorId });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific skill plan' })
  @ApiOkResponse({
    description: 'Returns a specific skill plan by ID',
    type: SkillPlanResponseDto
  })
  findOne(@Param('id') id: string) {
    return this.skillPlansService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a skill plan' })
  @ApiBody({ type: UpdateSkillPlanDto })
  @ApiOkResponse({
    description: 'Returns the updated skill plan',
    type: SkillPlanResponseDto
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateSkillPlanDto: {
      name?: string;
      description?: string;
      isPublic?: boolean;
      metadata?: {
        category: string;
        difficulty: 'beginner' | 'intermediate' | 'advanced';
        estimatedDuration: string;
        tags: string[];
      };
    },
  ) {
    // Convert DTO to Partial<SkillPlan> for service compatibility
    const partialPlan: Partial<SkillPlan> = {
      ...updateSkillPlanDto
    };
    return this.skillPlansService.update(id, req.user.id, partialPlan);
  }

  @Put(':planId/steps/:stepId')
  @ApiOperation({ summary: 'Update a step in a skill plan' })
  @ApiBody({ type: UpdateStepDto })
  @ApiOkResponse({
    description: 'Returns the updated step',
    type: SkillPlanResponseDto
  })
  updateStep(
    @Request() req,
    @Param('planId') planId: string,
    @Param('stepId') stepId: string,
    @Body() updateStepDto: UpdateStepDto,
  ) {
    // Convert DTO to Partial<SkillPlanStep> for service compatibility
    const partialStep: Partial<SkillPlanStep> = {
      title: updateStepDto.title,
      description: updateStepDto.description,
      order: updateStepDto.order
      // Resources and tasks will need special handling in the service
    };
    return this.skillPlansService.updateStep(planId, stepId, req.user.id, partialStep);
  }

  @Put(':planId/steps/:stepId/tasks/:taskIndex/complete')
  @ApiOperation({ summary: 'Mark a task as complete in a skill plan step' })
  @ApiOkResponse({
    description: 'Task has been marked as complete',
    type: SkillPlanResponseDto
  })
  markStepTaskComplete(
    @Request() req,
    @Param('planId') planId: string,
    @Param('stepId') stepId: string,
    @Param('taskIndex') taskIndex: number,
  ) {
    return this.skillPlansService.markStepTaskComplete(planId, stepId, taskIndex, req.user.id);
  }

  @Post(':id/progress')
  @ApiOperation({ summary: 'Update skill plan progress' })
  @ApiBody({ type: UpdateStepCompletionDto })
  @ApiOkResponse({
    description: 'Returns the updated progress information',
    schema: {
      properties: {
        progress: { type: 'number' },
        completedSteps: { 
          type: 'array',
          items: { type: 'number' }
        }
      }
    }
  })
  @HttpCode(200)
  async updateProgress(
    @Request() req,
    @Param('id') id: string,
    @Body() updateDto: UpdateStepCompletionDto
  ) {
    // Validate ID and step ID to prevent undefined values
    // This validation is critical to avoid "invalid input syntax for type uuid: 'undefined'" errors
    if (!id || typeof id !== 'string') {
      throw new BadRequestException('Invalid plan ID');
    }

    if (!updateDto?.order) {
      throw new BadRequestException('Step order is required');
    }
    
    // Try-catch block to handle errors explicitly for better debugging
    try {
      const updated = await this.skillPlansService.updateProgress(
        id,
        req.user.id,
        updateDto.order,
        updateDto.completed
      );
    
      return {
        progress: updated.progress,
        completedSteps: updated.steps
          .filter(step => step.isCompleted)
          .map(step => step.order)
      };
    } catch (error) {
      if (error instanceof BadRequestException || error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to update skill plan progress: ${error.message}`);
    }
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a skill plan' })
  @ApiOkResponse({
    description: 'The skill plan has been successfully deleted',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Skill plan deleted successfully' }
      }
    }
  })
  remove(@Request() req, @Param('id') id: string) {
    return this.skillPlansService.remove(id, req.user.id);
  }
}
