import { ApiProperty } from '@nestjs/swagger';

class Resource {
  @ApiProperty({
    description: 'Resource type',
    example: 'video'
  })
  type: string;

  @ApiProperty({
    description: 'Resource URL',
    example: 'https://example.com/video123'
  })
  url: string;

  @ApiProperty({
    description: 'Resource title',
    example: 'Introduction to TypeScript'
  })
  title: string;
}

class Task {
  @ApiProperty({
    description: 'Task description',
    example: 'Complete the code exercise'
  })
  description: string;

  @ApiProperty({
    description: 'Whether task is completed',
    example: false
  })
  isCompleted: boolean;
}

class Step {
  @ApiProperty({
    description: 'Step ID',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Step title',
    example: 'Getting Started with TypeScript'
  })
  title: string;

  @ApiProperty({
    description: 'Step description',
    example: 'Learn the basics of TypeScript including types, interfaces and classes'
  })
  description: string;

  @ApiProperty({
    description: 'Step order',
    example: 1
  })
  order: number;

  @ApiProperty({
    description: 'Whether step is completed',
    example: false
  })
  isCompleted: boolean;

  @ApiProperty({
    description: 'Step resources',
    type: [Resource]
  })
  resources: Resource[];

  @ApiProperty({
    description: 'Step tasks',
    type: [Task]
  })
  tasks: Task[];
}

export class SkillPlanResponseDto {
  @ApiProperty({
    description: 'Unique skill plan ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  id: string;

  @ApiProperty({
    description: 'Skill plan name',
    example: 'Learn TypeScript from Scratch'
  })
  name: string;

  @ApiProperty({
    description: 'Skill plan description',
    example: 'A comprehensive guide to learning TypeScript from basics to advanced concepts'
  })
  description: string;

  @ApiProperty({
    description: 'Whether skill plan is public',
    example: true
  })
  isPublic: boolean;

  @ApiProperty({
    description: 'Creator user ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  creatorId: string;

  @ApiProperty({
    description: 'Skill plan creation date',
    example: '2025-05-19T10:00:00Z'
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Skill plan metadata',
    example: {
      category: 'Programming',
      difficulty: 'intermediate',
      estimatedDuration: '2 weeks',
      tags: ['programming', 'typescript', 'web development']
    }
  })
  metadata: {
    category: string;
    difficulty: string;
    estimatedDuration: string;
    tags: string[];
  };

  @ApiProperty({
    description: 'Skill plan steps',
    type: [Step]
  })
  steps: Step[];

  @ApiProperty({
    description: 'Overall progress percentage',
    example: 50
  })
  progressPercent: number;
}
