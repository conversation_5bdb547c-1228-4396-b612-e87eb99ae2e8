import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsNumber, IsOptional, ValidateNested, IsArray } from 'class-validator';
import { Type } from 'class-transformer';

class UpdateResourceDto {
  @ApiProperty({
    description: 'Resource type (e.g., video, article, book)',
    example: 'video'
  })
  @IsString()
  @IsOptional()
  type?: string;

  @ApiProperty({
    description: 'Resource URL',
    example: 'https://example.com/updated-video'
  })
  @IsString()
  @IsOptional()
  url?: string;

  @ApiProperty({
    description: 'Resource title',
    example: 'Updated Introduction to TypeScript'
  })
  @IsString()
  @IsOptional()
  title?: string;
}

class UpdateTaskDto {
  @ApiProperty({
    description: 'Task description',
    example: 'Updated code exercise'
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Whether the task is completed',
    example: true
  })
  @IsOptional()
  isCompleted?: boolean;
}

export class UpdateStepDto {
  @ApiProperty({
    description: 'Step title',
    example: 'Updated Getting Started with TypeScript'
  })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({
    description: 'Step description',
    example: 'Updated basics of TypeScript'
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Step order (position in sequence)',
    example: 2
  })
  @IsNumber()
  @IsOptional()
  order?: number;

  @ApiProperty({
    description: 'Step resources',
    type: [UpdateResourceDto],
    required: false
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateResourceDto)
  resources?: UpdateResourceDto[];

  @ApiProperty({
    description: 'Step tasks',
    type: [UpdateTaskDto],
    required: false
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateTaskDto)
  tasks?: UpdateTaskDto[];
}
