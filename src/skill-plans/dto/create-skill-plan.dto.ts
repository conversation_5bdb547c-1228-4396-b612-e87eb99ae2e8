import { ApiProperty } from '@nestjs/swagger';
import { 
  IsString, IsNotEmpty, IsBoolean, ValidateNested, 
  IsArray, IsEnum, IsOptional, IsNumber, ArrayMinSize 
} from 'class-validator';
import { Type } from 'class-transformer';

class ResourceDto {
  @ApiProperty({
    description: 'Resource type (e.g., video, article, book)',
    example: 'video'
  })
  @IsString()
  @IsNotEmpty()
  type: string;

  @ApiProperty({
    description: 'Resource URL',
    example: 'https://example.com/video123'
  })
  @IsString()
  @IsNotEmpty()
  url: string;

  @ApiProperty({
    description: 'Resource title',
    example: 'Introduction to TypeScript'
  })
  @IsString()
  @IsNotEmpty()
  title: string;
}

class TaskDto {
  @ApiProperty({
    description: 'Task description',
    example: 'Complete the code exercise'
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Whether the task is completed',
    example: false
  })
  @IsBoolean()
  isCompleted: boolean;
}

class StepDto {
  @ApiProperty({
    description: 'Step title',
    example: 'Getting Started with TypeScript'
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Step description',
    example: 'Learn the basics of TypeScript including types, interfaces and classes'
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Step order (position in sequence)',
    example: 1
  })
  @IsNumber()
  order: number;

  @ApiProperty({
    description: 'Step resources',
    type: [ResourceDto],
    required: false
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ResourceDto)
  resources?: ResourceDto[];

  @ApiProperty({
    description: 'Step tasks',
    type: [TaskDto],
    required: false
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TaskDto)
  tasks?: TaskDto[];
}

class SkillPlanMetadataDto {
  @ApiProperty({
    description: 'Skill plan category',
    example: 'Programming'
  })
  @IsString()
  @IsNotEmpty()
  category: string;

  @ApiProperty({
    description: 'Skill plan difficulty level',
    example: 'intermediate',
    enum: ['beginner', 'intermediate', 'advanced']
  })
  @IsEnum(['beginner', 'intermediate', 'advanced'])
  difficulty: 'beginner' | 'intermediate' | 'advanced';

  @ApiProperty({
    description: 'Estimated duration to complete the skill plan',
    example: '2 weeks'
  })
  @IsString()
  estimatedDuration: string;

  @ApiProperty({
    description: 'Skill plan tags',
    example: ['programming', 'typescript', 'web development']
  })
  @IsArray()
  @IsString({ each: true })
  tags: string[];
}

export class CreateSkillPlanDto {
  @ApiProperty({
    description: 'Skill plan name',
    example: 'Learn TypeScript from Scratch'
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Skill plan description',
    example: 'A comprehensive guide to learning TypeScript from basics to advanced concepts'
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Whether the skill plan is publicly accessible',
    example: true
  })
  @IsBoolean()
  isPublic: boolean;

  @ApiProperty({
    description: 'Skill plan metadata',
    type: SkillPlanMetadataDto
  })
  @ValidateNested()
  @Type(() => SkillPlanMetadataDto)
  metadata: SkillPlanMetadataDto;

  @ApiProperty({
    description: 'Skill plan steps',
    type: [StepDto]
  })
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => StepDto)
  steps: StepDto[];
}
