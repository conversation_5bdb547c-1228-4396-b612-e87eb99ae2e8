import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsEnum, IsArray } from 'class-validator';

export class CreateAiSkillPlanDto {
  @ApiProperty({
    description: 'Topic or skill you want to learn',
    example: 'Learn React.js development'
  })
  @IsString()
  @IsNotEmpty()
  topic: string;

  @ApiProperty({
    description: 'Your current experience level',
    example: 'beginner',
    enum: ['beginner', 'intermediate', 'advanced']
  })
  @IsEnum(['beginner', 'intermediate', 'advanced'])
  level: 'beginner' | 'intermediate' | 'advanced';

  @ApiProperty({
    description: 'Estimated time you can dedicate per day (in minutes)',
    example: 60,
    required: false
  })
  @IsOptional()
  @IsString()
  timeCommitment?: string;

  @ApiProperty({
    description: 'Specific learning goals or objectives',
    example: 'Build a portfolio website, Learn component-based development',
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  goals?: string[];

  @ApiProperty({
    description: 'Preferred learning resources (video, article, practice, etc.)',
    example: ['video', 'practice'],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  preferredResources?: string[];

  @ApiProperty({
    description: 'Whether the skill plan should be public',
    example: false,
    required: false
  })
  @IsOptional()
  isPublic?: boolean = false;
}
