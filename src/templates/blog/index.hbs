<!-- Blog Index Page -->
<section class="min-h-screen bg-gray-900 pt-20">
    <div class="container mx-auto px-6 py-12">
        <!-- Header -->
        <div class="text-center mb-16 fade-in-up">
            <!-- Navigation -->
            <div class="mb-6">
                <a href="/" class="inline-flex items-center text-blue-400 hover:text-blue-300 transition-colors text-sm font-medium">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                    Back to Home
                </a>
            </div>
            
            <h1 class="text-4xl md:text-6xl font-bold mb-6 text-white">
                Power Up <span class="gradient-text">Blog</span>
            </h1>
            <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                Discover insights about wellness, AI technology, habit formation, and personal development
            </p>
        </div>

        <!-- Featured Posts -->
        {{#if featuredPosts}}
        <div class="mb-16 fade-in-up">
            <h2 class="text-3xl font-bold text-white mb-8">Featured Articles</h2>
            <div class="grid md:grid-cols-3 gap-8">
                {{#each featuredPosts}}
                <article class="bg-gray-800 border border-blue-500/20 rounded-xl overflow-hidden hover:border-blue-500/40 transition-all duration-300 group">
                    {{#if featuredImage}}
                    <div class="aspect-video overflow-hidden">
                        <img src="{{featuredImage}}" alt="{{title}}" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                    </div>
                    {{/if}}
                    <div class="p-6">
                        <div class="flex items-center gap-4 mb-4">
                            <span class="bg-blue-500/20 text-blue-400 px-3 py-1 rounded-full text-sm font-medium">
                                {{categoryDisplayName category}}
                            </span>
                            <span class="text-gray-500 text-sm">{{formatDateShort publishedAt}}</span>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-3 group-hover:text-blue-400 transition-colors">
                            <a href="/blog/{{slug}}">{{title}}</a>
                        </h3>
                        {{#if excerpt}}
                        <p class="text-gray-400 mb-4">{{truncate excerpt 120}}</p>
                        {{/if}}
                        <div class="flex items-center justify-between">
                            <a href="/blog/{{slug}}" class="text-blue-400 hover:text-blue-300 font-medium transition-colors">
                                Read More →
                            </a>
                            <div class="flex items-center gap-4 text-sm text-gray-500">
                                <span>{{views}} views</span>
                                <span>{{likes}} likes</span>
                            </div>
                        </div>
                    </div>
                </article>
                {{/each}}
            </div>
        </div>
        {{/if}}

        <div class="grid lg:grid-cols-4 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-3">
                <!-- Search and Filters -->
                <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6 mb-8 fade-in-up">
                    <form method="GET" action="/blog" class="grid md:grid-cols-4 gap-4">
                        <div>
                            <input type="text" name="query" value="{{searchParams.query}}" 
                                   placeholder="Search articles..." 
                                   class="w-full px-4 py-2 bg-gray-900 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        <div>
                            <select name="category" class="w-full px-4 py-2 bg-gray-900 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">All Categories</option>
                                {{#each categories}}
                                <option value="{{this}}" {{#if (eq this ../searchParams.category)}}selected{{/if}}>
                                    {{categoryDisplayName this}}
                                </option>
                                {{/each}}
                            </select>
                        </div>
                        <div>
                            <select name="sortBy" class="w-full px-4 py-2 bg-gray-900 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="publishedAt" {{#if (eq searchParams.sortBy "publishedAt")}}selected{{/if}}>Latest</option>
                                <option value="views" {{#if (eq searchParams.sortBy "views")}}selected{{/if}}>Most Viewed</option>
                                <option value="likes" {{#if (eq searchParams.sortBy "likes")}}selected{{/if}}>Most Liked</option>
                                <option value="title" {{#if (eq searchParams.sortBy "title")}}selected{{/if}}>Alphabetical</option>
                            </select>
                        </div>
                        <button type="submit" class="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-2 rounded-lg hover:from-blue-500 hover:to-blue-600 transition-all duration-300 font-medium">
                            Search
                        </button>
                    </form>
                </div>

                <!-- Blog Posts -->
                <div class="space-y-8">
                    {{#each posts}}
                    <article class="bg-gray-800 border border-blue-500/20 rounded-xl overflow-hidden hover:border-blue-500/40 transition-all duration-300 group fade-in-up">
                        <div class="grid md:grid-cols-3 gap-6">
                            {{#if featuredImage}}
                            <div class="md:col-span-1 aspect-video md:aspect-square overflow-hidden">
                                <img src="{{featuredImage}}" alt="{{title}}" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                            </div>
                            <div class="md:col-span-2 p-6">
                            {{else}}
                            <div class="md:col-span-3 p-6">
                            {{/if}}
                                <div class="flex items-center gap-4 mb-4">
                                    <span class="bg-blue-500/20 text-blue-400 px-3 py-1 rounded-full text-sm font-medium">
                                        {{categoryDisplayName category}}
                                    </span>
                                    <span class="text-gray-500 text-sm">{{formatDateShort publishedAt}}</span>
                                    <div class="flex items-center gap-1 text-gray-500 text-sm">
                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                                        </svg>
                                        <span>{{author.name}}</span>
                                    </div>
                                </div>
                                <h2 class="text-2xl font-bold text-white mb-3 group-hover:text-blue-400 transition-colors">
                                    <a href="/blog/{{slug}}">{{title}}</a>
                                </h2>
                                {{#if excerpt}}
                                <p class="text-gray-400 mb-4">{{truncate excerpt 200}}</p>
                                {{/if}}
                                {{#if tags}}
                                <div class="flex flex-wrap gap-2 mb-4">
                                    {{#each tags}}
                                    <span class="bg-gray-700 text-gray-300 px-2 py-1 rounded text-xs">{{this}}</span>
                                    {{/each}}
                                </div>
                                {{/if}}
                                <div class="flex items-center justify-between">
                                    <a href="/blog/{{slug}}" class="text-blue-400 hover:text-blue-300 font-medium transition-colors">
                                        Read Full Article →
                                    </a>
                                    <div class="flex items-center gap-4 text-sm text-gray-500">
                                        <span>{{views}} views</span>
                                        <span>{{likes}} likes</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </article>
                    {{/each}}
                </div>

                <!-- Pagination -->
                {{#if (gt totalPages 1)}}
                <div class="flex justify-center mt-12 fade-in-up">
                    <nav class="flex items-center gap-2">
                        {{#if (gt currentPage 1)}}
                        <a href="/blog?{{#each searchParams}}{{@key}}={{this}}&{{/each}}offset={{multiply (subtract currentPage 2) (or searchParams.limit 10)}}" 
                           class="px-4 py-2 bg-gray-800 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors">
                            Previous
                        </a>
                        {{/if}}
                        
                        {{#each (paginate currentPage totalPages 5)}}
                        {{#if isCurrent}}
                        <span class="px-4 py-2 bg-blue-600 text-white rounded-lg font-medium">{{number}}</span>
                        {{else}}
                        <a href="/blog?{{#each ../searchParams}}{{@key}}={{this}}&{{/each}}offset={{multiply (subtract number 1) (or ../searchParams.limit 10)}}" 
                           class="px-4 py-2 bg-gray-800 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors">
                            {{number}}
                        </a>
                        {{/if}}
                        {{/each}}
                        
                        {{#if (lt currentPage totalPages)}}
                        <a href="/blog?{{#each searchParams}}{{@key}}={{this}}&{{/each}}offset={{multiply currentPage (or searchParams.limit 10)}}" 
                           class="px-4 py-2 bg-gray-800 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors">
                            Next
                        </a>
                        {{/if}}
                    </nav>
                </div>
                {{/if}}

                {{#unless posts}}
                <div class="text-center py-16 fade-in-up">
                    <div class="text-gray-500 mb-4">
                        <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-400 mb-2">No articles found</h3>
                    <p class="text-gray-500">Try adjusting your search criteria or check back later for new content.</p>
                </div>
                {{/unless}}
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <div class="sticky top-24 space-y-8">
                    <!-- Categories -->
                    <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6 fade-in-up">
                        <h3 class="text-xl font-bold text-white mb-4">Categories</h3>
                        <div class="space-y-2">
                            {{#each categories}}
                            <a href="/blog/category/{{this}}" class="block text-gray-400 hover:text-blue-400 transition-colors py-1">
                                {{categoryDisplayName this}}
                            </a>
                            {{/each}}
                        </div>
                    </div>

                    <!-- Newsletter Signup -->
                    <div class="bg-gradient-to-br from-blue-900/50 to-slate-900/50 border border-blue-500/20 rounded-xl p-6 fade-in-up">
                        <h3 class="text-xl font-bold text-white mb-4">Stay Updated</h3>
                        <p class="text-gray-400 mb-4">Get the latest articles delivered to your inbox</p>
                        <form class="space-y-3">
                            <input type="email" placeholder="Your email address" 
                                   class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <button type="submit" class="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-2 rounded-lg hover:from-blue-500 hover:to-blue-600 transition-all duration-300 font-medium">
                                Subscribe
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Smooth scrolling and animations
document.addEventListener('DOMContentLoaded', function() {
    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
            }
        });
    }, observerOptions);

    // Observe all animation elements
    document.querySelectorAll('.fade-in-up').forEach(el => {
        observer.observe(el);
    });
});
</script>
