<!-- Blog Error Page -->
<section class="min-h-screen bg-gray-900 pt-20">
    <div class="container mx-auto px-6 py-12">
        <div class="max-w-2xl mx-auto text-center">
            <!-- Error Icon -->
            <div class="mb-8 fade-in-up">
                <div class="w-24 h-24 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-12 h-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-2.694-.833-3.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                    </svg>
                </div>
                
                <h1 class="text-4xl font-bold text-white mb-4">
                    {{#if error.title}}{{error.title}}{{else}}Oops! Something went wrong{{/if}}
                </h1>
                
                <p class="text-xl text-gray-400 mb-8">
                    {{#if error.message}}{{error.message}}{{else}}We're sorry, but we couldn't load the page you requested.{{/if}}
                </p>
                
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{#if error.backLink}}{{error.backLink}}{{else}}/blog{{/if}}" 
                       class="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-8 py-3 rounded-lg hover:from-blue-500 hover:to-blue-600 transition-all duration-300 font-medium">
                        {{#if error.backLink}}Go Back{{else}}Browse Blog{{/if}}
                    </a>
                    <a href="/" 
                       class="bg-gray-800 border border-gray-600 text-gray-300 px-8 py-3 rounded-lg hover:bg-gray-700 transition-colors">
                        Return Home
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Smooth scrolling and animations
document.addEventListener('DOMContentLoaded', function() {
    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
            }
        });
    }, observerOptions);

    // Observe all animation elements
    document.querySelectorAll('.fade-in-up').forEach(el => {
        observer.observe(el);
    });
});
</script>
