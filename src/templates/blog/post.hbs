<!-- Blog Post Detail Page -->
<section class="min-h-screen bg-gray-900 pt-20">
    <div class="container mx-auto px-6 py-12">
        <div class="max-w-4xl mx-auto">
            <!-- Back to Blog -->
            <div class="mb-8 fade-in-up">
                <a href="/blog" class="inline-flex items-center text-blue-400 hover:text-blue-300 transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                    Back to Blog
                </a>
            </div>

            <!-- Article Header -->
            <article class="fade-in-up">
                <header class="mb-8">
                    <div class="flex items-center gap-4 mb-6">
                        <span class="bg-blue-500/20 text-blue-400 px-4 py-2 rounded-full font-medium">
                            {{categoryDisplayName post.category}}
                        </span>
                        <span class="text-gray-400">{{formatDate post.publishedAt}}</span>
                        {{#if post.isFeatured}}
                        <span class="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                            Featured
                        </span>
                        {{/if}}
                    </div>
                    
                    <h1 class="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
                        {{post.title}}
                    </h1>
                    
                    {{#if post.excerpt}}
                    <p class="text-xl text-gray-400 mb-8 leading-relaxed">
                        {{post.excerpt}}
                    </p>
                    {{/if}}

                    <!-- Author and Meta Info -->
                    <div class="flex items-center justify-between border-b border-gray-700 pb-6">
                        <div class="flex items-center gap-4">
                            <div class="w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full flex items-center justify-center">
                                <span class="text-white font-bold text-lg">
                                    {{#if post.author.name}}{{substring post.author.name 0 1}}{{else}}A{{/if}}
                                </span>
                            </div>
                            <div>
                                <div class="text-white font-medium">
                                    {{#if post.author.name}}{{post.author.name}}{{else}}Power Up Team{{/if}}
                                </div>
                                <div class="text-gray-400 text-sm">Author</div>
                            </div>
                        </div>
                        
                        <div class="flex items-center gap-6 text-gray-400">
                            <div class="flex items-center gap-2">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                </svg>
                                <span>{{post.views}} views</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                </svg>
                                <span>{{post.likes}} likes</span>
                            </div>
                        </div>
                    </div>
                </header>

                <!-- Featured Image -->
                {{#if post.featuredImage}}
                <div class="mb-8 fade-in-up">
                    <img src="{{post.featuredImage}}" alt="{{post.title}}" 
                         class="w-full rounded-xl shadow-2xl border border-gray-700">
                </div>
                {{/if}}

                <!-- Article Content -->
                <div class="prose prose-lg prose-invert max-w-none mb-12 fade-in-up">
                    <div class="text-gray-300 leading-relaxed">
                        {{{editorJsToHtml post.content}}}
                    </div>
                </div>

                <!-- Tags -->
                {{#if post.tags}}
                <div class="mb-8 fade-in-up">
                    <h3 class="text-lg font-semibold text-white mb-4">Tags</h3>
                    <div class="flex flex-wrap gap-2">
                        {{#each post.tags}}
                        <span class="bg-gray-800 border border-gray-600 text-gray-300 px-3 py-1 rounded-full text-sm hover:bg-gray-700 transition-colors">
                            {{this}}
                        </span>
                        {{/each}}
                    </div>
                </div>
                {{/if}}

                <!-- Social Sharing -->
                <div class="border-t border-gray-700 pt-8 mb-12 fade-in-up">
                    <h3 class="text-lg font-semibold text-white mb-4">Share this article</h3>
                    <div class="flex items-center gap-4">
                        <a href="https://twitter.com/intent/tweet?text={{post.title}}&url={{@root.baseUrl}}/blog/{{post.slug}}" 
                           target="_blank" rel="noopener"
                           class="bg-blue-600 hover:bg-blue-500 text-white p-3 rounded-lg transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                            </svg>
                        </a>
                        <a href="https://www.facebook.com/sharer/sharer.php?u={{@root.baseUrl}}/blog/{{post.slug}}" 
                           target="_blank" rel="noopener"
                           class="bg-blue-800 hover:bg-blue-700 text-white p-3 rounded-lg transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                            </svg>
                        </a>
                        <a href="https://www.linkedin.com/sharing/share-offsite/?url={{@root.baseUrl}}/blog/{{post.slug}}" 
                           target="_blank" rel="noopener"
                           class="bg-blue-700 hover:bg-blue-600 text-white p-3 rounded-lg transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                        <button onclick="copyToClipboard('{{@root.baseUrl}}/blog/{{post.slug}}', this)" 
                                class="bg-gray-700 hover:bg-gray-600 text-white p-3 rounded-lg transition-colors">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </article>

            <!-- Related Posts -->
            {{#if relatedPosts}}
            <section class="fade-in-up">
                <h2 class="text-3xl font-bold text-white mb-8">Related Articles</h2>
                <div class="grid md:grid-cols-3 gap-8">
                    {{#each relatedPosts}}
                    <article class="bg-gray-800 border border-blue-500/20 rounded-xl overflow-hidden hover:border-blue-500/40 transition-all duration-300 group">
                        {{#if featuredImage}}
                        <div class="aspect-video overflow-hidden">
                            <img src="{{featuredImage}}" alt="{{title}}" class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                        </div>
                        {{/if}}
                        <div class="p-6">
                            <div class="flex items-center gap-4 mb-4">
                                <span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-sm font-medium">
                                    {{categoryDisplayName category}}
                                </span>
                                <span class="text-gray-500 text-sm">{{formatDateShort publishedAt}}</span>
                            </div>
                            <h3 class="text-lg font-bold text-white mb-3 group-hover:text-blue-400 transition-colors">
                                <a href="/blog/{{slug}}">{{title}}</a>
                            </h3>
                            {{#if excerpt}}
                            <p class="text-gray-400 text-sm mb-4">{{truncate excerpt 100}}</p>
                            {{/if}}
                            <a href="/blog/{{slug}}" class="text-blue-400 hover:text-blue-300 font-medium transition-colors text-sm">
                                Read More →
                            </a>
                        </div>
                    </article>
                    {{/each}}
                </div>
            </section>
            {{/if}}
        </div>
    </div>
</section>

<script>
// Copy to clipboard function
function copyToClipboard(text, buttonElement) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const button = buttonElement;
        const originalContent = button.innerHTML;
        button.innerHTML = '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
        button.classList.remove('bg-gray-700');
        button.classList.add('bg-green-600');
        
        setTimeout(() => {
            button.innerHTML = originalContent;
            button.classList.remove('bg-green-600');
            button.classList.add('bg-gray-700');
        }, 2000);
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
        
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            // Show success message for fallback
            const button = buttonElement;
            const originalContent = button.innerHTML;
            button.innerHTML = '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
            button.classList.remove('bg-gray-700');
            button.classList.add('bg-green-600');
            
            setTimeout(() => {
                button.innerHTML = originalContent;
                button.classList.remove('bg-green-600');
                button.classList.add('bg-gray-700');
            }, 2000);
        } catch (fallbackErr) {
            console.error('Fallback copy failed: ', fallbackErr);
        }
        document.body.removeChild(textArea);
    });
}

// Smooth scrolling and animations
document.addEventListener('DOMContentLoaded', function() {
    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
            }
        });
    }, observerOptions);

    // Observe all animation elements
    document.querySelectorAll('.fade-in-up').forEach(el => {
        observer.observe(el);
    });
});
</script>
