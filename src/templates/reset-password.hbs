<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}}</title>
    <style>
        html {
            background: #020617;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #020617 0%, #0f172a 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            max-width: 400px;
            width: 100%;
            background: #1e293b;
            border: 1px solid #334155;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            padding: 40px;
            box-sizing: border-box;
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo h1 {
            color: #60a5fa;
            font-size: 28px;
            margin: 0;
            font-weight: 700;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            color: #f1f5f9;
            font-weight: 500;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #475569;
            border-radius: 8px;
            font-size: 16px;
            color: #f1f5f9;
            background: #334155;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #60a5fa;
        }
        input[type="text"]::placeholder, input[type="password"]::placeholder {
            color: #94a3b8;
        }
        .btn {
            width: 100%;
            padding: 12px 16px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        .btn:hover {
            background: #2563eb;
        }
        .btn:disabled {
            background: #64748b;
            cursor: not-allowed;
        }
        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        .alert-success {
            background: #065f46;
            color: #d1fae5;
            border: 1px solid #10b981;
        }
        .alert-error {
            background: #7f1d1d;
            color: #fecaca;
            border: 1px solid #ef4444;
        }
        .help-text {
            font-size: 14px;
            color: #94a3b8;
            margin-top: 8px;
        }
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        .back-link a {
            color: #60a5fa;
            text-decoration: none;
            font-size: 14px;
        }
        .back-link a:hover {
            text-decoration: underline;
        }
        .code-input {
            text-align: center;
            font-family: 'Courier New', monospace;
            font-size: 18px;
            letter-spacing: 2px;
            text-transform: uppercase;
        }
        
        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(4px);
        }
        
        .modal-content {
            background-color: #1e293b;
            margin: 15% auto;
            padding: 30px;
            border: 1px solid #334155;
            border-radius: 12px;
            width: 90%;
            max-width: 400px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
        }
        
        .modal-header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .modal-header h3 {
            color: #f1f5f9;
            margin: 0;
            font-size: 20px;
            font-weight: 600;
        }
        
        .modal-form-group {
            margin-bottom: 20px;
        }
        
        .modal-form-group label {
            display: block;
            margin-bottom: 8px;
            color: #f1f5f9;
            font-weight: 500;
        }
        
        .modal-form-group input[type="email"] {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #475569;
            border-radius: 8px;
            font-size: 16px;
            color: #f1f5f9;
            background: #334155;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        .modal-form-group input[type="email"]:focus {
            outline: none;
            border-color: #60a5fa;
        }
        
        .modal-form-group input[type="email"]::placeholder {
            color: #94a3b8;
        }
        
        .modal-buttons {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }
        
        .modal-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        
        .modal-btn-cancel {
            background: #64748b;
            color: white;
        }
        
        .modal-btn-cancel:hover {
            background: #475569;
        }
        
        .modal-btn-send {
            background: #3b82f6;
            color: white;
        }
        
        .modal-btn-send:hover {
            background: #2563eb;
        }
        
        .modal-btn:disabled {
            background: #64748b;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <h1>Power Up</h1>
        </div>

        {{#if success}}
            <div class="alert alert-success">
                {{message}}
            </div>
        {{else}}
            {{#if error}}
                <div class="alert alert-error">
                    {{error}}
                </div>
            {{/if}}

            <form method="POST" action="/api/auth/reset-password">
                <div class="form-group">
                    <label for="code">Reset Code</label>
                    <input 
                        type="text" 
                        id="code" 
                        name="code" 
                        value="{{code}}"
                        placeholder="Enter 8-character code"
                        maxlength="8"
                        class="code-input"
                        required
                    >
                    <div class="help-text">
                        Enter the 8-character reset code you received in your email
                    </div>
                </div>

                <div class="form-group">
                    <label for="newPassword">New Password</label>
                    <input 
                        type="password" 
                        id="newPassword" 
                        name="newPassword" 
                        placeholder="Enter your new password"
                        minlength="6"
                        required
                    >
                    <div class="help-text">
                        Password must be at least 6 characters long
                    </div>
                </div>

                <button type="submit" class="btn">Reset Password</button>
            </form>

            <div class="back-link">
                <button id="requestNewCode" class="btn" style="background: #6b7280; margin-top: 10px;">Request New Code</button>
            </div>
        {{/if}}
    </div>

    <!-- Email Modal -->
    <div id="emailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Request New Reset Code</h3>
            </div>
            <div class="modal-form-group">
                <label for="modalEmail">Email Address</label>
                <input 
                    type="email" 
                    id="modalEmail" 
                    placeholder="Enter your email address"
                    required
                >
            </div>
            <div class="modal-buttons">
                <button type="button" id="modalCancel" class="modal-btn modal-btn-cancel">Cancel</button>
                <button type="button" id="modalSend" class="modal-btn modal-btn-send">Send Code</button>
            </div>
        </div>
    </div>

    <script>
        // Auto-format code input
        document.getElementById('code').addEventListener('input', function(e) {
            this.value = this.value.toUpperCase();
        });

        // Request new code functionality
        document.getElementById('requestNewCode').addEventListener('click', function(e) {
            e.preventDefault();
            
            // Show modal
            const modal = document.getElementById('emailModal');
            const emailInput = document.getElementById('modalEmail');
            modal.style.display = 'block';
            emailInput.focus();
        });

        // Modal cancel button
        document.getElementById('modalCancel').addEventListener('click', function() {
            const modal = document.getElementById('emailModal');
            const emailInput = document.getElementById('modalEmail');
            modal.style.display = 'none';
            emailInput.value = '';
        });

        // Modal send button
        document.getElementById('modalSend').addEventListener('click', async function() {
            const emailInput = document.getElementById('modalEmail');
            const email = emailInput.value.trim();

            if (!email) {
                alert('Please enter your email address.');
                emailInput.focus();
                return;
            }

            // Basic email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                alert('Please enter a valid email address.');
                emailInput.focus();
                return;
            }

            const sendButton = this;
            const cancelButton = document.getElementById('modalCancel');
            const originalText = sendButton.textContent;
            
            // Disable buttons during request
            sendButton.textContent = 'Sending...';
            sendButton.disabled = true;
            cancelButton.disabled = true;

            try {
                const response = await fetch('/api/auth/resend-reset-code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email: email })
                });

                const result = await response.json();

                if (response.ok) {
                    // Close modal and show success
                    const modal = document.getElementById('emailModal');
                    modal.style.display = 'none';
                    emailInput.value = '';
                    
                    alert('A new reset code has been sent to your email address.');
                    
                    // Clear the current code input so user can enter the new one
                    const codeInput = document.getElementById('code');
                    codeInput.value = '';
                    codeInput.focus();
                } else {
                    alert(result.message || 'Failed to send reset code. Please try again.');
                }
            } catch (error) {
                alert('Network error. Please check your connection and try again.');
            } finally {
                // Re-enable buttons
                sendButton.textContent = originalText;
                sendButton.disabled = false;
                cancelButton.disabled = false;
            }
        });

        // Close modal when clicking outside
        document.getElementById('emailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                const emailInput = document.getElementById('modalEmail');
                this.style.display = 'none';
                emailInput.value = '';
            }
        });

        // Handle Enter key in email input
        document.getElementById('modalEmail').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('modalSend').click();
            }
        });
    </script>
</body>
</html>
