<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{#if title}}{{title}}{{else}}Power Up - AI-Driven Personal Wellness Coach{{/if}}</title>
    <meta name="description" content="{{#if description}}{{description}}{{else}}Transform your life with Power Up - the ultimate AI-driven wellness app featuring personalized coaching, habit tracking, daily podcasts, and community challenges.{{/if}}">
    
    <!-- Web App Manifest -->
    <link rel="manifest" href="/manifest.json">
    
    <!-- Favicons -->
    <link rel="icon" type="image/x-icon" href="/assets/images/logo.png">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                        dark: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <style>
        * {
            scroll-behavior: smooth;
        }
        
        body {
            background: linear-gradient(135deg, #020617 0%, #0f172a 100%);
            overflow-x: hidden;
        }
        
        /* Ensure hero section has dark background */
        #hero {
            background: linear-gradient(135deg, #020617 0%, #0f172a 100%);
        }
        
        .section-height {
            min-height: 100vh;
        }
        
        .gradient-blue {
            background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%);
        }
        
        .gradient-text {
            background: linear-gradient(135deg, #60a5fa, #3b82f6, #2563eb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        /* Navbar */
        .navbar-transparent {
            background: transparent;
            transition: all 0.3s ease;
        }
        
        .navbar-scrolled {
            background: rgba(2, 6, 23, 0.95);
            backdrop-filter: blur(20px);
            box-shadow: 0 4px 20px rgba(96, 165, 250, 0.1);
        }
        
        /* Animations */
        .fade-in-up {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .fade-in-up.animate {
            opacity: 1;
            transform: translateY(0);
        }
        
        .slide-in-left {
            opacity: 0;
            transform: translateX(-50px);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .slide-in-left.animate {
            opacity: 1;
            transform: translateX(0);
        }
        
        .slide-in-right {
            opacity: 0;
            transform: translateX(50px);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .slide-in-right.animate {
            opacity: 1;
            transform: translateX(0);
        }
        
        /* Feature Cards */
        .feature-card {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(96, 165, 250, 0.2);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .feature-card:hover {
            transform: translateY(-10px) scale(1.02);
            border-color: rgba(96, 165, 250, 0.5);
            box-shadow: 0 25px 50px rgba(96, 165, 250, 0.2);
        }
        
        /* Glowing effects */
        .glow-blue {
            box-shadow: 0 0 30px rgba(96, 165, 250, 0.3);
        }
        
        .glow-blue:hover {
            box-shadow: 0 0 40px rgba(96, 165, 250, 0.5);
        }
        
        /* Animated background */
        .animated-bg {
            background: radial-gradient(circle at 20% 80%, rgba(96, 165, 250, 0.1) 0%, transparent 50%),
                       radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                       radial-gradient(circle at 40% 40%, rgba(37, 99, 235, 0.1) 0%, transparent 50%);
            animation: backgroundShift 20s ease-in-out infinite;
        }
        
        @keyframes backgroundShift {
            0%, 100% { 
                background: radial-gradient(circle at 20% 80%, rgba(96, 165, 250, 0.1) 0%, transparent 50%),
                           radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                           radial-gradient(circle at 40% 40%, rgba(37, 99, 235, 0.1) 0%, transparent 50%);
            }
            50% { 
                background: radial-gradient(circle at 80% 20%, rgba(96, 165, 250, 0.15) 0%, transparent 50%),
                           radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
                           radial-gradient(circle at 60% 60%, rgba(37, 99, 235, 0.15) 0%, transparent 50%);
            }
        }
        
        /* Counter animation */
        .counter {
            font-variant-numeric: tabular-nums;
        }
        
        /* SVG animations */
        .bounce-slow {
            animation: bounce 3s infinite;
        }
        
        .pulse-glow {
            animation: pulseGlow 2s ease-in-out infinite alternate;
        }
        
        @keyframes pulseGlow {
            from {
                filter: drop-shadow(0 0 10px rgba(96, 165, 250, 0.5));
            }
            to {
                filter: drop-shadow(0 0 20px rgba(96, 165, 250, 0.8));
            }
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #1e293b;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #60a5fa;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #3b82f6;
        }
        
        /* Mobile responsive */
        @media (max-width: 768px) {
            .section-height {
                min-height: 100vh;
                padding-top: 80px;
            }
        }
    </style>
</head>
<body class="bg-gray-900 text-white animated-bg">
    {{{body}}}
</body>
</html>
