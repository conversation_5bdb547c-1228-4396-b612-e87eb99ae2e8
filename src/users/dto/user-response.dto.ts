import { ApiProperty } from '@nestjs/swagger';

export class UserResponseDto {
  @ApiProperty({
    description: 'Unique user ID',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  id: string;

  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>'
  })
  email: string;

  @ApiProperty({
    description: 'User first name',
    example: 'John'
  })
  firstName: string;

  @ApiProperty({
    description: 'User last name',
    example: 'Doe'
  })
  lastName: string;

  @ApiProperty({
    description: 'User experience points',
    example: 150
  })
  xp: number;

  @ApiProperty({
    description: 'User badges earned',
    example: ['early-bird', 'consistent-performer'],
    type: [String]
  })
  badges: string[];

  @ApiProperty({
    description: 'Authentication provider',
    enum: ['local', 'google', 'apple'],
    example: 'local'
  })
  provider: string;

  @ApiProperty({
    description: 'User profile picture URL',
    example: 'https://example.com/avatar.jpg',
    nullable: true
  })
  picture?: string;

  @ApiProperty({
    description: 'User creation date',
    example: '2025-05-19T10:00:00Z'
  })
  createdAt: Date;

  @ApiProperty({
    description: 'User last update date',
    example: '2025-05-19T10:00:00Z'
  })
  updatedAt: Date;
}
