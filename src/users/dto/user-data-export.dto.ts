import { ApiProperty } from '@nestjs/swagger';

export class UserDataExportDto {
  @ApiProperty({ description: 'User profile information' })
  profile: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    xp: number;
    badges: string[];
    provider: string;
    picture?: string;
    createdAt: Date;
    updatedAt: Date;
  };

  @ApiProperty({ description: 'User habits data' })
  habits: Array<{
    id: string;
    name: string;
    description?: string;
    schedule: string[];
    currentStreak: number;
    longestStreak: number;
    completion: { [date: string]: boolean };
    reminderSettings?: {
      enabled: boolean;
      time: string;
      days: string[];
    };
    xpReward: number;
    lastCompletedAt?: Date;
    createdAt: Date;
    updatedAt: Date;
  }>;

  @ApiProperty({ description: 'User tasks data' })
  tasks: Array<{
    id: string;
    title: string;
    description?: string;
    completed: boolean;
    completedAt?: Date;
    priority: string;
    dueDate: Date;
    reminderSettings?: {
      enabled: boolean;
      time: string;
    };
    notifiedOverdue: boolean;
    createdAt: Date;
    updatedAt: Date;
  }>;

  @ApiProperty({ description: 'Data export timestamp' })
  exportedAt: Date;
}
