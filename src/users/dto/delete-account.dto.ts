import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, MinLength } from 'class-validator';

export class DeleteAccountDto {
  @ApiProperty({ 
    description: 'Password confirmation for account deletion',
    minLength: 6
  })
  @IsString()
  @MinLength(6)
  password: string;

  @ApiProperty({ 
    description: 'Optional reason for deleting the account',
    required: false
  })
  @IsOptional()
  @IsString()
  reason?: string;
}
