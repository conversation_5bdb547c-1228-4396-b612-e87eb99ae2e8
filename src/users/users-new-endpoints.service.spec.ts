import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { UsersService } from './users.service';
import { User, AuthProvider } from './entities/user.entity';
import { Repository } from 'typeorm';
import { NotFoundException, ForbiddenException } from '@nestjs/common';
import * as bcrypt from 'bcrypt';

describe('UsersService - New Endpoints', () => {
  let service: UsersService;
  let repository: Repository<User>;

  const mockUser: User = {
    id: '1',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    password: 'hashedPassword',
    xp: 0,
    badges: [],
    firebaseUid: undefined,
    provider: AuthProvider.LOCAL,
    picture: undefined,
    habits: [],
    tasks: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: undefined,
  };

  const mockRepository = {
    findOne: jest.fn(),
    softDelete: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getRepositoryToken(User),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    repository = module.get<Repository<User>>(getRepositoryToken(User));
  });

  describe('softDeleteAccount', () => {
    it('should soft delete a local user account with valid password', async () => {
      const hashedPassword = await bcrypt.hash('password123', 10);
      const userWithHashedPassword = { ...mockUser, password: hashedPassword };
      
      mockRepository.findOne.mockResolvedValue(userWithHashedPassword);
      mockRepository.softDelete.mockResolvedValue({ affected: 1 });

      const result = await service.softDeleteAccount('1', 'password123');

      expect(result).toEqual({ message: 'Account successfully deleted' });
      expect(mockRepository.softDelete).toHaveBeenCalledWith('1');
    });

    it('should soft delete a social login user account without password verification', async () => {
      const socialUser = { ...mockUser, provider: AuthProvider.GOOGLE, password: null };
      
      mockRepository.findOne.mockResolvedValue(socialUser);
      mockRepository.softDelete.mockResolvedValue({ affected: 1 });

      const result = await service.softDeleteAccount('1', '');

      expect(result).toEqual({ message: 'Account successfully deleted' });
      expect(mockRepository.softDelete).toHaveBeenCalledWith('1');
    });

    it('should throw ForbiddenException for invalid password', async () => {
      const hashedPassword = await bcrypt.hash('password123', 10);
      const userWithHashedPassword = { ...mockUser, password: hashedPassword };
      
      mockRepository.findOne.mockResolvedValue(userWithHashedPassword);

      await expect(service.softDeleteAccount('1', 'wrongpassword'))
        .rejects.toThrow(ForbiddenException);
    });
  });

  describe('exportUserData', () => {
    it('should export user data successfully', async () => {
      const userWithData = {
        ...mockUser,
        habits: [
          {
            id: 'habit1',
            name: 'Exercise',
            description: 'Daily exercise',
            schedule: ['monday', 'wednesday'],
            currentStreak: 5,
            longestStreak: 10,
            completion: { '2024-01-01': true },
            reminderSettings: { enabled: true, time: '08:00', days: ['monday'] },
            xpReward: 10,
            lastCompletedAt: new Date(),
            createdAt: new Date(),
            updatedAt: new Date(),
          }
        ],
        tasks: [
          {
            id: 'task1',
            title: 'Complete project',
            description: 'Finish the project',
            completed: false,
            completedAt: null,
            priority: 'high',
            dueDate: new Date(),
            reminderSettings: { enabled: true, time: '09:00' },
            notifiedOverdue: false,
            createdAt: new Date(),
            updatedAt: new Date(),
          }
        ]
      };

      mockRepository.findOne.mockResolvedValue(userWithData);

      const result = await service.exportUserData('1');

      expect(result).toHaveProperty('profile');
      expect(result).toHaveProperty('habits');
      expect(result).toHaveProperty('tasks');
      expect(result).toHaveProperty('exportedAt');
      expect(result.profile).not.toHaveProperty('password');
      expect(result.habits).toHaveLength(1);
      expect(result.tasks).toHaveLength(1);
    });

    it('should throw NotFoundException for non-existent user', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.exportUserData('999'))
        .rejects.toThrow(NotFoundException);
    });
  });
});
