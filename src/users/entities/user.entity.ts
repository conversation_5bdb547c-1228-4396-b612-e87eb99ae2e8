import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateC<PERSON>umn, UpdateDateColumn, DeleteDateColumn, OneToMany } from 'typeorm';
import { Exclude } from 'class-transformer';
import { Habit } from '../../habits/entities/habit.entity';
import { Task } from '../../tasks/entities/task.entity';
import { CalendarEvent } from '../../calendar/entities/calendar-event.entity';

export enum AuthProvider {
  LOCAL = 'local',
  GOOGLE = 'google',
  APPLE = 'apple',
}

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  email: string;

  @Column()
  firstName: string;

  @Column()
  lastName: string;

  @Column({ nullable: true })
  @Exclude()
  password: string;

  @Column({ default: 0 })
  xp: number;

  @Column('simple-array', { nullable: true })
  badges: string[];

  @Column({ nullable: true, name: 'firebase_uid' })
  firebaseUid?: string;

  @Column({
    type: 'enum',
    enum: AuthProvider,
    default: AuthProvider.LOCAL,
  })
  provider: AuthProvider;

  @Column({ nullable: true })
  picture?: string;

  @Column({ nullable: true, name: 'reset_code' })
  @Exclude()
  resetCode?: string;

  @Column({ nullable: true, name: 'reset_code_expires_at' })
  @Exclude()
  resetCodeExpiresAt?: Date;
  
  @OneToMany(() => Habit, habit => habit.user)
  habits: Habit[];

  @OneToMany(() => Task, task => task.user)
  tasks: Task[];

  @OneToMany(() => CalendarEvent, calendarEvent => calendarEvent.user)
  calendarEvents: CalendarEvent[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn({ nullable: true, name: 'deleted_at' })
  deletedAt?: Date;
}
