import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User, AuthProvider } from './entities/user.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

describe('UsersController - PDF Export (Integration)', () => {
  let app: INestApplication;
  let controller: UsersController;

  const mockUser: User = {
    id: '1',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    password: 'hashedPassword',
    xp: 150,
    badges: ['early_adopter'],
    firebaseUid: undefined,
    provider: AuthProvider.LOCAL,
    picture: undefined,
    habits: [],
    tasks: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: undefined,
  };

  const mockUsersService = {
    exportUserDataAsPDF: jest.fn(),
  };

  const mockRepository = {
    findOne: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockRepository,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: () => true })
      .compile();

    app = module.createNestApplication();
    await app.init();
    controller = module.get<UsersController>(UsersController);
  });

  afterEach(async () => {
    await app.close();
  });

  describe('GET /users/data/export/pdf', () => {
    it('should be defined', () => {
      expect(controller.exportUserDataAsPDF).toBeDefined();
    });

    it('should call the service method', async () => {
      const mockPdfBuffer = Buffer.from('%PDF-1.4 mock pdf content');
      mockUsersService.exportUserDataAsPDF.mockResolvedValue(mockPdfBuffer);

      const mockRequest = { user: { id: '1' } };
      const mockResponse = {
        set: jest.fn(),
        send: jest.fn(),
      };

      await controller.exportUserDataAsPDF(mockRequest, mockResponse);

      expect(mockUsersService.exportUserDataAsPDF).toHaveBeenCalledWith('1');
      expect(mockResponse.set).toHaveBeenCalledWith({
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename="user-data-export.pdf"',
        'Content-Length': mockPdfBuffer.length,
      });
      expect(mockResponse.send).toHaveBeenCalledWith(mockPdfBuffer);
    });
  });
});
