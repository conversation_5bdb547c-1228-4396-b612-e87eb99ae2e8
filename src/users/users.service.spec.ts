import { Test, TestingModule } from '@nestjs/testing';
import { UsersService } from './users.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { NotFoundException } from '@nestjs/common';

describe('UsersService', () => {
  let service: UsersService;
  let repository: Repository<User>;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
    findOneBy: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getRepositoryToken(User),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    repository = module.get<Repository<User>>(getRepositoryToken(User));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should successfully create a user', async () => {
      const createUserDto = {
        email: '<EMAIL>',
        password: 'testpass',
        firstName: 'Test',
        lastName: 'User',
      };

      const hashedPassword = await bcrypt.hash(createUserDto.password, 10);
      const user = {
        id: '1',
        ...createUserDto,
        password: hashedPassword,
        xp: 0,
        badges: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockRepository.create.mockReturnValue(user);
      mockRepository.save.mockResolvedValue(user);

      const result = await service.create(createUserDto);

      expect(result).toBeDefined();
      expect(result.id).toBe(user.id);
      expect(result.email).toBe(createUserDto.email);
      expect(result.firstName).toBe(createUserDto.firstName);
      expect(result.lastName).toBe(createUserDto.lastName);
      expect(await bcrypt.compare(createUserDto.password, result.password)).toBe(true);
    });

    it('should throw error if email already exists', async () => {
      const createUserDto = {
        email: '<EMAIL>',
        password: 'testpass',
        firstName: 'Test',
        lastName: 'User',
      };

      mockRepository.findOne.mockResolvedValue({ id: '1', ...createUserDto });

      await expect(service.create(createUserDto)).rejects.toThrow();
    });
  });

  describe('findOne', () => {
    it('should return a user if found', async () => {
      const user = {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        xp: 0,
        badges: [],
      };

      mockRepository.findOneBy.mockResolvedValue(user);

      const result = await service.findOne('1');
      expect(result).toBeDefined();
      expect(result.id).toBe(user.id);
    });

    it('should throw NotFoundException if user not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne('1')).rejects.toThrow(NotFoundException);
    });
  });

  describe('findByEmail', () => {
    it('should return a user if email found', async () => {
      const user = {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        xp: 0,
        badges: [],
      };

      mockRepository.findOne.mockResolvedValue(user);

      const result = await service.findByEmail('<EMAIL>');
      expect(result).toBeDefined();
      if (result) {
        expect(result.email).toBe(user.email);
      }
    });

    it('should return null if email not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      const result = await service.findByEmail('<EMAIL>');
      expect(result).toBeNull();
    });
  });
});
