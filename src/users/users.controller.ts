import { Controller, Get, Post, Put, Delete, Body, Param, UseGuards, Request, HttpCode, HttpStatus, Res } from '@nestjs/common';
import { UsersService } from './users.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiCreatedResponse, ApiOkResponse, ApiBody, ApiNoContentResponse } from '@nestjs/swagger';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserResponseDto } from './dto/user-response.dto';
import { DeleteAccountDto } from './dto/delete-account.dto';
import { UserDataExportDto } from './dto/user-data-export.dto';

@ApiTags('users')
@Controller('api/users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new user' })
  @ApiBody({ type: CreateUserDto })
  @ApiCreatedResponse({
    description: 'The user has been successfully created.',
    type: UserResponseDto
  })
  async create(@Body() createUserDto: CreateUserDto) {
    const user = await this.usersService.create(createUserDto);
    // @ts-ignore: Exclude password from response
    delete user.password;
    return user;
  }

  @UseGuards(JwtAuthGuard)
  @Get('profile')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get current user profile' })
  @ApiOkResponse({
    description: 'Returns the current user profile',
    type: UserResponseDto
  })
  async getProfile(@Request() req) {
    const user = await this.usersService.findOne(req.user.id);
    // @ts-ignore: Exclude password from response
    delete user.password;
    return user;
  }

  @UseGuards(JwtAuthGuard)
  @Put('profile')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update current user profile' })
  @ApiBody({ type: UpdateUserDto })
  @ApiOkResponse({
    description: 'Returns the updated user profile',
    type: UserResponseDto
  })
  async updateProfile(@Request() req, @Body() updateDto: UpdateUserDto) {
    const user = await this.usersService.update(req.user.id, updateDto);
    // @ts-ignore: Exclude password from response
    delete user.password;
    return user;
  }

  @UseGuards(JwtAuthGuard)
  @Get(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user by ID' })
  @ApiOkResponse({
    description: 'Returns user by ID',
    type: UserResponseDto
  })
  async findOne(@Param('id') id: string) {
    const user = await this.usersService.findOne(id);
    // @ts-ignore: Exclude password from response
    delete user.password;
    return user;
  }

  @UseGuards(JwtAuthGuard)
  @Delete('account')
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Soft delete user account' })
  @ApiBody({ type: DeleteAccountDto })
  @ApiOkResponse({
    description: 'Account successfully deleted',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Account successfully deleted' }
      }
    }
  })
  async deleteAccount(@Request() req, @Body() deleteAccountDto: DeleteAccountDto) {
    return this.usersService.softDeleteAccount(req.user.id, deleteAccountDto.password);
  }

  @UseGuards(JwtAuthGuard)
  @Get('data/export')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Export user data for GDPR compliance' })
  @ApiOkResponse({
    description: 'Returns all user data for export',
    type: UserDataExportDto
  })
  async exportUserData(@Request() req) {
    return this.usersService.exportUserData(req.user.id);
  }

  @UseGuards(JwtAuthGuard)
  @Get('data/export/pdf')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Export user data as PDF document' })
  @ApiOkResponse({
    description: 'Returns user data as PDF file',
    schema: {
      type: 'string',
      format: 'binary',
    },
    headers: {
      'Content-Type': {
        description: 'PDF file content type',
        schema: { type: 'string', example: 'application/pdf' }
      },
      'Content-Disposition': {
        description: 'File download attachment header',
        schema: { type: 'string', example: 'attachment; filename="user-data-export.pdf"' }
      }
    }
  })
  async exportUserDataAsPDF(@Request() req, @Res() res) {
    const pdfBuffer = await this.usersService.exportUserDataAsPDF(req.user.id);
    
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': 'attachment; filename="user-data-export.pdf"',
      'Content-Length': pdfBuffer.length,
    });
    
    res.send(pdfBuffer);
  }
}
