import { Injectable, ConflictException, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull } from 'typeorm';
import { User, AuthProvider } from './entities/user.entity';
import * as bcrypt from 'bcrypt';
import PDFDocument from 'pdfkit';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
  ) {}

  async create(createUserDto: {
    email: string;
    password?: string;
    firstName?: string;
    lastName?: string;
    name?: string;
    firebaseUid?: string;
    picture?: string;
    provider?: AuthProvider;
  }): Promise<User> {
    const existingUser = await this.usersRepository.findOne({
      where: { email: createUserDto.email, deletedAt: IsNull() },
    });

    if (existingUser) {
      throw new ConflictException('Email already exists');
    }

    // Handle different name formats
    let firstName = createUserDto.firstName;
    let lastName = createUserDto.lastName;
    
    if (!firstName && !lastName && createUserDto.name) {
      const nameParts = createUserDto.name.split(' ');
      firstName = nameParts[0];
      lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';
    }

    // Create user object
    const user = this.usersRepository.create({
      email: createUserDto.email,
      firstName: firstName || createUserDto.email.split('@')[0],
      lastName: lastName || '',
      badges: [],
      xp: 0,
      firebaseUid: createUserDto.firebaseUid,
      picture: createUserDto.picture,
      provider: createUserDto.provider || AuthProvider.LOCAL,
    });

    // Only hash and set password if it's provided (not for social login)
    if (createUserDto.password) {
      user.password = await bcrypt.hash(createUserDto.password, 10);
    }

    return this.usersRepository.save(user);
  }

  async findAll(): Promise<User[]> {
    return this.usersRepository.find({
      relations: ['habits', 'tasks'],
      where: { deletedAt: IsNull() } // Exclude soft-deleted users
    });
  }

  async findOne(id: string): Promise<User> {
    const user = await this.usersRepository.findOne({ 
      where: { id, deletedAt: IsNull() },
      relations: ['habits', 'tasks'],
    });
    
    if (!user) {
      throw new NotFoundException('User not found');
    }
    
    return user;
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.usersRepository.findOne({ 
      where: { email, deletedAt: IsNull() } 
    });
  }
  
  async findOneByEmail(email: string): Promise<User | null> {
    return this.usersRepository.findOne({ 
      where: { email, deletedAt: IsNull() } 
    });
  }
  
  async findByFirebaseUid(firebaseUid: string): Promise<User | null> {
    return this.usersRepository.findOne({ 
      where: { firebaseUid, deletedAt: IsNull() } 
    });
  }

  async update(id: string, updateDto: { firstName?: string; lastName?: string; firebaseUid?: string; picture?: string; isActive?: boolean }): Promise<User> {
    const user = await this.findOne(id);
    Object.assign(user, updateDto);
    return this.usersRepository.save(user);
  }

  async updatePassword(id: string, hashedPassword: string): Promise<User> {
    const user = await this.findOne(id);
    user.password = hashedPassword;
    // Clear reset code when password is updated
    user.resetCode = undefined;
    user.resetCodeExpiresAt = undefined;
    return this.usersRepository.save(user);
  }

  async setResetCode(userId: string, resetCode: string, expiresAt: Date): Promise<User> {
    const user = await this.findOne(userId);
    user.resetCode = resetCode;
    user.resetCodeExpiresAt = expiresAt;
    return this.usersRepository.save(user);
  }

  async findByResetCode(resetCode: string): Promise<User | null> {
    return this.usersRepository.findOne({
      where: { 
        resetCode,
        deletedAt: IsNull()
      },
    });
  }

  async clearResetCode(userId: string): Promise<User> {
    const user = await this.findOne(userId);
    user.resetCode = undefined;
    user.resetCodeExpiresAt = undefined;
    return this.usersRepository.save(user);
  }

  async updateXp(userId: string, xpToAdd: number): Promise<User> {
    const user = await this.findOne(userId);
    user.xp += xpToAdd;
    return this.usersRepository.save(user);
  }

  async addBadge(userId: string, badge: string): Promise<User> {
    const user = await this.findOne(userId);
    if (!user.badges.includes(badge)) {
      user.badges.push(badge);
      return this.usersRepository.save(user);
    }
    return user;
  }

  async softDeleteAccount(userId: string, password: string): Promise<{ message: string }> {
    const user = await this.findOne(userId);
    
    // For social login users (no password), skip password verification
    if (user.provider !== AuthProvider.LOCAL) {
      await this.usersRepository.softDelete(userId);
      return { message: 'Account successfully deleted' };
    }
    
    // For local users, verify password
    if (!user.password) {
      throw new BadRequestException('Password is required for account deletion');
    }
    
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new ForbiddenException('Invalid password');
    }
    
    await this.usersRepository.softDelete(userId);
    return { message: 'Account successfully deleted' };
  }

  async exportUserData(userId: string): Promise<any> {
    const user = await this.usersRepository.findOne({
      where: { id: userId, deletedAt: IsNull() },
      relations: ['habits', 'tasks'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Remove sensitive information
    const { password, ...userProfile } = user;

    // Format habits data
    const habitsData = user.habits?.map(habit => ({
      id: habit.id,
      name: habit.name,
      description: habit.description,
      schedule: habit.schedule,
      currentStreak: habit.currentStreak,
      longestStreak: habit.longestStreak,
      completion: habit.completion,
      reminderSettings: habit.reminderSettings,
      xpReward: habit.xpReward,
      lastCompletedAt: habit.lastCompletedAt,
      createdAt: habit.createdAt,
      updatedAt: habit.updatedAt,
    })) || [];

    // Format tasks data
    const tasksData = user.tasks?.map(task => ({
      id: task.id,
      title: task.title,
      description: task.description,
      completed: task.completed,
      completedAt: task.completedAt,
      priority: task.priority,
      dueDate: task.dueDate,
      reminderSettings: task.reminderSettings,
      notifiedOverdue: task.notifiedOverdue,
      createdAt: task.createdAt,
      updatedAt: task.updatedAt,
    })) || [];

    return {
      profile: userProfile,
      habits: habitsData,
      tasks: tasksData,
      exportedAt: new Date(),
    };
  }

  async exportUserDataAsPDF(userId: string): Promise<Buffer> {
    const user = await this.usersRepository.findOne({
      where: { id: userId, deletedAt: IsNull() },
      relations: ['habits', 'tasks'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return new Promise((resolve, reject) => {
      try {
        const doc = new PDFDocument({ 
          margin: 50,
          size: 'A4',
          bufferPages: true
        });
        const buffers: Buffer[] = [];

        doc.on('data', (buffer) => buffers.push(buffer));
        doc.on('end', () => resolve(Buffer.concat(buffers)));
        doc.on('error', (error) => {
          console.error('PDF generation error:', error);
          reject(error);
        });

        // Header
        doc.fontSize(24).font('Helvetica-Bold').text('Personal Data Export', { align: 'center' });
        doc.fontSize(16).font('Helvetica').text('Power Up Application', { align: 'center' });
        doc.moveDown();
        doc.fontSize(12).font('Helvetica').text(`Generated on: ${new Date().toLocaleDateString('en-US', { 
          year: 'numeric', 
          month: 'long', 
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })}`, { align: 'center' });
        doc.moveDown(2);

        // Add a line separator
        doc.strokeColor('#cccccc')
           .lineWidth(1)
           .moveTo(50, doc.y)
           .lineTo(550, doc.y)
           .stroke();
        doc.moveDown();

        // User Profile Section
        doc.fontSize(18).font('Helvetica-Bold').fillColor('#2C3E50').text('Profile Information');
        doc.moveDown();
        
        const profileData = [
          ['Full Name:', `${user.firstName} ${user.lastName}`],
          ['Email Address:', user.email],
          ['Experience Points:', `${user.xp.toLocaleString()} XP`],
          ['Badges Earned:', user.badges.length > 0 ? user.badges.join(', ') : 'None yet'],
          ['Account Type:', user.provider === AuthProvider.LOCAL ? 'Email/Password' : `Social Login (${user.provider})`],
          ['Member Since:', user.createdAt.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })],
        ];

        doc.fillColor('#000000'); // Reset color for content
        profileData.forEach(([label, value]) => {
          doc.fontSize(11).font('Helvetica-Bold').text(label, { continued: true, width: 150 });
          doc.font('Helvetica').text(` ${value}`, { width: 350 });
        });

        doc.moveDown(2);

        // Habits Section
        doc.fontSize(18).font('Helvetica-Bold').fillColor('#2C3E50').text('Habits & Goals');
        doc.moveDown();

        if (user.habits && user.habits.length > 0) {
          user.habits.forEach((habit, index) => {
            // Add some spacing between habits
            if (index > 0) doc.moveDown(0.5);
            
            doc.fontSize(14).font('Helvetica-Bold').fillColor('#34495E').text(`${index + 1}. ${habit.name}`);
            doc.fillColor('#000000'); // Reset color
            
            if (habit.description) {
              doc.fontSize(11).font('Helvetica').text(`${habit.description}`, { indent: 20 });
            }
            
            // Create a more organized layout
            const habitDetails = [
              `Schedule: ${habit.schedule.join(', ')}`,
              `Current Streak: ${habit.currentStreak} days`,
              `Best Streak: ${habit.longestStreak} days`,
              `XP Reward: ${habit.xpReward} points`,
            ];

            if (habit.lastCompletedAt) {
              habitDetails.push(`Last Completed: ${habit.lastCompletedAt.toLocaleDateString('en-US')}`);
            }
            
            habitDetails.push(`Created: ${habit.createdAt.toLocaleDateString('en-US')}`);
            
            habitDetails.forEach(detail => {
              doc.fontSize(10).font('Helvetica').text(detail, { indent: 20 });
            });
            
            doc.moveDown();
          });
        } else {
          doc.fontSize(12).font('Helvetica').fillColor('#7F8C8D').text('No habits created yet. Start building positive habits today!');
          doc.fillColor('#000000');
          doc.moveDown();
        }

        // Tasks Section
        doc.fontSize(18).font('Helvetica-Bold').text('Tasks');
        doc.moveDown();

        if (user.tasks && user.tasks.length > 0) {
          user.tasks.forEach((task, index) => {
            doc.fontSize(14).font('Helvetica-Bold').text(`${index + 1}. ${task.title}`);
            
            if (task.description) {
              doc.fontSize(12).font('Helvetica').text(`Description: ${task.description}`);
            }
            
            doc.text(`Status: ${task.completed ? 'Completed' : 'Pending'}`);
            doc.text(`Priority: ${task.priority}`);
            doc.text(`Due Date: ${task.dueDate.toLocaleDateString()}`);
            
            if (task.completedAt) {
              doc.text(`Completed: ${task.completedAt.toLocaleDateString()}`);
            }
            
            doc.text(`Created: ${task.createdAt.toLocaleDateString()}`);
            doc.moveDown();
          });
        } else {
          doc.fontSize(12).font('Helvetica').text('No tasks found.');
          doc.moveDown();
        }

        // Footer
        doc.moveDown(2);
        doc.fontSize(10).font('Helvetica').text('This document contains all your personal data as stored in our system.', { align: 'center' });
        doc.text('For privacy and security, please store this document securely.', { align: 'center' });

        doc.end();
      } catch (error) {
        reject(error);
      }
    });
  }
}
