import { ApiProperty } from '@nestjs/swagger';
import { FeedbackType, FeedbackPriority, FeedbackStatus, MobilePlatform } from '../entities/mobile-feedback.entity';

export class MobileFeedbackResponseDto {
  @ApiProperty({
    description: 'Feedback ID',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  id: string;

  @ApiProperty({
    description: 'Type of feedback',
    enum: FeedbackType
  })
  type: FeedbackType;

  @ApiProperty({
    description: 'Feedback title'
  })
  title: string;

  @ApiProperty({
    description: 'Feedback description'
  })
  description: string;

  @ApiProperty({
    description: 'Priority level',
    enum: FeedbackPriority
  })
  priority: FeedbackPriority;

  @ApiProperty({
    description: 'Current status',
    enum: FeedbackStatus
  })
  status: FeedbackStatus;

  @ApiProperty({
    description: 'Mobile platform',
    enum: MobilePlatform,
    required: false
  })
  platform?: MobilePlatform;

  @ApiProperty({
    description: 'App version',
    required: false
  })
  appVersion?: string;

  @ApiProperty({
    description: 'Device model',
    required: false
  })
  deviceModel?: string;

  @ApiProperty({
    description: 'OS version',
    required: false
  })
  osVersion?: string;

  @ApiProperty({
    description: 'User ID',
    required: false
  })
  userId?: string;

  @ApiProperty({
    description: 'User email',
    required: false
  })
  email?: string;

  @ApiProperty({
    description: 'User name',
    required: false
  })
  userName?: string;

  @ApiProperty({
    description: 'Contact back preference'
  })
  contactBack: boolean;

  @ApiProperty({
    description: 'Anonymous feedback'
  })
  anonymous: boolean;

  @ApiProperty({
    description: 'Rating (1-5 stars)',
    required: false
  })
  rating?: number;

  @ApiProperty({
    description: 'Feature context',
    required: false
  })
  featureContext?: string;

  @ApiProperty({
    description: 'Screenshot URLs',
    type: [String],
    required: false
  })
  screenshotUrls?: string[];

  @ApiProperty({
    description: 'Video URL',
    required: false
  })
  videoUrl?: string;

  @ApiProperty({
    description: 'Admin notes',
    required: false
  })
  adminNotes?: string;

  @ApiProperty({
    description: 'Assigned admin',
    required: false
  })
  assignedTo?: string;

  @ApiProperty({
    description: 'Resolution notes',
    required: false
  })
  resolutionNotes?: string;

  @ApiProperty({
    description: 'Tags',
    type: [String],
    required: false
  })
  tags?: string[];

  @ApiProperty({
    description: 'Creation date'
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update date'
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Resolution date',
    required: false
  })
  resolvedAt?: Date;

  @ApiProperty({
    description: 'Due date',
    required: false
  })
  dueDate?: Date;
}
