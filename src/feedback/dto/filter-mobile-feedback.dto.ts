import { <PERSON><PERSON>ption<PERSON>, <PERSON>E<PERSON>, IsString, <PERSON>Int, Min } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { FeedbackType, FeedbackStatus, FeedbackPriority, MobilePlatform } from '../entities/mobile-feedback.entity';

export class FilterMobileFeedbackDto {
  @ApiProperty({
    description: 'Filter by feedback type',
    enum: FeedbackType,
    required: false
  })
  @IsOptional()
  @IsEnum(FeedbackType)
  type?: FeedbackType;

  @ApiProperty({
    description: 'Filter by status',
    enum: FeedbackStatus,
    required: false
  })
  @IsOptional()
  @IsEnum(FeedbackStatus)
  status?: FeedbackStatus;

  @ApiProperty({
    description: 'Filter by priority',
    enum: FeedbackPriority,
    required: false
  })
  @IsOptional()
  @IsEnum(FeedbackPriority)
  priority?: FeedbackPriority;

  @ApiProperty({
    description: 'Filter by platform',
    enum: MobilePlatform,
    required: false
  })
  @IsOptional()
  @IsEnum(MobilePlatform)
  platform?: MobilePlatform;

  @ApiProperty({
    description: 'Filter by assigned admin',
    required: false
  })
  @IsOptional()
  @IsString()
  assignedTo?: string;

  @ApiProperty({
    description: 'Search term for title and description',
    required: false
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Page number for pagination',
    minimum: 1,
    default: 1,
    required: false
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  page?: number;

  @ApiProperty({
    description: 'Number of items per page',
    minimum: 1,
    maximum: 100,
    default: 20,
    required: false
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  limit?: number;

  @ApiProperty({
    description: 'Sort field',
    enum: ['createdAt', 'updatedAt', 'priority', 'status', 'rating'],
    default: 'createdAt',
    required: false
  })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiProperty({
    description: 'Sort direction',
    enum: ['ASC', 'DESC'],
    default: 'DESC',
    required: false
  })
  @IsOptional()
  @IsString()
  sortOrder?: 'ASC' | 'DESC';
}
