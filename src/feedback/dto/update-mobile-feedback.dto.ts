import { IsOptional, IsString, IsEnum, IsBoolean, IsInt, IsDateString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { FeedbackStatus, FeedbackPriority } from '../entities/mobile-feedback.entity';

export class UpdateMobileFeedbackDto {
  @ApiProperty({
    description: 'Feedback status',
    enum: FeedbackStatus,
    required: false
  })
  @IsOptional()
  @IsEnum(FeedbackStatus)
  status?: FeedbackStatus;

  @ApiProperty({
    description: 'Priority level',
    enum: FeedbackPriority,
    required: false
  })
  @IsOptional()
  @IsEnum(FeedbackPriority)
  priority?: FeedbackPriority;

  @ApiProperty({
    description: 'Admin notes',
    required: false
  })
  @IsOptional()
  @IsString()
  adminNotes?: string;

  @ApiProperty({
    description: 'Assigned admin user ID',
    required: false
  })
  @IsOptional()
  @IsString()
  assignedTo?: string;

  @ApiProperty({
    description: 'Resolution notes',
    required: false
  })
  @IsOptional()
  @IsString()
  resolutionNotes?: string;

  @ApiProperty({
    description: 'Estimated hours for completion',
    required: false
  })
  @IsOptional()
  @IsInt()
  estimatedHours?: number;

  @ApiProperty({
    description: 'Actual hours spent',
    required: false
  })
  @IsOptional()
  @IsInt()
  actualHours?: number;

  @ApiProperty({
    description: 'Due date for resolution',
    required: false
  })
  @IsOptional()
  @IsDateString()
  dueDate?: string;

  @ApiProperty({
    description: 'Resolution date',
    required: false
  })
  @IsOptional()
  @IsDateString()
  resolvedAt?: string;
}
