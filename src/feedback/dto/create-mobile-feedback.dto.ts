import { IsEmail, IsNotEmpty, IsOptional, IsString, IsEnum, IsBoolean, IsInt, IsArray, Min, Max, IsUrl } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { FeedbackType, FeedbackPriority, MobilePlatform } from '../entities/mobile-feedback.entity';

export class CreateMobileFeedbackDto {
  @ApiProperty({
    description: 'Type of feedback',
    enum: FeedbackType,
    example: FeedbackType.FEATURE_REQUEST
  })
  @IsEnum(FeedbackType)
  @IsNotEmpty()
  type: FeedbackType;

  @ApiProperty({
    description: 'Title/summary of the feedback',
    example: 'Add dark mode support',
    maxLength: 200
  })
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Detailed description of the feedback',
    example: 'It would be great to have a dark mode option for better usability during night time.'
  })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Priority level of the feedback',
    enum: FeedbackPriority,
    example: FeedbackPriority.MEDIUM,
    required: false
  })
  @IsOptional()
  @IsEnum(FeedbackPriority)
  priority?: FeedbackPriority;

  @ApiProperty({
    description: 'Mobile platform',
    enum: MobilePlatform,
    example: MobilePlatform.IOS,
    required: false
  })
  @IsOptional()
  @IsEnum(MobilePlatform)
  platform?: MobilePlatform;

  @ApiProperty({
    description: 'App version',
    example: '1.2.3',
    required: false
  })
  @IsOptional()
  @IsString()
  appVersion?: string;

  @ApiProperty({
    description: 'Device model',
    example: 'iPhone 14 Pro',
    required: false
  })
  @IsOptional()
  @IsString()
  deviceModel?: string;

  @ApiProperty({
    description: 'Operating system version',
    example: 'iOS 17.0',
    required: false
  })
  @IsOptional()
  @IsString()
  osVersion?: string;

  @ApiProperty({
    description: 'Screen resolution',
    example: '1179x2556',
    required: false
  })
  @IsOptional()
  @IsString()
  screenResolution?: string;

  @ApiProperty({
    description: 'User email for follow-up',
    example: '<EMAIL>',
    required: false
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({
    description: 'User name',
    example: 'John Doe',
    required: false
  })
  @IsOptional()
  @IsString()
  userName?: string;

  @ApiProperty({
    description: 'Whether user wants to be contacted back',
    example: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  contactBack?: boolean;

  @ApiProperty({
    description: 'Whether feedback should be anonymous',
    example: false,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  anonymous?: boolean;

  @ApiProperty({
    description: 'Overall rating (1-5 stars)',
    example: 4,
    minimum: 1,
    maximum: 5,
    required: false
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(5)
  rating?: number;

  @ApiProperty({
    description: 'Feature or screen context',
    example: 'Habits tracking screen',
    required: false
  })
  @IsOptional()
  @IsString()
  featureContext?: string;

  @ApiProperty({
    description: 'Steps to reproduce the issue (for bug reports)',
    example: '1. Open habits screen\n2. Tap on add habit\n3. App crashes',
    required: false
  })
  @IsOptional()
  @IsString()
  reproductionSteps?: string;

  @ApiProperty({
    description: 'Expected behavior (for bug reports)',
    example: 'App should open the add habit form',
    required: false
  })
  @IsOptional()
  @IsString()
  expectedBehavior?: string;

  @ApiProperty({
    description: 'Actual behavior (for bug reports)',
    example: 'App crashes and shows error message',
    required: false
  })
  @IsOptional()
  @IsString()
  actualBehavior?: string;

  @ApiProperty({
    description: 'Screenshot URLs',
    example: ['https://example.com/screenshot1.jpg', 'https://example.com/screenshot2.jpg'],
    required: false,
    type: [String]
  })
  @IsOptional()
  @IsArray()
  @IsUrl({}, { each: true })
  screenshotUrls?: string[];

  @ApiProperty({
    description: 'Video URL for demonstration',
    example: 'https://example.com/video.mp4',
    required: false
  })
  @IsOptional()
  @IsUrl()
  videoUrl?: string;

  @ApiProperty({
    description: 'Tags for categorization',
    example: ['urgent', 'accessibility', 'mobile'],
    required: false,
    type: [String]
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}
