import { 
  <PERSON>, 
  Get, 
  Post, 
  Patch, 
  Delete, 
  Body, 
  Param, 
  Query, 
  Render, 
  Res, 
  UseGuards,
  Redirect 
} from '@nestjs/common';
import type { Response } from 'express';
import { FeedbackService } from './feedback.service';
import { AdminSessionGuard } from '../admin/guards/admin-session.guard';
import { UpdateMobileFeedbackDto, FilterMobileFeedbackDto } from './dto';
import { FeedbackStatus } from './entities/mobile-feedback.entity';

@Controller('admin/feedback')
@UseGuards(AdminSessionGuard)
export class FeedbackAdminWebController {
  constructor(private readonly feedbackService: FeedbackService) {}

  @Get()
  @Render('admin/feedback/index')
  async index(@Query() filters: FilterMobileFeedbackDto) {
    const feedbackData = await this.feedbackService.findAll(filters);
    const stats = await this.feedbackService.getStats();
    
    return {
      title: 'Mobile Feedback Management - Power Up Control Panel',
      ...feedbackData,
      stats,
      filters,
      // Add filter options for template
      statusOptions: Object.values(FeedbackStatus),
      typeOptions: ['bug', 'feature-request', 'improvement', 'ui-ux', 'performance', 'content', 'general'],
      priorityOptions: ['low', 'medium', 'high', 'critical'],
      platformOptions: ['ios', 'android', 'web']
    };
  }

  @Get('stats')
  @Render('admin/feedback/stats')
  async stats() {
    const stats = await this.feedbackService.getStats();
    const priorityStats = await this.feedbackService.getPriorityStats();
    const topFeatureRequests = await this.feedbackService.getTopFeatureRequests(20);
    
    return {
      title: 'Feedback Statistics - Power Up Control Panel',
      stats,
      priorityStats,
      topFeatureRequests
    };
  }

  @Get(':id')
  @Render('admin/feedback/show')
  async show(@Param('id') id: string) {
    const feedback = await this.feedbackService.findOne(id);
    
    return {
      title: `Feedback #${feedback.id.slice(0, 8)} - Power Up Control Panel`,
      feedback,
      statusOptions: Object.values(FeedbackStatus),
      priorityOptions: ['low', 'medium', 'high', 'critical']
    };
  }

  @Get(':id/edit')
  @Render('admin/feedback/edit')
  async edit(@Param('id') id: string) {
    const feedback = await this.feedbackService.findOne(id);
    
    return {
      title: `Edit Feedback #${feedback.id.slice(0, 8)} - Power Up Control Panel`,
      feedback,
      statusOptions: Object.values(FeedbackStatus),
      priorityOptions: ['low', 'medium', 'high', 'critical']
    };
  }

  @Post(':id')
  async update(
    @Param('id') id: string,
    @Body() body: any,
    @Res() res: Response
  ) {
    try {
      const updateDto: UpdateMobileFeedbackDto = {
        status: body.status,
        priority: body.priority,
        adminNotes: body.adminNotes,
        assignedTo: body.assignedTo,
        resolutionNotes: body.resolutionNotes,
        estimatedHours: body.estimatedHours ? parseInt(body.estimatedHours) : undefined,
        actualHours: body.actualHours ? parseInt(body.actualHours) : undefined,
        dueDate: body.dueDate || undefined
      };

      await this.feedbackService.update(id, updateDto);
      res.redirect(`/admin/feedback/${id}?success=Feedback updated successfully`);
    } catch (error) {
      res.redirect(`/admin/feedback/${id}/edit?error=Failed to update feedback: ${error.message}`);
    }
  }

  @Post(':id/assign')
  async assign(
    @Param('id') id: string,
    @Body() body: { adminId: string },
    @Res() res: Response
  ) {
    try {
      await this.feedbackService.assignToAdmin(id, body.adminId);
      res.redirect(`/admin/feedback/${id}?success=Feedback assigned successfully`);
    } catch (error) {
      res.redirect(`/admin/feedback/${id}?error=Failed to assign feedback: ${error.message}`);
    }
  }

  @Post(':id/resolve')
  async resolve(
    @Param('id') id: string,
    @Body() body: { resolutionNotes?: string },
    @Res() res: Response
  ) {
    try {
      await this.feedbackService.markAsResolved(id, body.resolutionNotes);
      res.redirect(`/admin/feedback/${id}?success=Feedback marked as resolved`);
    } catch (error) {
      res.redirect(`/admin/feedback/${id}?error=Failed to resolve feedback: ${error.message}`);
    }
  }

  @Post(':id/delete')
  async delete(@Param('id') id: string, @Res() res: Response) {
    try {
      await this.feedbackService.remove(id);
      res.redirect('/admin/feedback?success=Feedback deleted successfully');
    } catch (error) {
      res.redirect('/admin/feedback?error=Failed to delete feedback');
    }
  }

  @Get('export/csv')
  async exportCsv(@Query() filters: FilterMobileFeedbackDto, @Res() res: Response) {
    try {
      const { data } = await this.feedbackService.findAll({ ...filters, limit: 10000 });
      
      // Create CSV content
      const csvHeaders = [
        'ID', 'Type', 'Title', 'Description', 'Priority', 'Status', 'Platform',
        'App Version', 'Device Model', 'OS Version', 'User Email', 'User Name',
        'Rating', 'Feature Context', 'Created At', 'Updated At', 'Resolved At',
        'Admin Notes', 'Assigned To'
      ];
      
      const csvRows = data.map(feedback => [
        feedback.id,
        feedback.type,
        `"${feedback.title.replace(/"/g, '""')}"`,
        `"${feedback.description.replace(/"/g, '""')}"`,
        feedback.priority,
        feedback.status,
        feedback.platform || '',
        feedback.appVersion || '',
        feedback.deviceModel || '',
        feedback.osVersion || '',
        feedback.email || '',
        feedback.userName || '',
        feedback.rating || '',
        feedback.featureContext || '',
        feedback.createdAt,
        feedback.updatedAt,
        feedback.resolvedAt || '',
        feedback.adminNotes ? `"${feedback.adminNotes.replace(/"/g, '""')}"` : '',
        feedback.assignedTo || ''
      ]);
      
      const csvContent = [csvHeaders.join(','), ...csvRows.map(row => row.join(','))].join('\n');
      
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="mobile-feedback-${new Date().toISOString().split('T')[0]}.csv"`);
      res.send(csvContent);
    } catch (error) {
      res.redirect('/admin/feedback?error=Failed to export feedback data');
    }
  }
}
