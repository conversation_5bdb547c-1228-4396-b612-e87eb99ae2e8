import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Patch, 
  Param, 
  Delete, 
  Query, 
  UseGuards, 
  Request,
  ParseUUIDPipe,
  HttpCode,
  HttpStatus
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBearerAuth,
  ApiBody,
  ApiParam,
  ApiQuery
} from '@nestjs/swagger';
import { FeedbackService } from './feedback.service';
import { CreateMobileFeedbackDto, UpdateMobileFeedbackDto, FilterMobileFeedbackDto, MobileFeedbackResponseDto } from './dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('Mobile Feedback')
@Controller('api/feedback')
export class FeedbackController {
  constructor(private readonly feedbackService: FeedbackService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Submit mobile app feedback' })
  @ApiResponse({ 
    status: 201, 
    description: 'Feedback submitted successfully',
    type: MobileFeedbackResponseDto
  })
  @ApiBody({ type: CreateMobileFeedbackDto })
  async create(@Body() createDto: CreateMobileFeedbackDto, @Request() req) {
    const userId = req.user?.id;
    const feedback = await this.feedbackService.create(createDto, userId);
    
    return {
      message: 'Thank you for your feedback! We appreciate your input and will review it soon.',
      feedback
    };
  }

  @Post('anonymous')
  @ApiOperation({ summary: 'Submit anonymous feedback (no authentication required)' })
  @ApiResponse({ 
    status: 201, 
    description: 'Anonymous feedback submitted successfully',
    type: MobileFeedbackResponseDto
  })
  @ApiBody({ type: CreateMobileFeedbackDto })
  async createAnonymous(@Body() createDto: CreateMobileFeedbackDto) {
    // Force anonymous flag to true for this endpoint
    const anonymousDto = { ...createDto, anonymous: true };
    const feedback = await this.feedbackService.create(anonymousDto);
    
    return {
      message: 'Thank you for your anonymous feedback!',
      feedback
    };
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all feedback (Admin only)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns paginated feedback list',
    type: [MobileFeedbackResponseDto]
  })
  @ApiQuery({ name: 'type', required: false, description: 'Filter by feedback type' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by status' })
  @ApiQuery({ name: 'priority', required: false, description: 'Filter by priority' })
  @ApiQuery({ name: 'platform', required: false, description: 'Filter by platform' })
  @ApiQuery({ name: 'search', required: false, description: 'Search in title and description' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  async findAll(@Query() filters: FilterMobileFeedbackDto) {
    return await this.feedbackService.findAll(filters);
  }

  @Get('my-feedback')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get current user\'s feedback' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns user\'s feedback list',
    type: [MobileFeedbackResponseDto]
  })
  async findMyFeedback(@Request() req, @Query() filters: FilterMobileFeedbackDto) {
    const userId = req.user.id;
    return await this.feedbackService.findByUser(userId, filters);
  }

  @Get('stats')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get feedback statistics (Admin only)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns feedback statistics'
  })
  async getStats() {
    return await this.feedbackService.getStats();
  }

  @Get('priority-stats')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get priority distribution statistics (Admin only)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns priority statistics'
  })
  async getPriorityStats() {
    return await this.feedbackService.getPriorityStats();
  }

  @Get('top-feature-requests')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get top feature requests (Admin only)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns top feature requests',
    type: [MobileFeedbackResponseDto]
  })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of feature requests to return' })
  async getTopFeatureRequests(@Query('limit') limit?: string) {
    const limitNum = limit ? parseInt(limit) : 10;
    return await this.feedbackService.getTopFeatureRequests(limitNum);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get feedback by ID' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns feedback details',
    type: MobileFeedbackResponseDto
  })
  @ApiParam({ name: 'id', description: 'Feedback ID' })
  async findOne(@Param('id', ParseUUIDPipe) id: string) {
    return await this.feedbackService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update feedback (Admin only)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Feedback updated successfully',
    type: MobileFeedbackResponseDto
  })
  @ApiParam({ name: 'id', description: 'Feedback ID' })
  @ApiBody({ type: UpdateMobileFeedbackDto })
  async update(
    @Param('id', ParseUUIDPipe) id: string, 
    @Body() updateDto: UpdateMobileFeedbackDto
  ) {
    return await this.feedbackService.update(id, updateDto);
  }

  @Patch(':id/resolve')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mark feedback as resolved (Admin only)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Feedback marked as resolved',
    type: MobileFeedbackResponseDto
  })
  @ApiParam({ name: 'id', description: 'Feedback ID' })
  @ApiBody({ 
    schema: { 
      type: 'object', 
      properties: { 
        resolutionNotes: { type: 'string', description: 'Optional resolution notes' } 
      } 
    } 
  })
  async markAsResolved(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: { resolutionNotes?: string }
  ) {
    return await this.feedbackService.markAsResolved(id, body.resolutionNotes);
  }

  @Patch(':id/assign')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Assign feedback to admin (Admin only)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Feedback assigned successfully',
    type: MobileFeedbackResponseDto
  })
  @ApiParam({ name: 'id', description: 'Feedback ID' })
  @ApiBody({ 
    schema: { 
      type: 'object', 
      properties: { 
        adminId: { type: 'string', description: 'Admin user ID' } 
      },
      required: ['adminId']
    } 
  })
  async assignToAdmin(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: { adminId: string }
  ) {
    return await this.feedbackService.assignToAdmin(id, body.adminId);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete feedback (Admin only)' })
  @ApiResponse({ 
    status: 204, 
    description: 'Feedback deleted successfully'
  })
  @ApiParam({ name: 'id', description: 'Feedback ID' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id', ParseUUIDPipe) id: string) {
    await this.feedbackService.remove(id);
  }
}
