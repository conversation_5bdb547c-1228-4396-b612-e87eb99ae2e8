import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FeedbackService } from './feedback.service';
import { FeedbackController } from './feedback.controller';
import { FeedbackAdminWebController } from './feedback-admin-web.controller';
import { MobileFeedback } from './entities/mobile-feedback.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([MobileFeedback])
  ],
  controllers: [FeedbackController, FeedbackAdminWebController],
  providers: [FeedbackService],
  exports: [FeedbackService],
})
export class FeedbackModule {}
