import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, ILike } from 'typeorm';
import { MobileFeedback, FeedbackStatus, FeedbackType } from './entities/mobile-feedback.entity';
import { CreateMobileFeedbackDto, UpdateMobileFeedbackDto, FilterMobileFeedbackDto } from './dto';

@Injectable()
export class FeedbackService {
  constructor(
    @InjectRepository(MobileFeedback)
    private mobileFeedbackRepository: Repository<MobileFeedback>,
  ) {}

  async create(createDto: CreateMobileFeedbackDto, userId?: string): Promise<MobileFeedback> {
    const feedbackData = {
      ...createDto,
      userId: createDto.anonymous ? undefined : userId,
    };

    const feedback = this.mobileFeedbackRepository.create(feedbackData);
    return await this.mobileFeedbackRepository.save(feedback);
  }

  async findAll(filters: FilterMobileFeedbackDto = {}): Promise<{
    data: MobileFeedback[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const {
      type,
      status,
      priority,
      platform,
      assignedTo,
      search,
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'DESC'
    } = filters;

    const queryBuilder = this.mobileFeedbackRepository.createQueryBuilder('feedback')
      .leftJoinAndSelect('feedback.user', 'user');

    // Apply filters
    if (type) {
      queryBuilder.andWhere('feedback.type = :type', { type });
    }

    if (status) {
      queryBuilder.andWhere('feedback.status = :status', { status });
    }

    if (priority) {
      queryBuilder.andWhere('feedback.priority = :priority', { priority });
    }

    if (platform) {
      queryBuilder.andWhere('feedback.platform = :platform', { platform });
    }

    if (assignedTo) {
      queryBuilder.andWhere('feedback.assignedTo = :assignedTo', { assignedTo });
    }

    if (search) {
      queryBuilder.andWhere(
        '(feedback.title ILIKE :search OR feedback.description ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Apply sorting
    const validSortFields = ['createdAt', 'updatedAt', 'priority', 'status', 'rating'];
    if (validSortFields.includes(sortBy)) {
      queryBuilder.orderBy(`feedback.${sortBy}`, sortOrder);
    } else {
      queryBuilder.orderBy('feedback.createdAt', 'DESC');
    }

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();
    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      totalPages
    };
  }

  async findOne(id: string): Promise<MobileFeedback> {
    const feedback = await this.mobileFeedbackRepository.findOne({
      where: { id },
      relations: ['user']
    });

    if (!feedback) {
      throw new NotFoundException('Feedback not found');
    }

    return feedback;
  }

  async findByUser(userId: string, filters: FilterMobileFeedbackDto = {}): Promise<{
    data: MobileFeedback[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const { page = 1, limit = 20, ...otherFilters } = filters;

    return this.findAll({
      ...otherFilters,
      page,
      limit
    });
  }

  async update(id: string, updateDto: UpdateMobileFeedbackDto): Promise<MobileFeedback> {
    const feedback = await this.findOne(id);

    // Auto-set resolved date when status changes to completed
    if (updateDto.status === FeedbackStatus.COMPLETED && feedback.status !== FeedbackStatus.COMPLETED) {
      updateDto.resolvedAt = new Date().toISOString();
    }

    Object.assign(feedback, updateDto);
    return await this.mobileFeedbackRepository.save(feedback);
  }

  async remove(id: string): Promise<void> {
    const feedback = await this.findOne(id);
    await this.mobileFeedbackRepository.remove(feedback);
  }

  async getStats(): Promise<{
    total: number;
    byStatus: Record<string, number>;
    byType: Record<string, number>;
    byPriority: Record<string, number>;
    byPlatform: Record<string, number>;
    averageRating: number;
    recentCount: number;
  }> {
    const total = await this.mobileFeedbackRepository.count();

    // Get counts by status
    const byStatus = await this.mobileFeedbackRepository
      .createQueryBuilder('feedback')
      .select('feedback.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('feedback.status')
      .getRawMany();

    // Get counts by type
    const byType = await this.mobileFeedbackRepository
      .createQueryBuilder('feedback')
      .select('feedback.type', 'type')
      .addSelect('COUNT(*)', 'count')
      .groupBy('feedback.type')
      .getRawMany();

    // Get counts by priority
    const byPriority = await this.mobileFeedbackRepository
      .createQueryBuilder('feedback')
      .select('feedback.priority', 'priority')
      .addSelect('COUNT(*)', 'count')
      .groupBy('feedback.priority')
      .getRawMany();

    // Get counts by platform
    const byPlatform = await this.mobileFeedbackRepository
      .createQueryBuilder('feedback')
      .select('feedback.platform', 'platform')
      .addSelect('COUNT(*)', 'count')
      .where('feedback.platform IS NOT NULL')
      .groupBy('feedback.platform')
      .getRawMany();

    // Get average rating
    const avgRating = await this.mobileFeedbackRepository
      .createQueryBuilder('feedback')
      .select('AVG(feedback.rating)', 'average')
      .where('feedback.rating IS NOT NULL')
      .getRawOne();

    // Get recent feedback count (last 7 days)
    const recentDate = new Date();
    recentDate.setDate(recentDate.getDate() - 7);
    
    const recentCount = await this.mobileFeedbackRepository
      .createQueryBuilder('feedback')
      .where('feedback.createdAt >= :date', { date: recentDate })
      .getCount();

    // Transform results to objects
    const statusCounts = byStatus.reduce((acc, item) => {
      acc[item.status] = parseInt(item.count);
      return acc;
    }, {});

    const typeCounts = byType.reduce((acc, item) => {
      acc[item.type] = parseInt(item.count);
      return acc;
    }, {});

    const priorityCounts = byPriority.reduce((acc, item) => {
      acc[item.priority] = parseInt(item.count);
      return acc;
    }, {});

    const platformCounts = byPlatform.reduce((acc, item) => {
      acc[item.platform] = parseInt(item.count);
      return acc;
    }, {});

    return {
      total,
      byStatus: statusCounts,
      byType: typeCounts,
      byPriority: priorityCounts,
      byPlatform: platformCounts,
      averageRating: parseFloat(avgRating?.average || '0'),
      recentCount
    };
  }

  async markAsResolved(id: string, resolutionNotes?: string): Promise<MobileFeedback> {
    const feedback = await this.findOne(id);
    
    feedback.status = FeedbackStatus.COMPLETED;
    feedback.resolvedAt = new Date();
    
    if (resolutionNotes) {
      feedback.resolutionNotes = resolutionNotes;
    }

    return await this.mobileFeedbackRepository.save(feedback);
  }

  async assignToAdmin(id: string, adminId: string): Promise<MobileFeedback> {
    const feedback = await this.findOne(id);
    feedback.assignedTo = adminId;
    feedback.status = FeedbackStatus.REVIEWING;

    return await this.mobileFeedbackRepository.save(feedback);
  }

  async getPriorityStats(): Promise<{ priority: string; count: number; percentage: number }[]> {
    const total = await this.mobileFeedbackRepository.count();
    
    if (total === 0) {
      return [];
    }

    const priorityStats = await this.mobileFeedbackRepository
      .createQueryBuilder('feedback')
      .select('feedback.priority', 'priority')
      .addSelect('COUNT(*)', 'count')
      .groupBy('feedback.priority')
      .orderBy('COUNT(*)', 'DESC')
      .getRawMany();

    return priorityStats.map(stat => ({
      priority: stat.priority,
      count: parseInt(stat.count),
      percentage: Math.round((parseInt(stat.count) / total) * 100)
    }));
  }

  async getTopFeatureRequests(limit: number = 10): Promise<MobileFeedback[]> {
    return await this.mobileFeedbackRepository.find({
      where: { type: FeedbackType.FEATURE_REQUEST },
      order: { createdAt: 'DESC' },
      take: limit,
      relations: ['user']
    });
  }
}
