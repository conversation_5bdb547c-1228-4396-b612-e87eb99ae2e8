import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';

export enum FeedbackType {
  BUG = 'bug',
  FEATURE_REQUEST = 'feature-request',
  IMPROVEMENT = 'improvement',
  UI_UX = 'ui-ux',
  PERFORMANCE = 'performance',
  CONTENT = 'content',
  GENERAL = 'general'
}

export enum FeedbackPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum FeedbackStatus {
  PENDING = 'pending',
  REVIEWING = 'reviewing',
  IN_PROGRESS = 'in-progress',
  COMPLETED = 'completed',
  REJECTED = 'rejected',
  DUPLICATE = 'duplicate'
}

export enum MobilePlatform {
  IOS = 'ios',
  ANDROID = 'android',
  WEB = 'web'
}

@Entity('mobile_feedback')
export class MobileFeedback {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ 
    type: 'enum', 
    enum: FeedbackType,
    default: FeedbackType.GENERAL
  })
  type: FeedbackType;

  @Column({ length: 200 })
  title: string;

  @Column('text')
  description: string;

  @Column({ 
    type: 'enum', 
    enum: FeedbackPriority,
    default: FeedbackPriority.MEDIUM
  })
  priority: FeedbackPriority;

  @Column({ 
    type: 'enum', 
    enum: FeedbackStatus,
    default: FeedbackStatus.PENDING
  })
  status: FeedbackStatus;

  @Column({ 
    type: 'enum', 
    enum: MobilePlatform,
    nullable: true
  })
  platform?: MobilePlatform;

  @Column({ name: 'app_version', nullable: true, length: 20 })
  appVersion?: string;

  @Column({ name: 'device_model', nullable: true, length: 100 })
  deviceModel?: string;

  @Column({ name: 'os_version', nullable: true, length: 50 })
  osVersion?: string;

  @Column({ name: 'screen_resolution', nullable: true, length: 20 })
  screenResolution?: string;

  @Column({ name: 'user_id', nullable: true })
  userId?: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'user_id' })
  user?: User;

  @Column({ nullable: true, length: 100 })
  email?: string;

  @Column({ name: 'user_name', nullable: true, length: 100 })
  userName?: string;

  @Column({ name: 'contact_back', default: false })
  contactBack: boolean;

  @Column({ name: 'anonymous', default: false })
  anonymous: boolean;

  @Column({ type: 'int', default: 0 })
  rating?: number; // 1-5 star rating

  @Column({ name: 'feature_context', nullable: true, length: 200 })
  featureContext?: string; // Which feature/screen the feedback is about

  @Column({ name: 'reproduction_steps', type: 'text', nullable: true })
  reproductionSteps?: string; // For bug reports

  @Column({ name: 'expected_behavior', type: 'text', nullable: true })
  expectedBehavior?: string; // For bug reports

  @Column({ name: 'actual_behavior', type: 'text', nullable: true })
  actualBehavior?: string; // For bug reports

  @Column({ name: 'screenshot_urls', type: 'json', nullable: true })
  screenshotUrls?: string[]; // Array of screenshot URLs

  @Column({ name: 'video_url', nullable: true })
  videoUrl?: string;

  @Column({ name: 'admin_notes', type: 'text', nullable: true })
  adminNotes?: string;

  @Column({ name: 'assigned_to', nullable: true })
  assignedTo?: string;

  @Column({ name: 'resolution_notes', type: 'text', nullable: true })
  resolutionNotes?: string;

  @Column({ name: 'estimated_hours', type: 'int', nullable: true })
  estimatedHours?: number;

  @Column({ name: 'actual_hours', type: 'int', nullable: true })
  actualHours?: number;

  @Column({ name: 'tags', type: 'json', nullable: true })
  tags?: string[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'resolved_at', nullable: true })
  resolvedAt?: Date;

  @Column({ name: 'due_date', nullable: true })
  dueDate?: Date;
}
