import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FeedbackService } from './feedback.service';
import { MobileFeedback, FeedbackType, FeedbackPriority, FeedbackStatus, MobilePlatform } from './entities/mobile-feedback.entity';

describe('FeedbackService', () => {
  let service: FeedbackService;
  let repository: Repository<MobileFeedback>;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
    find: jest.fn(),
    createQueryBuilder: jest.fn(),
    count: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FeedbackService,
        {
          provide: getRepositoryToken(MobileFeedback),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<FeedbackService>(FeedbackService);
    repository = module.get<Repository<MobileFeedback>>(getRepositoryToken(MobileFeedback));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create feedback successfully', async () => {
      const createDto = {
        type: FeedbackType.BUG,
        title: 'App crashes on startup',
        description: 'The app crashes immediately when opened',
        priority: FeedbackPriority.HIGH,
        platform: MobilePlatform.IOS,
        appVersion: '1.0.0',
        deviceModel: 'iPhone 14',
        osVersion: 'iOS 16.0'
      };

      const mockFeedback = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        ...createDto,
        status: FeedbackStatus.PENDING,
        contactBack: false,
        anonymous: false,
        rating: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockRepository.create.mockReturnValue(mockFeedback);
      mockRepository.save.mockResolvedValue(mockFeedback);

      const result = await service.create(createDto, 'user123');

      expect(mockRepository.create).toHaveBeenCalledWith({
        ...createDto,
        userId: 'user123'
      });
      expect(mockRepository.save).toHaveBeenCalledWith(mockFeedback);
      expect(result).toEqual(mockFeedback);
    });

    it('should create anonymous feedback', async () => {
      const createDto = {
        type: FeedbackType.FEATURE_REQUEST,
        title: 'Add dark mode',
        description: 'Please add dark mode support',
        anonymous: true
      };

      const mockFeedback = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        ...createDto,
        status: FeedbackStatus.PENDING,
        contactBack: false,
        rating: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockRepository.create.mockReturnValue(mockFeedback);
      mockRepository.save.mockResolvedValue(mockFeedback);

      const result = await service.create(createDto);

      expect(mockRepository.create).toHaveBeenCalledWith({
        ...createDto,
        userId: undefined
      });
      expect(result).toEqual(mockFeedback);
    });
  });

  describe('findOne', () => {
    it('should return feedback if found', async () => {
      const mockFeedback = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        type: FeedbackType.BUG,
        title: 'Test feedback',
        description: 'Test description',
        status: FeedbackStatus.PENDING
      };

      mockRepository.findOne.mockResolvedValue(mockFeedback);

      const result = await service.findOne('123e4567-e89b-12d3-a456-426614174000');

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { id: '123e4567-e89b-12d3-a456-426614174000' },
        relations: ['user']
      });
      expect(result).toEqual(mockFeedback);
    });

    it('should throw NotFoundException if feedback not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne('non-existent-id')).rejects.toThrow('Feedback not found');
    });
  });

  describe('markAsResolved', () => {
    it('should mark feedback as resolved', async () => {
      const mockFeedback = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        status: FeedbackStatus.PENDING,
        resolvedAt: null
      };

      const resolvedFeedback = {
        ...mockFeedback,
        status: FeedbackStatus.COMPLETED,
        resolvedAt: expect.any(Date),
        resolutionNotes: 'Issue fixed in latest update'
      };

      mockRepository.findOne.mockResolvedValue(mockFeedback);
      mockRepository.save.mockResolvedValue(resolvedFeedback);

      const result = await service.markAsResolved('123e4567-e89b-12d3-a456-426614174000', 'Issue fixed in latest update');

      expect(result.status).toBe(FeedbackStatus.COMPLETED);
      expect(result.resolvedAt).toBeInstanceOf(Date);
      expect(result.resolutionNotes).toBe('Issue fixed in latest update');
    });
  });

  describe('assignToAdmin', () => {
    it('should assign feedback to admin', async () => {
      const mockFeedback = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        status: FeedbackStatus.PENDING,
        assignedTo: null
      };

      const assignedFeedback = {
        ...mockFeedback,
        status: FeedbackStatus.REVIEWING,
        assignedTo: 'admin123'
      };

      mockRepository.findOne.mockResolvedValue(mockFeedback);
      mockRepository.save.mockResolvedValue(assignedFeedback);

      const result = await service.assignToAdmin('123e4567-e89b-12d3-a456-426614174000', 'admin123');

      expect(result.assignedTo).toBe('admin123');
      expect(result.status).toBe(FeedbackStatus.REVIEWING);
    });
  });
});
