import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AnalyticsService } from './analytics.service';
import { UserProgress } from './entities/user-progress.entity';
import { UsersService } from '../users/users.service';
import { HabitsService } from '../habits/habits.service';
import { TasksService } from '../tasks/tasks.service';
import { SkillPlansService } from '../skill-plans/skill-plans.service';
import { ChallengesService } from '../challenges/challenges.service';

describe('AnalyticsService', () => {
  let service: AnalyticsService;
  let userProgressRepository: Repository<UserProgress>;

  const mockUserProgressRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
  };

  const mockUsersService = {
    findAll: jest.fn(),
    findOne: jest.fn(),
  };

  const mockHabitsService = {
    findAll: jest.fn(),
    findAllByUser: jest.fn(),
  };

  const mockTasksService = {
    findAll: jest.fn(),
  };

  const mockSkillPlansService = {
    findAll: jest.fn(),
  };

  const mockChallengesService = {
    findAll: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AnalyticsService,
        {
          provide: getRepositoryToken(UserProgress),
          useValue: mockUserProgressRepository,
        },
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: HabitsService,
          useValue: mockHabitsService,
        },
        {
          provide: TasksService,
          useValue: mockTasksService,
        },
        {
          provide: SkillPlansService,
          useValue: mockSkillPlansService,
        },
        {
          provide: ChallengesService,
          useValue: mockChallengesService,
        },
      ],
    }).compile();

    service = module.get<AnalyticsService>(AnalyticsService);
    userProgressRepository = module.get<Repository<UserProgress>>(
      getRepositoryToken(UserProgress),
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getUserProgress', () => {
    const userId = '1';
    const testDate = new Date();
    const mockUser = {
      id: userId,
      xp: 100,
      // other user fields as needed
    };
    const mockProgress = [
      {
        date: testDate,
        habits: { completed: 3, total: 5, streaks: { '1': 2 } },
        tasks: { completed: 4, total: 6, overdue: 1 },
        challenges: { active: 2, completed: 1 },
        skillPlans: { active: 1, completedSteps: 3, totalSteps: 5 },
        metrics: {
          focusMinutes: 120,
          consistencyScore: 85,
          podcastsListened: 2,
          productiveHours: [],
          moodTracking: []
        },
        badgesEarned: [],
      },
    ];

    beforeEach(() => {
      mockUserProgressRepository.find.mockResolvedValue(mockProgress);
      mockUsersService.findOne.mockResolvedValue(mockUser);
    });

    it('should return user progress for the specified period', async () => {
      const result = await service.getUserProgress(userId, 'week');

      expect(result).toBeDefined();
      expect(result.habits).toBeDefined();
      expect(result.period).toBe('week');
      expect(result.currentXp).toBe(mockUser.xp);
    });
  });

  describe('getHabitAnalytics', () => {
    const userId = '1';
    const habits = [
      { id: '1', name: 'Exercise', currentStreak: 5 },
      { id: '2', name: 'Reading', currentStreak: 3 },
    ];
    const mockProgress = [
      {
        habits: { streaks: { '1': 5, '2': 3 } },
        date: new Date(),
      },
    ];

    beforeEach(() => {
      mockHabitsService.findAllByUser.mockResolvedValue(habits);
      mockUserProgressRepository.find.mockResolvedValue(mockProgress);
    });

    it('should return habit analytics', async () => {
      const result = await service.getHabitAnalytics(userId, 'week');

      expect(result).toBeDefined();
      expect(result.completionRate).toBeDefined();
      expect(result.byHabit).toBeDefined();
      expect(result.distribution).toBeDefined();
    });
  });

  describe('recordFocusSession', () => {
    const userId = '1';
    const minutes = 30;
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const mockUser = { id: userId, xp: 100 };
    const mockProgress = {
      user: mockUser,
      metrics: { 
        focusMinutes: 60,
        productiveHours: new Array(24).fill(0),
        consistencyScore: 85,
        podcastsListened: 0,
        moodTracking: []
      },
      date: today,
      habits: { completed: 0, total: 0, streaks: {} },
      tasks: { completed: 0, total: 0, overdue: 0 },
      skillPlans: { active: 0, completedSteps: 0, totalSteps: 0 },
      challenges: { active: 0, completed: 0 },
      xpGained: 0,
      totalXp: 100,
      badgesEarned: [],
    };

    beforeEach(() => {
      mockUserProgressRepository.findOne.mockResolvedValue(mockProgress);
      mockUserProgressRepository.save.mockResolvedValue({
        ...mockProgress,
        metrics: { 
          ...mockProgress.metrics,
          focusMinutes: 90,
        },
      });
    });

    it('should update focus minutes for the day', async () => {
      await service.recordFocusSession(userId, minutes);

      expect(mockUserProgressRepository.findOne).toHaveBeenCalled();
      expect(mockUserProgressRepository.save).toHaveBeenCalledWith({
        ...mockProgress,
        metrics: { 
          ...mockProgress.metrics,
          focusMinutes: 90,
        },
      });
    });
  });

  describe('getProductivityAnalytics', () => {
    const userId = '1';
    const mockProgress = [
      {
        date: new Date(),
        metrics: {
          focusMinutes: 120,
          consistencyScore: 85,
          productiveHours: new Array(24).fill(0),
        },
        tasks: { completed: 5, total: 7 },
      },
    ];

    beforeEach(() => {
      mockUserProgressRepository.find.mockResolvedValue(mockProgress);
    });

    it('should return productivity analytics', async () => {
      const result = await service.getProductivityAnalytics(userId, 'week');

      expect(result).toBeDefined();
      expect(result.mostProductiveTime).toBeDefined();
      expect(result.mostProductiveDay).toBeDefined();
      expect(result.focusTimeStats).toBeDefined();
    });
  });
});
