# Analytics Module Refactoring

This document describes the refactored analytics module structure that splits the monolithic `AnalyticsService` into multiple focused, maintainable classes.

## Architecture Overview

The analytics module has been refactored into the following specialized services:

### 1. ProgressTrackerService (`services/progress-tracker.service.ts`)
**Responsibility**: Handles daily progress tracking and data updates
- Daily progress tracking cron job
- Focus session recording
- Mood entry logging
- User progress updates
- Daily productivity tracking

**Key Methods**:
- `trackDailyProgress()` - Cron job that runs at midnight
- `updateDailyProgress()` - Updates daily metrics
- `recordFocusSession()` - Records focus time
- `recordMoodEntry()` - Logs mood data
- `updateUserProgress()` - Updates skill plans, XP, badges

### 2. AnalyticsCalculatorService (`services/analytics-calculator.service.ts`)
**Responsibility**: Handles calculations, metrics, and statistical analysis
- Progress summary calculations
- Habit analytics
- Task completion statistics
- Productivity analytics
- Mood analytics
- Weekly aggregation

**Key Methods**:
- `getUserProgress()` - Gets comprehensive user progress data
- `getHabitAnalytics()` - Analyzes habit performance
- `getTaskCompletionStats()` - Task completion analysis
- `getProductivityAnalytics()` - Productivity patterns
- `getMoodAnalytics()` - Mood trends and correlations
- `aggregateWeeklyStats()` - Weekly performance summary

### 3. InsightsGeneratorService (`services/insights-generator.service.ts`)
**Responsibility**: Generates AI-powered insights and recommendations
- Personalized insights generation
- AI-powered improvement reports
- Personality pattern analysis
- Behavioral recommendations
- Motivational messaging

**Key Methods**:
- `getPersonalizedInsights()` - Generates user-specific insights
- `getAIImprovementReport()` - AI-powered comprehensive analysis
- `generateAIInsights()` - Uses Google GenAI for advanced analysis
- `generateTraditionalInsights()` - Fallback analysis without AI

### 4. ReportGeneratorService (`services/report-generator.service.ts`)
**Responsibility**: Generates various types of reports and summaries
- Progress reports
- Habit reports
- Productivity reports
- Comprehensive reports
- Executive summaries

**Key Methods**:
- `generateProgressReport()` - Overall progress report
- `generateHabitReport()` - Habit-focused analysis
- `generateProductivityReport()` - Productivity insights
- `generateComprehensiveReport()` - All-in-one report
- `generateAIImprovementReport()` - AI-enhanced improvement report

### 5. AnalyticsService (`analytics.service.ts`)
**Responsibility**: Main orchestrator service that delegates to specialized services
- Maintains backward compatibility
- Provides a single interface for all analytics functionality
- Delegates calls to appropriate specialized services

## Benefits of the Refactored Architecture

### 1. **Single Responsibility Principle**
Each service has a clear, focused responsibility:
- Progress tracking is separate from calculations
- AI insights are isolated from basic analytics
- Report generation is its own concern

### 2. **Improved Maintainability**
- Smaller, focused classes are easier to understand and modify
- Changes to one concern don't affect others
- Testing is more granular and specific

### 3. **Better Scalability**
- Services can be scaled independently
- New analytics features can be added without modifying existing code
- AI features are isolated and can be enhanced separately

### 4. **Enhanced Testability**
- Each service can be unit tested in isolation
- Mock dependencies are easier to manage
- Test coverage is more targeted

### 5. **Dependency Management**
- Clear separation of concerns reduces coupling
- Services can be reused in different contexts
- Dependencies are more explicit and manageable

## Usage Examples

### Basic Analytics
```typescript
// Get user progress for the last month
const progress = await analyticsService.getUserProgress(userId, 'month');

// Get habit analytics
const habitStats = await analyticsService.getHabitAnalytics(userId, 'week');
```

### Progress Tracking
```typescript
// Record a focus session
await analyticsService.recordFocusSession(userId, 60);

// Log mood entry
await analyticsService.recordMoodEntry(userId, 'great');
```

### Report Generation
```typescript
// Generate comprehensive report
const report = await analyticsService.generateComprehensiveReport(userId);

// Generate AI-powered improvement report
const aiReport = await analyticsService.getAIImprovementReport(userId, 30);
```

### Direct Service Usage
```typescript
// If you need direct access to specialized functionality
constructor(
  private progressTracker: ProgressTrackerService,
  private insightsGenerator: InsightsGeneratorService,
) {}

// Use specialized services directly
const insights = await this.insightsGenerator.getPersonalizedInsights(userId);
```

## Migration Notes

The refactored `AnalyticsService` maintains backward compatibility with the original interface. All existing code using the analytics service should continue to work without modification.

If you need to access the specialized services directly, they are exported from the `AnalyticsModule` and can be injected independently.

## File Structure

```
src/analytics/
├── analytics.service.ts                    # Main orchestrator service
├── analytics.module.ts                     # Module definition
├── analytics.controller.ts                 # REST API endpoints
├── entities/
│   └── user-progress.entity.ts            # Database entity
├── services/
│   ├── index.ts                           # Service exports
│   ├── progress-tracker.service.ts       # Progress tracking
│   ├── analytics-calculator.service.ts   # Calculations & metrics
│   ├── insights-generator.service.ts     # AI insights
│   └── report-generator.service.ts       # Report generation
└── dto/                                   # Data transfer objects
```

## Future Enhancements

With this modular architecture, future enhancements can be easily added:

1. **New Analytics Types**: Add new calculation methods to `AnalyticsCalculatorService`
2. **Enhanced AI Features**: Extend `InsightsGeneratorService` with new AI models
3. **Custom Reports**: Add new report types to `ReportGeneratorService`
4. **Real-time Tracking**: Enhance `ProgressTrackerService` with real-time updates
5. **Caching Layer**: Add caching to individual services as needed

## Testing Strategy

Each service should be tested independently:

```typescript
describe('ProgressTrackerService', () => {
  // Test progress tracking functionality
});

describe('AnalyticsCalculatorService', () => {
  // Test calculation logic
});

describe('InsightsGeneratorService', () => {
  // Test AI insights generation
});

describe('ReportGeneratorService', () => {
  // Test report generation
});
```

Integration tests should verify that the main `AnalyticsService` properly orchestrates the specialized services.
