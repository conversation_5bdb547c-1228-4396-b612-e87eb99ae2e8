# Unified Analytics Response Structure

## Overview

All analytics services now return the same unified DTO structure (`UnifiedAnalyticsResponseDto`) in the Swagger configuration. This provides a consistent API interface while maintaining flexibility for different types of analytics data.

## Benefits

1. **Consistent API Documentation**: All analytics endpoints show the same response structure in Swagger
2. **Flexible Data Structure**: The unified format can accommodate any type of analytics data
3. **Better Client Integration**: Frontend clients can use a single interface for all analytics responses
4. **Extensible Design**: Easy to add new analytics types without changing the API contract

## Unified Response Structure

```typescript
{
  analyticsType: string;           // Type of analytics (user-progress, habit-analytics, etc.)
  overallScore: number;           // Primary metric/score for this analytics type
  period: string;                 // Time period (day, week, month, year, custom)
  periodStart: Date;              // Start date of the analytics period
  periodEnd: Date;                // End date of the analytics period
  metrics: BaseMetric[];          // Key metrics with values, units, and trends
  timeSeries?: TimeSeriesDataPoint[]; // Time-based data points
  insights?: AnalyticsInsight[];  // Insights and recommendations
  correlations?: CorrelationData[]; // Data correlations
  summary?: Record<string, any>;  // Summary statistics
  comparison?: {                  // Comparison with previous period
    overallChange: number;
    significantChanges: string[];
  };
  generatedAt: Date;              // When the data was generated
  metadata?: Record<string, any>; // Additional type-specific data
}
```

## Analytics Types

The `analyticsType` field identifies the type of analytics data:

- `user-progress` - Overall user progress analytics
- `habit-analytics` - Habit tracking and completion analytics
- `productivity-analytics` - Productivity patterns and insights
- `mood-analytics` - Mood tracking and correlations
- `insights` - Personalized insights and recommendations
- `weekly-stats` - Weekly aggregated statistics
- `task-completion` - Task completion analytics
- `ai-improvement-report` - AI-powered improvement recommendations

## Implementation

### Current State

All analytics controller endpoints now use `UnifiedAnalyticsResponseDto` in their Swagger `@ApiOkResponse` decorators:

```typescript
@Get('progress')
@ApiOperation({ summary: 'Get user progress analytics' })
@ApiOkResponse({
  description: 'Returns user progress analytics for the specified period',
  type: UnifiedAnalyticsResponseDto
})
getUserProgress(@Request() req, @Query() query: AnalyticsPeriodQueryDto) {
  return this.analyticsService.getUserProgress(req.user.id, query.period || 'week');
}
```

### Transformer Service

The `AnalyticsTransformerService` provides methods to transform existing analytics data into the unified format:

```typescript
// Transform user progress data
const unifiedResponse = this.analyticsTransformer.transformUserProgress(data, period);

// Transform habit analytics data
const unifiedResponse = this.analyticsTransformer.transformHabitAnalytics(data, period);

// Transform productivity analytics data
const unifiedResponse = this.analyticsTransformer.transformProductivityAnalytics(data, period);
```

### Migration Path

To fully implement the unified responses:

1. **Phase 1 (Current)**: Swagger documentation uses unified DTO
2. **Phase 2**: Update service methods to use transformer service
3. **Phase 3**: Optionally replace existing DTOs with unified format

Example implementation:

```typescript
async getUserProgress(userId: string, period: 'day' | 'week' | 'month' | 'year') {
  const data = await this.analyticsCalculator.getUserProgress(userId, period);
  return this.analyticsTransformer.transformUserProgress(data, period);
}
```

## Backward Compatibility

- Existing service implementations continue to work unchanged
- Original DTOs are preserved for internal use
- Transformer service bridges the gap between old and new formats
- Gradual migration is possible without breaking changes

## Usage Examples

### Frontend Integration

```typescript
// Single interface for all analytics data
interface AnalyticsResponse {
  analyticsType: string;
  overallScore: number;
  metrics: Metric[];
  timeSeries?: DataPoint[];
  insights?: Insight[];
  // ... other unified fields
}

// Handle any analytics endpoint response
function handleAnalyticsResponse(response: AnalyticsResponse) {
  switch (response.analyticsType) {
    case 'user-progress':
      renderProgressChart(response.timeSeries);
      break;
    case 'habit-analytics':
      renderHabitInsights(response.insights);
      break;
    // ... handle other types
  }
}
```

### API Documentation

All analytics endpoints now show consistent response structure in Swagger UI, making it easier for developers to understand and integrate with the API.

## Next Steps

1. **Test the unified structure** with existing analytics data
2. **Update service implementations** to use the transformer service
3. **Validate frontend compatibility** with the new structure
4. **Consider deprecating** old DTOs once migration is complete
5. **Add validation** for the unified response structure
