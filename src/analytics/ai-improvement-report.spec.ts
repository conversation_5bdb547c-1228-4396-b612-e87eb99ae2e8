/**
 * Test script for AI Improvement Report endpoint
 * This script demonstrates how to use the new AI improvement report endpoint
 */

import { Test, TestingModule } from '@nestjs/testing';
import { AnalyticsService } from './analytics.service';
import { AnalyticsController } from './analytics.controller';

describe('AI Improvement Report', () => {
  let service: AnalyticsService;
  let controller: AnalyticsController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AnalyticsController],
      providers: [
        {
          provide: AnalyticsService,
          useValue: {
            getAIImprovementReport: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AnalyticsService>(AnalyticsService);
    controller = module.get<AnalyticsController>(AnalyticsController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
    expect(service).toBeDefined();
  });

  it('should call getAIImprovementReport with correct parameters', async () => {
    const mockReport = {
      overallScore: 75,
      metrics: [],
      improvements: [],
      personalityInsights: [],
      strengths: ['Task completion'],
      criticalAreas: ['Morning routine'],
      projectedScore: 85,
      analysisDateRange: 30,
      generatedAt: new Date(),
      motivationalMessage: 'Great progress!',
    };

    (service.getAIImprovementReport as jest.Mock).mockResolvedValue(mockReport);

    const req = { user: { id: 'test-user-id' } };
    const result = await controller.getAIImprovementReport(req, 30);

    expect(service.getAIImprovementReport).toHaveBeenCalledWith('test-user-id', 30);
    expect(result).toEqual(mockReport);
  });

  it('should use default days parameter when not provided', async () => {
    const mockReport = {
      overallScore: 75,
      metrics: [],
      improvements: [],
      personalityInsights: [],
      strengths: ['Task completion'],
      criticalAreas: ['Morning routine'],
      projectedScore: 85,
      analysisDateRange: 30,
      generatedAt: new Date(),
      motivationalMessage: 'Great progress!',
    };

    (service.getAIImprovementReport as jest.Mock).mockResolvedValue(mockReport);

    const req = { user: { id: 'test-user-id' } };
    const result = await controller.getAIImprovementReport(req);

    expect(service.getAIImprovementReport).toHaveBeenCalledWith('test-user-id', 30);
    expect(result).toEqual(mockReport);
  });
});
