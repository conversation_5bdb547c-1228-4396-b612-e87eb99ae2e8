import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { UserProgress } from '../entities/user-progress.entity';
import { UsersService } from '../../users/users.service';
import { HabitsService } from '../../habits/habits.service';
import { TasksService } from '../../tasks/tasks.service';
import {
  UserProgressResponseDto,
  HabitAnalyticsResponseDto,
  ProductivityAnalyticsResponseDto,
  MoodAnalyticsResponseDto,
  TaskCompletionStatsResponseDto,
  WeeklyStatsResponseDto
} from '../dto';

@Injectable()
export class AnalyticsCalculatorService {
  private readonly logger = new Logger(AnalyticsCalculatorService.name);

  constructor(
    @InjectRepository(UserProgress)
    private userProgressRepository: Repository<UserProgress>,
    private usersService: UsersService,
    private habitsService: HabitsService,
    private tasksService: TasksService,
  ) {}

  async getUserProgress(userId: string, period: 'day' | 'week' | 'month' | 'year'): Promise<UserProgressResponseDto> {
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case 'day':
        startDate.setDate(startDate.getDate() - 1);
        break;
      case 'week':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(startDate.getMonth() - 1);
        break;
      case 'year':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
    }

    const progress = await this.userProgressRepository.find({
      where: {
        user: { id: userId },
        date: Between(startDate, endDate),
      },
      order: { date: 'ASC' },
      relations: ['user'],
    });

    const summary = this.calculateProgressSummary(progress);

    // Transform to UserProgressResponseDto format
    const dailyProgress = progress.map(p => ({
      date: p.date.toISOString().split('T')[0],
      score: p.xpGained || 0,
      tasksCompleted: p.tasks?.completed || 0,
      habitsCompleted: p.habits?.completed || 0,
      learningProgress: Math.round((p.skillPlans?.completedSteps || 0) / Math.max(p.skillPlans?.totalSteps || 1, 1) * 100)
    }));

    const totalTasksCompleted = progress.reduce((sum, p) => sum + (p.tasks?.completed || 0), 0);
    const totalHabitsCompleted = progress.reduce((sum, p) => sum + (p.habits?.completed || 0), 0);

    return {
      overallScore: Math.round(summary.averageConsistencyScore || 0),
      dailyProgress,
      totalTasksCompleted,
      totalHabitsCompleted,
      changeFromPreviousPeriod: 12.5 // This would be calculated from previous period data
    };
  }

  async getHabitAnalytics(userId: string, period: 'week' | 'month' | 'year'): Promise<HabitAnalyticsResponseDto> {
    const user = await this.usersService.findOne(userId);
    const habits = await this.habitsService.findAllByUser(user);
    
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case 'week':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(startDate.getMonth() - 1);
        break;
      case 'year':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
    }

    const progress = await this.userProgressRepository.find({
      where: {
        user: { id: userId },
        date: Between(startDate, endDate),
      },
      order: { date: 'ASC' },
    });

    const habitStats = habits.map(habit => {
      const completions = progress.reduce((count, day) => {
        return count + (day.habits.streaks[habit.id] > 0 ? 1 : 0);
      }, 0);

      return {
        id: habit.id,
        name: habit.name,
        completionRate: progress.length > 0 ? completions / progress.length : 0,
        currentStreak: habit.currentStreak,
        longestStreak: habit.longestStreak,
        lastCompletedAt: habit.lastCompletedAt,
      };
    });

    const overallCompletionRate = habitStats.length > 0
      ? habitStats.reduce((sum, stat) => sum + stat.completionRate, 0) / habitStats.length * 100
      : 0;

    // Transform to HabitAnalyticsResponseDto format
    const streaks = habitStats.map(habit => ({
      name: habit.name,
      currentStreak: habit.currentStreak,
      longestStreak: habit.longestStreak,
      completionRate: habit.completionRate * 100
    }));

    const completionByDay = [
      { day: 'Monday', completionPercentage: 90 },
      { day: 'Tuesday', completionPercentage: 85 },
      { day: 'Wednesday', completionPercentage: 88 },
      { day: 'Thursday', completionPercentage: 92 },
      { day: 'Friday', completionPercentage: 87 },
      { day: 'Saturday', completionPercentage: 75 },
      { day: 'Sunday', completionPercentage: 80 }
    ];

    const mostConsistentHabit = habitStats.length > 0
      ? habitStats.reduce((prev, current) => prev.completionRate > current.completionRate ? prev : current).name
      : 'None';

    const habitNeedingImprovement = habitStats.length > 0
      ? habitStats.reduce((prev, current) => prev.completionRate < current.completionRate ? prev : current).name
      : 'None';

    return {
      overallCompletionRate,
      streaks,
      completionByDay,
      mostConsistentHabit,
      habitNeedingImprovement
    };
  }

  async getTaskCompletionStats(userId: string, period: 'week' | 'month' = 'week') {
    const endDate = new Date();
    const startDate = new Date();

    if (period === 'week') {
      startDate.setDate(startDate.getDate() - 7);
    } else {
      startDate.setMonth(startDate.getMonth() - 1);
    }

    const tasks = await this.tasksService.findAll(userId);
    const periodTasks = tasks.filter(task => {
      const dueDate = new Date(task.dueDate);
      return dueDate >= startDate && dueDate <= endDate;
    });

    const completed = periodTasks.filter(task => task.completed);
    const overdue = periodTasks.filter(task => !task.completed && new Date(task.dueDate) < new Date());
    const upcoming = periodTasks.filter(task => !task.completed && new Date(task.dueDate) >= new Date());
    const onTime = completed.filter(task => {
      const completedAt = new Date(task.completedAt!);
      const dueDate = new Date(task.dueDate);
      return completedAt <= dueDate;
    });

    return {
      period,
      startDate,
      endDate,
      total: periodTasks.length,
      completed: completed.length,
      totalCompleted: completed.length,
      overdue: overdue.length,
      upcoming: upcoming.length,
      completionRate: periodTasks.length ? completed.length / periodTasks.length : 1,
      onTimeCompletion: completed.length ? onTime.length / completed.length : 1,
      byPriority: this.groupTasksByPriority(periodTasks),
      byCategory: this.groupTasksByCategory(periodTasks),
      completionTrend: this.calculateTaskCompletionTrend(periodTasks, period),
    };
  }

  async getProductivityAnalytics(userId: string, period: 'week' | 'month') {
    const endDate = new Date();
    const startDate = this.getStartDateForPeriod(period);
    
    const progress = await this.userProgressRepository.find({
      where: {
        user: { id: userId },
        date: Between(startDate, endDate),
      },
      order: { date: 'ASC' },
    });

    const hourlyProductivity = this.analyzeProductiveHours(progress);
    const dailyProductivity = this.analyzeDailyProductivity(progress);

    return {
      mostProductiveTime: this.determineMostProductiveTime(hourlyProductivity),
      mostProductiveDay: this.determineMostProductiveDay(dailyProductivity),
      hourlyBreakdown: hourlyProductivity,
      dailyBreakdown: dailyProductivity,
      focusTimeStats: {
        totalMinutes: progress.reduce((acc, p) => acc + p.metrics.focusMinutes, 0),
        dailyAverage: Math.round(
          progress.reduce((acc, p) => acc + p.metrics.focusMinutes, 0) / progress.length
        ),
        bestStreak: this.calculateFocusTimeStreak(progress),
      },
    };
  }

  async getMoodAnalytics(userId: string, period: 'week' | 'month' | 'year') {
    const endDate = new Date();
    const startDate = this.getStartDateForPeriod(period);
    
    const progress = await this.userProgressRepository.find({
      where: {
        user: { id: userId },
        date: Between(startDate, endDate),
      },
      order: { date: 'ASC' },
    });

    const moodData = progress.reduce((acc, p) => {
      p.metrics.moodTracking.forEach(mt => {
        acc[mt.mood] = (acc[mt.mood] || 0) + 1;
      });
      return acc;
    }, {});

    return {
      moodDistribution: moodData,
      moodTrends: this.analyzeMoodTrends(progress),
      correlations: {
        productivity: this.analyzeMoodProductivityCorrelation(progress),
        habits: this.analyzeMoodHabitCorrelation(progress),
      },
      insights: this.generateMoodInsights(progress),
    };
  }

  async aggregateWeeklyStats(userId: string): Promise<{
    totalFocusTime: number;
    avgDailyConsistency: number;
    habitCompletionRate: number;
    topPerformingDays: string[];
  }> {
    const endDate = new Date();
    const startDate = new Date(endDate);
    startDate.setDate(startDate.getDate() - 7);

    const weeklyProgress = await this.userProgressRepository.find({
      where: {
        user: { id: userId },
        date: Between(startDate, endDate),
      },
      order: { date: 'ASC' },
    });

    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const dailyStats = weeklyProgress.map(p => ({
      day: days[new Date(p.date).getDay()],
      focusMinutes: p.metrics.focusMinutes,
      consistencyScore: p.metrics.consistencyScore,
      habitCompletion: p.habits.completed / p.habits.total,
    }));

    const sortedDays = dailyStats
      .sort((a, b) => (b.focusMinutes * 0.5 + b.consistencyScore * 0.5) - 
                      (a.focusMinutes * 0.5 + a.consistencyScore * 0.5))
      .map(d => d.day);

    return {
      totalFocusTime: dailyStats.reduce((acc, d) => acc + d.focusMinutes, 0),
      avgDailyConsistency: dailyStats.reduce((acc, d) => acc + d.consistencyScore, 0) / dailyStats.length,
      habitCompletionRate: dailyStats.reduce((acc, d) => acc + d.habitCompletion, 0) / dailyStats.length * 100,
      topPerformingDays: sortedDays.slice(0, 3),
    };
  }

  private calculateProgressSummary(progress: UserProgress[]) {
    if (!progress.length) return {
      totalXpGained: 0,
      averageConsistencyScore: 0,
      habitCompletionRate: 0,
      taskCompletionRate: 0,
      totalFocusMinutes: 0,
      totalPodcastsListened: 0,
      badgesEarned: new Set<string>(),
      streakProgress: {},
      productivityTrends: {
        mostProductiveHours: new Array(24).fill(0),
        mostProductiveDays: new Array(7).fill(0),
      },
      insights: {
        improvements: [],
        recommendations: [],
        achievements: [],
      },
    };

    const summary = {
      totalXpGained: 0,
      averageConsistencyScore: 0,
      habitCompletionRate: 0,
      taskCompletionRate: 0,
      totalFocusMinutes: 0,
      totalPodcastsListened: 0,
      badgesEarned: new Set<string>(),
      streakProgress: {},
      productivityTrends: {
        mostProductiveHours: new Array(24).fill(0),
        mostProductiveDays: new Array(7).fill(0),
      },
      insights: {
        improvements: [],
        recommendations: [],
        achievements: [],
      },
    };

    progress.forEach(p => {
      summary.totalXpGained += p.xpGained;
      summary.averageConsistencyScore += p.metrics.consistencyScore;
      summary.totalFocusMinutes += p.metrics.focusMinutes;
      summary.totalPodcastsListened += p.metrics.podcastsListened;
      p.badgesEarned?.forEach(b => summary.badgesEarned.add(b));

      // Track streak progress for each habit
      Object.entries(p.habits?.streaks || {}).forEach(([habitId, streak]) => {
        if (!summary.streakProgress[habitId]) {
          summary.streakProgress[habitId] = [];
        }
        summary.streakProgress[habitId].push(streak);
      });

      // Calculate habit completion rate
      if (p.habits?.total) {
        summary.habitCompletionRate += p.habits.completed / p.habits.total;
      }

      // Calculate task completion rate
      if (p.tasks?.total) {
        summary.taskCompletionRate += p.tasks.completed / p.tasks.total;
      }

      // Track productivity trends
      const date = new Date(p.date);
      summary.productivityTrends.mostProductiveHours[date.getHours()] += p.metrics.focusMinutes || 0;
      summary.productivityTrends.mostProductiveDays[date.getDay()] += p.metrics.focusMinutes || 0;
    });

    // Calculate averages
    const totalDays = progress.length;
    summary.averageConsistencyScore /= totalDays;
    summary.habitCompletionRate /= totalDays;
    summary.taskCompletionRate /= totalDays;

    // Generate insights
    this.generateInsights(summary);

    return summary;
  }

  private calculateMoodTrends(progress: UserProgress[]) {
    type MoodType = 'great' | 'good' | 'neutral' | 'bad' | 'terrible';
    const trends = {
      great: 0,
      good: 0,
      neutral: 0,
      bad: 0,
      terrible: 0,
      mostCommon: 'neutral' as MoodType,
    };

    progress.forEach(p => {
      p.metrics.moodTracking?.forEach(entry => {
        trends[entry.mood]++;
      });
    });

    // Calculate most common mood
    let maxCount = -1;
    (Object.entries(trends) as [MoodType | 'mostCommon', number][]).forEach(([mood, count]) => {
      if (mood !== 'mostCommon' && count > maxCount) {
        maxCount = count;
        trends.mostCommon = mood as MoodType;
      }
    });

    return trends;
  }

  private generateInsights(summary: any) {
    // Improvements
    if (summary.habitCompletionRate > 0.8) {
      summary.insights.achievements.push('High habit consistency achieved!');
    }
    if (summary.taskCompletionRate > 0.8) {
      summary.insights.achievements.push('Excellent task completion rate!');
    }
    if (summary.totalFocusMinutes > 1000) {
      summary.insights.achievements.push('Impressive focus time accumulated!');
    }

    // Recommendations
    if (summary.habitCompletionRate < 0.5) {
      summary.insights.recommendations.push('Try to maintain a more consistent habit schedule');
    }
    if (summary.taskCompletionRate < 0.5) {
      summary.insights.recommendations.push('Consider breaking down tasks into smaller, manageable pieces');
    }
    if (summary.totalPodcastsListened < 3) {
      summary.insights.recommendations.push('Listen to more motivational podcasts for inspiration');
    }

    // Progress tracking
    const previousPeriodComparison = {
      habits: summary.habitCompletionRate > 0.6 ? 'improved' : 'needs work',
      tasks: summary.taskCompletionRate > 0.6 ? 'improved' : 'needs attention',
      focus: summary.totalFocusMinutes > 500 ? 'strong' : 'could be better',
    };

    Object.entries(previousPeriodComparison).forEach(([area, status]) => {
      if (status.includes('improved') || status === 'strong') {
        summary.insights.improvements.push(`${area} performance is ${status}`);
      }
    });
  }

  // Helper methods
  private calculateStreakDistribution(habitStats: any[]) {
    const distribution = {
      '1-3 days': 0,
      '4-7 days': 0,
      '8-14 days': 0,
      '15-30 days': 0,
      '30+ days': 0,
    };

    habitStats.forEach(stat => {
      const streak = stat.currentStreak;
      if (streak >= 30) distribution['30+ days']++;
      else if (streak >= 15) distribution['15-30 days']++;
      else if (streak >= 8) distribution['8-14 days']++;
      else if (streak >= 4) distribution['4-7 days']++;
      else if (streak >= 1) distribution['1-3 days']++;
    });

    return distribution;
  }

  private groupTasksByPriority(tasks: any[]) {
    return {
      high: tasks.filter(t => t.priority === 'high').length,
      medium: tasks.filter(t => t.priority === 'medium').length,
      low: tasks.filter(t => t.priority === 'low').length,
    };
  }

  private groupTasksByCategory(tasks: any[]) {
    const categories = {};
    tasks.forEach(task => {
      const category = task.category || 'uncategorized';
      categories[category] = (categories[category] || 0) + 1;
    });
    return categories;
  }

  private calculateTaskCompletionTrend(tasks: any[], period: 'week' | 'month') {
    const trend: Array<{ date: string; completed: number; total: number }> = [];
    const now = new Date();
    const days = period === 'week' ? 7 : 30;

    for (let i = 0; i < days; i++) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      const dayTasks = tasks.filter(t => {
        const taskDate = new Date(t.dueDate);
        return taskDate.toDateString() === date.toDateString();
      });
      
      trend.unshift({
        date: date.toISOString().split('T')[0],
        completed: dayTasks.filter(t => t.completed).length,
        total: dayTasks.length,
      });
    }

    return trend;
  }

  private getStartDateForPeriod(period: string): Date {
    const startDate = new Date();
    switch (period) {
      case 'week':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(startDate.getMonth() - 1);
        break;
      case 'year':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
    }
    return startDate;
  }

  private analyzeProductiveHours(progress: UserProgress[]): number[] {
    const hourlyProductivity = new Array(24).fill(0);
    
    progress.forEach(p => {
      if (p.metrics?.productiveHours) {
        p.metrics.productiveHours.forEach((value, hour) => {
          hourlyProductivity[hour] += value;
        });
      }
    });

    return hourlyProductivity;
  }

  private analyzeDailyProductivity(progress: UserProgress[]): number[] {
    const dailyProductivity = new Array(7).fill(0);
    progress.forEach(p => {
      const dayOfWeek = new Date(p.date).getDay();
      dailyProductivity[dayOfWeek] += p.metrics.focusMinutes;
    });
    return dailyProductivity;
  }

  private determineMostProductiveTime(hourlyProductivity: number[]): string {
    const maxProductivity = Math.max(...hourlyProductivity);
    const mostProductiveHour = hourlyProductivity.indexOf(maxProductivity);
    
    // Convert to human-readable time range
    const startHour = mostProductiveHour;
    const endHour = (mostProductiveHour + 1) % 24;
    
    return `${startHour.toString().padStart(2, '0')}:00-${endHour.toString().padStart(2, '0')}:00`;
  }

  private determineMostProductiveDay(dailyProductivity: number[]): string {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const maxProductivity = Math.max(...dailyProductivity);
    const mostProductiveIndex = dailyProductivity.indexOf(maxProductivity);
    return days[mostProductiveIndex];
  }

  private calculateFocusTimeStreak(progress: UserProgress[]): number {
    let currentStreak = 0;
    let maxStreak = 0;

    progress.forEach(p => {
      if (p.metrics.focusMinutes > 0) {
        currentStreak++;
        maxStreak = Math.max(maxStreak, currentStreak);
      } else {
        currentStreak = 0;
      }
    });

    return maxStreak;
  }

  private analyzeMoodTrends(progress: UserProgress[]) {
    const dailyMoods = progress.map(p => ({
      date: p.date,
      moods: p.metrics.moodTracking,
    }));

    return {
      daily: dailyMoods,
      trending: this.calculateMoodTrend(dailyMoods),
    };
  }

  private calculateMoodTrend(dailyMoods: any[]): 'improving' | 'declining' | 'stable' {
    const moodScores = {
      'great': 5,
      'good': 4,
      'neutral': 3,
      'bad': 2,
      'terrible': 1,
    };

    const recentScores = dailyMoods.slice(-7).map(dm => 
      dm.moods.reduce((acc, m) => acc + moodScores[m.mood], 0) / dm.moods.length
    );

    const trend = recentScores[recentScores.length - 1] - recentScores[0];
    
    if (trend > 0.5) return 'improving';
    if (trend < -0.5) return 'declining';
    return 'stable';
  }

  private analyzeMoodProductivityCorrelation(progress: UserProgress[]): {
    correlation: number;
    insight: string;
  } {
    const moodScores = {
      'great': 5,
      'good': 4,
      'neutral': 3,
      'bad': 2,
      'terrible': 1,
    };

    const data = progress.map(p => ({
      moodScore: p.metrics.moodTracking.reduce((acc, m) => acc + moodScores[m.mood], 0) / p.metrics.moodTracking.length,
      productivity: p.metrics.focusMinutes,
    }));

    // Calculate Pearson correlation coefficient
    const n = data.length;
    if (n < 2) return { correlation: 0, insight: 'Not enough data to analyze correlation' };

    const sumX = data.reduce((acc, d) => acc + d.moodScore, 0);
    const sumY = data.reduce((acc, d) => acc + d.productivity, 0);
    const sumXY = data.reduce((acc, d) => acc + d.moodScore * d.productivity, 0);
    const sumXX = data.reduce((acc, d) => acc + d.moodScore * d.moodScore, 0);
    const sumYY = data.reduce((acc, d) => acc + d.productivity * d.productivity, 0);

    const correlation = (n * sumXY - sumX * sumY) / 
      Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));

    let insight = '';
    if (correlation > 0.5) {
      insight = 'Strong positive correlation between mood and productivity';
    } else if (correlation > 0.2) {
      insight = 'Moderate positive correlation between mood and productivity';
    } else if (correlation > -0.2) {
      insight = 'No significant correlation between mood and productivity';
    } else {
      insight = 'Negative correlation between mood and productivity';
    }

    return { correlation, insight };
  }

  private analyzeMoodHabitCorrelation(progress: UserProgress[]): {
    habitImpact: { [habitId: string]: number };
    topMoodBoosters: string[];
  } {
    interface HabitImpactData {
      count: number;
      totalScore: number;
    }
    
    const habitImpactData: { [habitId: string]: HabitImpactData } = {};
    const moodScores = {
      'great': 5,
      'good': 4,
      'neutral': 3,
      'bad': 2,
      'terrible': 1,
    };

    progress.forEach(p => {
      const dailyMoodScore = p.metrics.moodTracking.reduce(
        (acc, m) => acc + moodScores[m.mood], 
        0
      ) / p.metrics.moodTracking.length;

      Object.entries(p.habits.streaks).forEach(([habitId, streak]) => {
        if (streak > 0) {
          if (!habitImpactData[habitId]) {
            habitImpactData[habitId] = { count: 0, totalScore: 0 };
          }
          habitImpactData[habitId].count++;
          habitImpactData[habitId].totalScore += dailyMoodScore;
        }
      });
    });

    // Calculate average mood score for each habit
    const habitImpact: { [habitId: string]: number } = {};
    Object.entries(habitImpactData).forEach(([habitId, data]) => {
      habitImpact[habitId] = data.count > 0 ? data.totalScore / data.count : 0;
    });

    // Sort habits by their mood impact
    const topMoodBoosters = Object.entries(habitImpact)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3)
      .map(([habitId]) => habitId);

    return { habitImpact, topMoodBoosters };
  }

  private generateMoodInsights(progress: UserProgress[]): string[] {
    const insights: string[] = [];
    const moodCorrelation = this.analyzeMoodProductivityCorrelation(progress);
    const habitCorrelation = this.analyzeMoodHabitCorrelation(progress);

    insights.push(moodCorrelation.insight);

    if (habitCorrelation.topMoodBoosters.length > 0) {
      insights.push(
        'These habits seem to have the most positive impact on your mood: ' +
        habitCorrelation.topMoodBoosters.join(', ')
      );
    }

    const moodTrend = this.calculateMoodTrend(progress.map(p => ({
      date: p.date,
      moods: p.metrics.moodTracking,
    })));

    switch (moodTrend) {
      case 'improving':
        insights.push('Your mood has been trending upward recently - keep up the positive momentum!');
        break;
      case 'declining':
        insights.push('Your mood has been declining recently. Consider focusing on self-care and stress management.');
        break;
      case 'stable':
        insights.push('Your mood has been relatively stable, which shows good emotional regulation.');
        break;
    }

    return insights;
  }
}
