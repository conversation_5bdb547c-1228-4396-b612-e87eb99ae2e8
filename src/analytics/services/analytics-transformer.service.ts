import { Injectable } from '@nestjs/common';
import { UnifiedAnalyticsResponseDto } from '../dto/unified-analytics-response.dto';

@Injectable()
export class AnalyticsTransformerService {
  
  /**
   * Transform user progress data to unified format
   */
  transformUserProgress(data: any, period: string): UnifiedAnalyticsResponseDto {
    const now = new Date();
    const periodStart = this.getPeriodStart(period, now);
    
    return {
      analyticsType: 'user-progress',
      overallScore: data.overallScore || 0,
      period,
      periodStart,
      periodEnd: now,
      metrics: [
        {
          name: 'Overall Progress Score',
          value: data.overallScore || 0,
          unit: 'percentage',
          trend: this.calculateTrend(data.changeFromPreviousPeriod),
          changePercentage: data.changeFromPreviousPeriod || 0
        },
        {
          name: 'Tasks Completed',
          value: data.totalTasksCompleted || 0,
          unit: 'count'
        },
        {
          name: 'Habits Completed',
          value: data.totalHabitsCompleted || 0,
          unit: 'count'
        }
      ],
      timeSeries: data.dailyProgress?.map((day: any) => ({
        label: day.date,
        value: day.score,
        metadata: {
          tasksCompleted: day.tasksCompleted,
          habitsCompleted: day.habitsCompleted,
          learningProgress: day.learningProgress
        }
      })) || [],
      summary: {
        totalTasks: data.totalTasksCompleted,
        totalHabits: data.totalHabitsCompleted,
        changeFromPrevious: data.changeFromPreviousPeriod
      },
      comparison: data.changeFromPreviousPeriod ? {
        overallChange: data.changeFromPreviousPeriod,
        significantChanges: [`Progress changed by ${data.changeFromPreviousPeriod}%`]
      } : undefined,
      generatedAt: now
    };
  }

  /**
   * Transform habit analytics data to unified format
   */
  transformHabitAnalytics(data: any, period: string): UnifiedAnalyticsResponseDto {
    const now = new Date();
    const periodStart = this.getPeriodStart(period, now);
    
    return {
      analyticsType: 'habit-analytics',
      overallScore: data.overallCompletionRate || 0,
      period,
      periodStart,
      periodEnd: now,
      metrics: [
        {
          name: 'Overall Completion Rate',
          value: data.overallCompletionRate || 0,
          unit: 'percentage'
        }
      ],
      timeSeries: data.completionByDay?.map((day: any) => ({
        label: day.day,
        value: day.completionRate,
        metadata: {
          completed: day.completed,
          total: day.total
        }
      })) || [],
      insights: [
        {
          type: 'strength',
          title: 'Most Consistent Habit',
          description: `Your most consistent habit is ${data.mostConsistentHabit}`,
          priority: 'medium'
        },
        {
          type: 'improvement',
          title: 'Habit Needing Improvement',
          description: `Focus on improving ${data.habitNeedingImprovement}`,
          priority: 'high'
        }
      ],
      summary: {
        mostConsistentHabit: data.mostConsistentHabit,
        habitNeedingImprovement: data.habitNeedingImprovement,
        streaks: data.streaks
      },
      generatedAt: now
    };
  }

  /**
   * Transform productivity analytics data to unified format
   */
  transformProductivityAnalytics(data: any, period: string): UnifiedAnalyticsResponseDto {
    const now = new Date();
    const periodStart = this.getPeriodStart(period, now);
    
    return {
      analyticsType: 'productivity-analytics',
      overallScore: data.overallProductivity || 0,
      period,
      periodStart,
      periodEnd: now,
      metrics: [
        {
          name: 'Overall Productivity',
          value: data.overallProductivity || 0,
          unit: 'percentage'
        },
        {
          name: 'Average Focus Time',
          value: data.averageFocusTime || 0,
          unit: 'minutes'
        }
      ],
      timeSeries: data.productivityByTimeOfDay?.map((timeSlot: any) => ({
        label: timeSlot.timeOfDay,
        value: timeSlot.productivityScore,
        metadata: {
          focusMinutes: timeSlot.focusMinutes,
          tasksCompleted: timeSlot.tasksCompleted
        }
      })) || [],
      insights: [
        {
          type: 'strength',
          title: 'Most Productive Time',
          description: `You are most productive during ${data.mostProductiveTime}`,
          priority: 'medium'
        },
        {
          type: 'strength',
          title: 'Most Productive Day',
          description: `Your most productive day is ${data.mostProductiveDay}`,
          priority: 'medium'
        }
      ],
      summary: {
        mostProductiveTime: data.mostProductiveTime,
        mostProductiveDay: data.mostProductiveDay,
        averageFocusTime: data.averageFocusTime
      },
      generatedAt: now
    };
  }

  /**
   * Transform mood analytics data to unified format
   */
  transformMoodAnalytics(data: any, period: string): UnifiedAnalyticsResponseDto {
    const now = new Date();
    const periodStart = this.getPeriodStart(period, now);
    
    return {
      analyticsType: 'mood-analytics',
      overallScore: data.averageMood ? data.averageMood * 20 : 0, // Convert 1-5 scale to 0-100
      period,
      periodStart,
      periodEnd: now,
      metrics: [
        {
          name: 'Average Mood Score',
          value: data.averageMood || 0,
          unit: 'score',
          trend: data.moodTrend === 'improving' ? 'improving' : 
                 data.moodTrend === 'declining' ? 'declining' : 'stable'
        }
      ],
      timeSeries: data.moodEntries?.map((entry: any) => ({
        label: entry.date,
        value: entry.moodScore,
        metadata: {
          mood: entry.mood
        }
      })) || [],
      correlations: data.correlations?.map((corr: any) => ({
        factorA: corr.factor,
        factorB: 'Mood',
        strength: corr.correlationStrength,
        type: corr.correlationType
      })) || [],
      summary: {
        averageMood: data.averageMood,
        mostCommonMood: data.mostCommonMood,
        moodTrend: data.moodTrend
      },
      generatedAt: now
    };
  }

  /**
   * Transform task completion stats data to unified format
   */
  transformTaskCompletionStats(data: any, period: string): UnifiedAnalyticsResponseDto {
    const now = new Date();
    const periodStart = this.getPeriodStart(period, now);

    return {
      analyticsType: 'task-completion',
      overallScore: data.overallCompletionRate || 0,
      period,
      periodStart,
      periodEnd: now,
      metrics: [
        {
          name: 'Overall Completion Rate',
          value: data.overallCompletionRate || 0,
          unit: 'percentage'
        },
        {
          name: 'On-Time Completion Rate',
          value: data.onTimeCompletionRate || 0,
          unit: 'percentage'
        },
        {
          name: 'Average Tasks Per Day',
          value: data.averageTasksPerDay || 0,
          unit: 'count'
        }
      ],
      timeSeries: data.completionByDay?.map((day: any) => ({
        label: day.day,
        value: day.completionRate,
        metadata: {
          completed: day.completed,
          due: day.due
        }
      })) || [],
      insights: [
        {
          type: 'strength',
          title: 'Most Productive Day',
          description: `You complete the most tasks on ${data.mostProductiveDay}`,
          priority: 'medium'
        }
      ],
      summary: {
        overallCompletionRate: data.overallCompletionRate,
        onTimeCompletionRate: data.onTimeCompletionRate,
        averageTasksPerDay: data.averageTasksPerDay,
        mostProductiveDay: data.mostProductiveDay,
        completionByPriority: data.completionByPriority
      },
      generatedAt: now
    };
  }

  /**
   * Transform weekly stats data to unified format
   */
  transformWeeklyStats(data: any): UnifiedAnalyticsResponseDto {
    const now = new Date();
    const weekStart = new Date(now);
    weekStart.setDate(weekStart.getDate() - 7);

    return {
      analyticsType: 'weekly-stats',
      overallScore: data.weeklyProductivityScore || 0,
      period: 'week',
      periodStart: weekStart,
      periodEnd: now,
      metrics: [
        {
          name: 'Weekly Productivity Score',
          value: data.weeklyProductivityScore || 0,
          unit: 'percentage'
        },
        {
          name: 'Total Tasks',
          value: data.totalTasks || 0,
          unit: 'count'
        },
        {
          name: 'Total Habits',
          value: data.totalHabits || 0,
          unit: 'count'
        },
        {
          name: 'Total Focus Minutes',
          value: data.totalFocusMinutes || 0,
          unit: 'minutes'
        }
      ],
      timeSeries: data.dailyStats?.map((day: any) => ({
        label: day.day,
        value: day.productivityScore,
        metadata: {
          tasksCompleted: day.tasksCompleted,
          habitsCompleted: day.habitsCompleted,
          focusMinutes: day.focusMinutes
        }
      })) || [],
      insights: [
        {
          type: 'strength',
          title: 'Most Productive Day',
          description: `Your most productive day this week was ${data.mostProductiveDay}`,
          priority: 'medium'
        }
      ],
      summary: {
        totalTasks: data.totalTasks,
        totalHabits: data.totalHabits,
        totalFocusMinutes: data.totalFocusMinutes,
        mostProductiveDay: data.mostProductiveDay,
        dailyStats: data.dailyStats
      },
      generatedAt: now
    };
  }

  /**
   * Transform AI improvement report data to unified format
   */
  transformAIImprovementReport(data: any, days: number): UnifiedAnalyticsResponseDto {
    const now = new Date();
    const periodStart = new Date(now);
    periodStart.setDate(periodStart.getDate() - days);

    return {
      analyticsType: 'ai-improvement-report',
      overallScore: data.overallScore || 0,
      period: 'custom',
      periodStart,
      periodEnd: now,
      metrics: data.metrics?.map((metric: any) => ({
        name: metric.name,
        value: metric.currentScore,
        unit: 'percentage',
        trend: metric.trend,
        changePercentage: metric.changePercentage
      })) || [],
      insights: [
        ...(data.improvements?.map((improvement: any) => ({
          type: 'improvement' as const,
          title: improvement.title,
          description: improvement.description,
          priority: improvement.priority,
          actionableSteps: improvement.actionableSteps
        })) || []),
        ...(data.personalityInsights?.map((insight: any) => ({
          type: 'recommendation' as const,
          title: insight.title,
          description: insight.description,
          priority: insight.priority || 'medium' as const
        })) || [])
      ],
      summary: {
        overallScore: data.overallScore,
        projectedScore: data.projectedScore,
        analysisDateRange: data.analysisDateRange,
        strengths: data.strengths,
        criticalAreas: data.criticalAreas,
        motivationalMessage: data.motivationalMessage
      },
      metadata: {
        generatedAt: data.generatedAt,
        analysisDateRange: days,
        confidence: 'high'
      },
      generatedAt: now
    };
  }

  /**
   * Transform insights data to unified format
   */
  transformInsights(data: any): UnifiedAnalyticsResponseDto {
    const now = new Date();

    return {
      analyticsType: 'insights',
      overallScore: 75, // Default score for insights
      period: 'custom',
      periodStart: now,
      periodEnd: now,
      metrics: [],
      insights: [
        ...(data.insights?.map((insight: any) => ({
          type: 'recommendation' as const,
          title: insight.title,
          description: insight.description,
          priority: insight.priority || 'medium' as const,
          actionableSteps: insight.actionableSteps
        })) || []),
        ...(data.recommendations?.map((rec: any) => ({
          type: 'recommendation' as const,
          title: rec.title,
          description: rec.description,
          priority: rec.priority || 'medium' as const,
          actionableSteps: rec.actionableSteps
        })) || [])
      ],
      summary: {
        strengths: data.strengths,
        improvementAreas: data.improvementAreas,
        totalInsights: data.insights?.length || 0,
        totalRecommendations: data.recommendations?.length || 0
      },
      generatedAt: now
    };
  }

  /**
   * Calculate trend based on change percentage
   */
  private calculateTrend(changePercentage?: number): 'improving' | 'declining' | 'stable' {
    if (!changePercentage) return 'stable';
    if (changePercentage > 5) return 'improving';
    if (changePercentage < -5) return 'declining';
    return 'stable';
  }

  /**
   * Get period start date based on period type
   */
  private getPeriodStart(period: string, endDate: Date): Date {
    const start = new Date(endDate);
    
    switch (period) {
      case 'day':
        start.setHours(0, 0, 0, 0);
        break;
      case 'week':
        start.setDate(start.getDate() - 7);
        break;
      case 'month':
        start.setMonth(start.getMonth() - 1);
        break;
      case 'year':
        start.setFullYear(start.getFullYear() - 1);
        break;
      default:
        start.setDate(start.getDate() - 7); // Default to week
    }
    
    return start;
  }
}
