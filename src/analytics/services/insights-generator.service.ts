import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, MoreThanOrEqual } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { GoogleGenAI } from '@google/genai';
import { UserProgress } from '../entities/user-progress.entity';
import { UsersService } from '../../users/users.service';
import { HabitsService } from '../../habits/habits.service';
import { TasksService } from '../../tasks/tasks.service';
import { SkillPlansService } from '../../skill-plans/skill-plans.service';
import { ChallengesService } from '../../challenges/challenges.service';

@Injectable()
export class InsightsGeneratorService {
  private readonly logger = new Logger(InsightsGeneratorService.name);
  private readonly genAI: GoogleGenAI;

  constructor(
    @InjectRepository(UserProgress)
    private userProgressRepository: Repository<UserProgress>,
    private usersService: UsersService,
    private habitsService: HabitsService,
    private tasksService: TasksService,
    private skillPlansService: SkillPlansService,
    private challengesService: ChallengesService,
    private configService: ConfigService,
  ) {
    // Initialize Google GenAI client
    const geminiApiKey = this.configService.get<string>('gemini.apiKey');
    if (geminiApiKey) {
      this.genAI = new GoogleGenAI({ apiKey: geminiApiKey });
    } else {
      this.logger.warn('GEMINI_API_KEY is not configured - AI features will be limited');
    }
  }

  async getPersonalizedInsights(userId: string) {
    const recentProgress = await this.userProgressRepository.find({
      where: {
        user: { id: userId },
        date: MoreThanOrEqual(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)),
      },
      order: { date: 'DESC' },
    });

    return this.generateUserInsights(userId, recentProgress);
  }

  async getAIImprovementReport(userId: string, days: number = 30): Promise<any> {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get user progress data for the specified period
    const progressData = await this.userProgressRepository.find({
      where: {
        user: { id: userId },
        date: Between(startDate, endDate),
      },
      order: { date: 'DESC' },
      relations: ['user'],
    });

    if (progressData.length === 0) {
      // Return default report if no data available
      return this.generateDefaultReport();
    }

    // Get user details
    const user = progressData[0].user;

    // Gather comprehensive user data
    const comprehensiveData = await this.gatherComprehensiveUserData(user);

    // Calculate basic metrics
    const metrics = this.calculateLifestyleMetrics(progressData);
    const overallScore = this.calculateOverallLifestyleScore(metrics);

    // Use AI to generate intelligent insights and recommendations
    let aiGeneratedContent: {
      personalityInsights: any[];
      improvements: any[];
      performanceAnalysis: { strengths: string[]; criticalAreas: string[] };
      motivationalMessage: string;
    } | null = null;
    
    if (this.genAI) {
      try {
        aiGeneratedContent = await this.generateAIInsights({
          user,
          progressData,
          metrics,
          days,
        });
      } catch (error) {
        this.logger.error('Error generating AI insights:', error);
      }
    }

    // Merge AI insights with traditional analysis
    const personalityInsights = aiGeneratedContent?.personalityInsights || 
      this.generatePersonalityInsights(progressData);
    
    const improvements = aiGeneratedContent?.improvements || 
      this.generateImprovementRecommendations(progressData, metrics);
    
    const { strengths, criticalAreas } = aiGeneratedContent?.performanceAnalysis || 
      this.analyzePerformanceAreas(metrics);
    
    const motivationalMessage = aiGeneratedContent?.motivationalMessage || 
      this.generateMotivationalMessage(progressData, overallScore);

    // Calculate projected score
    const projectedScore = Math.min(100, overallScore + (improvements.length > 0 ? improvements[0].improvementPotential : 0));

    return {
      overallScore,
      metrics,
      improvements,
      personalityInsights,
      strengths,
      criticalAreas,
      projectedScore,
      analysisDateRange: days,
      generatedAt: new Date(),
      motivationalMessage,
      aiEnhanced: !!aiGeneratedContent,
    };
  }

  private async generateUserInsights(userId: string, progress: UserProgress[]): Promise<{
    mostProductiveTime: string;
    strongestHabits: string[];
    areasForImprovement: string[];
    streakMilestones: { [habitId: string]: number };
    recommendedActions: string[];
  }> {
    const user = await this.usersService.findOne(userId);
    const habits = await this.habitsService.findAllByUser(user);
    const tasks = await this.tasksService.findAll(userId);
    
    // Analyze productive hours
    const productiveHours = this.analyzeProductiveHours(progress);
    const mostProductiveTime = this.determineMostProductiveTime(productiveHours);

    // Analyze habits
    const habitAnalysis = this.analyzeHabits(habits, progress);
    const strongestHabits = habitAnalysis.strongest;
    const weakestHabits = habitAnalysis.weakest;

    // Generate recommendations
    const recommendedActions = await this.generateRecommendations({
      habits: habitAnalysis,
      tasks,
      productiveHours,
      progress,
    });

    return {
      mostProductiveTime,
      strongestHabits,
      areasForImprovement: weakestHabits,
      streakMilestones: this.identifyStreakMilestones(habits),
      recommendedActions,
    };
  }

  private async generateAIInsights(context: {
    user: any;
    progressData: any[];
    metrics: any[];
    days: number;
  }): Promise<{
    personalityInsights: any[];
    improvements: any[];
    performanceAnalysis: { strengths: string[]; criticalAreas: string[] };
    motivationalMessage: string;
  }> {
    if (!this.genAI) {
      this.logger.warn('GenAI not initialized - falling back to traditional insights');
      return this.generateTraditionalInsights(context);
    }

    try {
      const prompt = this.buildAIInsightsPrompt(context);
      
      const response = await this.genAI.models.generateContent({
        model: "gemini-2.0-flash",
        contents: [{ parts: [{ text: prompt }] }],
      });
      
      const content = response.text?.trim();
      if (!content) {
        throw new Error('Empty response from AI');
      }

      // Parse the AI response
      const aiInsights = this.parseAIInsightsResponse(content);
      return aiInsights;
      
    } catch (error) {
      this.logger.error('Error generating AI insights:', error);
      return this.generateTraditionalInsights(context);
    }
  }

  private buildAIInsightsPrompt(context: {
    user: any;
    progressData: any[];
    metrics: any[];
    days: number;
  }): string {
    const { user, progressData, metrics, days } = context;
    
    // Calculate summary statistics
    const totalDays = progressData.length;
    const avgTaskCompletion = totalDays > 0 ? 
      progressData.reduce((acc, p) => acc + (p.tasks.total > 0 ? (p.tasks.completed / p.tasks.total) * 100 : 0), 0) / totalDays : 0;
    const avgHabitConsistency = totalDays > 0 ? 
      progressData.reduce((acc, p) => acc + (p.habits.total > 0 ? (p.habits.completed / p.habits.total) * 100 : 0), 0) / totalDays : 0;
    const avgFocusTime = totalDays > 0 ? 
      progressData.reduce((acc, p) => acc + (p.metrics?.focusMinutes || 0), 0) / totalDays : 0;

    // Analyze productivity patterns
    const productivityHours = this.analyzeProductivityPatterns(progressData);
    const moodPatterns = this.analyzeMoodPatterns(progressData);

    const prompt = `
As a lifestyle improvement AI consultant, analyze the following user data and provide personalized insights and recommendations in JSON format.

User Profile:
- Name: ${user.firstName} ${user.lastName}
- Analysis Period: ${days} days (${totalDays} days of actual data)

Performance Metrics:
- Average Task Completion Rate: ${avgTaskCompletion.toFixed(1)}%
- Average Habit Consistency: ${avgHabitConsistency.toFixed(1)}%
- Average Daily Focus Time: ${avgFocusTime.toFixed(1)} minutes
- Productivity Patterns: ${productivityHours}
- Mood Patterns: ${moodPatterns}

Current Metrics:
${metrics.map(m => `- ${m.name}: ${m.currentScore}/100 (${m.trend}, ${m.changePercentage}% change)`).join('\n')}

Please provide a comprehensive analysis in the following JSON format:

{
  "personalityInsights": [
    {
      "insight": "Specific behavioral insight based on data patterns",
      "category": "Category (e.g., Productivity Pattern, Emotional Pattern, Behavioral Pattern)",
      "confidence": 80
    }
  ],
  "improvements": [
    {
      "area": "Specific area for improvement",
      "currentScore": 65,
      "improvementPotential": 25,
      "actions": [
        {
          "title": "Specific actionable recommendation",
          "description": "Detailed description of the action",
          "impact": "high|medium|low",
          "difficulty": "easy|medium|hard",
          "timeToResults": 14,
          "category": "Category of improvement"
        }
      ],
      "evidence": "Data-based evidence supporting this recommendation"
    }
  ],
  "performanceAnalysis": {
    "strengths": ["List of user's strong areas"],
    "criticalAreas": ["Areas needing immediate attention"]
  },
  "motivationalMessage": "Personalized, encouraging message based on their progress and personality"
}

Guidelines:
1. Base all insights on the actual data provided
2. Be specific and actionable in recommendations
3. Consider their personality patterns when suggesting improvements
4. Prioritize improvements by potential impact
5. Make the motivational message personal and encouraging
6. Ensure confidence scores reflect data quality and pattern strength
7. Focus on sustainable, realistic changes

Return only the JSON response, no additional text.`;

    return prompt;
  }

  private parseAIInsightsResponse(content: string): {
    personalityInsights: any[];
    improvements: any[];
    performanceAnalysis: { strengths: string[]; criticalAreas: string[] };
    motivationalMessage: string;
  } {
    try {
      // Clean up the response - remove any markdown formatting
      const cleanedContent = content
        .replace(/```json/g, '')
        .replace(/```/g, '')
        .trim();

      const parsed = JSON.parse(cleanedContent);
      
      // Validate the structure
      return {
        personalityInsights: parsed.personalityInsights || [],
        improvements: parsed.improvements || [],
        performanceAnalysis: parsed.performanceAnalysis || { strengths: [], criticalAreas: [] },
        motivationalMessage: parsed.motivationalMessage || 'Keep up the great work!',
      };
    } catch (error) {
      this.logger.error('Error parsing AI insights response:', error);
      // Return default structure if parsing fails
      return {
        personalityInsights: [],
        improvements: [],
        performanceAnalysis: { strengths: [], criticalAreas: [] },
        motivationalMessage: 'Keep pushing forward - every step counts!',
      };
    }
  }

  private generateTraditionalInsights(context: {
    user: any;
    progressData: any[];
    metrics: any[];
    days: number;
  }): {
    personalityInsights: any[];
    improvements: any[];
    performanceAnalysis: { strengths: string[]; criticalAreas: string[] };
    motivationalMessage: string;
  } {
    // Fallback to original logic when AI is not available
    const { progressData, metrics } = context;
    
    const personalityInsights = this.generatePersonalityInsights(progressData);
    const improvements = this.generateImprovementRecommendations(progressData, metrics);
    const performanceAnalysis = this.analyzePerformanceAreas(metrics);
    const overallScore = this.calculateOverallLifestyleScore(metrics);
    const motivationalMessage = this.generateMotivationalMessage(progressData, overallScore);

    return {
      personalityInsights,
      improvements,
      performanceAnalysis,
      motivationalMessage,
    };
  }

  private generateDefaultReport(): any {
    return {
      overallScore: 50,
      metrics: [
        {
          name: 'Task Completion',
          currentScore: 50,
          targetScore: 80,
          trend: 'stable',
          changePercentage: 0,
        },
        {
          name: 'Habit Consistency',
          currentScore: 50,
          targetScore: 85,
          trend: 'stable',
          changePercentage: 0,
        },
      ],
      improvements: [
        {
          area: 'Getting Started',
          currentScore: 50,
          improvementPotential: 30,
          actions: [
            {
              title: 'Start Building Daily Habits',
              description: 'Begin with 2-3 simple habits like drinking water, making your bed, or reading for 10 minutes',
              impact: 'high',
              difficulty: 'easy',
              timeToResults: 7,
              category: 'Habits',
            },
          ],
          evidence: 'No activity data available yet. Start tracking your daily activities to get personalized insights.',
        },
      ],
      personalityInsights: [
        {
          insight: 'Welcome to your improvement journey! Start small and stay consistent.',
          category: 'Getting Started',
          confidence: 100,
        },
      ],
      strengths: ['Ready to improve'],
      criticalAreas: ['Need to start tracking activities'],
      projectedScore: 80,
      analysisDateRange: 30,
      generatedAt: new Date(),
      motivationalMessage: 'Every expert was once a beginner. Start your journey today!',
    };
  }

  // Helper methods for analysis
  private analyzeProductiveHours(progress: UserProgress[]): number[] {
    const hourlyProductivity = new Array(24).fill(0);
    
    progress.forEach(p => {
      if (p.metrics?.productiveHours) {
        p.metrics.productiveHours.forEach((value, hour) => {
          hourlyProductivity[hour] += value;
        });
      }
    });

    return hourlyProductivity;
  }

  private determineMostProductiveTime(hourlyProductivity: number[]): string {
    const maxProductivity = Math.max(...hourlyProductivity);
    const mostProductiveHour = hourlyProductivity.indexOf(maxProductivity);
    
    // Convert to human-readable time range
    const startHour = mostProductiveHour;
    const endHour = (mostProductiveHour + 1) % 24;
    
    return `${startHour.toString().padStart(2, '0')}:00-${endHour.toString().padStart(2, '0')}:00`;
  }

  private analyzeHabits(habits: any[], progress: UserProgress[]) {
    const habitStats = habits.map(habit => {
      const completionRate = progress.reduce((acc, p) => {
        return acc + (p.habits.streaks[habit.id] > 0 ? 1 : 0);
      }, 0) / progress.length;

      return {
        id: habit.id,
        name: habit.name,
        completionRate,
        currentStreak: habit.currentStreak,
      };
    });

    // Sort by completion rate and current streak
    habitStats.sort((a, b) => 
      (b.completionRate * 0.7 + b.currentStreak * 0.3) - 
      (a.completionRate * 0.7 + a.currentStreak * 0.3)
    );

    return {
      strongest: habitStats.slice(0, 3).map(h => h.name),
      weakest: habitStats.slice(-3).map(h => h.name),
      all: habitStats,
    };
  }

  private identifyStreakMilestones(habits: any[]): { [habitId: string]: number } {
    const milestones = {};
    
    habits.forEach(habit => {
      if (habit.currentStreak >= 7) {
        milestones[habit.id] = habit.currentStreak;
      }
    });

    return milestones;
  }

  private async generateRecommendations({
    habits,
    tasks,
    productiveHours,
    progress,
  }): Promise<string[]> {
    const recommendations: string[] = [];

    // Analyze task patterns
    if (tasks?.length > 0) {
      const overdueTasks = tasks.filter(t => !t.completed && new Date(t.dueDate) < new Date());
      if (overdueTasks.length > 0) {
        recommendations.push(`You have ${overdueTasks.length} overdue tasks. Consider breaking them into smaller steps.`);
      }
    }

    // Calculate average consistency score
    if (progress?.length > 0) {
      const recentProgress = progress.slice(-7);
      const validProgress = recentProgress.filter(p => p.metrics?.consistencyScore !== undefined);
      
      if (validProgress.length > 0) {
        const avgConsistency = validProgress.reduce((acc, p) => acc + p.metrics.consistencyScore, 0) / validProgress.length;
        
        if (avgConsistency < 60) {
          recommendations.push('Focus on building more consistent daily routines to improve your overall performance.');
        }
      }
    }

    return recommendations;
  }

  private analyzeProductivityPatterns(progressData: any[]): string {
    const productiveHours = progressData
      .filter(p => p.metrics?.productiveHours)
      .flatMap(p => p.metrics.productiveHours.map((value, hour) => ({ hour, value })))
      .reduce((acc, { hour, value }) => {
        acc[hour] = (acc[hour] || 0) + value;
        return acc;
      }, {} as { [hour: number]: number });

    if (Object.keys(productiveHours).length === 0) {
      return 'No productivity data available';
    }

    const bestHour = Object.entries(productiveHours)
      .sort(([, a], [, b]) => (b as number) - (a as number))[0];

    if (bestHour) {
      const hour = parseInt(bestHour[0]);
      const timeLabel = hour < 12 ? 'morning' : hour < 17 ? 'afternoon' : 'evening';
      return `Most productive during ${timeLabel} hours (around ${hour}:00)`;
    }

    return 'Productivity patterns unclear';
  }

  private analyzeMoodPatterns(progressData: any[]): string {
    const moodEntries = progressData
      .filter(p => p.metrics?.moodTracking?.length > 0)
      .flatMap(p => p.metrics.moodTracking);

    if (moodEntries.length === 0) {
      return 'No mood data available';
    }

    const moodCounts = moodEntries.reduce((acc, entry) => {
      acc[entry.mood] = (acc[entry.mood] || 0) + 1;
      return acc;
    }, {} as { [mood: string]: number });

    const dominantMood = Object.entries(moodCounts)
      .sort(([, a], [, b]) => (b as number) - (a as number))[0][0];

    return `Most common mood: ${dominantMood} (${moodCounts[dominantMood]} entries)`;
  }

  private calculateLifestyleMetrics(progressData: any[]): any[] {
    const totalDays = progressData.length;
    if (totalDays === 0) return [];

    // Task completion metrics
    const avgTaskCompletion = progressData.reduce((acc, p) => {
      return acc + (p.tasks.total > 0 ? (p.tasks.completed / p.tasks.total) * 100 : 0);
    }, 0) / totalDays;

    const taskTrend = this.calculateTrend(
      progressData.slice(-7).map(p => p.tasks.total > 0 ? (p.tasks.completed / p.tasks.total) * 100 : 0),
      progressData.slice(-14, -7).map(p => p.tasks.total > 0 ? (p.tasks.completed / p.tasks.total) * 100 : 0)
    );

    // Habit consistency metrics
    const avgHabitConsistency = progressData.reduce((acc, p) => {
      return acc + (p.habits.total > 0 ? (p.habits.completed / p.habits.total) * 100 : 0);
    }, 0) / totalDays;

    const habitTrend = this.calculateTrend(
      progressData.slice(-7).map(p => p.habits.total > 0 ? (p.habits.completed / p.habits.total) * 100 : 0),
      progressData.slice(-14, -7).map(p => p.habits.total > 0 ? (p.habits.completed / p.habits.total) * 100 : 0)
    );

    // Focus time metrics
    const avgFocusTime = progressData.reduce((acc, p) => acc + (p.metrics?.focusMinutes || 0), 0) / totalDays;
    const focusTrend = this.calculateTrend(
      progressData.slice(-7).map(p => p.metrics?.focusMinutes || 0),
      progressData.slice(-14, -7).map(p => p.metrics?.focusMinutes || 0)
    );

    // Consistency score metrics
    const avgConsistencyScore = progressData.reduce((acc, p) => acc + (p.metrics?.consistencyScore || 50), 0) / totalDays;
    const consistencyTrend = this.calculateTrend(
      progressData.slice(-7).map(p => p.metrics?.consistencyScore || 50),
      progressData.slice(-14, -7).map(p => p.metrics?.consistencyScore || 50)
    );

    return [
      {
        name: 'Task Completion Rate',
        currentScore: Math.round(avgTaskCompletion),
        targetScore: 85,
        trend: taskTrend.trend,
        changePercentage: taskTrend.change,
      },
      {
        name: 'Habit Consistency',
        currentScore: Math.round(avgHabitConsistency),
        targetScore: 90,
        trend: habitTrend.trend,
        changePercentage: habitTrend.change,
      },
      {
        name: 'Daily Focus Time',
        currentScore: Math.min(100, Math.round((avgFocusTime / 120) * 100)), // Target: 2 hours
        targetScore: 80,
        trend: focusTrend.trend,
        changePercentage: focusTrend.change,
      },
      {
        name: 'Overall Consistency',
        currentScore: Math.round(avgConsistencyScore),
        targetScore: 85,
        trend: consistencyTrend.trend,
        changePercentage: consistencyTrend.change,
      },
    ];
  }

  private calculateTrend(recentValues: number[], olderValues: number[]): { trend: 'improving' | 'declining' | 'stable', change: number } {
    if (recentValues.length === 0 || olderValues.length === 0) {
      return { trend: 'stable', change: 0 };
    }

    const recentAvg = recentValues.reduce((a, b) => a + b, 0) / recentValues.length;
    const olderAvg = olderValues.reduce((a, b) => a + b, 0) / olderValues.length;
    
    const change = olderAvg > 0 ? ((recentAvg - olderAvg) / olderAvg) * 100 : 0;
    
    let trend: 'improving' | 'declining' | 'stable' = 'stable';
    if (Math.abs(change) > 5) {
      trend = change > 0 ? 'improving' : 'declining';
    }

    return { trend, change: Math.round(change * 10) / 10 };
  }

  private generatePersonalityInsights(progressData: any[]): any[] {
    const insights: any[] = [];

    // Analyze productive times
    const productiveHours = progressData
      .filter(p => p.metrics?.productiveHours)
      .flatMap(p => p.metrics.productiveHours.map((value, hour) => ({ hour, value })))
      .reduce((acc, { hour, value }) => {
        acc[hour] = (acc[hour] || 0) + value;
        return acc;
      }, {} as { [hour: number]: number });

    const bestHour = Object.entries(productiveHours)
      .sort(([, a], [, b]) => (b as number) - (a as number))[0];

    if (bestHour) {
      const hour = parseInt(bestHour[0]);
      const timeLabel = hour < 12 ? 'morning' : hour < 17 ? 'afternoon' : 'evening';
      insights.push({
        insight: `You are most productive during the ${timeLabel} hours (around ${hour}:00)`,
        category: 'Productivity Pattern',
        confidence: 75,
      });
    }

    // Analyze mood patterns
    const moodEntries = progressData
      .filter(p => p.metrics?.moodTracking?.length > 0)
      .flatMap(p => p.metrics.moodTracking);

    if (moodEntries.length > 0) {
      const moodCounts = moodEntries.reduce((acc, entry) => {
        acc[entry.mood] = (acc[entry.mood] || 0) + 1;
        return acc;
      }, {} as { [mood: string]: number });

      const dominantMood = Object.entries(moodCounts)
        .sort(([, a], [, b]) => (b as number) - (a as number))[0][0];

      insights.push({
        insight: `Your most common mood state is "${dominantMood}", indicating ${this.getMoodInsight(dominantMood)}`,
        category: 'Emotional Pattern',
        confidence: 80,
      });
    }

    // Analyze consistency patterns
    const consistencyScores = progressData.map(p => p.metrics?.consistencyScore || 50);
    const avgConsistency = consistencyScores.reduce((a, b) => a + b, 0) / consistencyScores.length;

    if (avgConsistency > 75) {
      insights.push({
        insight: 'You have excellent consistency in your daily routines',
        category: 'Behavioral Pattern',
        confidence: 85,
      });
    } else if (avgConsistency < 50) {
      insights.push({
        insight: 'Your routine consistency could benefit from more structure and planning',
        category: 'Behavioral Pattern',
        confidence: 80,
      });
    }

    return insights;
  }

  private getMoodInsight(mood: string): string {
    const insights = {
      'great': 'high emotional well-being and life satisfaction',
      'good': 'positive emotional balance and contentment',
      'neutral': 'stable emotional state with room for more joy',
      'bad': 'challenges that may benefit from attention and support',
      'terrible': 'significant emotional distress that deserves care and attention',
    };
    return insights[mood] || 'a unique emotional experience';
  }

  private generateImprovementRecommendations(progressData: any[], metrics: any[]): any[] {
    const improvements: any[] = [];

    // Task completion improvement
    const taskMetric = metrics.find(m => m.name === 'Task Completion Rate');
    if (taskMetric && taskMetric.currentScore < 70) {
      improvements.push({
        area: 'Task Management',
        currentScore: taskMetric.currentScore,
        improvementPotential: Math.min(30, 85 - taskMetric.currentScore),
        actions: [
          {
            title: 'Implement Time Blocking',
            description: 'Schedule specific time blocks for different types of tasks to improve focus and completion rates',
            impact: 'high',
            difficulty: 'medium',
            timeToResults: 14,
            category: 'Productivity',
          },
          {
            title: 'Use the 2-Minute Rule',
            description: 'Complete any task that takes less than 2 minutes immediately instead of adding it to your list',
            impact: 'medium',
            difficulty: 'easy',
            timeToResults: 3,
            category: 'Productivity',
          },
        ],
        evidence: `Your task completion rate is ${taskMetric.currentScore}%, which is below optimal performance`,
      });
    }

    // Habit consistency improvement
    const habitMetric = metrics.find(m => m.name === 'Habit Consistency');
    if (habitMetric && habitMetric.currentScore < 75) {
      improvements.push({
        area: 'Habit Building',
        currentScore: habitMetric.currentScore,
        improvementPotential: Math.min(25, 90 - habitMetric.currentScore),
        actions: [
          {
            title: 'Start with Habit Stacking',
            description: 'Link new habits to existing ones. For example: "After I brush my teeth, I will do 10 push-ups"',
            impact: 'high',
            difficulty: 'easy',
            timeToResults: 21,
            category: 'Habits',
          },
          {
            title: 'Use Environmental Design',
            description: 'Make good habits obvious and bad habits invisible by designing your environment accordingly',
            impact: 'medium',
            difficulty: 'medium',
            timeToResults: 7,
            category: 'Habits',
          },
        ],
        evidence: `Your habit consistency is ${habitMetric.currentScore}%, indicating room for more reliable daily routines`,
      });
    }

    // Focus time improvement
    const focusMetric = metrics.find(m => m.name === 'Daily Focus Time');
    if (focusMetric && focusMetric.currentScore < 60) {
      improvements.push({
        area: 'Deep Work & Focus',
        currentScore: focusMetric.currentScore,
        improvementPotential: Math.min(35, 80 - focusMetric.currentScore),
        actions: [
          {
            title: 'Implement Pomodoro Technique',
            description: 'Work in 25-minute focused intervals followed by 5-minute breaks to maintain high concentration',
            impact: 'high',
            difficulty: 'easy',
            timeToResults: 7,
            category: 'Focus',
          },
          {
            title: 'Create a Distraction-Free Zone',
            description: 'Designate a specific area for focused work with minimal distractions and proper setup',
            impact: 'medium',
            difficulty: 'medium',
            timeToResults: 3,
            category: 'Focus',
          },
        ],
        evidence: `Your daily focus time indicates potential for deeper, more concentrated work sessions`,
      });
    }

    // Sort by impact potential
    return improvements.sort((a, b) => b.improvementPotential - a.improvementPotential);
  }

  private calculateOverallLifestyleScore(metrics: any[]): number {
    if (metrics.length === 0) return 50;

    const weights = {
      'Task Completion Rate': 0.25,
      'Habit Consistency': 0.35,
      'Daily Focus Time': 0.25,
      'Overall Consistency': 0.15,
    };

    const weightedScore = metrics.reduce((acc, metric) => {
      const weight = weights[metric.name] || 0;
      return acc + (metric.currentScore * weight);
    }, 0);

    return Math.round(weightedScore);
  }

  private analyzePerformanceAreas(metrics: any[]): { strengths: string[], criticalAreas: string[] } {
    const strengths: string[] = [];
    const criticalAreas: string[] = [];

    metrics.forEach(metric => {
      if (metric.currentScore >= 80) {
        strengths.push(metric.name);
      } else if (metric.currentScore < 50) {
        criticalAreas.push(metric.name);
      }
    });

    // Add default values if none found
    if (strengths.length === 0) {
      strengths.push('Willingness to improve');
    }
    if (criticalAreas.length === 0) {
      criticalAreas.push('Consistency in daily routines');
    }

    return { strengths, criticalAreas };
  }

  private generateMotivationalMessage(progressData: any[], overallScore: number): string {
    const messages = {
      excellent: [
        "Outstanding work! You're in the top tier of lifestyle management. Keep up this amazing momentum!",
        "Your consistency is truly impressive! You're setting a great example of what disciplined living looks like.",
        "Fantastic results! Your daily habits are clearly paying off in meaningful ways.",
      ],
      good: [
        "Great progress! You're building solid foundations for long-term success.",
        "You're doing well! A few small adjustments could take your results to the next level.",
        "Nice work! Your efforts are showing real results and positive trends.",
      ],
      average: [
        "You're on the right track! Every small step forward is building toward bigger changes.",
        "Good foundation! Focus on consistency in one or two key areas to accelerate your progress.",
        "Solid start! The key is maintaining momentum - you've got this!",
      ],
      needsWork: [
        "Every journey starts with a single step. You're building the foundation for positive change!",
        "Starting is the hardest part, and you've already begun. Focus on small, consistent wins.",
        "Remember, progress isn't about perfection. It's about moving forward, one day at a time.",
      ],
    };

    let category: keyof typeof messages;
    if (overallScore >= 80) category = 'excellent';
    else if (overallScore >= 65) category = 'good';
    else if (overallScore >= 50) category = 'average';
    else category = 'needsWork';

    const relevantMessages = messages[category];
    return relevantMessages[Math.floor(Math.random() * relevantMessages.length)];
  }

  private async gatherComprehensiveUserData(user: any) {
    try {
      // Fetch user's habits
      const userHabits = await this.habitsService.findAllByUser(user);
      const habitsData = userHabits.map(habit => ({
        name: habit.name,
        streak: habit.currentStreak,
        category: 'General',
        difficulty: 'medium',
      }));

      // Fetch user's tasks
      const userTasks = await this.tasksService.findAll(user.id);
      const tasksData = userTasks.map(task => ({
        title: task.title,
        priority: task.priority,
        completed: task.completed,
        dueDate: task.dueDate,
        category: 'General',
      }));

      // Fetch user's skill plans
      const userSkillPlans = await this.skillPlansService.findAll({ creatorId: user.id });
      const skillPlansData = userSkillPlans.map(plan => ({
        name: plan.name,
        progress: plan.progress,
        category: plan.metadata?.category || 'General',
        difficulty: 'medium',
      }));

      // Fetch user's challenges
      const allChallenges = await this.challengesService.findAll();
      const userChallenges = allChallenges.filter(challenge => 
        challenge.participants?.some(participant => participant.id === user.id)
      );
      const challengesData = userChallenges.map(challenge => ({
        name: challenge.name,
        progress: challenge.progress[user.id] || null,
        category: 'General',
        difficulty: 'medium',
      }));

      return {
        habits: habitsData,
        tasks: tasksData,
        skillPlans: skillPlansData,
        challenges: challengesData,
        userProfile: {
          firstName: user.firstName,
          goals: user.goals || [],
          badges: user.badges || [],
          xp: user.xp || 0,
          level: user.level || 1,
        },
      };
    } catch (error) {
      this.logger.error('Error gathering comprehensive user data:', error);
      return {};
    }
  }
}
