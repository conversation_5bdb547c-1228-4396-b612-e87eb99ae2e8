import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { UserProgress } from '../entities/user-progress.entity';
import { UsersService } from '../../users/users.service';
import { HabitsService } from '../../habits/habits.service';
import { TasksService } from '../../tasks/tasks.service';
import { SkillPlansService } from '../../skill-plans/skill-plans.service';
import { ChallengesService } from '../../challenges/challenges.service';
import { UpdateDailyProgressDto, UpdateUserProgressDto } from '../dto';

@Injectable()
export class ProgressTrackerService {
  private readonly logger = new Logger(ProgressTrackerService.name);

  constructor(
    @InjectRepository(UserProgress)
    private userProgressRepository: Repository<UserProgress>,
    private usersService: UsersService,
    private habitsService: HabitsService,
    private tasksService: TasksService,
    private skillPlansService: SkillPlansService,
    private challengesService: ChallengesService,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async trackDailyProgress() {
    const users = await this.usersService.findAll();
    const date = new Date();
    date.setHours(0, 0, 0, 0);

    for (const user of users) {
      try {
        // Get habits progress
        const habits = await this.habitsService.findAllByUser(user);
        const habitStats = {
          completed: habits.filter(h => h.completion[date.toISOString().split('T')[0]]).length,
          total: habits.length,
          streaks: habits.reduce((acc, h) => ({ ...acc, [h.id]: h.currentStreak }), {}),
        };

        // Get tasks progress
        const tasks = await this.tasksService.findAll(user.id);
        const taskStats = {
          completed: tasks.filter(t => t.completed).length,
          total: tasks.length,
          overdue: tasks.filter(t => !t.completed && new Date(t.dueDate) < date).length,
        };

        // Get skill plans progress
        const skillPlans = await this.skillPlansService.findAll({ creatorId: user.id });
        const skillPlanStats = {
          active: skillPlans.length,
          completedSteps: skillPlans.reduce((acc, sp) => 
            acc + sp.steps.filter(s => s.tasks?.every(t => t.isCompleted)).length, 0),
          totalSteps: skillPlans.reduce((acc, sp) => acc + sp.steps.length, 0),
        };

        // Get challenges progress
        const challenges = await this.challengesService.findAll();
        const userChallenges = challenges.filter(c => 
          c.participants?.some(p => p.id === user.id)
        );
        const now = new Date();
        const challengeStats = {
          active: userChallenges.filter(c => 
            new Date(c.startDate) <= now && new Date(c.endDate) >= now
          ).length,
          completed: userChallenges.filter(c => 
            new Date(c.endDate) < now
          ).length,
        };

        // Calculate consistency score
        const consistencyScore = this.calculateConsistencyScore({
          habitCompletion: habitStats.total > 0 ? habitStats.completed / habitStats.total : 0,
          taskCompletion: taskStats.total > 0 ? taskStats.completed / taskStats.total : 0,
          skillPlanProgress: skillPlanStats.totalSteps > 0 ? skillPlanStats.completedSteps / skillPlanStats.totalSteps : 0,
        });

        // Create or update user progress
        const progress = this.userProgressRepository.create({
          user,
          date,
          habits: habitStats,
          tasks: taskStats,
          skillPlans: skillPlanStats,
          challenges: challengeStats,
          metrics: {
            consistencyScore,
            focusMinutes: 0,
            podcastsListened: 0,
            moodTracking: [],
            productiveHours: new Array(24).fill(0),
          },
          xpGained: 0,
          badgesEarned: [],
        });

        await this.userProgressRepository.save(progress);
      } catch (error) {
        this.logger.error(`Error tracking progress for user ${user.id}:`, error);
      }
    }
  }

  async updateDailyProgress(userId: string, updates: UpdateDailyProgressDto) {
    const date = new Date();
    date.setHours(0, 0, 0, 0);

    const progress = await this.userProgressRepository.findOne({
      where: {
        user: { id: userId },
        date,
      },
    });

    if (progress) {
      if (updates.xpGained) {
        progress.xpGained += updates.xpGained;
      }

      if (updates.badgeEarned && !progress.badgesEarned.includes(updates.badgeEarned)) {
        progress.badgesEarned.push(updates.badgeEarned);
      }

      if (updates.focusMinutes) {
        progress.metrics.focusMinutes += updates.focusMinutes;
        this.trackDailyProductivity(progress);
      }

      if (updates.podcastListened) {
        progress.metrics.podcastsListened += 1;
      }

      await this.userProgressRepository.save(progress);
    }
  }

  async recordFocusSession(userId: string, minutes: number): Promise<void> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    let progress = await this.userProgressRepository.findOne({
      where: {
        user: { id: userId },
        date: today,
      },
      relations: ['user'],
    });

    if (progress) {
      progress.metrics.focusMinutes += minutes;
      this.trackDailyProductivity(progress);
      await this.userProgressRepository.save(progress);
    }
  }

  async recordMoodEntry(userId: string, mood: 'great' | 'good' | 'neutral' | 'bad' | 'terrible'): Promise<void> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    let progress = await this.userProgressRepository.findOne({
      where: {
        user: { id: userId },
        date: today,
      },
      relations: ['user'],
    });

    if (progress) {
      progress.metrics.moodTracking.push({
        mood,
        timestamp: new Date().toISOString(),
      });
      await this.userProgressRepository.save(progress);
    }
  }

  async updateUserProgress(userId: string, update: UpdateUserProgressDto): Promise<UserProgress> {
    let progress = await this.userProgressRepository.findOne({
      where: { user: { id: userId }, date: new Date() }
    });

    if (!progress) {
      progress = this.userProgressRepository.create({
        user: { id: userId },
        date: new Date(),
        habits: { completed: 0, total: 0, streaks: {} },
        tasks: { completed: 0, total: 0, overdue: 0 },
        skillPlans: { active: 0, completedSteps: 0, totalSteps: 0 },
        challenges: { active: 0, completed: 0 },
        metrics: {
          focusMinutes: 0,
          podcastsListened: 0,
          consistencyScore: 0,
          productiveHours: new Array(24).fill(0),
          moodTracking: [],
        },
        xpGained: 0,
        totalXp: 0,
        badgesEarned: []
      });
    }

    if (update.skillPlans) {
      progress.skillPlans = {
        ...progress.skillPlans,
        ...update.skillPlans
      };
    }

    if (update.xpGained) {
      progress.xpGained += update.xpGained;
    }

    if (update.badgesEarned) {
      progress.badgesEarned = [...(progress.badgesEarned || []), ...update.badgesEarned];
    }

    return this.userProgressRepository.save(progress);
  }

  private calculateConsistencyScore(metrics: {
    habitCompletion: number;
    taskCompletion: number;
    skillPlanProgress: number;
  }): number {
    const weights = {
      habits: 0.4,
      tasks: 0.4,
      skillPlans: 0.2,
    };

    const score = (
      metrics.habitCompletion * weights.habits +
      metrics.taskCompletion * weights.tasks +
      metrics.skillPlanProgress * weights.skillPlans
    ) * 100;

    return Math.round(Math.min(100, Math.max(0, score)));
  }

  private trackDailyProductivity(progress: UserProgress): void {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (progress.date.getTime() === today.getTime()) {
      const hour = new Date().getHours();
      if (!progress.metrics.productiveHours.includes(hour) && progress.metrics.focusMinutes > 0) {
        progress.metrics.productiveHours[hour] += 1;
      }
    }
  }
}
