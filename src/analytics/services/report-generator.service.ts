import { Injectable, Logger } from '@nestjs/common';
import { ProgressTrackerService } from './progress-tracker.service';
import { AnalyticsCalculatorService } from './analytics-calculator.service';
import { InsightsGeneratorService } from './insights-generator.service';

@Injectable()
export class ReportGeneratorService {
  private readonly logger = new Logger(ReportGeneratorService.name);

  constructor(
    private progressTracker: ProgressTrackerService,
    private analyticsCalculator: AnalyticsCalculatorService,
    private insightsGenerator: InsightsGeneratorService,
  ) {}

  async generateProgressReport(userId: string, period: 'day' | 'week' | 'month' | 'year') {
    try {
      // Get basic progress data
      const progressData = await this.analyticsCalculator.getUserProgress(userId, period);
      
      // Get habit analytics
      const habitAnalytics = period !== 'day' 
        ? await this.analyticsCalculator.getHabitAnalytics(userId, period as 'week' | 'month' | 'year')
        : null;

      // Get task completion stats
      const taskStats = period === 'week' || period === 'month'
        ? await this.analyticsCalculator.getTaskCompletionStats(userId, period)
        : null;

      // Get productivity analytics
      const productivityAnalytics = period === 'week' || period === 'month'
        ? await this.analyticsCalculator.getProductivityAnalytics(userId, period)
        : null;

      // Get personalized insights
      const personalizedInsights = await this.insightsGenerator.getPersonalizedInsights(userId);

      return {
        period,
        generatedAt: new Date(),
        progress: progressData,
        habits: habitAnalytics,
        tasks: taskStats,
        productivity: productivityAnalytics,
        insights: personalizedInsights,
        summary: this.generateSummary(progressData, habitAnalytics, taskStats),
      };
    } catch (error) {
      this.logger.error(`Error generating progress report for user ${userId}:`, error);
      throw error;
    }
  }

  async generateHabitReport(userId: string, period: 'week' | 'month' | 'year') {
    try {
      const habitAnalytics = await this.analyticsCalculator.getHabitAnalytics(userId, period);
      const personalizedInsights = await this.insightsGenerator.getPersonalizedInsights(userId);

      // Extract habit-specific insights
      const habitSpecificInsights = {
        strongestHabits: personalizedInsights.strongestHabits,
        areasForImprovement: personalizedInsights.areasForImprovement,
        streakMilestones: personalizedInsights.streakMilestones,
        recommendations: personalizedInsights.recommendedActions.filter(action => 
          action.toLowerCase().includes('habit')
        ),
      };

      return {
        period,
        generatedAt: new Date(),
        analytics: habitAnalytics,
        insights: habitSpecificInsights,
        summary: this.generateHabitSummary(habitAnalytics),
      };
    } catch (error) {
      this.logger.error(`Error generating habit report for user ${userId}:`, error);
      throw error;
    }
  }

  async generateProductivityReport(userId: string, period: 'week' | 'month') {
    try {
      const productivityAnalytics = await this.analyticsCalculator.getProductivityAnalytics(userId, period);
      const moodAnalytics = await this.analyticsCalculator.getMoodAnalytics(userId, period);
      const weeklyStats = await this.analyticsCalculator.aggregateWeeklyStats(userId);

      return {
        period,
        generatedAt: new Date(),
        productivity: productivityAnalytics,
        mood: moodAnalytics,
        weeklyStats,
        summary: this.generateProductivitySummary(productivityAnalytics, weeklyStats),
        recommendations: this.generateProductivityRecommendations(productivityAnalytics, moodAnalytics),
      };
    } catch (error) {
      this.logger.error(`Error generating productivity report for user ${userId}:`, error);
      throw error;
    }
  }

  async generateAIImprovementReport(userId: string, days: number = 30) {
    try {
      return await this.insightsGenerator.getAIImprovementReport(userId, days);
    } catch (error) {
      this.logger.error(`Error generating AI improvement report for user ${userId}:`, error);
      throw error;
    }
  }

  async generateComprehensiveReport(userId: string) {
    try {
      // Get multiple report types
      const [
        monthlyProgress,
        habitReport,
        productivityReport,
        aiInsights
      ] = await Promise.all([
        this.generateProgressReport(userId, 'month'),
        this.generateHabitReport(userId, 'month'),
        this.generateProductivityReport(userId, 'month'),
        this.generateAIImprovementReport(userId, 30)
      ]);

      return {
        generatedAt: new Date(),
        reportType: 'comprehensive',
        userId,
        sections: {
          progress: monthlyProgress,
          habits: habitReport,
          productivity: productivityReport,
          aiInsights,
        },
        executiveSummary: this.generateExecutiveSummary({
          progress: monthlyProgress,
          habits: habitReport,
          productivity: productivityReport,
          aiInsights,
        }),
      };
    } catch (error) {
      this.logger.error(`Error generating comprehensive report for user ${userId}:`, error);
      throw error;
    }
  }

  private generateSummary(progressData: any, habitAnalytics: any, taskStats: any) {
    const highlights: string[] = [];
    const concerns: string[] = [];

    // Analyze progress highlights
    if (progressData.consistencyScore > 75) {
      highlights.push('High consistency score maintained');
    }
    if (progressData.habits.completionRate > 0.8) {
      highlights.push('Excellent habit completion rate');
    }
    if (progressData.tasks.completionRate > 0.8) {
      highlights.push('Outstanding task completion performance');
    }

    // Identify concerns
    if (progressData.consistencyScore < 50) {
      concerns.push('Consistency score needs improvement');
    }
    if (taskStats && taskStats.overdue > 0) {
      concerns.push(`${taskStats.overdue} overdue tasks need attention`);
    }

    return {
      highlights,
      concerns,
      overallTrend: progressData.consistencyScore > 65 ? 'positive' : 'needs_attention',
      keyMetrics: {
        consistencyScore: progressData.consistencyScore,
        habitCompletion: Math.round(progressData.habits.completionRate * 100),
        taskCompletion: Math.round(progressData.tasks.completionRate * 100),
        focusTime: progressData.focusTime.totalMinutes,
      },
    };
  }

  private generateHabitSummary(habitAnalytics: any) {
    const totalHabits = habitAnalytics.byHabit.length;
    const strongHabits = habitAnalytics.byHabit.filter(h => h.completionRate > 0.8).length;
    const strugglingHabits = habitAnalytics.byHabit.filter(h => h.completionRate < 0.5).length;

    return {
      totalHabits,
      strongHabits,
      strugglingHabits,
      overallCompletionRate: Math.round(habitAnalytics.completionRate * 100),
      longestCurrentStreak: Math.max(...habitAnalytics.byHabit.map(h => h.currentStreak)),
      recommendation: strugglingHabits > totalHabits / 2 
        ? 'Focus on fewer habits for better consistency'
        : 'Great habit performance! Consider adding new challenges',
    };
  }

  private generateProductivitySummary(productivityAnalytics: any, weeklyStats: any) {
    return {
      mostProductiveTime: productivityAnalytics.mostProductiveTime,
      mostProductiveDay: productivityAnalytics.mostProductiveDay,
      weeklyFocusTime: weeklyStats.totalFocusTime,
      dailyAverage: productivityAnalytics.focusTimeStats.dailyAverage,
      consistencyRating: weeklyStats.avgDailyConsistency > 70 ? 'excellent' : 
                        weeklyStats.avgDailyConsistency > 50 ? 'good' : 'needs_improvement',
    };
  }

  private generateProductivityRecommendations(productivityAnalytics: any, moodAnalytics: any): string[] {
    const recommendations: string[] = [];

    // Time-based recommendations
    if (productivityAnalytics.focusTimeStats.dailyAverage < 60) {
      recommendations.push('Aim to increase daily focus time to at least 60 minutes for better results');
    }

    // Mood-based recommendations
    if (moodAnalytics.correlations.productivity.correlation > 0.5) {
      recommendations.push('Your mood strongly affects productivity. Focus on mood management techniques');
    }

    // Pattern-based recommendations
    const peakHour = productivityAnalytics.mostProductiveTime.split('-')[0];
    recommendations.push(`Schedule your most important work during your peak hours: ${productivityAnalytics.mostProductiveTime}`);

    return recommendations;
  }

  private generateExecutiveSummary(reports: {
    progress: any;
    habits: any;
    productivity: any;
    aiInsights: any;
  }) {
    const { progress, habits, productivity, aiInsights } = reports;

    return {
      overallScore: aiInsights.overallScore,
      keyAchievements: [
        ...progress.summary.highlights,
        ...(habits.summary.strongHabits > 0 ? [`${habits.summary.strongHabits} strong habits maintained`] : []),
      ],
      priorityAreas: [
        ...progress.summary.concerns,
        ...(aiInsights.criticalAreas || []),
      ],
      topRecommendations: aiInsights.improvements.slice(0, 3).map(imp => ({
        area: imp.area,
        impact: imp.improvementPotential,
        primaryAction: imp.actions[0]?.title,
      })),
      motivationalMessage: aiInsights.motivationalMessage,
      projectedImprovement: aiInsights.projectedScore - aiInsights.overallScore,
    };
  }
}
