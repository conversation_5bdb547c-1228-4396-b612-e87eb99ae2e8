import { Controller, Get, Query, UseGuards, Request, Post, Body } from '@nestjs/common';
import { AnalyticsService } from './analytics.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiOkResponse, ApiBody, ApiCreatedResponse } from '@nestjs/swagger';
import {
  AnalyticsPeriodQueryDto,
  HabitAnalyticsQueryDto,
  ProductivityAnalyticsQueryDto,
  MoodAnalyticsQueryDto,
  TaskAnalyticsQueryDto,
  HabitCorrelationQueryDto,
  AIImprovementReportQueryDto,
  UpdateDailyProgressDto,
  UpdateUserProgressDto,
  FocusSessionDto,
  MoodEntryDto,
  UserProgressResponseDto,
  HabitAnalyticsResponseDto,
  ProductivityAnalyticsResponseDto,
  MoodAnalyticsResponseDto,
  InsightsResponseDto,
  WeeklyStatsResponseDto,
  TaskCompletionStatsResponseDto,
  AIImprovementReportResponseDto,
  RecordFocusSessionResponseDto,
  RecordMoodEntryResponseDto,
  HabitCorrelationsResponseDto,
  StreakMilestonesResponseDto,
  UpdateDailyProgressResponseDto,
  ProgressReportResponseDto,
  HabitReportResponseDto,
  ProductivityReportResponseDto,
  ComprehensiveReportResponseDto
} from './dto';

@ApiTags('analytics')
@Controller('api/analytics')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Get('progress')
  @ApiOperation({ summary: 'Get user progress analytics' })
  @ApiOkResponse({
    description: 'Returns user progress analytics for the specified period',
    type: UserProgressResponseDto
  })
  getUserProgress(
    @Request() req,
    @Query() query: AnalyticsPeriodQueryDto,
  ) {
    return this.analyticsService.getUserProgress(req.user.id, query.period || 'week');
  }

  @Get('habits')
  @ApiOperation({ summary: 'Get detailed habit analytics' })
  @ApiOkResponse({
    description: 'Returns detailed habit analytics for the specified period',
    type: HabitAnalyticsResponseDto
  })
  getHabitAnalytics(
    @Request() req,
    @Query() query: HabitAnalyticsQueryDto,
  ) {
    return this.analyticsService.getHabitAnalytics(req.user.id, query.period || 'month');
  }

  @Get('productivity')
  @ApiOperation({ summary: 'Get productivity patterns and insights' })
  @ApiOkResponse({
    description: 'Returns productivity analytics for the specified period',
    type: ProductivityAnalyticsResponseDto
  })
  getProductivityAnalytics(
    @Request() req,
    @Query() query: ProductivityAnalyticsQueryDto,
  ) {
    return this.analyticsService.getProductivityAnalytics(req.user.id, query.period || 'week');
  }

  @Get('mood')
  @ApiOperation({ summary: 'Get mood tracking analytics and patterns' })
  @ApiOkResponse({
    description: 'Returns mood tracking analytics for the specified period',
    type: MoodAnalyticsResponseDto
  })
  getMoodAnalytics(
    @Request() req,
    @Query() query: MoodAnalyticsQueryDto,
  ) {
    return this.analyticsService.getMoodAnalytics(req.user.id, query.period || 'month');
  }

  @Get('insights')
  @ApiOperation({ summary: 'Get personalized insights and recommendations' })
  @ApiOkResponse({
    description: 'Returns personalized insights and recommendations',
    type: InsightsResponseDto
  })
  getPersonalizedInsights(@Request() req) {
    return this.analyticsService.getPersonalizedInsights(req.user.id);
  }

  @Post('focus-session')
  @ApiOperation({ summary: 'Record a completed focus session' })
  @ApiBody({ type: FocusSessionDto })
  @ApiCreatedResponse({
    description: 'Focus session has been recorded successfully',
    type: RecordFocusSessionResponseDto
  })
  recordFocusSession(
    @Request() req,
    @Body() data: FocusSessionDto,
  ) {
    return this.analyticsService.recordFocusSession(req.user.id, data.minutes);
  }

  @Post('mood')
  @ApiOperation({ summary: 'Record a mood entry' })
  @ApiBody({ type: MoodEntryDto })
  @ApiCreatedResponse({
    description: 'Mood entry has been recorded successfully',
    type: RecordMoodEntryResponseDto
  })
  recordMoodEntry(
    @Request() req,
    @Body() data: MoodEntryDto,
  ) {
    return this.analyticsService.recordMoodEntry(req.user.id, data.mood);
  }

  @Get('weekly-stats')
  @ApiOperation({ summary: 'Get aggregated weekly statistics' })
  @ApiOkResponse({
    description: 'Returns aggregated weekly statistics',
    type: WeeklyStatsResponseDto
  })
  getWeeklyStats(@Request() req) {
    return this.analyticsService.aggregateWeeklyStats(req.user.id);
  }

  @Get('habit-correlations')
  @ApiOperation({ summary: 'Get habit correlations with mood and productivity' })
  @ApiOkResponse({
    description: 'Returns habit correlations with mood and productivity',
    type: HabitCorrelationsResponseDto
  })
  getHabitCorrelations(
    @Request() req,
    @Query() query: HabitCorrelationQueryDto,
  ) {
    return this.analyticsService.getHabitCorrelations(req.user.id, query.period || 'month');
  }

  @Get('streak-milestones')
  @ApiOperation({ summary: 'Get current streak milestones for habits' })
  @ApiOkResponse({
    description: 'Returns current streak milestones for habits',
    type: StreakMilestonesResponseDto
  })
  getStreakMilestones(@Request() req) {
    return this.analyticsService.getStreakMilestones(req.user.id);
  }

  @Get('tasks')
  @ApiOperation({ summary: 'Get task completion analytics' })
  @ApiOkResponse({
    description: 'Returns task completion analytics',
    type: TaskCompletionStatsResponseDto
  })
  getTaskCompletionStats(
    @Request() req,
    @Query() query: TaskAnalyticsQueryDto
  ) {
    return this.analyticsService.getTaskCompletionStats(req.user.id, query.period || 'week');
  }

  @Post('daily-progress')
  @ApiOperation({ summary: 'Update daily progress with XP, badges, focus time, and podcast listening' })
  @ApiBody({ type: UpdateDailyProgressDto })
  @ApiCreatedResponse({
    description: 'Daily progress has been updated successfully',
    type: UpdateDailyProgressResponseDto
  })
  updateDailyProgress(
    @Request() req,
    @Body() data: UpdateDailyProgressDto,
  ) {
    return this.analyticsService.updateDailyProgress(req.user.id, data);
  }

  @Post('user-progress')
  @ApiOperation({ summary: 'Update comprehensive user progress including skill plans and achievements' })
  @ApiBody({ type: UpdateUserProgressDto })
  @ApiCreatedResponse({
    description: 'User progress has been updated successfully',
    type: UserProgressResponseDto
  })
  updateUserProgress(
    @Request() req,
    @Body() data: UpdateUserProgressDto,
  ) {
    return this.analyticsService.updateUserProgress(req.user.id, data);
  }

  @Get('ai-improvement-report')
  @ApiOperation({ summary: 'Get AI-powered lifestyle improvement report' })
  @ApiOkResponse({
    description: 'Returns AI-powered lifestyle improvement report based on user data',
    type: AIImprovementReportResponseDto
  })
  getAIImprovementReport(
    @Request() req,
    @Query() query: AIImprovementReportQueryDto
  ) {
    return this.analyticsService.getAIImprovementReport(req.user.id, query.days || 30);
  }

  @Get('reports/progress')
  @ApiOperation({ summary: 'Generate comprehensive progress report' })
  @ApiOkResponse({
    description: 'Returns generated progress report for the specified period',
    type: ProgressReportResponseDto
  })
  generateProgressReport(
    @Request() req,
    @Query() query: AnalyticsPeriodQueryDto,
  ) {
    return this.analyticsService.generateProgressReport(req.user.id, query.period || 'week');
  }

  @Get('reports/habits')
  @ApiOperation({ summary: 'Generate detailed habit report' })
  @ApiOkResponse({
    description: 'Returns generated habit report for the specified period',
    type: HabitReportResponseDto
  })
  generateHabitReport(
    @Request() req,
    @Query() query: HabitAnalyticsQueryDto,
  ) {
    return this.analyticsService.generateHabitReport(req.user.id, query.period || 'month');
  }

  @Get('reports/productivity')
  @ApiOperation({ summary: 'Generate productivity report' })
  @ApiOkResponse({
    description: 'Returns generated productivity report for the specified period',
    type: ProductivityReportResponseDto
  })
  generateProductivityReport(
    @Request() req,
    @Query() query: ProductivityAnalyticsQueryDto,
  ) {
    return this.analyticsService.generateProductivityReport(req.user.id, query.period || 'week');
  }

  @Get('reports/comprehensive')
  @ApiOperation({ summary: 'Generate comprehensive analytics report' })
  @ApiOkResponse({
    description: 'Returns comprehensive analytics report covering all aspects',
    type: ComprehensiveReportResponseDto
  })
  generateComprehensiveReport(@Request() req) {
    return this.analyticsService.generateComprehensiveReport(req.user.id);
  }

}
