import { ApiProperty } from '@nestjs/swagger';
import { IsN<PERSON>ber, IsOptional, IsBoolean, IsString, Min } from 'class-validator';

export class UpdateDailyProgressDto {
  @ApiProperty({
    description: 'XP points gained',
    example: 10,
    minimum: 0,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  xpGained?: number;

  @ApiProperty({
    description: 'Badge earned',
    example: 'early_bird',
    required: false
  })
  @IsOptional()
  @IsString()
  badgeEarned?: string;

  @ApiProperty({
    description: 'Focus session duration in minutes',
    example: 25,
    minimum: 0,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  focusMinutes?: number;

  @ApiProperty({
    description: 'Whether a podcast was listened to',
    example: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  podcastListened?: boolean;
}
