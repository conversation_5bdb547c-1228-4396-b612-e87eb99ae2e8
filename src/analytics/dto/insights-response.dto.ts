import { ApiProperty } from '@nestjs/swagger';

class Insight {
  @ApiProperty({
    description: 'Insight title',
    example: 'Morning Exercise Impact'
  })
  title: string;

  @ApiProperty({
    description: 'Insight description',
    example: 'You are 35% more productive on days when you exercise in the morning'
  })
  description: string;

  @ApiProperty({
    description: 'Insight category',
    example: 'Productivity'
  })
  category: string;

  @ApiProperty({
    description: 'Confidence level (0-100)',
    example: 85
  })
  confidenceLevel: number;
}

class Recommendation {
  @ApiProperty({
    description: 'Recommendation text',
    example: 'Try exercising for at least 15 minutes before starting your workday'
  })
  text: string;

  @ApiProperty({
    description: 'Expected impact area',
    example: 'Productivity'
  })
  impactArea: string;

  @ApiProperty({
    description: 'Priority level',
    example: 'high',
    enum: ['low', 'medium', 'high']
  })
  priority: 'low' | 'medium' | 'high';
}

export class InsightsResponseDto {
  @ApiProperty({
    description: 'Personalized insights',
    type: [Insight]
  })
  insights: Insight[];

  @ApiProperty({
    description: 'Personalized recommendations',
    type: [Recommendation]
  })
  recommendations: Recommendation[];

  @ApiProperty({
    description: 'Areas of strength',
    example: ['Morning routine', 'Task completion']
  })
  strengths: string[];

  @ApiProperty({
    description: 'Areas for improvement',
    example: ['Evening productivity', 'Weekend habits']
  })
  improvementAreas: string[];
}
