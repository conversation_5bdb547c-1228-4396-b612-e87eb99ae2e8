import { ApiProperty } from '@nestjs/swagger';

class DailyStats {
  @ApiProperty({
    description: 'Day of week',
    example: 'Monday'
  })
  day: string;

  @ApiProperty({
    description: 'Tasks completed',
    example: 8
  })
  tasksCompleted: number;

  @ApiProperty({
    description: 'Habits completed',
    example: 5
  })
  habitsCompleted: number;

  @ApiProperty({
    description: 'Focus minutes',
    example: 125
  })
  focusMinutes: number;

  @ApiProperty({
    description: 'Productivity score',
    example: 85
  })
  productivityScore: number;
}

export class WeeklyStatsResponseDto {
  @ApiProperty({
    description: 'Weekly stats by day',
    type: [DailyStats]
  })
  dailyStats: DailyStats[];

  @ApiProperty({
    description: 'Total tasks completed this week',
    example: 42
  })
  totalTasks: number;

  @ApiProperty({
    description: 'Total habits completed this week',
    example: 28
  })
  totalHabits: number;

  @ApiProperty({
    description: 'Total focus minutes this week',
    example: 645
  })
  totalFocusMinutes: number;

  @ApiProperty({
    description: 'Weekly productivity score',
    example: 78
  })
  weeklyProductivityScore: number;

  @ApiProperty({
    description: 'Most productive day',
    example: 'Tuesday'
  })
  mostProductiveDay: string;
}
