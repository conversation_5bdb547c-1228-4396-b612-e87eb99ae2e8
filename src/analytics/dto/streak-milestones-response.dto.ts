import { ApiProperty } from '@nestjs/swagger';

class HabitStreak {
  @ApiProperty({
    description: 'Habit ID',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  habitId: string;

  @ApiProperty({
    description: 'Habit name',
    example: 'Daily Reading'
  })
  habitName: string;

  @ApiProperty({
    description: 'Current streak length',
    example: 15
  })
  currentStreak: number;

  @ApiProperty({
    description: 'Longest streak achieved',
    example: 28
  })
  longestStreak: number;

  @ApiProperty({
    description: 'Next milestone target',
    example: 30
  })
  nextMilestone: number;

  @ApiProperty({
    description: 'Days until next milestone',
    example: 15
  })
  daysToMilestone: number;

  @ApiProperty({
    description: 'Milestone achievement percentage',
    example: 50
  })
  milestoneProgress: number;
}

class Achievement {
  @ApiProperty({
    description: 'Achievement name',
    example: 'Week Warrior'
  })
  name: string;

  @ApiProperty({
    description: 'Achievement description',
    example: 'Complete a habit for 7 consecutive days'
  })
  description: string;

  @ApiProperty({
    description: 'When the achievement was earned',
    example: '2025-05-19T10:30:00Z'
  })
  earnedAt: Date;

  @ApiProperty({
    description: 'Habit that earned this achievement',
    example: 'Daily Reading'
  })
  habitName: string;
}

export class StreakMilestonesResponseDto {
  @ApiProperty({
    description: 'Current habit streaks and milestones',
    type: [HabitStreak]
  })
  streaks: HabitStreak[];

  @ApiProperty({
    description: 'Recently earned achievements',
    type: [Achievement]
  })
  recentAchievements: Achievement[];

  @ApiProperty({
    description: 'Total active streaks',
    example: 5
  })
  totalActiveStreaks: number;

  @ApiProperty({
    description: 'Longest current streak',
    example: 28
  })
  longestCurrentStreak: number;

  @ApiProperty({
    description: 'Next milestone coming up',
    example: 'Daily Reading - 30 days'
  })
  nextUpcomingMilestone: string;

  @ApiProperty({
    description: 'Days until next milestone',
    example: 2
  })
  daysToNextMilestone: number;

  @ApiProperty({
    description: 'Motivational message',
    example: 'You are just 2 days away from your next milestone! Keep it up!'
  })
  motivationalMessage: string;

  @ApiProperty({
    description: 'When the data was generated',
    example: '2025-05-19T10:30:00Z'
  })
  generatedAt: Date;
}
