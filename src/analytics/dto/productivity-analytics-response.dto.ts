import { ApiProperty } from '@nestjs/swagger';

class TimeOfDayProductivity {
  @ApiProperty({
    description: 'Time of day',
    example: 'Morning'
  })
  timeOfDay: string;

  @ApiProperty({
    description: 'Productivity score',
    example: 85
  })
  productivityScore: number;

  @ApiProperty({
    description: 'Number of tasks completed during this time',
    example: 12
  })
  tasksCompleted: number;
}

class DayOfWeekProductivity {
  @ApiProperty({
    description: 'Day of week',
    example: 'Tuesday'
  })
  dayOfWeek: string;

  @ApiProperty({
    description: 'Productivity score',
    example: 90
  })
  productivityScore: number;
}

export class ProductivityAnalyticsResponseDto {
  @ApiProperty({
    description: 'Overall productivity score',
    example: 78
  })
  overallProductivity: number;
  
  @ApiProperty({
    description: 'Productivity by time of day',
    type: [TimeOfDayProductivity]
  })
  productivityByTimeOfDay: TimeOfDayProductivity[];
  
  @ApiProperty({
    description: 'Productivity by day of week',
    type: [DayOfWeekProductivity]
  })
  productivityByDayOfWeek: DayOfWeekProductivity[];
  
  @ApiProperty({
    description: 'Most productive time of day',
    example: 'Morning'
  })
  mostProductiveTime: string;
  
  @ApiProperty({
    description: 'Most productive day of week',
    example: 'Tuesday'
  })
  mostProductiveDay: string;
  
  @ApiProperty({
    description: 'Focus time in minutes per day (average)',
    example: 127
  })
  averageFocusTime: number;
}
