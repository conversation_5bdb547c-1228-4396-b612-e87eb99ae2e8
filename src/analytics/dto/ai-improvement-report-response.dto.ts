import { ApiProperty } from '@nestjs/swagger';

class LifestyleMetric {
  @ApiProperty({
    description: 'Metric name',
    example: 'Task Completion Rate'
  })
  name: string;

  @ApiProperty({
    description: 'Current score (0-100)',
    example: 75
  })
  currentScore: number;

  @ApiProperty({
    description: 'Target score (0-100)',
    example: 85
  })
  targetScore: number;

  @ApiProperty({
    description: 'Trend direction',
    example: 'improving',
    enum: ['improving', 'declining', 'stable']
  })
  trend: 'improving' | 'declining' | 'stable';

  @ApiProperty({
    description: 'Percentage change from last period',
    example: 12.5
  })
  changePercentage: number;
}

class ImprovementAction {
  @ApiProperty({
    description: 'Action title',
    example: 'Establish Morning Routine'
  })
  title: string;

  @ApiProperty({
    description: 'Detailed description of the action',
    example: 'Start your day with a 10-minute meditation followed by reviewing your top 3 priorities'
  })
  description: string;

  @ApiProperty({
    description: 'Expected impact level',
    example: 'high',
    enum: ['low', 'medium', 'high']
  })
  impact: 'low' | 'medium' | 'high';

  @ApiProperty({
    description: 'Implementation difficulty',
    example: 'medium',
    enum: ['easy', 'medium', 'hard']
  })
  difficulty: 'easy' | 'medium' | 'hard';

  @ApiProperty({
    description: 'Estimated time to see results (in days)',
    example: 14
  })
  timeToResults: number;

  @ApiProperty({
    description: 'Category of improvement',
    example: 'Productivity'
  })
  category: string;
}

class LifestyleImprovement {
  @ApiProperty({
    description: 'Area of improvement',
    example: 'Morning Productivity'
  })
  area: string;

  @ApiProperty({
    description: 'Current performance score (0-100)',
    example: 68
  })
  currentScore: number;

  @ApiProperty({
    description: 'Potential improvement percentage',
    example: 25
  })
  improvementPotential: number;

  @ApiProperty({
    description: 'Specific actions to take',
    type: [ImprovementAction]
  })
  actions: ImprovementAction[];

  @ApiProperty({
    description: 'Evidence supporting this improvement area',
    example: 'Your task completion rate drops by 40% in the afternoon compared to morning'
  })
  evidence: string;
}

class PersonalityInsight {
  @ApiProperty({
    description: 'Insight about user behavior pattern',
    example: 'You are a morning person who performs best between 8-11 AM'
  })
  insight: string;

  @ApiProperty({
    description: 'Behavioral pattern category',
    example: 'Circadian Rhythm'
  })
  category: string;

  @ApiProperty({
    description: 'Confidence level of this insight (0-100)',
    example: 87
  })
  confidence: number;
}

export class AIImprovementReportResponseDto {
  @ApiProperty({
    description: 'Overall lifestyle health score (0-100)',
    example: 72
  })
  overallScore: number;

  @ApiProperty({
    description: 'Key lifestyle metrics with scores and trends',
    type: [LifestyleMetric]
  })
  metrics: LifestyleMetric[];

  @ApiProperty({
    description: 'Top improvement opportunities ranked by impact',
    type: [LifestyleImprovement]
  })
  improvements: LifestyleImprovement[];

  @ApiProperty({
    description: 'Insights about user behavior patterns',
    type: [PersonalityInsight]
  })
  personalityInsights: PersonalityInsight[];

  @ApiProperty({
    description: 'Areas where user is performing well',
    example: ['Habit consistency', 'Goal completion', 'Sleep schedule']
  })
  strengths: string[];

  @ApiProperty({
    description: 'Critical areas needing immediate attention',
    example: ['Afternoon productivity slump', 'Weekend routine inconsistency']
  })
  criticalAreas: string[];

  @ApiProperty({
    description: 'Projected lifestyle score after implementing top 3 improvements',
    example: 84
  })
  projectedScore: number;

  @ApiProperty({
    description: 'Number of days of data used for this analysis',
    example: 30
  })
  analysisDateRange: number;

  @ApiProperty({
    description: 'When this report was generated',
    example: '2025-06-13T10:30:00Z'
  })
  generatedAt: Date;

  @ApiProperty({
    description: 'Motivational message based on progress',
    example: 'Great progress this month! Your consistency has improved by 15%. Keep up the momentum!'
  })
  motivationalMessage: string;
}
