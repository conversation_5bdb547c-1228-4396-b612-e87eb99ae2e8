import { ApiProperty } from '@nestjs/swagger';

class OverallMetric {
  @ApiProperty({
    description: 'Metric category',
    example: 'productivity',
    enum: ['productivity', 'habits', 'mood', 'focus', 'tasks', 'learning']
  })
  category: 'productivity' | 'habits' | 'mood' | 'focus' | 'tasks' | 'learning';

  @ApiProperty({
    description: 'Metric name',
    example: 'Overall Productivity Score'
  })
  name: string;

  @ApiProperty({
    description: 'Current value',
    example: 78.5
  })
  currentValue: number;

  @ApiProperty({
    description: 'Target value',
    example: 85
  })
  targetValue: number;

  @ApiProperty({
    description: 'Progress towards target (percentage)',
    example: 92.4
  })
  progressToTarget: number;

  @ApiProperty({
    description: 'Trend direction',
    example: 'improving',
    enum: ['improving', 'stable', 'declining']
  })
  trend: 'improving' | 'stable' | 'declining';
}

class KeyInsight {
  @ApiProperty({
    description: 'Insight priority',
    example: 'high',
    enum: ['low', 'medium', 'high', 'critical']
  })
  priority: 'low' | 'medium' | 'high' | 'critical';

  @ApiProperty({
    description: 'Insight category',
    example: 'productivity'
  })
  category: string;

  @ApiProperty({
    description: 'Insight title',
    example: 'Morning Productivity Peak'
  })
  title: string;

  @ApiProperty({
    description: 'Detailed insight description',
    example: 'Your productivity consistently peaks between 9-11 AM across all tracked metrics'
  })
  description: string;

  @ApiProperty({
    description: 'Data supporting this insight',
    example: 'Based on 30 days of focus time, task completion, and mood data'
  })
  evidence: string;

  @ApiProperty({
    description: 'Actionable recommendations',
    example: ['Schedule important tasks in the morning', 'Protect 9-11 AM from meetings']
  })
  actionableSteps: string[];
}

class AreaSummary {
  @ApiProperty({
    description: 'Area name',
    example: 'Habit Consistency'
  })
  area: string;

  @ApiProperty({
    description: 'Performance score (0-100)',
    example: 85
  })
  score: number;

  @ApiProperty({
    description: 'Performance level',
    example: 'Excellent',
    enum: ['Poor', 'Fair', 'Good', 'Very Good', 'Excellent']
  })
  level: string;

  @ApiProperty({
    description: 'Key strengths in this area',
    example: ['Consistent morning routine', 'Strong weekend habits']
  })
  strengths: string[];

  @ApiProperty({
    description: 'Areas for improvement',
    example: ['Evening routine needs work', 'Exercise consistency']
  })
  improvements: string[];
}

export class ComprehensiveReportResponseDto {
  @ApiProperty({
    description: 'Report generation date',
    example: '2025-05-19T10:30:00Z'
  })
  generatedAt: Date;

  @ApiProperty({
    description: 'Analysis period covered',
    example: '30 days'
  })
  analysisPeriod: string;

  @ApiProperty({
    description: 'Overall wellness score (0-100)',
    example: 78.5
  })
  overallWellnessScore: number;

  @ApiProperty({
    description: 'Key metrics across all areas',
    type: [OverallMetric]
  })
  overallMetrics: OverallMetric[];

  @ApiProperty({
    description: 'Performance summary by area',
    type: [AreaSummary]
  })
  areaSummaries: AreaSummary[];

  @ApiProperty({
    description: 'Top insights and patterns discovered',
    type: [KeyInsight]
  })
  keyInsights: KeyInsight[];

  @ApiProperty({
    description: 'Strongest performance areas',
    example: ['Habit Consistency', 'Morning Productivity']
  })
  topStrengths: string[];

  @ApiProperty({
    description: 'Areas needing most attention',
    example: ['Evening Routine', 'Weekend Productivity']
  })
  improvementPriorities: string[];

  @ApiProperty({
    description: 'Recommended focus for next period',
    example: ['Establish evening routine', 'Optimize weekend schedule', 'Increase focus time']
  })
  nextPeriodFocus: string[];

  @ApiProperty({
    description: 'Progress compared to previous period',
    example: 8.5
  })
  progressFromPreviousPeriod: number;

  @ApiProperty({
    description: 'Motivational summary message',
    example: 'Great progress this month! Your consistency has improved significantly. Focus on evening routines to reach the next level.'
  })
  motivationalMessage: string;

  @ApiProperty({
    description: 'Confidence level of analysis',
    example: 'High',
    enum: ['Low', 'Medium', 'High']
  })
  analysisConfidence: string;

  @ApiProperty({
    description: 'Number of data points analyzed',
    example: 450
  })
  dataPointsAnalyzed: number;
}
