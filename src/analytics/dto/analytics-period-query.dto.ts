import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';

export class AnalyticsPeriodQueryDto {
  @ApiProperty({
    description: 'Time period for analytics',
    example: 'week',
    enum: ['day', 'week', 'month', 'year'],
    required: false,
    default: 'week'
  })
  @IsOptional()
  @IsEnum(['day', 'week', 'month', 'year'])
  period?: 'day' | 'week' | 'month' | 'year' = 'week';
}
