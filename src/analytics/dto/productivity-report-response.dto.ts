import { ApiProperty } from '@nestjs/swagger';

class ProductivityMetric {
  @ApiProperty({
    description: 'Metric name',
    example: 'Focus Time'
  })
  name: string;

  @ApiProperty({
    description: 'Metric value',
    example: 125.5
  })
  value: number;

  @ApiProperty({
    description: 'Metric unit',
    example: 'minutes'
  })
  unit: string;

  @ApiProperty({
    description: 'Change from previous period',
    example: 15.2
  })
  changeFromPrevious: number;
}

class ProductivityPattern {
  @ApiProperty({
    description: 'Pattern type',
    example: 'peak_hours',
    enum: ['peak_hours', 'low_energy', 'consistent', 'variable']
  })
  type: 'peak_hours' | 'low_energy' | 'consistent' | 'variable';

  @ApiProperty({
    description: 'Pattern description',
    example: 'Most productive between 9 AM and 11 AM'
  })
  description: string;

  @ApiProperty({
    description: 'Time periods when this pattern occurs',
    example: ['09:00-11:00', '14:00-16:00']
  })
  timePeriods: string[];

  @ApiProperty({
    description: 'Productivity score during this pattern',
    example: 92
  })
  productivityScore: number;
}

class ProductivityRecommendation {
  @ApiProperty({
    description: 'Recommendation category',
    example: 'schedule_optimization',
    enum: ['schedule_optimization', 'break_management', 'focus_improvement', 'energy_management']
  })
  category: 'schedule_optimization' | 'break_management' | 'focus_improvement' | 'energy_management';

  @ApiProperty({
    description: 'Recommendation title',
    example: 'Optimize Morning Schedule'
  })
  title: string;

  @ApiProperty({
    description: 'Detailed recommendation',
    example: 'Schedule your most important tasks between 9-11 AM when your productivity peaks'
  })
  description: string;

  @ApiProperty({
    description: 'Expected impact',
    example: 'High',
    enum: ['Low', 'Medium', 'High']
  })
  impact: 'Low' | 'Medium' | 'High';

  @ApiProperty({
    description: 'Implementation difficulty',
    example: 'Easy',
    enum: ['Easy', 'Medium', 'Hard']
  })
  difficulty: 'Easy' | 'Medium' | 'Hard';
}

export class ProductivityReportResponseDto {
  @ApiProperty({
    description: 'Report period',
    example: 'week'
  })
  period: string;

  @ApiProperty({
    description: 'Report start date',
    example: '2025-05-13T00:00:00Z'
  })
  startDate: Date;

  @ApiProperty({
    description: 'Report end date',
    example: '2025-05-19T23:59:59Z'
  })
  endDate: Date;

  @ApiProperty({
    description: 'Overall productivity score',
    example: 78.5
  })
  overallProductivityScore: number;

  @ApiProperty({
    description: 'Key productivity metrics',
    type: [ProductivityMetric]
  })
  metrics: ProductivityMetric[];

  @ApiProperty({
    description: 'Most productive day of the period',
    example: 'Tuesday'
  })
  mostProductiveDay: string;

  @ApiProperty({
    description: 'Most productive time of day',
    example: '09:00-11:00'
  })
  mostProductiveTime: string;

  @ApiProperty({
    description: 'Identified productivity patterns',
    type: [ProductivityPattern]
  })
  patterns: ProductivityPattern[];

  @ApiProperty({
    description: 'Average daily focus time',
    example: 125.5
  })
  averageDailyFocusTime: number;

  @ApiProperty({
    description: 'Peak productivity score achieved',
    example: 95
  })
  peakProductivityScore: number;

  @ApiProperty({
    description: 'Productivity recommendations',
    type: [ProductivityRecommendation]
  })
  recommendations: ProductivityRecommendation[];

  @ApiProperty({
    description: 'Comparison with previous period',
    example: 12.3
  })
  changeFromPreviousPeriod: number;

  @ApiProperty({
    description: 'When the report was generated',
    example: '2025-05-19T10:30:00Z'
  })
  generatedAt: Date;
}
