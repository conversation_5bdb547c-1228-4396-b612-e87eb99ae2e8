import { ApiProperty } from '@nestjs/swagger';

export class RecordFocusSessionResponseDto {
  @ApiProperty({
    description: 'Success message',
    example: 'Focus session recorded successfully'
  })
  message: string;

  @ApiProperty({
    description: 'Minutes recorded',
    example: 25
  })
  minutesRecorded: number;

  @ApiProperty({
    description: 'Total focus minutes for today',
    example: 125
  })
  totalTodayMinutes: number;

  @ApiProperty({
    description: 'When the session was recorded',
    example: '2025-05-19T10:30:00Z'
  })
  recordedAt: Date;
}
