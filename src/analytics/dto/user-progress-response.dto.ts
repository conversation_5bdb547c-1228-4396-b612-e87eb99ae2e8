import { ApiProperty } from '@nestjs/swagger';

class DailyProgress {
  @ApiProperty({
    description: 'Date for the progress data point',
    example: '2025-05-19'
  })
  date: string;

  @ApiProperty({
    description: 'Overall progress score for the day',
    example: 85
  })
  score: number;

  @ApiProperty({
    description: 'Tasks completed on this day',
    example: 5
  })
  tasksCompleted: number;

  @ApiProperty({
    description: 'Habits completed on this day',
    example: 3
  })
  habitsCompleted: number;

  @ApiProperty({
    description: 'Learning progress on this day (percentage)',
    example: 15
  })
  learningProgress: number;
}

export class UserProgressResponseDto {
  @ApiProperty({
    description: 'Overall progress score for the period',
    example: 75
  })
  overallScore: number;

  @ApiProperty({
    description: 'Daily progress breakdown',
    type: [DailyProgress]
  })
  dailyProgress: DailyProgress[];

  @ApiProperty({
    description: 'Total tasks completed in the period',
    example: 28
  })
  totalTasksCompleted: number;

  @ApiProperty({
    description: 'Total habits completed in the period',
    example: 21
  })
  totalHabitsCompleted: number;

  @ApiProperty({
    description: 'Comparison to previous period (percentage change)',
    example: 12.5
  })
  changeFromPreviousPeriod: number;
}
