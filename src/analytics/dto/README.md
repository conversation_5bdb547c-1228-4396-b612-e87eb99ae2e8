# Analytics Module DTOs

This document provides an overview of all the Data Transfer Objects (DTOs) used in the Analytics module for API requests and responses.

## Request DTOs

### Query Parameter DTOs

#### `AnalyticsPeriodQueryDto`
Used for endpoints that accept time period parameters with full range options.
- **Periods**: `day`, `week`, `month`, `year`
- **Default**: `week`
- **Used by**: `/api/analytics/progress`

#### `HabitAnalyticsQueryDto`
Used for habit-specific analytics endpoints.
- **Periods**: `week`, `month`, `year`
- **Default**: `month`
- **Used by**: `/api/analytics/habits`, `/api/analytics/reports/habits`

#### `ProductivityAnalyticsQueryDto`
Used for productivity analytics endpoints.
- **Periods**: `week`, `month`
- **Default**: `week`
- **Used by**: `/api/analytics/productivity`, `/api/analytics/reports/productivity`

#### `MoodAnalyticsQueryDto`
Used for mood tracking analytics endpoints.
- **Periods**: `week`, `month`, `year`
- **Default**: `month`
- **Used by**: `/api/analytics/mood`

#### `TaskAnalyticsQueryDto`
Used for task completion analytics endpoints.
- **Periods**: `week`, `month`
- **Default**: `week`
- **Used by**: `/api/analytics/tasks`

#### `HabitCorrelationQueryDto`
Used for habit correlation analysis endpoints.
- **Periods**: `month`, `year`
- **Default**: `month`
- **Used by**: `/api/analytics/habit-correlations`

#### `AIImprovementReportQueryDto`
Used for AI-powered improvement report endpoints.
- **Parameter**: `days` (7-365)
- **Default**: `30`
- **Used by**: `/api/analytics/ai-improvement-report`

### Body DTOs

#### `UpdateDailyProgressDto`
Used for updating daily progress metrics.
- **Fields**:
  - `xpGained?: number` - XP points gained (min: 0)
  - `badgeEarned?: string` - Badge name earned
  - `focusMinutes?: number` - Focus session duration (min: 0)
  - `podcastListened?: boolean` - Podcast listening status
- **Used by**: `POST /api/analytics/daily-progress`

#### `UpdateUserProgressDto`
Used for comprehensive user progress updates.
- **Fields**:
  - `skillPlans?: SkillPlansUpdateDto` - Skill plan progress
  - `xpGained?: number` - XP points gained (min: 0)
  - `badgesEarned?: string[]` - Array of badge names
- **Used by**: `POST /api/analytics/user-progress`

#### `FocusSessionDto`
Used for recording focus sessions.
- **Fields**:
  - `minutes: number` - Session duration in minutes (min: 1)
- **Used by**: `POST /api/analytics/focus-session`

#### `MoodEntryDto`
Used for recording mood entries.
- **Fields**:
  - `mood: 'great' | 'good' | 'neutral' | 'bad' | 'terrible'` - Mood value
- **Used by**: `POST /api/analytics/mood`

## Response DTOs

All response DTOs are properly documented with Swagger decorators and include:

- `UserProgressResponseDto` - User progress analytics
- `HabitAnalyticsResponseDto` - Habit analytics data
- `ProductivityAnalyticsResponseDto` - Productivity metrics
- `MoodAnalyticsResponseDto` - Mood tracking analytics
- `InsightsResponseDto` - Personalized insights
- `WeeklyStatsResponseDto` - Weekly statistics
- `TaskCompletionStatsResponseDto` - Task completion analytics
- `AIImprovementReportResponseDto` - AI-powered improvement recommendations

## API Endpoints

### Analytics Endpoints
- `GET /api/analytics/progress` - Get user progress analytics
- `GET /api/analytics/habits` - Get detailed habit analytics
- `GET /api/analytics/productivity` - Get productivity patterns
- `GET /api/analytics/mood` - Get mood tracking analytics
- `GET /api/analytics/insights` - Get personalized insights
- `GET /api/analytics/weekly-stats` - Get weekly statistics
- `GET /api/analytics/habit-correlations` - Get habit correlations
- `GET /api/analytics/streak-milestones` - Get streak milestones
- `GET /api/analytics/tasks` - Get task completion analytics
- `GET /api/analytics/ai-improvement-report` - Get AI improvement report

### Progress Update Endpoints
- `POST /api/analytics/daily-progress` - Update daily progress
- `POST /api/analytics/user-progress` - Update comprehensive user progress
- `POST /api/analytics/focus-session` - Record focus session
- `POST /api/analytics/mood` - Record mood entry

### Report Generation Endpoints
- `GET /api/analytics/reports/progress` - Generate progress report
- `GET /api/analytics/reports/habits` - Generate habit report
- `GET /api/analytics/reports/productivity` - Generate productivity report
- `GET /api/analytics/reports/comprehensive` - Generate comprehensive report

## Validation

All DTOs include proper validation decorators:
- `@IsEnum()` for enumerated values
- `@IsNumber()`, `@Min()`, `@Max()` for numeric values
- `@IsOptional()` for optional fields
- `@IsArray()`, `@IsString()` for arrays and strings
- `@ValidateNested()` for nested objects
- `@Type()` for type transformation

## Swagger Documentation

All DTOs are fully documented with:
- `@ApiProperty()` decorators with descriptions and examples
- Proper enum definitions
- Default values
- Validation constraints
- Response type mappings in controller endpoints

## Usage in Services

The DTOs are used throughout the analytics module:
- **Analytics Service**: Uses DTOs for type safety in method signatures
- **Progress Tracker Service**: Uses DTOs for update operations
- **Controller**: Uses DTOs for request validation and response documentation

This ensures consistent data structure and automatic API documentation generation through Swagger.
