import { ApiProperty } from '@nestjs/swagger';

class ProgressSummary {
  @ApiProperty({
    description: 'Overall progress score',
    example: 78
  })
  overallScore: number;

  @ApiProperty({
    description: 'XP gained in period',
    example: 450
  })
  xpGained: number;

  @ApiProperty({
    description: 'Tasks completed',
    example: 28
  })
  tasksCompleted: number;

  @ApiProperty({
    description: 'Habits completed',
    example: 21
  })
  habitsCompleted: number;

  @ApiProperty({
    description: 'Focus minutes',
    example: 675
  })
  focusMinutes: number;
}

class ProgressTrend {
  @ApiProperty({
    description: 'Date',
    example: '2025-05-19'
  })
  date: string;

  @ApiProperty({
    description: 'Progress score for the day',
    example: 85
  })
  score: number;

  @ApiProperty({
    description: 'XP gained on this day',
    example: 65
  })
  xpGained: number;
}

class ProgressInsight {
  @ApiProperty({
    description: 'Insight type',
    example: 'improvement',
    enum: ['strength', 'improvement', 'warning', 'achievement']
  })
  type: 'strength' | 'improvement' | 'warning' | 'achievement';

  @ApiProperty({
    description: 'Insight title',
    example: 'Consistent Morning Routine'
  })
  title: string;

  @ApiProperty({
    description: 'Detailed insight description',
    example: 'You have maintained your morning routine for 15 consecutive days, showing excellent consistency.'
  })
  description: string;

  @ApiProperty({
    description: 'Recommended actions',
    example: ['Continue your morning routine', 'Consider adding meditation']
  })
  recommendations: string[];
}

export class ProgressReportResponseDto {
  @ApiProperty({
    description: 'Report period',
    example: 'week'
  })
  period: string;

  @ApiProperty({
    description: 'Report start date',
    example: '2025-05-13T00:00:00Z'
  })
  startDate: Date;

  @ApiProperty({
    description: 'Report end date',
    example: '2025-05-19T23:59:59Z'
  })
  endDate: Date;

  @ApiProperty({
    description: 'Progress summary for the period',
    type: ProgressSummary
  })
  summary: ProgressSummary;

  @ApiProperty({
    description: 'Daily progress trends',
    type: [ProgressTrend]
  })
  trends: ProgressTrend[];

  @ApiProperty({
    description: 'Key insights and recommendations',
    type: [ProgressInsight]
  })
  insights: ProgressInsight[];

  @ApiProperty({
    description: 'Comparison with previous period (percentage change)',
    example: 12.5
  })
  changeFromPreviousPeriod: number;

  @ApiProperty({
    description: 'Performance rating',
    example: 'Excellent',
    enum: ['Poor', 'Fair', 'Good', 'Very Good', 'Excellent']
  })
  performanceRating: string;

  @ApiProperty({
    description: 'When the report was generated',
    example: '2025-05-19T10:30:00Z'
  })
  generatedAt: Date;
}
