import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';

export class MoodAnalyticsQueryDto {
  @ApiProperty({
    description: 'Time period for mood analytics',
    example: 'month',
    enum: ['week', 'month', 'year'],
    required: false,
    default: 'month'
  })
  @IsOptional()
  @IsEnum(['week', 'month', 'year'])
  period?: 'week' | 'month' | 'year' = 'month';
}
