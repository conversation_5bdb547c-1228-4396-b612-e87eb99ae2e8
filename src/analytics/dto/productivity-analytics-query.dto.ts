import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';

export class ProductivityAnalyticsQueryDto {
  @ApiProperty({
    description: 'Time period for productivity analytics',
    example: 'week',
    enum: ['week', 'month'],
    required: false,
    default: 'week'
  })
  @IsOptional()
  @IsEnum(['week', 'month'])
  period?: 'week' | 'month' = 'week';
}
