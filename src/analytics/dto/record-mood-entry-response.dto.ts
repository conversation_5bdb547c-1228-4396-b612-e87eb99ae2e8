import { ApiProperty } from '@nestjs/swagger';

export class RecordMoodEntryResponseDto {
  @ApiProperty({
    description: 'Success message',
    example: 'Mood entry recorded successfully'
  })
  message: string;

  @ApiProperty({
    description: 'Mood that was recorded',
    example: 'good',
    enum: ['great', 'good', 'neutral', 'bad', 'terrible']
  })
  moodRecorded: 'great' | 'good' | 'neutral' | 'bad' | 'terrible';

  @ApiProperty({
    description: 'Numeric mood score (1-5)',
    example: 4
  })
  moodScore: number;

  @ApiProperty({
    description: 'When the mood was recorded',
    example: '2025-05-19T10:30:00Z'
  })
  recordedAt: Date;

  @ApiProperty({
    description: 'Streak of consecutive mood entries',
    example: 7
  })
  moodStreak: number;
}
