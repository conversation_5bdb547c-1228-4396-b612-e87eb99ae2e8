import { ApiProperty } from '@nestjs/swagger';
import { IsN<PERSON><PERSON>, IsOptional, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';

export class AIImprovementReportQueryDto {
  @ApiProperty({
    description: 'Number of days to analyze for the AI improvement report',
    example: 30,
    minimum: 7,
    maximum: 365,
    required: false,
    default: 30
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(7)
  @Max(365)
  days?: number = 30;
}
