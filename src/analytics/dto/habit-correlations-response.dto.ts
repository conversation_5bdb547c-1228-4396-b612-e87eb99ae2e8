import { ApiProperty } from '@nestjs/swagger';

class HabitMoodCorrelation {
  @ApiProperty({
    description: 'Habit name',
    example: 'Exercise'
  })
  habitName: string;

  @ApiProperty({
    description: 'Correlation strength with mood (-1 to 1)',
    example: 0.75
  })
  correlationStrength: number;

  @ApiProperty({
    description: 'Whether correlation is positive or negative',
    example: 'positive'
  })
  correlationType: 'positive' | 'negative';

  @ApiProperty({
    description: 'Statistical significance level',
    example: 0.95
  })
  significance: number;
}

class HabitProductivityCorrelation {
  @ApiProperty({
    description: 'Habit name',
    example: 'Morning routine'
  })
  habitName: string;

  @ApiProperty({
    description: 'Correlation strength with productivity (-1 to 1)',
    example: 0.82
  })
  correlationStrength: number;

  @ApiProperty({
    description: 'Whether correlation is positive or negative',
    example: 'positive'
  })
  correlationType: 'positive' | 'negative';

  @ApiProperty({
    description: 'Impact on daily productivity score',
    example: 15.5
  })
  productivityImpact: number;
}

export class HabitCorrelationsResponseDto {
  @ApiProperty({
    description: 'Time period analyzed',
    example: 'month'
  })
  period: string;

  @ApiProperty({
    description: 'Habit correlations with mood',
    type: [HabitMoodCorrelation]
  })
  moodCorrelations: HabitMoodCorrelation[];

  @ApiProperty({
    description: 'Habit correlations with productivity',
    type: [HabitProductivityCorrelation]
  })
  productivityCorrelations: HabitProductivityCorrelation[];

  @ApiProperty({
    description: 'Most mood-boosting habit',
    example: 'Exercise'
  })
  topMoodBooster: string;

  @ApiProperty({
    description: 'Most productivity-enhancing habit',
    example: 'Morning routine'
  })
  topProductivityBooster: string;

  @ApiProperty({
    description: 'Number of data points analyzed',
    example: 30
  })
  dataPoints: number;

  @ApiProperty({
    description: 'When the analysis was generated',
    example: '2025-05-19T10:30:00Z'
  })
  generatedAt: Date;
}
