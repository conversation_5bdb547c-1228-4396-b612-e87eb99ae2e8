import { ApiProperty } from '@nestjs/swagger';

class HabitStreak {
  @ApiProperty({
    description: 'Habit name',
    example: 'Morning Meditation'
  })
  name: string;

  @ApiProperty({
    description: 'Current streak (consecutive days)',
    example: 14
  })
  currentStreak: number;

  @ApiProperty({
    description: 'Longest streak ever achieved',
    example: 21
  })
  longestStreak: number;

  @ApiProperty({
    description: 'Completion rate (percentage)',
    example: 85.7
  })
  completionRate: number;
}

class HabitCompletionByDay {
  @ApiProperty({
    description: 'Day of week',
    example: 'Monday'
  })
  day: string;

  @ApiProperty({
    description: 'Completion percentage for this day',
    example: 90
  })
  completionPercentage: number;
}

export class HabitAnalyticsResponseDto {
  @ApiProperty({
    description: 'Overall habit completion rate',
    example: 82.5
  })
  overallCompletionRate: number;
  
  @ApiProperty({
    description: 'Habit streaks',
    type: [HabitStreak]
  })
  streaks: HabitStreak[];
  
  @ApiProperty({
    description: 'Completion rates by day of week',
    type: [HabitCompletionByDay]
  })
  completionByDay: HabitCompletionByDay[];
  
  @ApiProperty({
    description: 'Most consistent habit',
    example: 'Reading'
  })
  mostConsistentHabit: string;
  
  @ApiProperty({
    description: 'Habit needing most improvement',
    example: 'Exercise'
  })
  habitNeedingImprovement: string;
}
