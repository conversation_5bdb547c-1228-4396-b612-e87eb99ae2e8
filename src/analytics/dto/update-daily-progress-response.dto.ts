import { ApiProperty } from '@nestjs/swagger';

export class UpdateDailyProgressResponseDto {
  @ApiProperty({
    description: 'Success message',
    example: 'Daily progress updated successfully'
  })
  message: string;

  @ApiProperty({
    description: 'XP gained from this update',
    example: 50
  })
  xpGained: number;

  @ApiProperty({
    description: 'Total XP after update',
    example: 1250
  })
  totalXp: number;

  @ApiProperty({
    description: 'Badge earned (if any)',
    example: 'Focus Master',
    nullable: true
  })
  badgeEarned?: string;

  @ApiProperty({
    description: 'Focus minutes added',
    example: 25
  })
  focusMinutesAdded: number;

  @ApiProperty({
    description: 'Total focus minutes for today',
    example: 125
  })
  totalTodayFocusMinutes: number;

  @ApiProperty({
    description: 'Whether a podcast was listened to',
    example: true
  })
  podcastListened: boolean;

  @ApiProperty({
    description: 'Current daily progress score',
    example: 85
  })
  dailyProgressScore: number;

  @ApiProperty({
    description: 'Current level',
    example: 5
  })
  currentLevel: number;

  @ApiProperty({
    description: 'Progress to next level (percentage)',
    example: 75
  })
  progressToNextLevel: number;

  @ApiProperty({
    description: 'When the progress was updated',
    example: '2025-05-19T10:30:00Z'
  })
  updatedAt: Date;
}
