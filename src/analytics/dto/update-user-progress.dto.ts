import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>N<PERSON>ber, IsOptional, IsArray, IsString, Min, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

class SkillPlansUpdateDto {
  @ApiProperty({
    description: 'Number of active skill plans',
    example: 2,
    minimum: 0
  })
  @IsNumber()
  @Min(0)
  active: number;

  @ApiProperty({
    description: 'Number of completed steps',
    example: 15,
    minimum: 0
  })
  @IsNumber()
  @Min(0)
  completedSteps: number;

  @ApiProperty({
    description: 'Total number of steps',
    example: 20,
    minimum: 0
  })
  @IsNumber()
  @Min(0)
  totalSteps: number;
}

export class UpdateUserProgressDto {
  @ApiProperty({
    description: 'Skill plans progress update',
    type: SkillPlansUpdateDto,
    required: false
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SkillPlansUpdateDto)
  skillPlans?: SkillPlansUpdateDto;

  @ApiProperty({
    description: 'XP points gained',
    example: 50,
    minimum: 0,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  xpGained?: number;

  @ApiProperty({
    description: 'Array of badges earned',
    example: ['consistency_master', 'focus_champion'],
    type: [String],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  badgesEarned?: string[];
}
