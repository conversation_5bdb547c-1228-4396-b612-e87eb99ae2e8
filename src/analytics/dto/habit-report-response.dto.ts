import { ApiProperty } from '@nestjs/swagger';

class HabitPerformance {
  @ApiProperty({
    description: 'Habit ID',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  habitId: string;

  @ApiProperty({
    description: 'Habit name',
    example: 'Daily Reading'
  })
  habitName: string;

  @ApiProperty({
    description: 'Completion rate for the period',
    example: 85.7
  })
  completionRate: number;

  @ApiProperty({
    description: 'Current streak',
    example: 12
  })
  currentStreak: number;

  @ApiProperty({
    description: 'Longest streak in period',
    example: 15
  })
  longestStreak: number;

  @ApiProperty({
    description: 'Total completions in period',
    example: 18
  })
  totalCompletions: number;

  @ApiProperty({
    description: 'Performance trend',
    example: 'improving',
    enum: ['improving', 'stable', 'declining']
  })
  trend: 'improving' | 'stable' | 'declining';
}

class HabitInsight {
  @ApiProperty({
    description: 'Insight type',
    example: 'strength',
    enum: ['strength', 'weakness', 'opportunity', 'pattern']
  })
  type: 'strength' | 'weakness' | 'opportunity' | 'pattern';

  @ApiProperty({
    description: 'Habit name this insight relates to',
    example: 'Exercise'
  })
  habitName: string;

  @ApiProperty({
    description: 'Insight description',
    example: 'You consistently complete exercise on weekdays but struggle on weekends'
  })
  description: string;

  @ApiProperty({
    description: 'Suggested improvements',
    example: ['Schedule weekend workouts', 'Find weekend-friendly activities']
  })
  suggestions: string[];
}

export class HabitReportResponseDto {
  @ApiProperty({
    description: 'Report period',
    example: 'month'
  })
  period: string;

  @ApiProperty({
    description: 'Report start date',
    example: '2025-04-19T00:00:00Z'
  })
  startDate: Date;

  @ApiProperty({
    description: 'Report end date',
    example: '2025-05-19T23:59:59Z'
  })
  endDate: Date;

  @ApiProperty({
    description: 'Overall habit completion rate',
    example: 78.5
  })
  overallCompletionRate: number;

  @ApiProperty({
    description: 'Individual habit performances',
    type: [HabitPerformance]
  })
  habitPerformances: HabitPerformance[];

  @ApiProperty({
    description: 'Best performing habit',
    example: 'Daily Reading'
  })
  bestHabit: string;

  @ApiProperty({
    description: 'Habit needing most improvement',
    example: 'Exercise'
  })
  improvementNeeded: string;

  @ApiProperty({
    description: 'Total active habits tracked',
    example: 5
  })
  totalHabitsTracked: number;

  @ApiProperty({
    description: 'Habits with perfect completion',
    example: ['Daily Reading', 'Morning Routine']
  })
  perfectHabits: string[];

  @ApiProperty({
    description: 'Key insights about habit patterns',
    type: [HabitInsight]
  })
  insights: HabitInsight[];

  @ApiProperty({
    description: 'Comparison with previous period',
    example: 8.5
  })
  changeFromPreviousPeriod: number;

  @ApiProperty({
    description: 'When the report was generated',
    example: '2025-05-19T10:30:00Z'
  })
  generatedAt: Date;
}
