import { ApiProperty } from '@nestjs/swagger';

class DailyMood {
  @ApiProperty({
    description: 'Date of mood entry',
    example: '2025-05-19'
  })
  date: string;

  @ApiProperty({
    description: 'Mood value',
    example: 'good',
    enum: ['great', 'good', 'neutral', 'bad', 'terrible']
  })
  mood: 'great' | 'good' | 'neutral' | 'bad' | 'terrible';

  @ApiProperty({
    description: 'Numeric representation of mood (1-5)',
    example: 4
  })
  moodScore: number;
}

class MoodCorrelation {
  @ApiProperty({
    description: 'Factor correlated with mood',
    example: 'Exercise'
  })
  factor: string;

  @ApiProperty({
    description: 'Correlation strength (0-1)',
    example: 0.75
  })
  correlationStrength: number;

  @ApiProperty({
    description: 'Whether correlation is positive or negative',
    example: 'positive'
  })
  correlationType: 'positive' | 'negative';
}

export class MoodAnalyticsResponseDto {
  @ApiProperty({
    description: 'Daily mood entries',
    type: [DailyMood]
  })
  moodEntries: DailyMood[];

  @ApiProperty({
    description: 'Average mood score for the period',
    example: 3.8
  })
  averageMood: number;

  @ApiProperty({
    description: 'Most common mood entry',
    example: 'good'
  })
  mostCommonMood: string;

  @ApiProperty({
    description: 'Mood trend (improving, stable, declining)',
    example: 'improving'
  })
  moodTrend: string;

  @ApiProperty({
    description: 'Factors correlated with mood',
    type: [MoodCorrelation]
  })
  correlations: MoodCorrelation[];
}
