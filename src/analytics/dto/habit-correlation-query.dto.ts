import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';

export class HabitCorrelationQueryDto {
  @ApiProperty({
    description: 'Time period for habit correlation analysis',
    example: 'month',
    enum: ['month', 'year'],
    required: false,
    default: 'month'
  })
  @IsOptional()
  @IsEnum(['month', 'year'])
  period?: 'month' | 'year' = 'month';
}
