import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';

export class HabitAnalyticsQueryDto {
  @ApiProperty({
    description: 'Time period for habit analytics',
    example: 'month',
    enum: ['week', 'month', 'year'],
    required: false,
    default: 'month'
  })
  @IsOptional()
  @IsEnum(['week', 'month', 'year'])
  period?: 'week' | 'month' | 'year' = 'month';
}
