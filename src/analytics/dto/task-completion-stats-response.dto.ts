import { ApiProperty } from '@nestjs/swagger';

class TaskCompletionByDay {
  @ApiProperty({
    description: 'Day',
    example: '2025-05-19'
  })
  day: string;

  @ApiProperty({
    description: 'Tasks completed',
    example: 6
  })
  completed: number;

  @ApiProperty({
    description: 'Tasks due',
    example: 8
  })
  due: number;

  @ApiProperty({
    description: 'Completion rate',
    example: 75
  })
  completionRate: number;
}

class TaskCompletionByPriority {
  @ApiProperty({
    description: 'Priority level',
    example: 'high'
  })
  priority: string;

  @ApiProperty({
    description: 'Tasks completed',
    example: 15
  })
  completed: number;

  @ApiProperty({
    description: 'Tasks due',
    example: 18
  })
  due: number;

  @ApiProperty({
    description: 'Completion rate',
    example: 83.3
  })
  completionRate: number;
}

export class TaskCompletionStatsResponseDto {
  @ApiProperty({
    description: 'Overall task completion rate',
    example: 78.5
  })
  overallCompletionRate: number;

  @ApiProperty({
    description: 'Tasks completed on time rate',
    example: 65.2
  })
  onTimeCompletionRate: number;

  @ApiProperty({
    description: 'Task completion by day',
    type: [TaskCompletionByDay]
  })
  completionByDay: TaskCompletionByDay[];

  @ApiProperty({
    description: 'Task completion by priority',
    type: [TaskCompletionByPriority]
  })
  completionByPriority: TaskCompletionByPriority[];

  @ApiProperty({
    description: 'Average tasks completed per day',
    example: 4.3
  })
  averageTasksPerDay: number;

  @ApiProperty({
    description: 'Most productive day for completing tasks',
    example: 'Monday'
  })
  mostProductiveDay: string;
}
