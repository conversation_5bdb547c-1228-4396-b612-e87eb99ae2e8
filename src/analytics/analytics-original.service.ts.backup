import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { ConfigService } from '@nestjs/config';
import { GoogleGenAI } from '@google/genai';
import { UserProgress } from './entities/user-progress.entity';
import { UsersService } from '../users/users.service';
import { HabitsService } from '../habits/habits.service';
import { TasksService } from '../tasks/tasks.service';
import { SkillPlansService } from '../skill-plans/skill-plans.service';
import { ChallengesService } from '../challenges/challenges.service';

@Injectable()
export class AnalyticsService {
  private readonly logger = new Logger(AnalyticsService.name);
  private readonly genAI: GoogleGenAI;

  constructor(
    @InjectRepository(UserProgress)
    private userProgressRepository: Repository<UserProgress>,
    private usersService: UsersService,
    private habitsService: HabitsService,
    private tasksService: TasksService,
    @Inject(forwardRef(() => SkillPlansService))
    private skillPlansService: SkillPlansService,
    private challengesService: ChallengesService,
    private configService: ConfigService,
  ) {
    // Initialize Google GenAI client
    const geminiApiKey = this.configService.get<string>('gemini.apiKey');
    if (geminiApiKey) {
      this.genAI = new GoogleGenAI({ apiKey: geminiApiKey });
    } else {
      this.logger.warn('GEMINI_API_KEY is not configured - AI features will be limited');
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async trackDailyProgress() {
    const users = await this.usersService.findAll();
    const date = new Date();
    date.setHours(0, 0, 0, 0);

    for (const user of users) {
      try {
        // Get habits progress
        const habits = await this.habitsService.findAllByUser(user);
        const habitStats = {
          completed: habits.filter(h => h.completion[date.toISOString().split('T')[0]]).length,
          total: habits.length,
          streaks: habits.reduce((acc, h) => ({ ...acc, [h.id]: h.currentStreak }), {}),
        };

        // Get tasks progress
        const tasks = await this.tasksService.findAll(user.id);
        const taskStats = {
          completed: tasks.filter(t => t.completed).length,
          total: tasks.length,
          overdue: tasks.filter(t => !t.completed && new Date(t.dueDate) < date).length,
        };

        // Get skill plans progress
        const skillPlans = await this.skillPlansService.findAll({ creatorId: user.id });
        const skillPlanStats = {
          active: skillPlans.length,
          completedSteps: skillPlans.reduce((acc, sp) => 
            acc + sp.steps.filter(s => s.tasks?.every(t => t.isCompleted)).length, 0),
          totalSteps: skillPlans.reduce((acc, sp) => acc + sp.steps.length, 0),
        };

        // Get challenges progress
        const challenges = await this.challengesService.findAll();
        const userChallenges = challenges.filter(c => 
          c.participants.some(p => p.id === user.id));
        const challengeStats = {
          active: userChallenges.filter(c => 
            new Date(c.endDate) >= date && new Date(c.startDate) <= date).length,
          completed: userChallenges.filter(c => new Date(c.endDate) < date).length,
        };

        // Calculate consistency score (0-100)
        const consistencyScore = this.calculateConsistencyScore({
          habitCompletion: habitStats.completed / habitStats.total,
          taskCompletion: taskStats.completed / taskStats.total,
          skillPlanProgress: skillPlanStats.completedSteps / skillPlanStats.totalSteps,
        });

        // Create progress record
        const progress = this.userProgressRepository.create({
          user,
          date,
          habits: habitStats,
          tasks: taskStats,
          skillPlans: skillPlanStats,
          challenges: challengeStats,
          xpGained: 0,
          totalXp: user.xp,
          badgesEarned: [],
          metrics: {
            focusMinutes: 0,
            podcastsListened: 0,
            consistencyScore,
            productiveHours: new Array(24).fill(0),
            moodTracking: [],
          },
        });

        await this.userProgressRepository.save(progress);
      } catch (error) {
        this.logger.error(`Failed to track progress for user ${user.id}:`, error);
      }
    }
  }

  private calculateConsistencyScore(metrics: {
    habitCompletion: number;
    taskCompletion: number;
    skillPlanProgress: number;
  }): number {
    const weights = {
      habits: 0.4,
      tasks: 0.4,
      skillPlans: 0.2,
    };

    const score = (
      metrics.habitCompletion * weights.habits +
      metrics.taskCompletion * weights.tasks +
      metrics.skillPlanProgress * weights.skillPlans
    ) * 100;

    return Math.round(Math.min(100, Math.max(0, score)));
  }

  async updateDailyProgress(userId: string, updates: {
    xpGained?: number;
    badgeEarned?: string;
    focusMinutes?: number;
    podcastListened?: boolean;
  }) {
    const date = new Date();
    date.setHours(0, 0, 0, 0);

    const progress = await this.userProgressRepository.findOne({
      where: {
        user: { id: userId },
        date,
      },
    });

    if (progress) {
      if (updates.xpGained) {
        progress.xpGained += updates.xpGained;
      }

      if (updates.badgeEarned && !progress.badgesEarned.includes(updates.badgeEarned)) {
        progress.badgesEarned.push(updates.badgeEarned);
      }

      if (updates.focusMinutes) {
        progress.metrics.focusMinutes += updates.focusMinutes;
      }

      if (updates.podcastListened) {
        progress.metrics.podcastsListened += 1;
      }

      await this.userProgressRepository.save(progress);
    }
  }

  async getUserProgress(userId: string, period: 'day' | 'week' | 'month' | 'year') {
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case 'day':
        startDate.setDate(startDate.getDate() - 1);
        break;
      case 'week':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(startDate.getMonth() - 1);
        break;
      case 'year':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
    }

    const progress = await this.userProgressRepository.find({
      where: {
        user: { id: userId },
        date: Between(startDate, endDate),
      },
      order: { date: 'ASC' },
      relations: ['user'],
    });

    const summary = this.calculateProgressSummary(progress);
    const user = await this.usersService.findOne(userId);
    
    return {
      period,
      startDate,
      endDate,
      totalXpGained: summary.totalXpGained,
      habits: {
        total: progress.length > 0 ? progress[progress.length - 1].habits.total : 0,
        active: progress.length > 0 ? progress[progress.length - 1].habits.completed : 0,
        completionRate: summary.habitCompletionRate,
        streaks: summary.streakProgress,
      },
      tasks: {
        total: progress.length > 0 ? progress[progress.length - 1].tasks.total : 0,
        completed: progress.length > 0 ? progress[progress.length - 1].tasks.completed : 0,
        overdue: progress.length > 0 ? progress[progress.length - 1].tasks.overdue : 0,
        completionRate: summary.taskCompletionRate,
      },
      challenges: {
        active: progress.length > 0 ? progress[progress.length - 1].challenges.active : 0,
        completed: progress.length > 0 ? progress[progress.length - 1].challenges.completed : 0,
      },
      skillPlans: {
        active: progress.length > 0 ? progress[progress.length - 1].skillPlans.active : 0,
        completedSteps: progress.length > 0 ? progress[progress.length - 1].skillPlans.completedSteps : 0,
        totalSteps: progress.length > 0 ? progress[progress.length - 1].skillPlans.totalSteps : 0,
        progress: progress.length > 0 ? Math.round((progress[progress.length - 1].skillPlans.completedSteps / progress[progress.length - 1].skillPlans.totalSteps) * 100) : 0,
      },
      focusTime: {
        totalMinutes: summary.totalFocusMinutes,
        distribution: summary.productivityTrends.mostProductiveHours,
      },
      podcastsListened: summary.totalPodcastsListened,
      consistencyScore: summary.averageConsistencyScore,
      mood: this.calculateMoodTrends(progress),
      badgesEarned: Array.from(summary.badgesEarned),
      insights: summary.insights,
      currentXp: user.xp,
    };
  }

  private calculateProgressSummary(progress: UserProgress[]) {
    if (!progress.length) return {
      totalXpGained: 0,
      averageConsistencyScore: 0,
      habitCompletionRate: 0,
      taskCompletionRate: 0,
      totalFocusMinutes: 0,
      totalPodcastsListened: 0,
      badgesEarned: new Set<string>(),
      streakProgress: {},
      productivityTrends: {
        mostProductiveHours: new Array(24).fill(0),
        mostProductiveDays: new Array(7).fill(0),
      },
      insights: {
        improvements: [],
        recommendations: [],
        achievements: [],
      },
    };

    const summary = {
      totalXpGained: 0,
      averageConsistencyScore: 0,
      habitCompletionRate: 0,
      taskCompletionRate: 0,
      totalFocusMinutes: 0,
      totalPodcastsListened: 0,
      badgesEarned: new Set<string>(),
      streakProgress: {},
      productivityTrends: {
        mostProductiveHours: new Array(24).fill(0),
        mostProductiveDays: new Array(7).fill(0),
      },
      insights: {
        improvements: [],
        recommendations: [],
        achievements: [],
      },
    };

    progress.forEach(p => {
      summary.totalXpGained += p.xpGained;
      summary.averageConsistencyScore += p.metrics.consistencyScore;
      summary.totalFocusMinutes += p.metrics.focusMinutes;
      summary.totalPodcastsListened += p.metrics.podcastsListened;
      p.badgesEarned?.forEach(b => summary.badgesEarned.add(b));

      // Track streak progress for each habit
      Object.entries(p.habits?.streaks || {}).forEach(([habitId, streak]) => {
        if (!summary.streakProgress[habitId] || streak > summary.streakProgress[habitId]) {
          summary.streakProgress[habitId] = streak;
        }
      });

      // Calculate habit completion rate
      if (p.habits?.total) {
        summary.habitCompletionRate += p.habits.completed / p.habits.total;
      }

      // Calculate task completion rate
      if (p.tasks?.total) {
        summary.taskCompletionRate += p.tasks.completed / p.tasks.total;
      }

      // Track productivity trends
      const date = new Date(p.date);
      summary.productivityTrends.mostProductiveHours[date.getHours()] += p.metrics.focusMinutes || 0;
      summary.productivityTrends.mostProductiveDays[date.getDay()] += p.metrics.focusMinutes || 0;
    });

    // Calculate averages
    const totalDays = progress.length;
    summary.averageConsistencyScore /= totalDays;
    summary.habitCompletionRate /= totalDays;
    summary.taskCompletionRate /= totalDays;

    // Generate insights
    this.generateInsights(summary);

    return summary;
  }

  private calculateMoodTrends(progress: UserProgress[]) {
    type MoodType = 'great' | 'good' | 'neutral' | 'bad' | 'terrible';
    const trends = {
      great: 0,
      good: 0,
      neutral: 0,
      bad: 0,
      terrible: 0,
      mostCommon: 'neutral' as MoodType,
    };

    progress.forEach(p => {
      p.metrics.moodTracking?.forEach(entry => {
        trends[entry.mood] += 1;
      });
    });

    // Calculate most common mood
    let maxCount = -1;
    (Object.entries(trends) as [MoodType | 'mostCommon', number][]).forEach(([mood, count]) => {
      if (mood !== 'mostCommon' && count > maxCount) {
        maxCount = count;
        trends.mostCommon = mood as MoodType;
      }
    });

    return trends;
  }

  private generateInsights(summary: any) {
    // Improvements
    if (summary.habitCompletionRate > 0.8) {
      summary.insights.achievements.push('High habit consistency achieved!');
    }
    if (summary.taskCompletionRate > 0.8) {
      summary.insights.achievements.push('Excellent task completion rate!');
    }
    if (summary.totalFocusMinutes > 1000) {
      summary.insights.achievements.push('Impressive focus time accumulated!');
    }

    // Recommendations
    if (summary.habitCompletionRate < 0.5) {
      summary.insights.recommendations.push('Try to maintain a more consistent habit schedule');
    }
    if (summary.taskCompletionRate < 0.5) {
      summary.insights.recommendations.push('Consider breaking down tasks into smaller, manageable pieces');
    }
    if (summary.totalPodcastsListened < 3) {
      summary.insights.recommendations.push('Listen to more motivational podcasts for inspiration');
    }

    // Progress tracking
    const previousPeriodComparison = {
      habits: summary.habitCompletionRate > 0.6 ? 'improved' : 'needs work',
      tasks: summary.taskCompletionRate > 0.6 ? 'improved' : 'needs attention',
      focus: summary.totalFocusMinutes > 500 ? 'strong' : 'could be better',
    };

    Object.entries(previousPeriodComparison).forEach(([area, status]) => {
      if (status.includes('improved') || status === 'strong') {
        summary.insights.improvements.push(`${area} performance has ${status}`);
      }
    });
  }

  async getHabitAnalytics(userId: string, period: 'week' | 'month' | 'year') {
    const user = await this.usersService.findOne(userId);
    const habits = await this.habitsService.findAllByUser(user);
    
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case 'week':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(startDate.getMonth() - 1);
        break;
      case 'year':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
    }

    const progress = await this.userProgressRepository.find({
      where: {
        user: { id: userId },
        date: Between(startDate, endDate),
      },
      order: { date: 'ASC' },
    });

    const habitStats = habits.map(habit => {
      const completions = progress.reduce((count, day) => {
        return count + (day.habits?.streaks?.[habit.id] ? 1 : 0);
      }, 0);

      return {
        id: habit.id,
        name: habit.name,
        completionRate: progress.length > 0 ? completions / progress.length : 0,
        currentStreak: habit.currentStreak,
        longestStreak: habit.longestStreak,
        lastCompletedAt: habit.lastCompletedAt,
      };
    });

    const totalCompletions = habitStats.reduce((acc, stat) => acc + Math.round(stat.completionRate * 100), 0);
    const overallCompletionRate = habitStats.length > 0 
      ? habitStats.reduce((sum, stat) => sum + stat.completionRate, 0) / habitStats.length
      : 0;
    
    return {
      completionRate: overallCompletionRate,
      streaks: habitStats.reduce((acc, habit) => ({ ...acc, [habit.id]: habit.currentStreak }), {}),
      totalCompleted: totalCompletions,
      byHabit: habitStats.map(habit => ({
        id: habit.id,
        name: habit.name,
        completionRate: habit.completionRate,
        currentStreak: habit.currentStreak,
        longestStreak: habit.longestStreak,
      })),
      distribution: this.calculateStreakDistribution(habitStats),
    };
  }

  private calculateStreakDistribution(habitStats: any[]) {
    const distribution = {
      '1-3 days': 0,
      '4-7 days': 0,
      '8-14 days': 0,
      '15-30 days': 0,
      '30+ days': 0,
    };

    habitStats.forEach(stat => {
      const streak = stat.currentStreak;
      if (streak >= 30) distribution['30+ days']++;
      else if (streak >= 15) distribution['15-30 days']++;
      else if (streak >= 8) distribution['8-14 days']++;
      else if (streak >= 4) distribution['4-7 days']++;
      else if (streak >= 1) distribution['1-3 days']++;
    });

    return distribution;
  }

  async getTaskCompletionStats(userId: string, period: 'week' | 'month' = 'week') {
    const endDate = new Date();
    const startDate = new Date();

    if (period === 'week') {
      startDate.setDate(startDate.getDate() - 7);
    } else {
      startDate.setMonth(startDate.getMonth() - 1);
    }

    const tasks = await this.tasksService.findAll(userId);
    const periodTasks = tasks.filter(task => {
      const dueDate = new Date(task.dueDate);
      return dueDate >= startDate && dueDate <= endDate;
    });

    const completed = periodTasks.filter(task => task.completed);
    const overdue = periodTasks.filter(task => !task.completed && new Date(task.dueDate) < new Date());
    const upcoming = periodTasks.filter(task => !task.completed && new Date(task.dueDate) >= new Date());
    const onTime = completed.filter(task => {
      const completedAt = new Date(task.completedAt!);
      const dueDate = new Date(task.dueDate);
      return completedAt <= dueDate;
    });

    return {
      period,
      startDate,
      endDate,
      total: periodTasks.length,
      completed: completed.length,
      totalCompleted: completed.length,
      overdue: overdue.length,
      upcoming: upcoming.length,
      completionRate: periodTasks.length ? completed.length / periodTasks.length : 1,
      onTimeCompletion: completed.length ? onTime.length / completed.length : 1,
      byPriority: this.groupTasksByPriority(periodTasks),
      byCategory: this.groupTasksByCategory(periodTasks),
      completionTrend: this.calculateTaskCompletionTrend(periodTasks, period),
    };
  }

  private groupTasksByPriority(tasks: any[]) {
    return {
      high: tasks.filter(t => t.priority === 'high').length,
      medium: tasks.filter(t => t.priority === 'medium').length,
      low: tasks.filter(t => t.priority === 'low').length,
    };
  }

  private groupTasksByCategory(tasks: any[]) {
    const categories = {};
    tasks.forEach(task => {
      const category = task.category || 'uncategorized';
      categories[category] = (categories[category] || 0) + 1;
    });
    return categories;
  }

  private calculateTaskCompletionTrend(tasks: any[], period: 'week' | 'month') {
    const trend: Array<{ date: string; completed: number; total: number }> = [];
    const now = new Date();
    const days = period === 'week' ? 7 : 30;

    for (let i = 0; i < days; i++) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      const dayTasks = tasks.filter(t => {
        const taskDate = new Date(t.completedAt || t.dueDate);
        return taskDate.toDateString() === date.toDateString();
      });
      
      trend.unshift({
        date: date.toISOString().split('T')[0],
        completed: dayTasks.filter(t => t.completed).length,
        total: dayTasks.length,
      });
    }

    return trend;
  }

  private async generateUserInsights(userId: string, progress: UserProgress[]): Promise<{
    mostProductiveTime: string;
    strongestHabits: string[];
    areasForImprovement: string[];
    streakMilestones: { [habitId: string]: number };
    recommendedActions: string[];
  }> {
    const user = await this.usersService.findOne(userId);
    const habits = await this.habitsService.findAllByUser(user);
    const tasks = await this.tasksService.findAll(userId);
    
    // Analyze productive hours
    const productiveHours = this.analyzeProductiveHours(progress);
    const mostProductiveTime = this.determineMostProductiveTime(productiveHours);

    // Analyze habits
    const habitAnalysis = this.analyzeHabits(habits, progress);
    const strongestHabits = habitAnalysis.strongest;
    const weakestHabits = habitAnalysis.weakest;

    // Generate recommendations
    const recommendedActions = await this.generateRecommendations({
      habits: habitAnalysis,
      tasks,
      productiveHours,
      progress,
    });

    return {
      mostProductiveTime,
      strongestHabits,
      areasForImprovement: weakestHabits,
      streakMilestones: this.identifyStreakMilestones(habits),
      recommendedActions,
    };
  }

  private analyzeProductiveHours(progress: UserProgress[]): number[] {
    const hourlyProductivity = new Array(24).fill(0);
    
    progress.forEach(p => {
      if (p.metrics?.productiveHours) {
        p.metrics.productiveHours.forEach(hour => {
          if (typeof hour === 'number' && hour >= 0 && hour < 24) {
            hourlyProductivity[hour]++;
          }
        });
      }
    });

    return hourlyProductivity;
  }

  private determineMostProductiveTime(hourlyProductivity: number[]): string {
    const maxProductivity = Math.max(...hourlyProductivity);
    const mostProductiveHour = hourlyProductivity.indexOf(maxProductivity);
    
    // Convert to human-readable time range
    const startHour = mostProductiveHour;
    const endHour = (mostProductiveHour + 1) % 24;
    
    return `${startHour.toString().padStart(2, '0')}:00-${endHour.toString().padStart(2, '0')}:00`;
  }

  private analyzeHabits(habits: any[], progress: UserProgress[]) {
    const habitStats = habits.map(habit => {
      const completionRate = progress.reduce((acc, p) => {
        const isCompleted = p.habits.streaks[habit.id] > 0;
        return acc + (isCompleted ? 1 : 0);
      }, 0) / progress.length;

      return {
        id: habit.id,
        name: habit.name,
        completionRate,
        currentStreak: habit.currentStreak,
      };
    });

    // Sort by completion rate and current streak
    habitStats.sort((a, b) => 
      (b.completionRate * 0.7 + b.currentStreak * 0.3) - 
      (a.completionRate * 0.7 + a.currentStreak * 0.3)
    );

    return {
      strongest: habitStats.slice(0, 3).map(h => h.name),
      weakest: habitStats.slice(-3).map(h => h.name),
      all: habitStats,
    };
  }

  private identifyStreakMilestones(habits: any[]): { [habitId: string]: number } {
    const milestones = {};
    
    habits.forEach(habit => {
      if (habit.currentStreak >= 7) {
        milestones[habit.id] = Math.floor(habit.currentStreak / 7) * 7;
      }
    });

    return milestones;
  }

  private async generateRecommendations({
    habits,
    tasks,
    productiveHours,
    progress,
  }): Promise<string[]> {
    const recommendations: string[] = [];

    // Analyze task patterns
    if (tasks?.length > 0) {
      const overdueTasks = tasks.filter(t => !t.completed && new Date(t.dueDate) < new Date());
      if (overdueTasks.length > 0) {
        recommendations.push(
          `Prioritize completing overdue tasks (${overdueTasks.length} pending)`
        );
      }
    }

    // Calculate average consistency score
    if (progress?.length > 0) {
      const recentProgress = progress.slice(-7);
      const validProgress = recentProgress.filter(p => p.metrics?.consistencyScore !== undefined);
      
      if (validProgress.length > 0) {
        const averageConsistency = validProgress.reduce(
          (acc, p) => acc + (p.metrics.consistencyScore || 0), 0
        ) / validProgress.length;

        if (averageConsistency < 70) {
          recommendations.push(
            'Your consistency score has been below target. Try to maintain regular habits and complete daily tasks.'
          );
        }
      }
    }

    return recommendations;
  }

  async getProductivityAnalytics(userId: string, period: 'week' | 'month') {
    const endDate = new Date();
    const startDate = this.getStartDateForPeriod(period);
    
    const progress = await this.userProgressRepository.find({
      where: {
        user: { id: userId },
        date: Between(startDate, endDate),
      },
      order: { date: 'ASC' },
    });

    const hourlyProductivity = this.analyzeProductiveHours(progress);
    const dailyProductivity = this.analyzeDailyProductivity(progress);

    return {
      mostProductiveTime: this.determineMostProductiveTime(hourlyProductivity),
      mostProductiveDay: this.determineMostProductiveDay(dailyProductivity),
      hourlyBreakdown: hourlyProductivity,
      dailyBreakdown: dailyProductivity,
      focusTimeStats: {
        totalMinutes: progress.reduce((acc, p) => acc + p.metrics.focusMinutes, 0),
        dailyAverage: Math.round(
          progress.reduce((acc, p) => acc + p.metrics.focusMinutes, 0) / progress.length
        ),
        bestStreak: this.calculateFocusTimeStreak(progress),
      },
    };
  }

  async getMoodAnalytics(userId: string, period: 'week' | 'month' | 'year') {
    const endDate = new Date();
    const startDate = this.getStartDateForPeriod(period);
    
    const progress = await this.userProgressRepository.find({
      where: {
        user: { id: userId },
        date: Between(startDate, endDate),
      },
      order: { date: 'ASC' },
    });

    const moodData = progress.reduce((acc, p) => {
      p.metrics.moodTracking.forEach(mt => {
        acc[mt.mood] = (acc[mt.mood] || 0) + 1;
      });
      return acc;
    }, {});

    return {
      moodDistribution: moodData,
      moodTrends: this.analyzeMoodTrends(progress),
      correlations: {
        productivity: this.analyzeMoodProductivityCorrelation(progress),
        habits: this.analyzeMoodHabitCorrelation(progress),
      },
      insights: this.generateMoodInsights(progress),
    };
  }

  async getPersonalizedInsights(userId: string) {
    const recentProgress = await this.userProgressRepository.find({
      where: {
        user: { id: userId },
        date: MoreThanOrEqual(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)),
      },
      order: { date: 'DESC' },
    });

    return this.generateUserInsights(userId, recentProgress);
  }

  private getStartDateForPeriod(period: string): Date {
    const startDate = new Date();
    switch (period) {
      case 'week':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(startDate.getMonth() - 1);
        break;
      case 'year':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
    }
    return startDate;
  }

  private calculateWeeklyCompletion(habitId: string, progress: UserProgress[]): number[] {
    const weeklyData = new Array(7).fill(0);
    progress.forEach(p => {
      const dayOfWeek = new Date(p.date).getDay();
      if (p.habits.streaks[habitId] > 0) {
        weeklyData[dayOfWeek]++;
      }
    });
    return weeklyData;
  }

  private analyzeDailyProductivity(progress: UserProgress[]): number[] {
    const dailyProductivity = new Array(7).fill(0);
    progress.forEach(p => {
      const dayOfWeek = new Date(p.date).getDay();
      dailyProductivity[dayOfWeek] += p.metrics.focusMinutes;
    });
    return dailyProductivity;
  }

  private calculateFocusTimeStreak(progress: UserProgress[]): number {
    let currentStreak = 0;
    let maxStreak = 0;

    progress.forEach(p => {
      if (p.metrics.focusMinutes > 0) {
        currentStreak++;
        maxStreak = Math.max(maxStreak, currentStreak);
      } else {
        currentStreak = 0;
      }
    });

    return maxStreak;
  }

  private analyzeMoodTrends(progress: UserProgress[]) {
    const dailyMoods = progress.map(p => ({
      date: p.date,
      moods: p.metrics.moodTracking,
    }));

    return {
      daily: dailyMoods,
      trending: this.calculateMoodTrend(dailyMoods),
    };
  }

  private calculateMoodTrend(dailyMoods: any[]): 'improving' | 'declining' | 'stable' {
    const moodScores = {
      'great': 5,
      'good': 4,
      'neutral': 3,
      'bad': 2,
      'terrible': 1,
    };

    const recentScores = dailyMoods.slice(-7).map(dm => 
      dm.moods.reduce((acc, m) => acc + moodScores[m.mood], 0) / dm.moods.length
    );

    const trend = recentScores[recentScores.length - 1] - recentScores[0];
    
    if (trend > 0.5) return 'improving';
    if (trend < -0.5) return 'declining';
    return 'stable';
  }

  private analyzeHabitTimeOfDay(habitId: string, progress: UserProgress[]): {
    morning: number;
    afternoon: number;
    evening: number;
    night: number;
  } {
    const timeSlots = {
      morning: 0,   // 5AM - 11AM
      afternoon: 0, // 11AM - 5PM
      evening: 0,   // 5PM - 11PM
      night: 0,     // 11PM - 5AM
    };

    progress.forEach(p => {
      if (p.habits.streaks[habitId] > 0) {
        const completionHour = new Date(p.date).getHours();
        if (completionHour >= 5 && completionHour < 11) timeSlots.morning++;
        else if (completionHour >= 11 && completionHour < 17) timeSlots.afternoon++;
        else if (completionHour >= 17 && completionHour < 23) timeSlots.evening++;
        else timeSlots.night++;
      }
    });

    return timeSlots;
  }

  private calculateOverallCompletionRate(habits: any[], progress: UserProgress[]): number {
    if (!progress.length || !habits.length) return 0;

    const totalCompletions = progress.reduce((acc, p) => {
      const completionsForDay = Object.values(p.habits.streaks).filter(streak => streak > 0).length;
      return acc + completionsForDay;
    }, 0);

    const totalPossibleCompletions = habits.length * progress.length;
    return (totalCompletions / totalPossibleCompletions) * 100;
  }

  private determineMostProductiveDay(dailyProductivity: number[]): string {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const maxProductivity = Math.max(...dailyProductivity);
    const mostProductiveIndex = dailyProductivity.indexOf(maxProductivity);
    return days[mostProductiveIndex];
  }

  private analyzeMoodProductivityCorrelation(progress: UserProgress[]): {
    correlation: number;
    insight: string;
  } {
    const moodScores = {
      'great': 5,
      'good': 4,
      'neutral': 3,
      'bad': 2,
      'terrible': 1,
    };

    const data = progress.map(p => ({
      moodScore: p.metrics.moodTracking.reduce((acc, m) => acc + moodScores[m.mood], 0) / p.metrics.moodTracking.length,
      productivity: p.metrics.focusMinutes,
    }));

    // Calculate Pearson correlation coefficient
    const n = data.length;
    if (n < 2) return { correlation: 0, insight: 'Not enough data to analyze correlation' };

    const sumX = data.reduce((acc, d) => acc + d.moodScore, 0);
    const sumY = data.reduce((acc, d) => acc + d.productivity, 0);
    const sumXY = data.reduce((acc, d) => acc + d.moodScore * d.productivity, 0);
    const sumXX = data.reduce((acc, d) => acc + d.moodScore * d.moodScore, 0);
    const sumYY = data.reduce((acc, d) => acc + d.productivity * d.productivity, 0);

    const correlation = (n * sumXY - sumX * sumY) / 
      Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));

    let insight = '';
    if (correlation > 0.5) {
      insight = 'Strong positive correlation between mood and productivity';
    } else if (correlation > 0.2) {
      insight = 'Moderate positive correlation between mood and productivity';
    } else if (correlation > -0.2) {
      insight = 'No significant correlation between mood and productivity';
    } else {
      insight = 'Negative correlation between mood and productivity';
    }

    return { correlation, insight };
  }

  private analyzeMoodHabitCorrelation(progress: UserProgress[]): {
    habitImpact: { [habitId: string]: number };
    topMoodBoosters: string[];
  } {
    interface HabitImpactData {
      count: number;
      totalScore: number;
    }
    
    const habitImpactData: { [habitId: string]: HabitImpactData } = {};
    const moodScores = {
      'great': 5,
      'good': 4,
      'neutral': 3,
      'bad': 2,
      'terrible': 1,
    };

    progress.forEach(p => {
      const dailyMoodScore = p.metrics.moodTracking.reduce(
        (acc, m) => acc + moodScores[m.mood], 
        0
      ) / p.metrics.moodTracking.length;

      Object.entries(p.habits.streaks).forEach(([habitId, streak]) => {
        if (!habitImpactData[habitId]) {
          habitImpactData[habitId] = { count: 0, totalScore: 0 };
        }
        if (streak > 0) {
          habitImpactData[habitId].count++;
          habitImpactData[habitId].totalScore += dailyMoodScore;
        }
      });
    });

    // Calculate average mood score for each habit
    const habitImpact: { [habitId: string]: number } = {};
    Object.entries(habitImpactData).forEach(([habitId, data]) => {
      habitImpact[habitId] = data.count > 0 ? data.totalScore / data.count : 0;
    });

    // Sort habits by their mood impact
    const topMoodBoosters = Object.entries(habitImpact)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3)
      .map(([habitId]) => habitId);

    return { habitImpact, topMoodBoosters };
  }

  private generateMoodInsights(progress: UserProgress[]): string[] {
    const insights: string[] = [];
    const moodCorrelation = this.analyzeMoodProductivityCorrelation(progress);
    const habitCorrelation = this.analyzeMoodHabitCorrelation(progress);

    insights.push(moodCorrelation.insight);

    if (habitCorrelation.topMoodBoosters.length > 0) {
      insights.push(
        'These habits seem to have the most positive impact on your mood: ' +
        habitCorrelation.topMoodBoosters.join(', ')
      );
    }

    const moodTrend = this.calculateMoodTrend(progress.map(p => ({
      date: p.date,
      moods: p.metrics.moodTracking,
    })));

    switch (moodTrend) {
      case 'improving':
        insights.push('Your mood has been improving over the past week');
        break;
      case 'declining':
        insights.push('Your mood has been declining. Consider focusing on mood-boosting activities');
        break;
      case 'stable':
        insights.push('Your mood has been stable');
        break;
    }

    return insights;
  }

  private trackDailyProductivity(progress: UserProgress): void {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (progress.date.getTime() === today.getTime()) {
      const hour = new Date().getHours();
      if (!progress.metrics.productiveHours.includes(hour) && progress.metrics.focusMinutes > 0) {
        progress.metrics.productiveHours.push(hour);
      }
    }
  }

  private async updateDailyInsights(progress: UserProgress): Promise<void> {
    const recentProgress = await this.userProgressRepository.find({
      where: {
        user: { id: progress.user.id },
        date: LessThanOrEqual(progress.date),
      },
      order: { date: 'DESC' },
      take: 30,
    });

    const insights = await this.generateUserInsights(progress.user.id, recentProgress);
    progress.insights = insights;
    await this.userProgressRepository.save(progress);
  }

  async recordFocusSession(userId: string, minutes: number): Promise<void> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    let progress = await this.userProgressRepository.findOne({
      where: {
        user: { id: userId },
        date: today,
      },
      relations: ['user'],
    });

    if (progress) {
      progress.metrics.focusMinutes += minutes;
      this.trackDailyProductivity(progress);
      await this.updateDailyInsights(progress);
    }
  }

  async recordMoodEntry(userId: string, mood: 'great' | 'good' | 'neutral' | 'bad' | 'terrible'): Promise<void> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    let progress = await this.userProgressRepository.findOne({
      where: {
        user: { id: userId },
        date: today,
      },
      relations: ['user'],
    });

    if (progress) {
      progress.metrics.moodTracking.push({
        mood,
        timestamp: new Date().toISOString(),
      });
      await this.updateDailyInsights(progress);
    }
  }

  async aggregateWeeklyStats(userId: string): Promise<{
    totalFocusTime: number;
    avgDailyConsistency: number;
    habitCompletionRate: number;
    topPerformingDays: string[];
  }> {
    const endDate = new Date();
    const startDate = new Date(endDate);
    startDate.setDate(startDate.getDate() - 7);

    const weeklyProgress = await this.userProgressRepository.find({
      where: {
        user: { id: userId },
        date: Between(startDate, endDate),
      },
      order: { date: 'ASC' },
    });

    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const dailyStats = weeklyProgress.map(p => ({
      day: days[new Date(p.date).getDay()],
      focusMinutes: p.metrics.focusMinutes,
      consistencyScore: p.metrics.consistencyScore,
      habitCompletion: p.habits.completed / p.habits.total,
    }));

    const sortedDays = dailyStats
      .sort((a, b) => (b.focusMinutes * 0.5 + b.consistencyScore * 0.5) - 
                      (a.focusMinutes * 0.5 + a.consistencyScore * 0.5))
      .map(d => d.day);

    return {
      totalFocusTime: dailyStats.reduce((acc, d) => acc + d.focusMinutes, 0),
      avgDailyConsistency: dailyStats.reduce((acc, d) => acc + d.consistencyScore, 0) / dailyStats.length,
      habitCompletionRate: dailyStats.reduce((acc, d) => acc + d.habitCompletion, 0) / dailyStats.length * 100,
      topPerformingDays: sortedDays.slice(0, 3),
    };
  }

  async updateUserProgress(userId: string, update: {
    skillPlans?: {
      active: number;
      completedSteps: number;
      totalSteps: number;
    };
    xpGained?: number;
    badgesEarned?: string[];
  }): Promise<UserProgress> {
    let progress = await this.userProgressRepository.findOne({
      where: { user: { id: userId }, date: new Date() }
    });

    if (!progress) {
      progress = this.userProgressRepository.create({
        user: { id: userId },
        date: new Date(),
        skillPlans: { active: 0, completedSteps: 0, totalSteps: 0 },
        xpGained: 0,
        badgesEarned: []
      });
    }

    if (update.skillPlans) {
      progress.skillPlans = {
        ...progress.skillPlans,
        ...update.skillPlans
      };
    }

    if (update.xpGained) {
      progress.xpGained += update.xpGained;
    }

    if (update.badgesEarned) {
      progress.badgesEarned = [...(progress.badgesEarned || []), ...update.badgesEarned];
    }

    return this.userProgressRepository.save(progress);
  }

  private async generateAIInsights(context: {
    user: any;
    progressData: any[];
    metrics: any[];
    days: number;
  }): Promise<{
    personalityInsights: any[];
    improvements: any[];
    performanceAnalysis: { strengths: string[]; criticalAreas: string[] };
    motivationalMessage: string;
  }> {
    if (!this.genAI) {
      this.logger.warn('GenAI not initialized - falling back to traditional insights');
      return this.generateTraditionalInsights(context);
    }

    try {
      const prompt = this.buildAIInsightsPrompt(context);
      
      const response = await this.genAI.models.generateContent({
        model: "gemini-2.0-flash",
        contents: [{ parts: [{ text: prompt }] }],
      });
      
      const content = response.text?.trim();
      if (!content) {
        throw new Error('Empty response from GenAI');
      }

      // Parse the AI response
      const aiInsights = this.parseAIInsightsResponse(content);
      return aiInsights;
      
    } catch (error) {
      this.logger.error('Error generating AI insights:', error);
      return this.generateTraditionalInsights(context);
    }
  }

  private buildAIInsightsPrompt(context: {
    user: any;
    progressData: any[];
    metrics: any[];
    days: number;
  }): string {
    const { user, progressData, metrics, days } = context;
    
    // Calculate summary statistics
    const totalDays = progressData.length;
    const avgTaskCompletion = totalDays > 0 ? 
      progressData.reduce((acc, p) => acc + (p.tasks.total > 0 ? (p.tasks.completed / p.tasks.total) * 100 : 0), 0) / totalDays : 0;
    const avgHabitConsistency = totalDays > 0 ? 
      progressData.reduce((acc, p) => acc + (p.habits.total > 0 ? (p.habits.completed / p.habits.total) * 100 : 0), 0) / totalDays : 0;
    const avgFocusTime = totalDays > 0 ? 
      progressData.reduce((acc, p) => acc + (p.metrics?.focusMinutes || 0), 0) / totalDays : 0;

    // Analyze productivity patterns
    const productivityHours = this.analyzeProductivityPatterns(progressData);
    const moodPatterns = this.analyzeMoodPatterns(progressData);

    const prompt = `
As a lifestyle improvement AI consultant, analyze the following user data and provide personalized insights and recommendations in JSON format.

User Profile:
- Name: ${user.firstName} ${user.lastName}
- Analysis Period: ${days} days (${totalDays} days of actual data)

Performance Metrics:
- Average Task Completion Rate: ${avgTaskCompletion.toFixed(1)}%
- Average Habit Consistency: ${avgHabitConsistency.toFixed(1)}%
- Average Daily Focus Time: ${avgFocusTime.toFixed(1)} minutes
- Productivity Patterns: ${productivityHours}
- Mood Patterns: ${moodPatterns}

Current Metrics:
${metrics.map(m => `- ${m.name}: ${m.currentScore}/100 (${m.trend}, ${m.changePercentage}% change)`).join('\n')}

Please provide a comprehensive analysis in the following JSON format:

{
  "personalityInsights": [
    {
      "insight": "Specific behavioral insight based on data patterns",
      "category": "Category (e.g., Productivity Pattern, Emotional Pattern, Behavioral Pattern)",
      "confidence": 80
    }
  ],
  "improvements": [
    {
      "area": "Specific area for improvement",
      "currentScore": 65,
      "improvementPotential": 25,
      "actions": [
        {
          "title": "Specific actionable recommendation",
          "description": "Detailed description of the action",
          "impact": "high|medium|low",
          "difficulty": "easy|medium|hard",
          "timeToResults": 14,
          "category": "Category of improvement"
        }
      ],
      "evidence": "Data-based evidence supporting this recommendation"
    }
  ],
  "performanceAnalysis": {
    "strengths": ["List of user's strong areas"],
    "criticalAreas": ["Areas needing immediate attention"]
  },
  "motivationalMessage": "Personalized, encouraging message based on their progress and personality"
}

Guidelines:
1. Base all insights on the actual data provided
2. Be specific and actionable in recommendations
3. Consider their personality patterns when suggesting improvements
4. Prioritize improvements by potential impact
5. Make the motivational message personal and encouraging
6. Ensure confidence scores reflect data quality and pattern strength
7. Focus on sustainable, realistic changes

Return only the JSON response, no additional text.`;

    return prompt;
  }

  private parseAIInsightsResponse(content: string): {
    personalityInsights: any[];
    improvements: any[];
    performanceAnalysis: { strengths: string[]; criticalAreas: string[] };
    motivationalMessage: string;
  } {
    try {
      // Clean up the response - remove any markdown formatting
      const cleanedContent = content
        .replace(/```json/g, '')
        .replace(/```/g, '')
        .trim();

      const parsed = JSON.parse(cleanedContent);
      
      // Validate the structure
      return {
        personalityInsights: parsed.personalityInsights || [],
        improvements: parsed.improvements || [],
        performanceAnalysis: parsed.performanceAnalysis || { strengths: [], criticalAreas: [] },
        motivationalMessage: parsed.motivationalMessage || 'Keep up the great work!',
      };
    } catch (error) {
      this.logger.error('Error parsing AI insights response:', error);
      // Return default structure if parsing fails
      return {
        personalityInsights: [],
        improvements: [],
        performanceAnalysis: { strengths: [], criticalAreas: [] },
        motivationalMessage: 'Keep pushing forward - every step counts!',
      };
    }
  }

  private generateTraditionalInsights(context: {
    user: any;
    progressData: any[];
    metrics: any[];
    days: number;
  }): {
    personalityInsights: any[];
    improvements: any[];
    performanceAnalysis: { strengths: string[]; criticalAreas: string[] };
    motivationalMessage: string;
  } {
    // Fallback to original logic when AI is not available
    const { progressData, metrics } = context;
    
    const personalityInsights = this.generatePersonalityInsights(progressData);
    const improvements = this.generateImprovementRecommendations(progressData, metrics);
    const performanceAnalysis = this.analyzePerformanceAreas(metrics);
    const overallScore = this.calculateOverallLifestyleScore(metrics);
    const motivationalMessage = this.generateMotivationalMessage(progressData, overallScore);

    return {
      personalityInsights,
      improvements,
      performanceAnalysis,
      motivationalMessage,
    };
  }

  private analyzeProductivityPatterns(progressData: any[]): string {
    const productiveHours = progressData
      .filter(p => p.metrics?.productiveHours)
      .flatMap(p => p.metrics.productiveHours.map((value, hour) => ({ hour, value })))
      .reduce((acc, { hour, value }) => {
        acc[hour] = (acc[hour] || 0) + value;
        return acc;
      }, {} as { [hour: number]: number });

    if (Object.keys(productiveHours).length === 0) {
      return 'No productivity data available';
    }

    const bestHour = Object.entries(productiveHours)
      .sort(([, a], [, b]) => (b as number) - (a as number))[0];

    if (bestHour) {
      const hour = parseInt(bestHour[0]);
      const timeLabel = hour < 12 ? 'morning' : hour < 17 ? 'afternoon' : 'evening';
      return `Most productive during ${timeLabel} hours (around ${hour}:00)`;
    }

    return 'Productivity patterns unclear';
  }

  private analyzeMoodPatterns(progressData: any[]): string {
    const moodEntries = progressData
      .filter(p => p.metrics?.moodTracking?.length > 0)
      .flatMap(p => p.metrics.moodTracking);

    if (moodEntries.length === 0) {
      return 'No mood data available';
    }

    const moodCounts = moodEntries.reduce((acc, entry) => {
      acc[entry.mood] = (acc[entry.mood] || 0) + 1;
      return acc;
    }, {} as { [mood: string]: number });

    const dominantMood = Object.entries(moodCounts)
      .sort(([, a], [, b]) => (b as number) - (a as number))[0][0];

    return `Most common mood: ${dominantMood} (${moodCounts[dominantMood]} entries)`;
  }

  async getAIImprovementReport(userId: string, days: number = 30): Promise<any> {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get user progress data for the specified period
    const progressData = await this.userProgressRepository.find({
      where: {
        user: { id: userId },
        date: Between(startDate, endDate),
      },
      order: { date: 'DESC' },
      relations: ['user'],
    });

    if (progressData.length === 0) {
      // Return default report if no data available
      return this.generateDefaultReport();
    }

    // Get user details
    const user = progressData[0].user;

    // Gather comprehensive user data
    const comprehensiveData = await this.gatherComprehensiveUserData(user);

    // Calculate basic metrics
    const metrics = this.calculateLifestyleMetrics(progressData);
    const overallScore = this.calculateOverallLifestyleScore(metrics);

    // Use AI to generate intelligent insights and recommendations
    let aiGeneratedContent: {
      personalityInsights: any[];
      improvements: any[];
      performanceAnalysis: { strengths: string[]; criticalAreas: string[] };
      motivationalMessage: string;
    } | null = null;
    
    if (this.genAI) {
      try {
        aiGeneratedContent = await this.generateAIInsights({
          user,
          progressData,
          metrics,
          days,
        });
      } catch (error) {
        this.logger.error('Failed to generate AI insights:', error);
        // Fall back to rule-based insights
      }
    }

    // Merge AI insights with traditional analysis
    const personalityInsights = aiGeneratedContent?.personalityInsights || 
      this.generatePersonalityInsights(progressData);
    
    const improvements = aiGeneratedContent?.improvements || 
      this.generateImprovementRecommendations(progressData, metrics);
    
    const { strengths, criticalAreas } = aiGeneratedContent?.performanceAnalysis || 
      this.analyzePerformanceAreas(metrics);
    
    const motivationalMessage = aiGeneratedContent?.motivationalMessage || 
      this.generateMotivationalMessage(progressData, overallScore);

    // Calculate projected score
    const projectedScore = Math.min(100, overallScore + (improvements.length > 0 ? improvements[0].improvementPotential : 0));

    return {
      overallScore,
      metrics,
      improvements,
      personalityInsights,
      strengths,
      criticalAreas,
      projectedScore,
      analysisDateRange: days,
      generatedAt: new Date(),
      motivationalMessage,
      aiEnhanced: !!aiGeneratedContent,
    };
  }

  private generateDefaultReport(): any {
    return {
      overallScore: 50,
      metrics: [
        {
          name: 'Task Completion',
          currentScore: 50,
          targetScore: 80,
          trend: 'stable',
          changePercentage: 0,
        },
        {
          name: 'Habit Consistency',
          currentScore: 50,
          targetScore: 85,
          trend: 'stable',
          changePercentage: 0,
        },
      ],
      improvements: [
        {
          area: 'Getting Started',
          currentScore: 50,
          improvementPotential: 30,
          actions: [
            {
              title: 'Start Building Daily Habits',
              description: 'Begin with 2-3 simple habits like drinking water, making your bed, or reading for 10 minutes',
              impact: 'high',
              difficulty: 'easy',
              timeToResults: 7,
              category: 'Habits',
            },
          ],
          evidence: 'No activity data available yet. Start tracking your daily activities to get personalized insights.',
        },
      ],
      personalityInsights: [
        {
          insight: 'Welcome to your improvement journey! Start small and stay consistent.',
          category: 'Getting Started',
          confidence: 100,
        },
      ],
      strengths: ['Ready to improve'],
      criticalAreas: ['Need to start tracking activities'],
      projectedScore: 80,
      analysisDateRange: 30,
      generatedAt: new Date(),
      motivationalMessage: 'Every expert was once a beginner. Start your journey today!',
    };
  }

  private calculateLifestyleMetrics(progressData: any[]): any[] {
    const totalDays = progressData.length;
    if (totalDays === 0) return [];

    // Task completion metrics
    const avgTaskCompletion = progressData.reduce((acc, p) => {
      return acc + (p.tasks.total > 0 ? (p.tasks.completed / p.tasks.total) * 100 : 0);
    }, 0) / totalDays;

    const taskTrend = this.calculateTrend(
      progressData.slice(-7).map(p => p.tasks.total > 0 ? (p.tasks.completed / p.tasks.total) * 100 : 0),
      progressData.slice(-14, -7).map(p => p.tasks.total > 0 ? (p.tasks.completed / p.tasks.total) * 100 : 0)
    );

    // Habit consistency metrics
    const avgHabitConsistency = progressData.reduce((acc, p) => {
      return acc + (p.habits.total > 0 ? (p.habits.completed / p.habits.total) * 100 : 0);
    }, 0) / totalDays;

    const habitTrend = this.calculateTrend(
      progressData.slice(-7).map(p => p.habits.total > 0 ? (p.habits.completed / p.habits.total) * 100 : 0),
      progressData.slice(-14, -7).map(p => p.habits.total > 0 ? (p.habits.completed / p.habits.total) * 100 : 0)
    );

    // Focus time metrics
    const avgFocusTime = progressData.reduce((acc, p) => acc + (p.metrics?.focusMinutes || 0), 0) / totalDays;
    const focusTrend = this.calculateTrend(
      progressData.slice(-7).map(p => p.metrics?.focusMinutes || 0),
      progressData.slice(-14, -7).map(p => p.metrics?.focusMinutes || 0)
    );

    // Consistency score metrics
    const avgConsistencyScore = progressData.reduce((acc, p) => acc + (p.metrics?.consistencyScore || 50), 0) / totalDays;
    const consistencyTrend = this.calculateTrend(
      progressData.slice(-7).map(p => p.metrics?.consistencyScore || 50),
      progressData.slice(-14, -7).map(p => p.metrics?.consistencyScore || 50)
    );

    return [
      {
        name: 'Task Completion Rate',
        currentScore: Math.round(avgTaskCompletion),
        targetScore: 85,
        trend: taskTrend.trend,
        changePercentage: taskTrend.change,
      },
      {
        name: 'Habit Consistency',
        currentScore: Math.round(avgHabitConsistency),
        targetScore: 90,
        trend: habitTrend.trend,
        changePercentage: habitTrend.change,
      },
      {
        name: 'Daily Focus Time',
        currentScore: Math.min(100, Math.round((avgFocusTime / 120) * 100)), // Target: 2 hours
        targetScore: 80,
        trend: focusTrend.trend,
        changePercentage: focusTrend.change,
      },
      {
        name: 'Overall Consistency',
        currentScore: Math.round(avgConsistencyScore),
        targetScore: 85,
        trend: consistencyTrend.trend,
        changePercentage: consistencyTrend.change,
      },
    ];
  }

  private calculateTrend(recentValues: number[], olderValues: number[]): { trend: 'improving' | 'declining' | 'stable', change: number } {
    if (recentValues.length === 0 || olderValues.length === 0) {
      return { trend: 'stable', change: 0 };
    }

    const recentAvg = recentValues.reduce((a, b) => a + b, 0) / recentValues.length;
    const olderAvg = olderValues.reduce((a, b) => a + b, 0) / olderValues.length;
    
    const change = olderAvg > 0 ? ((recentAvg - olderAvg) / olderAvg) * 100 : 0;
    
    let trend: 'improving' | 'declining' | 'stable' = 'stable';
    if (Math.abs(change) > 5) {
      trend = change > 0 ? 'improving' : 'declining';
    }

    return { trend, change: Math.round(change * 10) / 10 };
  }

  private generatePersonalityInsights(progressData: any[]): any[] {
    const insights: any[] = [];

    // Analyze productive times
    const productiveHours = progressData
      .filter(p => p.metrics?.productiveHours)
      .flatMap(p => p.metrics.productiveHours.map((value, hour) => ({ hour, value })))
      .reduce((acc, { hour, value }) => {
        acc[hour] = (acc[hour] || 0) + value;
        return acc;
      }, {} as { [hour: number]: number });

    const bestHour = Object.entries(productiveHours)
      .sort(([, a], [, b]) => (b as number) - (a as number))[0];

    if (bestHour) {
      const hour = parseInt(bestHour[0]);
      const timeLabel = hour < 12 ? 'morning' : hour < 17 ? 'afternoon' : 'evening';
      insights.push({
        insight: `You are most productive during the ${timeLabel} hours (around ${hour}:00)`,
        category: 'Productivity Pattern',
        confidence: 75,
      });
    }

    // Analyze mood patterns
    const moodEntries = progressData
      .filter(p => p.metrics?.moodTracking?.length > 0)
      .flatMap(p => p.metrics.moodTracking);

    if (moodEntries.length > 0) {
      const moodCounts = moodEntries.reduce((acc, entry) => {
        acc[entry.mood] = (acc[entry.mood] || 0) + 1;
        return acc;
      }, {} as { [mood: string]: number });

      const dominantMood = Object.entries(moodCounts)
        .sort(([, a], [, b]) => (b as number) - (a as number))[0][0];

      insights.push({
        insight: `Your most common mood state is "${dominantMood}", indicating ${this.getMoodInsight(dominantMood)}`,
        category: 'Emotional Pattern',
        confidence: 80,
      });
    }

    // Analyze consistency patterns
    const consistencyScores = progressData.map(p => p.metrics?.consistencyScore || 50);
    const avgConsistency = consistencyScores.reduce((a, b) => a + b, 0) / consistencyScores.length;

    if (avgConsistency > 75) {
      insights.push({
        insight: 'You have excellent consistency in your daily routines',
        category: 'Behavioral Pattern',
        confidence: 85,
      });
    } else if (avgConsistency < 50) {
      insights.push({
        insight: 'Your routine consistency could benefit from more structure and planning',
        category: 'Behavioral Pattern',
        confidence: 80,
      });
    }

    return insights;
  }

  private getMoodInsight(mood: string): string {
    const insights = {
      'great': 'high emotional well-being and life satisfaction',
      'good': 'positive emotional balance and contentment',
      'neutral': 'stable emotional state with room for more joy',
      'bad': 'challenges that may benefit from attention and support',
      'terrible': 'significant emotional distress that deserves care and attention',
    };
    return insights[mood] || 'a unique emotional experience';
  }

  private generateImprovementRecommendations(progressData: any[], metrics: any[]): any[] {
    const improvements: any[] = [];

    // Task completion improvement
    const taskMetric = metrics.find(m => m.name === 'Task Completion Rate');
    if (taskMetric && taskMetric.currentScore < 70) {
      improvements.push({
        area: 'Task Management',
        currentScore: taskMetric.currentScore,
        improvementPotential: Math.min(30, 85 - taskMetric.currentScore),
        actions: [
          {
            title: 'Implement Time Blocking',
            description: 'Schedule specific time blocks for different types of tasks to improve focus and completion rates',
            impact: 'high',
            difficulty: 'medium',
            timeToResults: 14,
            category: 'Productivity',
          },
          {
            title: 'Use the 2-Minute Rule',
            description: 'Complete any task that takes less than 2 minutes immediately instead of adding it to your list',
            impact: 'medium',
            difficulty: 'easy',
            timeToResults: 3,
            category: 'Productivity',
          },
        ],
        evidence: `Your task completion rate is ${taskMetric.currentScore}%, which is below optimal performance`,
      });
    }

    // Habit consistency improvement
    const habitMetric = metrics.find(m => m.name === 'Habit Consistency');
    if (habitMetric && habitMetric.currentScore < 75) {
      improvements.push({
        area: 'Habit Building',
        currentScore: habitMetric.currentScore,
        improvementPotential: Math.min(25, 90 - habitMetric.currentScore),
        actions: [
          {
            title: 'Start with Habit Stacking',
            description: 'Link new habits to existing ones. For example: "After I brush my teeth, I will do 10 push-ups"',
            impact: 'high',
            difficulty: 'easy',
            timeToResults: 21,
            category: 'Habits',
          },
          {
            title: 'Use Environmental Design',
            description: 'Make good habits obvious and bad habits invisible by designing your environment accordingly',
            impact: 'medium',
            difficulty: 'medium',
            timeToResults: 7,
            category: 'Habits',
          },
        ],
        evidence: `Your habit consistency is ${habitMetric.currentScore}%, indicating room for more reliable daily routines`,
      });
    }

    // Focus time improvement
    const focusMetric = metrics.find(m => m.name === 'Daily Focus Time');
    if (focusMetric && focusMetric.currentScore < 60) {
      improvements.push({
        area: 'Deep Work & Focus',
        currentScore: focusMetric.currentScore,
        improvementPotential: Math.min(35, 80 - focusMetric.currentScore),
        actions: [
          {
            title: 'Implement Pomodoro Technique',
            description: 'Work in 25-minute focused intervals followed by 5-minute breaks to maintain high concentration',
            impact: 'high',
            difficulty: 'easy',
            timeToResults: 7,
            category: 'Focus',
          },
          {
            title: 'Create a Distraction-Free Zone',
            description: 'Designate a specific area for focused work with minimal distractions and proper setup',
            impact: 'medium',
            difficulty: 'medium',
            timeToResults: 3,
            category: 'Focus',
          },
        ],
        evidence: `Your daily focus time indicates potential for deeper, more concentrated work sessions`,
      });
    }

    // Sort by impact potential
    return improvements.sort((a, b) => b.improvementPotential - a.improvementPotential);
  }

  private calculateOverallLifestyleScore(metrics: any[]): number {
    if (metrics.length === 0) return 50;

    const weights = {
      'Task Completion Rate': 0.25,
      'Habit Consistency': 0.35,
      'Daily Focus Time': 0.25,
      'Overall Consistency': 0.15,
    };

    const weightedScore = metrics.reduce((acc, metric) => {
      const weight = weights[metric.name] || 0;
      return acc + (metric.currentScore * weight);
    }, 0);

    return Math.round(weightedScore);
  }

  private analyzePerformanceAreas(metrics: any[]): { strengths: string[], criticalAreas: string[] } {
    const strengths: string[] = [];
    const criticalAreas: string[] = [];

    metrics.forEach(metric => {
      if (metric.currentScore >= 80) {
        strengths.push(metric.name);
      } else if (metric.currentScore < 50) {
        criticalAreas.push(metric.name);
      }
    });

    // Add default values if none found
    if (strengths.length === 0) {
      strengths.push('Willingness to improve');
    }
    if (criticalAreas.length === 0) {
      criticalAreas.push('Consistency in daily routines');
    }

    return { strengths, criticalAreas };
  }

  private generateMotivationalMessage(progressData: any[], overallScore: number): string {
    const messages = {
      excellent: [
        "Outstanding work! You're in the top tier of lifestyle management. Keep up this amazing momentum!",
        "Your consistency is truly impressive! You're setting a great example of what disciplined living looks like.",
        "Fantastic results! Your daily habits are clearly paying off in meaningful ways.",
      ],
      good: [
        "Great progress! You're building solid foundations for long-term success.",
        "You're doing well! A few small adjustments could take your results to the next level.",
        "Nice work! Your efforts are showing real results and positive trends.",
      ],
      average: [
        "You're on the right track! Every small step forward is building toward bigger changes.",
        "Good foundation! Focus on consistency in one or two key areas to accelerate your progress.",
        "Solid start! The key is maintaining momentum - you've got this!",
      ],
      needsWork: [
        "Every journey starts with a single step. You're building the foundation for positive change!",
        "Starting is the hardest part, and you've already begun. Focus on small, consistent wins.",
        "Remember, progress isn't about perfection. It's about moving forward, one day at a time.",
      ],
    };

    let category: keyof typeof messages;
    if (overallScore >= 80) category = 'excellent';
    else if (overallScore >= 65) category = 'good';
    else if (overallScore >= 50) category = 'average';
    else category = 'needsWork';

    const relevantMessages = messages[category];
    return relevantMessages[Math.floor(Math.random() * relevantMessages.length)];
  }

  private async gatherComprehensiveUserData(user: any) {
    try {
      // Fetch user's habits
      const userHabits = await this.habitsService.findAllByUser(user);
      const habitsData = userHabits.map(habit => ({
        name: habit.name,
        streak: habit.currentStreak,
        category: 'General', // Default category
        difficulty: 'medium', // Default difficulty
      }));

      // Fetch user's tasks
      const userTasks = await this.tasksService.findAll(user.id);
      const tasksData = userTasks.map(task => ({
        title: task.title,
        priority: task.priority,
        completed: task.completed,
        dueDate: task.dueDate,
        category: 'General', // Default category
      }));

      // Fetch user's skill plans
      const userSkillPlans = await this.skillPlansService.findAll({ creatorId: user.id });
      const skillPlansData = userSkillPlans.map(plan => ({
        name: plan.name,
        progress: plan.progress,
        category: plan.metadata?.category || 'General',
        difficulty: 'medium', // Default difficulty
      }));

      // Fetch user's challenges
      const allChallenges = await this.challengesService.findAll();
      const userChallenges = allChallenges.filter(challenge => 
        challenge.participants?.some(participant => participant.id === user.id)
      );
      const challengesData = userChallenges.map(challenge => ({
        name: challenge.name,
        progress: challenge.progress[user.id] || null,
        category: 'General', // Default category
        difficulty: 'medium', // Default difficulty
      }));

      return {
        habits: habitsData,
        tasks: tasksData,
        skillPlans: skillPlansData,
        challenges: challengesData,
        userProfile: {
          firstName: user.firstName,
          goals: user.goals || [],
          badges: user.badges || [],
          xp: user.xp || 0,
          level: user.level || 1,
        },
      };
    } catch (error) {
      this.logger.error('Error gathering comprehensive user data:', error);
      return {};
    }
  }
}
