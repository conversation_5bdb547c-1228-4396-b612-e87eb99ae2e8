import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserProgress } from './entities/user-progress.entity';
import { AnalyticsService } from './analytics.service';
import { AnalyticsController } from './analytics.controller';
import {
  ProgressTrackerService,
  AnalyticsCalculatorService,
  InsightsGeneratorService,
  ReportGeneratorService,
  AnalyticsTransformerService,
} from './services';
import { UsersModule } from '../users/users.module';
import { HabitsModule } from '../habits/habits.module';
import { TasksModule } from '../tasks/tasks.module';
import { SkillPlansModule } from '../skill-plans/skill-plans.module';
import { ChallengesModule } from '../challenges/challenges.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserProgress]),
    UsersModule,
    HabitsModule,
    TasksModule,
    forwardRef(() => SkillPlansModule),
    ChallengesModule,
  ],
  providers: [
    AnalyticsService,
    ProgressTrackerService,
    AnalyticsCalculatorService,
    InsightsGeneratorService,
    ReportGeneratorService,
    AnalyticsTransformerService,
  ],
  controllers: [AnalyticsController],
  exports: [
    AnalyticsService,
    ProgressTrackerService,
    AnalyticsCalculatorService,
    InsightsGeneratorService,
    ReportGeneratorService,
    AnalyticsTransformerService,
  ],
})
export class AnalyticsModule {}
