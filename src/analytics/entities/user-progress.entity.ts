import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';

@Entity('user_progress')
export class UserProgress {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User)
  user: User;

  @Column({ type: 'date' })
  date: Date;

  @Column({ type: 'jsonb' })
  habits: {
    completed: number;
    total: number;
    streaks: { [habitId: string]: number };
  };

  @Column({ type: 'jsonb' })
  tasks: {
    completed: number;
    total: number;
    overdue: number;
  };

  @Column({ type: 'jsonb' })
  skillPlans: {
    active: number;
    completedSteps: number;
    totalSteps: number;
  };

  @Column({ type: 'jsonb' })
  challenges: {
    active: number;
    completed: number;
  };

  @Column({ type: 'int' })
  xpGained: number;

  @Column({ type: 'int' })
  totalXp: number;

  @Column('simple-array')
  badgesEarned: string[];

  @Column({ type: 'jsonb' })
  metrics: {
    focusMinutes: number;
    podcastsListened: number;
    consistencyScore: number;
    productiveHours: number[];
    moodTracking: {
      mood: 'great' | 'good' | 'neutral' | 'bad' | 'terrible';
      timestamp: string;
    }[];
  };

  @Column({ type: 'jsonb', nullable: true })
  insights: {
    mostProductiveTime: string;
    strongestHabits: string[];
    areasForImprovement: string[];
    streakMilestones: { [habitId: string]: number };
    recommendedActions: string[];
  };

  @CreateDateColumn()
  createdAt: Date;
}
