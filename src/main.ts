import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { NestExpressApplication } from '@nestjs/platform-express';
import { RequestMethod } from '@nestjs/common';
import { ViewEngineService } from './common/view-engine';
import helmet from 'helmet';
import { createClient } from 'redis';
import RedisStore from 'connect-redis';
import session from 'express-session';
import { AdminModule } from './admin/admin.module';
import { HelpModule } from './help/help.module';
import { join } from 'path';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);
  
  // Configure Helmet for security headers
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: [
          "'self'", 
          "'unsafe-inline'", // Allow inline styles for admin dashboard
          "https://cdn.tailwindcss.com", // Allow Tailwind CSS CDN if used
          "https://fonts.googleapis.com", // Allow Google Fonts if used
          "https://unpkg.com" // Allow unpkg CDN for editor.js
        ],
        scriptSrc: [
          "'self'",
          "'unsafe-inline'", // Allow inline scripts for admin functionality
          "'unsafe-hashes'", // Allow inline event handlers
          "https://cdn.tailwindcss.com", // Allow Tailwind CSS CDN if used
          "https://unpkg.com" // Allow unpkg CDN for editor.js
        ],
        scriptSrcAttr: [
          "'unsafe-inline'", // Allow inline event handlers in attributes
          "'unsafe-hashes'" // Allow inline event handlers
        ],
        fontSrc: [
          "'self'",
          "https://fonts.gstatic.com" // Allow Google Fonts if used
        ],
        imgSrc: [
          "'self'", 
          "data:", // Allow data URLs for images
          "https:" // Allow HTTPS images
        ],
        connectSrc: [
          "'self'",
          "https://api.openai.com", // Allow OpenAI API connections
          "https://api.elevenlabs.io" // Allow ElevenLabs API connections
        ]
      },
    },
    crossOriginEmbedderPolicy: false, // Disable for better compatibility with external APIs
  }));
  
  // Initialize Redis client for session storage
  const redisClient = createClient({
    socket: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      connectTimeout: 5000,
      reconnectStrategy: (retries) => Math.min(retries * 50, 500),
    },
  });

  // Handle Redis connection events
  redisClient.on('error', (err) => {
    console.error('Redis Client Error:', err);
    // In development, log more details
    if (process.env.NODE_ENV === 'development') {
      console.error('Redis connection details:', {
        host: process.env.REDIS_HOST || 'localhost',
        port: process.env.REDIS_PORT || '6379'
      });
    }
  });

  redisClient.on('connect', () => {
    console.log('Redis Client Connected');
  });

  redisClient.on('ready', () => {
    console.log('Redis Client Ready');
  });

  redisClient.on('reconnecting', () => {
    console.log('Redis Client Reconnecting...');
  });

  // Connect to Redis with error handling
  try {
    await redisClient.connect();
    console.log('✅ Redis connection established successfully');
  } catch (error) {
    console.error('❌ Failed to connect to Redis:', error);
    console.error('Please ensure Redis is running on:', {
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || '6379'
    });
    
    // In production, we might want to exit the process
    if (process.env.NODE_ENV === 'production') {
      console.error('Redis is required in production. Exiting...');
      process.exit(1);
    } else {
      console.warn('⚠️  Redis connection failed in development mode. Sessions will use memory store.');
    }
  }

  // Initialize Redis store
  const redisStore = new RedisStore({
    client: redisClient,
    prefix: 'powerup:sess:',
  });
  
  // Configure session middleware with Redis store
  app.use(session({
    store: redisStore,
    secret: process.env.SESSION_SECRET || 'your-secret-key-change-in-production',
    resave: false,
    saveUninitialized: false,
    name: 'powerup.sid', // Custom session name
    cookie: {
      secure: process.env.NODE_ENV === 'production',
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      sameSite: 'lax', // Better CSRF protection
    },
  }));
  
  // Configure Handlebars view engines
  const viewEngineService = app.get(ViewEngineService);
  await viewEngineService.configureHandlebars(app);

  // Configure static file serving for uploads
  app.useStaticAssets(join(__dirname, '..', 'uploads'), {
    prefix: '/uploads/',
  });



  const config = new DocumentBuilder()
    .setTitle('Power Up API')
    .setDescription('The Power Up API description')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();