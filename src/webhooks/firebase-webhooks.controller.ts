import { <PERSON>, Post, Body, Logger } from '@nestjs/common';
import { FirebaseService } from '../firebase/firebase.service';
import { UsersService } from '../users/users.service';
import { Public } from '../auth/decorators/public.decorator';
import { ApiTags, ApiOperation, ApiBody } from '@nestjs/swagger';
import { FirebaseWebhookDto } from './dto/firebase-webhook.dto';

@ApiTags('webhooks')
@Controller('api/webhooks/firebase')
export class FirebaseWebhooksController {
  private readonly logger = new Logger(FirebaseWebhooksController.name);

  constructor(
    private readonly firebaseService: FirebaseService,
    private readonly usersService: UsersService,
  ) {}

  @Public()
  @Post('auth')
  @ApiOperation({ summary: 'Handle Firebase Authentication webhooks' })
  @ApiBody({
    description: 'Firebase Auth webhook payload',
    type: FirebaseWebhookDto,
    examples: {
      userCreated: {
        value: {
          event: {
            type: 'user.created',
            data: {
              uid: 'firebase-uid',
              email: '<EMAIL>',
              metadata: { createdAt: '2023-05-20T12:00:00.000Z' },
            },
          },
        },
      },
      userDeleted: {
        value: {
          event: {
            type: 'user.deleted',
            data: {
              uid: 'firebase-uid',
            },
          },
        },
      },
    },
  })
  async handleAuthWebhook(@Body() payload: FirebaseWebhookDto) {
    this.logger.log(`Received Firebase auth webhook: ${payload.event.type}`);
    
    try {
      switch (payload.event.type) {
        case 'user.created':
          await this.handleUserCreated(payload.event.data);
          break;
        case 'user.deleted':
          await this.handleUserDeleted(payload.event.data);
          break;
        default:
          this.logger.warn(`Unhandled Firebase auth webhook event type: ${payload.event.type}`);
      }
      
      return { success: true };
    } catch (error) {
      this.logger.error(`Error processing Firebase auth webhook: ${error.message}`, error.stack);
      return { success: false, error: error.message };
    }
  }

  private async handleUserCreated(data: FirebaseWebhookDto['event']['data']) {
    // Only process if we have an email (we need it to create a user in our system)
    if (!data.email) {
      this.logger.warn(`No email provided for user.created event: ${data.uid}`);
      return;
    }

    // Check if user already exists in our database by Firebase UID
    let user = await this.usersService.findByFirebaseUid(data.uid);
    
    if (!user) {
      // Check if user exists by email
      user = await this.usersService.findOneByEmail(data.email);
      
      if (user) {
        // Update existing user with Firebase UID
        this.logger.log(`Updating existing user with Firebase UID: ${user.email}`);
        await this.usersService.update(user.id, {
          firebaseUid: data.uid,
        });
      } else {
        // Create new user
        this.logger.log(`Creating new user from Firebase auth: ${data.email}`);
        await this.usersService.create({
          email: data.email,
          name: data.email.split('@')[0], // Default name from email
          firebaseUid: data.uid,
        });
      }
    }
  }

  private async handleUserDeleted(data: FirebaseWebhookDto['event']['data']) {
    // Find user by Firebase UID
    const user = await this.usersService.findByFirebaseUid(data.uid);
    
    if (!user) {
      this.logger.warn(`User not found for deletion: ${data.uid}`);
      return;
    }
    
    // Option 1: Delete the user
    // await this.usersService.remove(user.id);
    
    // Option 2: Mark the user as inactive (preferred approach to preserve data)
    this.logger.log(`Marking user as inactive after Firebase deletion: ${user.email}`);
    await this.usersService.update(user.id, {
      isActive: false,
      firebaseUid: '', // Remove the association with Firebase
    });
  }
}
