import { Test, TestingModule } from '@nestjs/testing';
import { FirebaseWebhooksController } from './firebase-webhooks.controller';
import { FirebaseService } from '../firebase/firebase.service';
import { UsersService } from '../users/users.service';
import { Logger } from '@nestjs/common';

describe('FirebaseWebhooksController', () => {
  let controller: FirebaseWebhooksController;
  let usersService: UsersService;

  // Mock services
  const mockFirebaseService = {
    verifyIdToken: jest.fn(),
  };

  const mockUsersService = {
    findByFirebaseUid: jest.fn(),
    findOneByEmail: jest.fn(),
    update: jest.fn(),
    create: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      controllers: [FirebaseWebhooksController],
      providers: [
        { provide: FirebaseService, useValue: mockFirebaseService },
        { provide: UsersService, useValue: mockUsersService },
        Logger,
      ],
    }).compile();

    controller = module.get<FirebaseWebhooksController>(FirebaseWebhooksController);
    usersService = module.get<UsersService>(UsersService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('handleAuthWebhook', () => {
    it('should handle user.created event', async () => {
      const payload = {
        event: {
          type: 'user.created',
          data: {
            uid: 'firebase-uid-123',
            email: '<EMAIL>',
            metadata: {
              createdAt: '2023-05-20T12:00:00.000Z',
            },
          },
        },
      };

      // User doesn't exist yet
      mockUsersService.findByFirebaseUid.mockResolvedValue(null);
      mockUsersService.findOneByEmail.mockResolvedValue(null);
      mockUsersService.create.mockResolvedValue({
        id: 'user-id-123',
        email: '<EMAIL>',
        firebaseUid: 'firebase-uid-123',
      });

      const result = await controller.handleAuthWebhook(payload);

      expect(result).toEqual({ success: true });
      expect(mockUsersService.findByFirebaseUid).toHaveBeenCalledWith('firebase-uid-123');
      expect(mockUsersService.findOneByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(mockUsersService.create).toHaveBeenCalledWith({
        email: '<EMAIL>',
        name: 'test',
        firebaseUid: 'firebase-uid-123',
      });
    });

    it('should update existing user on user.created event if they exist by email', async () => {
      const payload = {
        event: {
          type: 'user.created',
          data: {
            uid: 'firebase-uid-123',
            email: '<EMAIL>',
            metadata: {
              createdAt: '2023-05-20T12:00:00.000Z',
            },
          },
        },
      };

      // User exists by email but without Firebase UID
      mockUsersService.findByFirebaseUid.mockResolvedValue(null);
      mockUsersService.findOneByEmail.mockResolvedValue({
        id: 'user-id-123',
        email: '<EMAIL>',
        firebaseUid: null,
      });

      const result = await controller.handleAuthWebhook(payload);

      expect(result).toEqual({ success: true });
      expect(mockUsersService.findByFirebaseUid).toHaveBeenCalledWith('firebase-uid-123');
      expect(mockUsersService.findOneByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(mockUsersService.update).toHaveBeenCalledWith('user-id-123', {
        firebaseUid: 'firebase-uid-123',
      });
      expect(mockUsersService.create).not.toHaveBeenCalled();
    });

    it('should handle user.deleted event', async () => {
      const payload = {
        event: {
          type: 'user.deleted',
          data: {
            uid: 'firebase-uid-123',
          },
        },
      };

      // User exists
      mockUsersService.findByFirebaseUid.mockResolvedValue({
        id: 'user-id-123',
        email: '<EMAIL>',
        firebaseUid: 'firebase-uid-123',
      });

      const result = await controller.handleAuthWebhook(payload);

      expect(result).toEqual({ success: true });
      expect(mockUsersService.findByFirebaseUid).toHaveBeenCalledWith('firebase-uid-123');
      expect(mockUsersService.update).toHaveBeenCalledWith('user-id-123', {
        isActive: false,
        firebaseUid: '',
      });
    });

    it('should handle user.deleted event when user does not exist', async () => {
      const payload = {
        event: {
          type: 'user.deleted',
          data: {
            uid: 'firebase-uid-123',
          },
        },
      };

      // User doesn't exist
      mockUsersService.findByFirebaseUid.mockResolvedValue(null);

      const result = await controller.handleAuthWebhook(payload);

      expect(result).toEqual({ success: true });
      expect(mockUsersService.findByFirebaseUid).toHaveBeenCalledWith('firebase-uid-123');
      expect(mockUsersService.update).not.toHaveBeenCalled();
    });

    it('should handle unrecognized event types', async () => {
      const payload = {
        event: {
          type: 'user.unknown',
          data: {
            uid: 'firebase-uid-123',
          },
        },
      };

      const result = await controller.handleAuthWebhook(payload);

      expect(result).toEqual({ success: true });
      expect(mockUsersService.findByFirebaseUid).not.toHaveBeenCalled();
      expect(mockUsersService.update).not.toHaveBeenCalled();
      expect(mockUsersService.create).not.toHaveBeenCalled();
    });

    it('should handle errors and return error response', async () => {
      const payload = {
        event: {
          type: 'user.created',
          data: {
            uid: 'firebase-uid-123',
            email: '<EMAIL>',
          },
        },
      };

      // Simulate error
      const error = new Error('Database error');
      mockUsersService.findByFirebaseUid.mockRejectedValue(error);

      const result = await controller.handleAuthWebhook(payload);

      expect(result).toEqual({ 
        success: false, 
        error: 'Database error' 
      });
    });
  });
});
