import { ApiProperty } from '@nestjs/swagger';

export class FirebaseMetadataDto {
  @ApiProperty({
    description: 'Created at timestamp',
    example: '2023-05-20T12:00:00.000Z',
    required: false
  })
  createdAt?: string;
}

export class FirebaseEventDataDto {
  @ApiProperty({
    description: 'Firebase user ID',
    example: 'firebase-uid-123456'
  })
  uid: string;

  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
    required: false
  })
  email?: string;

  @ApiProperty({
    description: 'User metadata',
    required: false,
    type: FirebaseMetadataDto
  })
  metadata?: FirebaseMetadataDto;
}

class FirebaseEventDto {
  @ApiProperty({
    description: 'Event type',
    example: 'user.created',
    enum: ['user.created', 'user.deleted']
  })
  type: string;

  @ApiProperty({
    description: 'Event data',
    type: FirebaseEventDataDto
  })
  data: FirebaseEventDataDto;
}

export class FirebaseWebhookDto {
  @ApiProperty({
    description: 'Firebase event',
    type: FirebaseEventDto
  })
  event: FirebaseEventDto;
}
