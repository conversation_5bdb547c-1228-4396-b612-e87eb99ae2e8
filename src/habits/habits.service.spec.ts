import { Test, TestingModule } from '@nestjs/testing';
import { HabitsService } from './habits.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Habit } from './entities/habit.entity';
import { NotFoundException } from '@nestjs/common';
import { NotificationsService } from '../notifications/notifications.service';
import { User, AuthProvider } from '../users/entities/user.entity';

describe('HabitsService', () => {
  let service: HabitsService;
  let repository: Repository<Habit>;
  let notificationsService: NotificationsService;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    delete: jest.fn(),
    remove: jest.fn(),
  };

  const mockUser = {
    id: '1',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    password: 'hashedPassword',
    xp: 0,
    badges: [],
    firebaseUid: undefined,
    provider: AuthProvider.LOCAL,
    picture: undefined,
    habits: [],
    tasks: [],
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: undefined,
  } as User;

  const mockNotificationsService = {
    sendNotificationWithPreferences: jest.fn().mockResolvedValue(undefined),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HabitsService,
        {
          provide: getRepositoryToken(Habit),
          useValue: mockRepository,
        },
        {
          provide: NotificationsService,
          useValue: mockNotificationsService,
        },
      ],
    }).compile();

    service = module.get<HabitsService>(HabitsService);
    repository = module.get<Repository<Habit>>(getRepositoryToken(Habit));
    notificationsService = module.get<NotificationsService>(NotificationsService);

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new habit', async () => {
      const createHabitDto = {
        name: 'Morning Exercise',
        description: 'Do 30 minutes of exercise every morning',
        schedule: ['MONDAY', 'WEDNESDAY', 'FRIDAY'],
        reminderSettings: {
          enabled: true,
          time: '07:00',
          days: ['MONDAY', 'WEDNESDAY', 'FRIDAY']
        },
      };

      const habit = {
        id: '1',
        ...createHabitDto,
        currentStreak: 0,
        longestStreak: 0,
        completion: {},
        createdAt: new Date(),
        updatedAt: new Date(),
        user: mockUser,
      };

      mockRepository.create.mockReturnValue(habit);
      mockRepository.save.mockResolvedValue(habit);

      const result = await service.create(createHabitDto, mockUser);

      expect(result).toBeDefined();
      expect(result.id).toBe(habit.id);
      expect(result.name).toBe(createHabitDto.name);
      expect(result.currentStreak).toBe(0);
    });
  });

  describe('findAllByUser', () => {
    it('should return all habits for a user', async () => {
      const userId = '1';
      const habits = [
        {
          id: '1',
          name: 'Morning Exercise',
          currentStreak: 5,
          user: mockUser,
        },
        {
          id: '2',
          name: 'Reading',
          currentStreak: 3,
          user: mockUser,
        },
      ];

      mockRepository.find.mockResolvedValue(habits);

      const result = await service.findAllByUser(mockUser);

      expect(result).toBeDefined();
      expect(result).toHaveLength(2);
      expect(result[0].name).toBe('Morning Exercise');
    });
  });

  describe('findOne', () => {
    it('should return a habit if found', async () => {
      const habitId = '1';
      const habit = {
        id: habitId,
        name: 'Morning Exercise',
        user: mockUser,
      };

      mockRepository.findOne.mockResolvedValue(habit);

      const result = await service.findOne(habitId, mockUser);

      expect(result).toBeDefined();
      expect(result.id).toBe(habitId);
    });

    it('should throw NotFoundException if habit not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne('1', mockUser)).rejects.toThrow(NotFoundException);
    });
  });

  describe('complete', () => {
    it('should mark a habit as complete for today and update streak', async () => {
      const habitId = '1';
      const today = new Date().toISOString().split('T')[0];
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayStr = yesterday.toISOString().split('T')[0];
      
      const habit = {
        id: habitId,
        name: 'Morning Exercise',
        currentStreak: 6,
        longestStreak: 6,
        completion: { [yesterdayStr]: true }, // Make sure yesterday was completed
        user: mockUser,
      };

      const updatedHabit = {
        ...habit,
        currentStreak: 7,
        longestStreak: 7,
        completion: { [yesterdayStr]: true, [today]: true },
        lastCompletedAt: new Date(),
      };

      mockRepository.findOne.mockResolvedValue(habit);
      mockRepository.save.mockResolvedValue(updatedHabit);
      mockNotificationsService.sendNotificationWithPreferences.mockResolvedValueOnce(undefined);

      const result = await service.complete(habitId, mockUser);

      expect(result).toBeDefined();
      expect(result.currentStreak).toBe(7);
      expect(result.completion[today]).toBe(true);
      expect(mockNotificationsService.sendNotificationWithPreferences).toHaveBeenCalledWith(
        mockUser.id,
        'habit_streak',
        expect.objectContaining({
          title: 'Streak Achievement!',
          body: expect.stringContaining('7-day streak'),
          type: 'habit_streak',
          data: expect.objectContaining({
            habitId: habitId,
            habitName: habit.name,
            streak: '7'
          })
        })
      );
    });

    it('should handle marking already completed habit', async () => {
      const habitId = '1';
      const today = new Date().toISOString().split('T')[0];
      
      const habit = {
        id: habitId,
        name: 'Morning Exercise',
        currentStreak: 5,
        longestStreak: 5,
        completion: { [today]: true },
        user: mockUser,
      };

      mockRepository.findOne.mockResolvedValue(habit);

      await expect(service.complete(habitId, mockUser))
        .rejects
        .toThrow('Habit already completed for today');
    });
  });

  describe('remove', () => {
    it('should delete a habit', async () => {
      const habitId = '1';
      const habit = {
        id: habitId,
        name: 'Morning Exercise',
        user: mockUser,
      };

      mockRepository.findOne.mockResolvedValue(habit);
      mockRepository.remove.mockResolvedValue(habit);

      await service.remove(habitId, mockUser);

      expect(mockRepository.remove).toHaveBeenCalledWith(habit);
    });

    it('should throw NotFoundException if habit not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.remove('1', mockUser)).rejects.toThrow(NotFoundException);
    });
  });
});
