import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Habit } from './entities/habit.entity';
import { User } from '../users/entities/user.entity';
import { NotificationsService } from '../notifications/notifications.service';
import { NotificationType } from '../notifications/types/notification-types';
import { Cron, CronExpression } from '@nestjs/schedule';

@Injectable()
export class HabitsService {
  constructor(
    @InjectRepository(Habit)
    private habitsRepository: Repository<Habit>,
    private notificationsService: NotificationsService,
  ) {}

  async create(createHabitDto: {
    name: string;
    description?: string;
    schedule: string[];
    reminderSettings?: {
      enabled: boolean;
      time: string;
      days: string[];
    }
  }, user: User): Promise<Habit> {
    if (!createHabitDto.name) {
      throw new BadRequestException('Name are required');
    }

    const habit = this.habitsRepository.create({
      ...createHabitDto,
      user,
      currentStreak: 0,
      longestStreak: 0,
      completion: {},
      xpReward: 10,
    });

    const savedHabit = await this.habitsRepository.save(habit);

    // Schedule reminder if enabled
    if (savedHabit.reminderSettings?.enabled) {
      await this.scheduleReminder(savedHabit);
    }

    return savedHabit;
  }

  async findAllByUser(user: User): Promise<Habit[]> {
    return this.habitsRepository.find({
      where: { user: { id: user.id } },
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string, user: User): Promise<Habit> {
    const habit = await this.habitsRepository.findOne({
      where: { id, user: { id: user.id } },
      relations: ['user'],
    });

    if (!habit) {
      throw new NotFoundException(`Habit with ID ${id} not found`);
    }

    return habit;
  }

  async update(id: string, updateHabitDto: {
    name?: string;
    description?: string;
    schedule?: string[];
    reminderSettings?: {
      enabled: boolean;
      time: string;
      days: string[];
    }
  }, user: User): Promise<Habit> {
    const habit = await this.findOne(id, user);
    const updatedHabit = Object.assign(habit, updateHabitDto);
    const savedHabit = await this.habitsRepository.save(updatedHabit);

    // If reminder settings were updated, reschedule
    if (updateHabitDto.reminderSettings) {
      await this.scheduleReminder(savedHabit);
    }

    return savedHabit;
  }

  async complete(id: string, user: User): Promise<Habit> {
    const habit = await this.findOne(id, user);
    const today = new Date().toISOString().split('T')[0];
    
    if (habit.completion[today]) {
      throw new BadRequestException('Habit already completed for today');
    }
    
    // Update completion status
    habit.completion = {
      ...habit.completion,
      [today]: true
    };
    
    habit.lastCompletedAt = new Date();
    
    // Update streak
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toISOString().split('T')[0];
    
    let streakUpdated = false;
    if (habit.completion[yesterdayStr]) {
      habit.currentStreak += 1;
      streakUpdated = true;
      if (habit.currentStreak > habit.longestStreak) {
        habit.longestStreak = habit.currentStreak;
      }
    } else {
      habit.currentStreak = 1;
    }
    
    // Save the habit first to ensure the update is successful
    const updatedHabit = await this.habitsRepository.save(habit);

    // Send notifications after successful save
    if (streakUpdated && habit.currentStreak > 0) {
      // Send streak notification at 3, 7, 14, 30, 60, 90, 180, 365 days etc
      if ([3, 7, 14, 30, 60, 90, 180, 365].includes(habit.currentStreak)) {
        await this.notificationsService.sendNotificationWithPreferences(
          user.id,
          'habit_streak' as NotificationType,
          {
            title: 'Streak Achievement!',
            body: `Amazing! You've maintained a ${habit.currentStreak}-day streak for "${habit.name}"! 🔥`,
            type: 'habit_streak' as NotificationType,
            data: {
              habitId: habit.id,
              habitName: habit.name,
              streak: habit.currentStreak.toString()
            }
          }
        );
      }

      // Send milestone notification for significant streaks
      if (habit.currentStreak >= 100 && habit.currentStreak % 100 === 0) {
        await this.notificationsService.sendNotificationWithPreferences(
          user.id,
          'habit_milestone' as NotificationType,
          {
            title: 'Milestone Achievement! 🏆',
            body: `Incredible! You've reached a ${habit.currentStreak}-day streak for "${habit.name}"! This is a major milestone!`,
            type: 'habit_milestone' as NotificationType,
            data: {
              habitId: habit.id,
              habitName: habit.name,
              milestone: `${habit.currentStreak}-day streak`
            }
          }
        );
      }
    }

    return updatedHabit;
  }

  async remove(id: string, user: User): Promise<void> {
    const habit = await this.findOne(id, user);
    await this.habitsRepository.remove(habit);
  }

  private async scheduleReminder(habit: Habit): Promise<void> {
    if (!habit.reminderSettings?.enabled) {
      return;
    }

    const now = new Date();
    const [hours, minutes] = habit.reminderSettings.time.split(':').map(Number);
    const reminderTime = new Date(now);
    reminderTime.setHours(hours, minutes, 0, 0);

    // If time for today has passed, don't send reminder
    if (reminderTime < now) {
      return;
    }

    // Get day of week
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const today = days[now.getDay()];

    // Check if reminder should be sent today
    if (!habit.reminderSettings.days.includes(today)) {
      return;
    }

    // Check if habit is already completed for today
    const todayStr = now.toISOString().split('T')[0];
    if (habit.completion[todayStr]) {
      return;
    }

    // Calculate delay until reminder time
    const delay = reminderTime.getTime() - now.getTime();
    
    // Schedule the reminder
    setTimeout(async () => {
      try {
        await this.notificationsService.sendNotificationWithPreferences(
          habit.user.id,
          'habit_reminder' as NotificationType,
          {
            title: 'Habit Reminder',
            body: `Time to work on your habit: "${habit.name}"`,
            type: 'habit_reminder' as NotificationType,
            data: {
              habitId: habit.id,
              habitName: habit.name
            }
          }
        );
      } catch (error) {
        console.error(`Failed to send habit reminder for habit ${habit.id}:`, error);
      }
    }, delay);
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async scheduleHabitReminders() {
    try {
      // Query for all habits that have reminders enabled
      const habits = await this.habitsRepository
        .createQueryBuilder('habit')
        .leftJoinAndSelect('habit.user', 'user')
        .where("habit.reminderSettings->>'enabled' = :enabled", { enabled: 'true' })
        .getMany();

      for (const habit of habits) {
        await this.scheduleReminder(habit);
      }
    } catch (error) {
      console.error('Failed to schedule habit reminders:', error);
    }
  }
}
