import { Controller, Get, Post, Put, Body, Param, UseGuards, Request, HttpCode } from '@nestjs/common';
import { HabitsService } from './habits.service';
import { UsersService } from '../users/users.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiCreatedResponse, ApiOkResponse, ApiBody } from '@nestjs/swagger';
import { CreateHabitDto } from './dto/create-habit.dto';
import { UpdateHabitDto } from './dto/update-habit.dto';
import { HabitResponseDto } from './dto/habit-response.dto';

@ApiTags('habits')
@Controller('api/habits')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class HabitsController {
  constructor(
    private readonly habitsService: HabitsService,
    private readonly usersService: UsersService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new habit' })
  @ApiCreatedResponse({ description: 'The habit has been successfully created.', type: HabitResponseDto })
  @ApiBody({ type: CreateHabitDto })
  async create(@Request() req, @Body() createHabitDto: CreateHabitDto) {
    const user = await this.usersService.findOne(req.user.id);
    return this.habitsService.create(createHabitDto, user);
  }

  @Get()
  @ApiOperation({ summary: 'Get all habits' })
  @ApiOkResponse({ description: 'Returns all habits for the user', type: [HabitResponseDto] })
  async findAll(@Request() req) {
    const user = await this.usersService.findOne(req.user.id);
    return this.habitsService.findAllByUser(user);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific habit' })
  @ApiOkResponse({ description: 'Returns a specific habit', type: HabitResponseDto })
  async findOne(@Request() req, @Param('id') id: string) {
    const user = await this.usersService.findOne(req.user.id);
    return this.habitsService.findOne(id, user);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a habit' })
  @ApiOkResponse({ description: 'Returns the updated habit', type: HabitResponseDto })
  @ApiBody({ type: UpdateHabitDto })
  async update(@Request() req, @Param('id') id: string, @Body() updateHabitDto: UpdateHabitDto) {
    const user = await this.usersService.findOne(req.user.id);
    return this.habitsService.update(id, updateHabitDto, user);
  }

  @Post(':id/complete')
  @ApiOperation({ summary: 'Mark habit as completed' })
  @ApiOkResponse({ description: 'Returns the completed habit', type: HabitResponseDto })
  @HttpCode(200)
  async complete(@Request() req, @Param('id') id: string) {
    const user = await this.usersService.findOne(req.user.id);
    return this.habitsService.complete(id, user);
  }
}
