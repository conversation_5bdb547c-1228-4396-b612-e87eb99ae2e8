import { <PERSON>tity, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';

@Entity('habits')
export class Habit {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column('simple-array', { nullable: true })
  schedule: string[];

  @Column({ default: 0 })
  currentStreak: number;

  @Column({ default: 0 })
  longestStreak: number;

  @Column({ type: 'jsonb', default: {} })
  completion: { [date: string]: boolean };

  @Column({ type: 'jsonb', nullable: true })
  reminderSettings: {
    enabled: boolean;
    time: string;
    days: string[];
  };

  @Column({ default: 10 })
  xpReward: number;

  @ManyToOne(() => User, user => user.habits)
  user: User;

  @Column({ type: 'date', nullable: true })
  lastCompletedAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
