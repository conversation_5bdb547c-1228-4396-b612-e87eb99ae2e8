import { IsString, IsNotEmpty, Is<PERSON>rray, ValidateNested, IsBoolean, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

class ReminderSettingsDto {
  @ApiProperty({
    description: 'Whether reminders are enabled for this habit',
    example: true,
    required: true
  })
  @IsBoolean()
  enabled: boolean;

  @ApiProperty({
    description: 'Time of day for the reminder in HH:MM format',
    example: '08:00',
    required: true
  })
  @IsString()
  time: string;

  @ApiProperty({
    description: 'Days of the week for the reminder',
    example: ['Monday', 'Wednesday', 'Friday'],
    required: true,
    type: [String]
  })
  @IsArray()
  @IsString({ each: true })
  days: string[];
}

export class CreateHabitDto {
  @ApiProperty({
    description: 'Name of the habit',
    example: 'Morning Meditation',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Description of the habit',
    example: 'A 10-minute meditation practice every morning to start the day focused',
    required: false
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Days or frequency of the habit',
    example: ['daily', 'weekdays', 'Monday', 'Wednesday', 'Friday'],
    required: true,
    type: [String]
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  schedule: string[];

  @ApiProperty({
    description: 'Reminder settings for the habit',
    type: ReminderSettingsDto,
    required: false
  })
  @ValidateNested()
  @Type(() => ReminderSettingsDto)
  @IsOptional()
  reminderSettings?: ReminderSettingsDto;
}
