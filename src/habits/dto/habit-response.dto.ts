import { ApiProperty } from '@nestjs/swagger';
import { CreateHabitDto } from './create-habit.dto';

class ReminderSettingsResponseDto {
  @ApiProperty({
    description: 'Whether reminder is enabled',
    example: true
  })
  enabled: boolean;

  @ApiProperty({
    description: 'Time for the reminder',
    example: '08:00'
  })
  time: string;

  @ApiProperty({
    description: 'Days for the reminder',
    example: ['Monday', 'Wednesday', 'Friday']
  })
  days: string[];
}

export class HabitResponseDto {
  @ApiProperty({
    description: 'Unique habit ID',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  id: string;

  @ApiProperty({
    description: 'Habit name',
    example: 'Morning Meditation'
  })
  name: string;

  @ApiProperty({
    description: 'Habit description',
    example: 'A 10-minute mindfulness practice',
    nullable: true
  })
  description?: string;

  @ApiProperty({
    description: 'Schedule (days of week)',
    example: ['Monday', 'Wednesday', 'Friday']
  })
  schedule: string[];

  @ApiProperty({
    description: 'Current streak count',
    example: 5
  })
  currentStreak: number;

  @ApiProperty({
    description: 'Longest streak achieved',
    example: 12
  })
  longestStreak: number;

  @ApiProperty({
    description: 'Completion record by date',
    example: { '2025-06-01': true, '2025-06-02': false }
  })
  completion: { [date: string]: boolean };

  @ApiProperty({
    description: 'Experience points reward for completing habit',
    example: 10
  })
  xpReward: number;

  @ApiProperty({
    description: 'Date when habit was last completed',
    example: '2025-06-01',
    nullable: true
  })
  lastCompletedAt?: Date;

  @ApiProperty({
    description: 'Habit creation date',
    example: '2025-05-19T10:00:00Z'
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Habit last update date',
    example: '2025-05-19T10:00:00Z'
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Reminder settings',
    type: ReminderSettingsResponseDto,
    nullable: true
  })
  reminderSettings?: ReminderSettingsResponseDto;
}
