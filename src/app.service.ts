import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getHealth(): { status: string; timestamp: string; uptime: number } {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    };
  }

  getStatus(): { status: string; version: string; features: string[] } {
    return {
      status: 'operational',
      version: '1.0.0',
      features: [
        'AI-Powered Coaching',
        'Personalized Podcasts',
        'Habit Tracking',
        'Task Management',
        'Community Challenges',
        'Skill Development Plans',
        'Analytics & Insights'
      ]
    };
  }
}
