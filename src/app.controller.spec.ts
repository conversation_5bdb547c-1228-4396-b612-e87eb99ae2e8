import { Test, TestingModule } from '@nestjs/testing';
import { AppController } from './app.controller';
import { AppService } from './app.service';

describe('AppController', () => {
  let appController: AppController;

  beforeEach(async () => {
    const app: TestingModule = await Test.createTestingModule({
      controllers: [AppController],
      providers: [AppService],
    }).compile();

    appController = app.get<AppController>(AppController);
  });

  describe('health', () => {
    it('should return health status with timestamp and uptime', () => {
      const result = appController.getHealth();
      expect(result).toHaveProperty('status', 'ok');
      expect(result).toHaveProperty('timestamp');
      expect(result).toHaveProperty('uptime');
    });
  });

  describe('status', () => {
    it('should return API status information', () => {
      const result = appController.getStatus();
      expect(result).toHaveProperty('status', 'operational');
      expect(result).toHaveProperty('version', '1.0.0');
      expect(result).toHaveProperty('features');
      expect(Array.isArray(result.features)).toBe(true);
    });
  });
});
