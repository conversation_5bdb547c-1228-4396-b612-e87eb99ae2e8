import { Injectable, UnauthorizedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdminUser } from './entities/admin-user.entity';
import * as bcrypt from 'bcrypt';

@Injectable()
export class AdminAuthService {
  constructor(
    @InjectRepository(AdminUser)
    private adminUserRepository: Repository<AdminUser>,
  ) {}

  async validateAdmin(email: string, password: string): Promise<AdminUser | null> {
    const admin = await this.adminUserRepository.findOne({
      where: { email, isActive: true },
    });

    if (admin && await bcrypt.compare(password, admin.password)) {
      // Update last login
      await this.adminUserRepository.update(admin.id, {
        lastLoginAt: new Date(),
      });
      return admin;
    }

    return null;
  }

  async createDefaultAdmin(): Promise<void> {
    const existingAdmin = await this.adminUserRepository.findOne({
      where: { email: '<EMAIL>' },
    });

    if (!existingAdmin) {
      const hashedPassword = await bcrypt.hash('AhmSal1996;', 10);
      
      const admin = this.adminUserRepository.create({
        email: '<EMAIL>',
        firstName: 'Ahmed',
        lastName: 'Salah',
        password: hashedPassword,
        isActive: true,
      });

      await this.adminUserRepository.save(admin);
    }
  }

  async findById(id: string): Promise<AdminUser | null> {
    return this.adminUserRepository.findOne({
      where: { id, isActive: true },
    });
  }
}
