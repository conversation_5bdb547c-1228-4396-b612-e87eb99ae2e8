import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, Request } from '@nestjs/common';
import { AdminService } from './admin.service';
import { AdminAuthGuard } from './guards/admin-auth.guard';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiQuery, ApiCreatedResponse, ApiOkResponse, ApiBody } from '@nestjs/swagger';
import { CreateHelpArticleDto, UpdateHelpArticleDto, HelpArticleResponseDto } from '../help/dto';
import { SearchBlogPostDto, CreateBlogPostDto, UpdateBlogPostDto } from '../blog/dto';

@ApiTags('admin')
@Controller('api/admin')
@UseGuards(AdminAuthGuard)
@ApiBearerAuth()
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  // Dashboard
  @Get('dashboard/stats')
  @ApiOperation({ summary: 'Get dashboard statistics' })
  @ApiOkResponse({ description: 'Returns dashboard statistics' })
  async getDashboardStats() {
    return this.adminService.getDashboardStats();
  }

  // Landing Page Content Management
  @Get('landing-page')
  @ApiOperation({ summary: 'Get all landing page content sections' })
  @ApiQuery({ name: 'section', required: false, description: 'Specific section to retrieve' })
  @ApiOkResponse({ description: 'Returns landing page content' })
  async getLandingPageContent(@Query('section') section?: string) {
    return this.adminService.getLandingPageContent(section);
  }

  @Put('landing-page/:section')
  @ApiOperation({ summary: 'Update landing page content for a specific section' })
  @ApiOkResponse({ description: 'Landing page content updated successfully' })
  async updateLandingPageContent(
    @Param('section') section: string,
    @Body() content: any,
  ) {
    return this.adminService.updateLandingPageContent(section, content);
  }

  @Put('landing-page')
  @ApiOperation({ summary: 'Update all landing page content at once' })
  @ApiOkResponse({ description: 'Landing page content updated successfully' })
  async updateAllLandingPageContent(@Body() data: any) {
    return this.adminService.updateAllLandingPageContent(data);
  }

  // App Constants Management
  @Get('app-constants')
  @ApiOperation({ summary: 'Get app constants' })
  @ApiOkResponse({ description: 'Returns app constants' })
  async getAppConstants() {
    return this.adminService.getAppConstants();
  }

  @Put('app-constants')
  @ApiOperation({ summary: 'Update app constants' })
  @ApiOkResponse({ description: 'App constants updated successfully' })
  async updateAppConstants(@Body() data: any) {
    return this.adminService.updateAppConstants(data);
  }

  // Help Module Management
  @Get('help')
  @ApiOperation({ summary: 'Get all help articles' })
  @ApiOkResponse({ description: 'Returns all help articles', type: [HelpArticleResponseDto] })
  async getHelpArticles() {
    return this.adminService.getHelpArticles();
  }

  @Get('help/:id')
  @ApiOperation({ summary: 'Get help article by ID' })
  @ApiOkResponse({ description: 'Returns help article', type: HelpArticleResponseDto })
  async getHelpArticle(@Param('id') id: string) {
    return this.adminService.getHelpArticle(id);
  }

  @Post('help')
  @ApiOperation({ summary: 'Create new help article' })
  @ApiCreatedResponse({ description: 'Help article created successfully', type: HelpArticleResponseDto })
  @ApiBody({ type: CreateHelpArticleDto })
  async createHelpArticle(@Body() data: CreateHelpArticleDto) {
    return this.adminService.createHelpArticle(data);
  }

  @Put('help/:id')
  @ApiOperation({ summary: 'Update help article' })
  @ApiOkResponse({ description: 'Help article updated successfully', type: HelpArticleResponseDto })
  @ApiBody({ type: UpdateHelpArticleDto })
  async updateHelpArticle(@Param('id') id: string, @Body() data: UpdateHelpArticleDto) {
    return this.adminService.updateHelpArticle(id, data);
  }

  @Delete('help/:id')
  @ApiOperation({ summary: 'Delete help article' })
  @ApiOkResponse({ description: 'Help article deleted successfully' })
  async deleteHelpArticle(@Param('id') id: string) {
    return this.adminService.deleteHelpArticle(id);
  }

  // Community Data Management
  @Get('community')
  @ApiOperation({ summary: 'Get community data overview' })
  @ApiOkResponse({ description: 'Returns community data and statistics' })
  async getCommunityData() {
    return this.adminService.getCommunityData();
  }

  @Get('community/challenges')
  @ApiOperation({ summary: 'Get all challenges' })
  @ApiOkResponse({ description: 'Returns all challenges' })
  async getChallenges() {
    return this.adminService.getChallenges();
  }

  @Get('community/challenges/:id')
  @ApiOperation({ summary: 'Get challenge by ID' })
  @ApiOkResponse({ description: 'Returns challenge details' })
  async getChallenge(@Param('id') id: string) {
    return this.adminService.getChallenge(id);
  }

  @Put('community/challenges/:id')
  @ApiOperation({ summary: 'Update challenge' })
  @ApiOkResponse({ description: 'Challenge updated successfully' })
  async updateChallenge(@Param('id') id: string, @Body() data: any) {
    return this.adminService.updateChallenge(id, data);
  }

  @Delete('community/challenges/:id')
  @ApiOperation({ summary: 'Delete challenge' })
  @ApiOkResponse({ description: 'Challenge deleted successfully' })
  async deleteChallenge(@Param('id') id: string) {
    return this.adminService.deleteChallenge(id);
  }

  // Blog Management
  @Get('blog/stats')
  @ApiOperation({ summary: 'Get blog statistics' })
  @ApiOkResponse({ description: 'Returns blog statistics' })
  async getBlogStats() {
    return this.adminService.getBlogStats();
  }

  @Get('blog')
  @ApiOperation({ summary: 'Get all blog posts' })
  @ApiOkResponse({ description: 'Returns all blog posts' })
  async getAllBlogPosts(@Query() searchDto: SearchBlogPostDto) {
    return this.adminService.getAllBlogPosts(searchDto);
  }

  @Get('blog/:id')
  @ApiOperation({ summary: 'Get blog post by ID' })
  @ApiOkResponse({ description: 'Returns blog post' })
  async getBlogPost(@Param('id') id: string) {
    return this.adminService.getBlogPost(id);
  }

  @Post('blog')
  @ApiOperation({ summary: 'Create new blog post' })
  @ApiCreatedResponse({ description: 'Blog post created successfully' })
  async createBlogPost(@Body() data: CreateBlogPostDto, @Request() req) {
    return this.adminService.createBlogPost(data, req.user.id);
  }

  @Put('blog/:id')
  @ApiOperation({ summary: 'Update blog post' })
  @ApiOkResponse({ description: 'Blog post updated successfully' })
  async updateBlogPost(@Param('id') id: string, @Body() data: UpdateBlogPostDto) {
    return this.adminService.updateBlogPost(id, data);
  }

  @Delete('blog/:id')
  @ApiOperation({ summary: 'Delete blog post' })
  @ApiOkResponse({ description: 'Blog post deleted successfully' })
  async deleteBlogPost(@Param('id') id: string) {
    return this.adminService.deleteBlogPost(id);
  }
}
