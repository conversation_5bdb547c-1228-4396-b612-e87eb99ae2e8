import { 
  Controller, 
  Post, 
  UseInterceptors, 
  UploadedFile, 
  Body, 
  HttpException, 
  HttpStatus,
  UseGuards
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { AdminSessionGuard } from './guards/admin-session.guard';
import { diskStorage } from 'multer';
import { extname, join } from 'path';
import { v4 as uuid } from 'uuid';
import * as fs from 'fs';

const storage = diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = join(process.cwd(), 'uploads', 'images');
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const filename = `${uuid()}${extname(file.originalname)}`;
    cb(null, filename);
  },
});

@Controller('admin/api/upload')
@UseGuards(AdminSessionGuard)
export class UploadController {
  
  @Post('image')
  @UseInterceptors(FileInterceptor('image', { 
    storage,
    fileFilter: (req, file, cb) => {
      if (!file.mimetype.match(/\/(jpg|jpeg|png|gif|webp)$/)) {
        return cb(new HttpException('Only image files are allowed!', HttpStatus.BAD_REQUEST), false);
      }
      cb(null, true);
    },
    limits: {
      fileSize: 5 * 1024 * 1024, // 5MB limit
    },
  }))
  async uploadImage(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new HttpException('No file uploaded', HttpStatus.BAD_REQUEST);
    }

    const imageUrl = `/uploads/images/${file.filename}`;
    
    return {
      success: 1,
      file: {
        url: imageUrl,
        name: file.originalname,
        size: file.size
      }
    };
  }

  @Post('image-by-url')
  async uploadImageByUrl(@Body() body: { url: string }) {
    if (!body.url) {
      throw new HttpException('URL is required', HttpStatus.BAD_REQUEST);
    }

    try {
      // For now, just return the URL as-is
      // In a production app, you might want to download and re-host the image
      return {
        success: 1,
        file: {
          url: body.url
        }
      };
    } catch (error) {
      throw new HttpException('Failed to process image URL', HttpStatus.BAD_REQUEST);
    }
  }
}
