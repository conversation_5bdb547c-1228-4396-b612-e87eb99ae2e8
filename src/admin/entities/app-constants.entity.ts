import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('app_constants')
export class AppConstants {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 20 })
  appVersion: string;

  @Column({ type: 'integer' })
  buildNumber: number;

  @Column({ length: 20 })
  minSupportedVersion: string;

  @Column({ length: 20, nullable: true })
  forceUpdateVersion?: string;

  @Column({ length: 100 })
  appName: string;

  @Column({ type: 'text', nullable: true })
  appDescription?: string;

  @Column({ length: 500, nullable: true })
  playStoreUrl?: string;

  @Column({ length: 500, nullable: true })
  appStoreUrl?: string;

  @Column({ length: 500 })
  apiBaseUrl: string;

  @Column({ length: 10 })
  apiVersion: string;

  @Column({ type: 'integer', default: 30000 })
  requestTimeout: number;

  @Column({ type: 'integer', default: 3 })
  maxRetries: number;

  @Column({ default: true })
  enablePushNotifications: boolean;

  @Column({ default: true })
  enableAnalytics: boolean;

  @Column({ default: true })
  enableCrashReporting: boolean;

  @Column({ default: false })
  enableBetaFeatures: boolean;

  @Column({ default: true })
  enableOfflineMode: boolean;

  @Column({ default: true })
  enableDarkMode: boolean;

  @Column({ length: 255, nullable: true })
  supportEmail?: string;

  @Column({ length: 500, nullable: true })
  websiteUrl?: string;

  @Column({ length: 500, nullable: true })
  privacyPolicyUrl?: string;

  @Column({ length: 500, nullable: true })
  termsOfServiceUrl?: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
