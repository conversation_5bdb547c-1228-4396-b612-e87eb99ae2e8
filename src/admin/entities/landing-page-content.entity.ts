import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('landing_page_content')
export class LandingPageContent {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 200, nullable: true })
  heroTitle: string;

  @Column({ type: 'text', nullable: true })
  heroSubtitle: string;

  @Column({ length: 500, nullable: true })
  heroImage: string;

  @Column({ length: 100, nullable: true })
  ctaText: string;

  @Column({ length: 500, nullable: true })
  ctaLink: string;

  @Column({ length: 100, nullable: true })
  feature1Title: string;

  @Column({ type: 'text', nullable: true })
  feature1Description: string;

  @Column({ length: 100, nullable: true })
  feature2Title: string;

  @Column({ type: 'text', nullable: true })
  feature2Description: string;

  @Column({ length: 100, nullable: true })
  feature3Title: string;

  @Column({ type: 'text', nullable: true })
  feature3Description: string;

  @Column({ length: 200, nullable: true })
  aboutTitle: string;

  @Column({ type: 'text', nullable: true })
  aboutContent: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
