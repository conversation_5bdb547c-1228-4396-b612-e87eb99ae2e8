import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdminUser } from './entities/admin-user.entity';
import { LandingPageContent } from './entities/landing-page-content.entity';
import { AppConstants } from './entities/app-constants.entity';
import { HelpService } from '../help/help.service';
import { ChallengesService } from '../challenges/challenges.service';
import { AdminAuthService } from './admin-auth.service';
import { BlogService } from '../blog/blog.service';
import { CreateBlogPostDto, UpdateBlogPostDto, SearchBlogPostDto } from '../blog/dto';

@Injectable()
export class AdminService {
  constructor(
    @InjectRepository(AdminUser)
    private readonly adminUserRepository: Repository<AdminUser>,
    @InjectRepository(LandingPageContent)
    private readonly landingPageRepository: Repository<LandingPageContent>,
    @InjectRepository(AppConstants)
    private readonly appConstantsRepository: Repository<AppConstants>,
    private readonly helpService: HelpService,
    private readonly challengesService: ChallengesService,
    private readonly adminAuthService: AdminAuthService,
    private readonly blogService: BlogService,
  ) {}

  async onApplicationBootstrap() {
    await this.adminAuthService.createDefaultAdmin();
    await this.createDefaultContent();
  }

  // Landing Page Content Management
  async getLandingPageContent(section?: string) {
    const content = await this.landingPageRepository.findOne({
      where: {},
    });
    
    if (!content) {
      return null;
    }

    if (section) {
      // Return specific section content
      switch (section) {
        case 'hero':
          return {
            title: content.heroTitle,
            subtitle: content.heroSubtitle,
            image: content.heroImage,
            ctaText: content.ctaText,
            ctaLink: content.ctaLink,
          };
        case 'features':
          return {
            feature1: {
              title: content.feature1Title,
              description: content.feature1Description,
            },
            feature2: {
              title: content.feature2Title,
              description: content.feature2Description,
            },
            feature3: {
              title: content.feature3Title,
              description: content.feature3Description,
            },
          };
        case 'about':
          return {
            title: content.aboutTitle,
            content: content.aboutContent,
          };
        default:
          return null;
      }
    }
    
    return content;
  }

  async updateLandingPageContent(section: string, data: any) {
    let content = await this.landingPageRepository.findOne({
      where: {},
    });

    if (!content) {
      content = this.landingPageRepository.create({});
    }

    // Update specific section
    switch (section) {
      case 'hero':
        if (data.title) content.heroTitle = data.title;
        if (data.subtitle) content.heroSubtitle = data.subtitle;
        if (data.image) content.heroImage = data.image;
        if (data.ctaText) content.ctaText = data.ctaText;
        if (data.ctaLink) content.ctaLink = data.ctaLink;
        break;
      case 'feature1':
        if (data.title) content.feature1Title = data.title;
        if (data.description) content.feature1Description = data.description;
        break;
      case 'feature2':
        if (data.title) content.feature2Title = data.title;
        if (data.description) content.feature2Description = data.description;
        break;
      case 'feature3':
        if (data.title) content.feature3Title = data.title;
        if (data.description) content.feature3Description = data.description;
        break;
      case 'about':
        if (data.title) content.aboutTitle = data.title;
        if (data.content) content.aboutContent = data.content;
        break;
    }

    return this.landingPageRepository.save(content);
  }

  async updateAllLandingPageContent(data: any) {
    let content = await this.landingPageRepository.findOne({
      where: {},
    });

    if (!content) {
      content = this.landingPageRepository.create({});
    }

    // Update all fields directly from the form data
    if (data.heroTitle) content.heroTitle = data.heroTitle;
    if (data.heroSubtitle) content.heroSubtitle = data.heroSubtitle;
    if (data.heroImage) content.heroImage = data.heroImage;
    if (data.ctaText) content.ctaText = data.ctaText;
    if (data.ctaLink) content.ctaLink = data.ctaLink;
    if (data.feature1Title) content.feature1Title = data.feature1Title;
    if (data.feature1Description) content.feature1Description = data.feature1Description;
    if (data.feature2Title) content.feature2Title = data.feature2Title;
    if (data.feature2Description) content.feature2Description = data.feature2Description;
    if (data.feature3Title) content.feature3Title = data.feature3Title;
    if (data.feature3Description) content.feature3Description = data.feature3Description;
    if (data.aboutTitle) content.aboutTitle = data.aboutTitle;
    if (data.aboutContent) content.aboutContent = data.aboutContent;

    return this.landingPageRepository.save(content);
  }

  // App Constants Management
  async getAppConstants() {
    return this.appConstantsRepository.findOne({
      where: {},
    });
  }

  async updateAppConstants(data: Partial<AppConstants>) {
    let existingConstant = await this.appConstantsRepository.findOne({
      where: {},
    });

    if (existingConstant) {
      Object.assign(existingConstant, data);
      return this.appConstantsRepository.save(existingConstant);
    } else {
      const newConstant = this.appConstantsRepository.create({
        appVersion: '1.0.0',
        buildNumber: 1,
        minSupportedVersion: '1.0.0',
        appName: 'Power Up',
        apiBaseUrl: 'https://api.powerup.com',
        apiVersion: 'v1',
        requestTimeout: 30000,
        maxRetries: 3,
        enablePushNotifications: true,
        enableAnalytics: true,
        enableCrashReporting: true,
        enableBetaFeatures: false,
        enableOfflineMode: true,
        enableDarkMode: true,
        websiteUrl: 'http://localhost:3000',
        ...data,
      });
      return this.appConstantsRepository.save(newConstant);
    }
  }

  // Help Module Management
  async getHelpArticles() {
    // Get all articles for admin (including unpublished ones)
    return await this.helpService.getAllHelpArticlesForAdmin();
  }

  async getHelpArticle(id: string) {
    return await this.helpService.getHelpArticleForAdmin(id);
  }

  async createHelpArticle(data: any) {
    return await this.helpService.createHelpArticle({
      title: data.title,
      description: data.description || '',
      content: data.content,
      category: data.category,
      tags: data.tags ? (Array.isArray(data.tags) ? data.tags : data.tags.split(',').map((t: string) => t.trim())) : [],
      published: data.isActive ?? data.published ?? true,
      sortOrder: data.order ? parseInt(data.order) : 0,
      authorId: 'admin'
    });
  }

  async updateHelpArticle(id: string, data: any) {
    return await this.helpService.updateHelpArticle(id, {
      title: data.title,
      description: data.description,
      content: data.content,
      category: data.category,
      tags: data.tags ? (Array.isArray(data.tags) ? data.tags : data.tags.split(',').map((t: string) => t.trim())) : undefined,
      published: data.isActive ?? data.published,
      sortOrder: data.order ? parseInt(data.order) : undefined,
    });
  }

  async deleteHelpArticle(id: string) {
    return await this.helpService.deleteHelpArticle(id);
  }

  // Community Data Management
  async getCommunityData() {
    const challenges = await this.challengesService.findAll();
    const stats = {
      totalChallenges: challenges.length,
      activeChallenges: challenges.filter(c => 
        new Date(c.startDate) <= new Date() && new Date(c.endDate) >= new Date()
      ).length,
      totalParticipants: challenges.reduce((sum, challenge) => 
        sum + (challenge.participants?.length || 0), 0
      ),
    };

    return {
      challenges,
      stats,
    };
  }

  async getChallenges() {
    return this.challengesService.findAll();
  }

  async getChallenge(id: string) {
    return this.challengesService.findOne(id);
  }

  async updateChallenge(id: string, data: any) {
    return this.challengesService.update(id, data);
  }

  async deleteChallenge(id: string) {
    return this.challengesService.remove(id);
  }

  // Dashboard Statistics
  async getDashboardStats() {
    const [
      landingPageSections,
      appConstants,
      helpArticles,
      communityData,
      blogStats,
    ] = await Promise.all([
      this.getLandingPageContent(),
      this.getAppConstants(),
      this.getHelpArticles(),
      this.getCommunityData(),
      this.getBlogStats(),
    ]);

    return {
      landingPageSections: Array.isArray(landingPageSections) ? landingPageSections.length : 0,
      appConstants: appConstants ? 1 : 0,
      helpArticles: Array.isArray(helpArticles) ? helpArticles.length : 0,
      challenges: communityData.stats.totalChallenges,
      activeChallenges: communityData.stats.activeChallenges,
      totalParticipants: communityData.stats.totalParticipants,
      totalBlogPosts: blogStats.totalPosts || 0,
      publishedBlogPosts: blogStats.publishedPosts || 0,
      totalBlogViews: blogStats.totalViews || 0,
    };
  }

  // Blog Management Methods
  async getBlogStats() {
    return this.blogService.getStats();
  }

  async getAllBlogPosts(searchDto?: SearchBlogPostDto) {
    return this.blogService.findAllForAdmin(searchDto);
  }

  async getBlogPost(id: string) {
    return this.blogService.findOne(id);
  }

  async createBlogPost(data: CreateBlogPostDto, authorId: string) {
    return this.blogService.create(data, authorId);
  }

  async updateBlogPost(id: string, data: UpdateBlogPostDto) {
    return this.blogService.update(id, data);
  }

  async deleteBlogPost(id: string) {
    return this.blogService.remove(id);
  }

  private async createDefaultContent() {
    // Check if landing page content exists
    const existingLandingPage = await this.landingPageRepository.findOne({
      where: {},
    });

    if (!existingLandingPage) {
      const content = this.landingPageRepository.create({
        heroTitle: 'Welcome to Power Up',
        heroSubtitle: 'Transform your life with our powerful habit tracking app. Build better habits, achieve your goals, and unlock your potential.',
        heroImage: 'https://images.unsplash.com/photo-1551632811-561732d1e306?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        ctaText: 'Get Started Today',
        ctaLink: 'https://play.google.com/store/apps/details?id=com.powerup',
        feature1Title: 'Habit Tracking',
        feature1Description: 'Track your daily habits and build consistency with our intuitive interface and smart reminders.',
        feature2Title: 'Goal Setting',
        feature2Description: 'Set ambitious goals and break them down into manageable steps with our goal management system.',
        feature3Title: 'Progress Analytics',
        feature3Description: 'Visualize your progress with detailed analytics and insights to stay motivated on your journey.',
        aboutTitle: 'About Power Up',
        aboutContent: 'Power Up is more than just a habit tracker - it\'s your personal growth companion. Our app combines the latest behavioral science with intuitive design to help you build lasting positive changes in your life. Whether you\'re looking to develop new skills, maintain healthy routines, or achieve ambitious goals, Power Up provides the tools and motivation you need to succeed.',
      });
      await this.landingPageRepository.save(content);
    }

    // Check if app constants exist
    const existingAppConstants = await this.appConstantsRepository.findOne({
      where: {},
    });

    if (!existingAppConstants) {
      const appConstants = this.appConstantsRepository.create({
        appVersion: '1.0.0',
        buildNumber: 1,
        minSupportedVersion: '1.0.0',
        appName: 'Power Up',
        apiBaseUrl: 'https://api.powerup.com',
        apiVersion: 'v1',
        requestTimeout: 30000,
        maxRetries: 3,
        enablePushNotifications: true,
        enableAnalytics: true,
        enableCrashReporting: true,
        enableBetaFeatures: false,
        enableOfflineMode: true,
        enableDarkMode: true,
        websiteUrl: 'http://localhost:3000',
      });
      await this.appConstantsRepository.save(appConstants);
    }
  }
}
