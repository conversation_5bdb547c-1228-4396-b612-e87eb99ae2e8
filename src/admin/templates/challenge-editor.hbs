<div class="space-y-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 dark:text-white">Edit Challenge</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">Modify the selected community challenge</p>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm">
        <div class="p-6">
            <form action="/admin/community/challenges/{{challenge.id}}" method="POST" class="space-y-6">
                <input type="hidden" name="id" value="{{challenge.id}}">
                
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Challenge Name *</label>
                    <input type="text" id="name" name="name" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" required 
                           value="{{challenge.name}}" placeholder="30-Day Fitness Challenge">
                </div>

                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description *</label>
                    <textarea id="description" name="description" rows="4" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" required 
                              placeholder="Join us for a 30-day journey to improve your fitness...">{{challenge.description}}</textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Category *</label>
                        <select id="category" name="category" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                            <option value="">Select Category</option>
                            <option value="fitness" {{#if (eq challenge.category "fitness")}}selected{{/if}}>Fitness</option>
                            <option value="health" {{#if (eq challenge.category "health")}}selected{{/if}}>Health</option>
                            <option value="productivity" {{#if (eq challenge.category "productivity")}}selected{{/if}}>Productivity</option>
                            <option value="mindfulness" {{#if (eq challenge.category "mindfulness")}}selected{{/if}}>Mindfulness</option>
                            <option value="learning" {{#if (eq challenge.category "learning")}}selected{{/if}}>Learning</option>
                            <option value="social" {{#if (eq challenge.category "social")}}selected{{/if}}>Social</option>
                        </select>
                    </div>
                    <div>
                        <label for="duration" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Duration (days) *</label>
                        <input type="number" id="duration" name="duration" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                               required min="1" max="365" value="{{challenge.duration}}">
                    </div>
                    <div>
                        <label for="difficulty" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Difficulty *</label>
                        <select id="difficulty" name="difficulty" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                            <option value="">Select Difficulty</option>
                            <option value="beginner" {{#if (eq challenge.difficulty "beginner")}}selected{{/if}}>Beginner</option>
                            <option value="intermediate" {{#if (eq challenge.difficulty "intermediate")}}selected{{/if}}>Intermediate</option>
                            <option value="advanced" {{#if (eq challenge.difficulty "advanced")}}selected{{/if}}>Advanced</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="startDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Start Date *</label>
                        <input type="datetime-local" id="startDate" name="startDate" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                               required value="{{formatDateForInput challenge.startDate}}">
                    </div>
                    <div>
                        <label for="endDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">End Date *</label>
                        <input type="datetime-local" id="endDate" name="endDate" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                               required value="{{formatDateForInput challenge.endDate}}">
                    </div>
                </div>

                <div>
                    <label for="rules" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Rules & Guidelines</label>
                    <textarea id="rules" name="rules" rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                              placeholder="1. Complete daily tasks...">{{challenge.rules}}</textarea>
                </div>

                <div>
                    <label for="rewards" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Rewards (one per line)</label>
                    <textarea id="rewards" name="rewards" rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                              placeholder="Digital badge&#10;Points: 100&#10;Exclusive content access">{{join challenge.rewards "\n"}}</textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="maxParticipants" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Max Participants</label>
                        <input type="number" id="maxParticipants" name="maxParticipants" 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" min="1" value="{{challenge.maxParticipants}}" 
                               placeholder="Leave empty for unlimited">
                    </div>
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status *</label>
                        <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                            <option value="draft" {{#if (eq challenge.status "draft")}}selected{{/if}}>Draft</option>
                            <option value="active" {{#if (eq challenge.status "active")}}selected{{/if}}>Active</option>
                            <option value="completed" {{#if (eq challenge.status "completed")}}selected{{/if}}>Completed</option>
                        </select>
                    </div>
                </div>

                <div class="flex items-center">
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" name="isFeatured" {{#if challenge.isFeatured}}checked{{/if}} class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        <span class="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">Featured Challenge</span>
                    </label>
                </div>

                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <a href="/admin/community" class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors">Cancel</a>
                    <button type="submit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg shadow-sm transition-colors">Update Challenge</button>
                </div>
            </form>
        </div>
    </div>
</div>


