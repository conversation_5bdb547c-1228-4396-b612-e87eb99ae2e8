<div class="space-y-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 dark:text-white">App Constants Management</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">Manage mobile app version, metadata, and configuration</p>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-white">App Version & Metadata</h2>
                <button class="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium" onclick="saveAppConstants()">Save Changes</button>
            </div>
        </div>

        <div class="p-6">
            <form id="appConstantsForm" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="appVersion" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">App Version</label>
                        <input type="text" id="appVersion" name="appVersion" value="{{constants.appVersion}}" 
                               placeholder="1.0.0" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>
                    <div>
                        <label for="buildNumber" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Build Number</label>
                        <input type="number" id="buildNumber" name="buildNumber" value="{{constants.buildNumber}}" 
                               placeholder="1" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="minSupportedVersion" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Min Supported Version</label>
                        <input type="text" id="minSupportedVersion" name="minSupportedVersion" value="{{constants.minSupportedVersion}}" 
                               placeholder="1.0.0" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>
                    <div>
                        <label for="forceUpdateVersion" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Force Update Version</label>
                        <input type="text" id="forceUpdateVersion" name="forceUpdateVersion" value="{{constants.forceUpdateVersion}}" 
                               placeholder="1.0.0" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>
                </div>

                <div>
                    <label for="appName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">App Name</label>
                    <input type="text" id="appName" name="appName" value="{{constants.appName}}" 
                           placeholder="Power Up" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                </div>

                <div>
                    <label for="appDescription" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">App Description</label>
                    <textarea id="appDescription" name="appDescription" rows="3" 
                              placeholder="Transform your life with our powerful habit tracking app" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">{{constants.appDescription}}</textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="playStoreUrl" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Play Store URL</label>
                        <input type="url" id="playStoreUrl" name="playStoreUrl" value="{{constants.playStoreUrl}}" 
                               placeholder="https://play.google.com/store/apps/details?id=com.powerup" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>
                    <div>
                        <label for="appStoreUrl" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">App Store URL</label>
                        <input type="url" id="appStoreUrl" name="appStoreUrl" value="{{constants.appStoreUrl}}" 
                               placeholder="https://apps.apple.com/app/power-up/id123456789" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white">API Configuration</h2>
        </div>

        <div class="p-6 space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="apiBaseUrl" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">API Base URL</label>
                    <input type="url" id="apiBaseUrl" name="apiBaseUrl" value="{{constants.apiBaseUrl}}" 
                           placeholder="https://api.powerup.com" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                </div>
                <div>
                    <label for="apiVersion" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">API Version</label>
                    <input type="text" id="apiVersion" name="apiVersion" value="{{constants.apiVersion}}" 
                           placeholder="v1" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="requestTimeout" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Request Timeout (ms)</label>
                    <input type="number" id="requestTimeout" name="requestTimeout" value="{{constants.requestTimeout}}" 
                           placeholder="30000" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                </div>
                <div>
                    <label for="maxRetries" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Max Retries</label>
                    <input type="number" id="maxRetries" name="maxRetries" value="{{constants.maxRetries}}" 
                           placeholder="3" required class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Feature Flags</h2>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <label class="flex items-center cursor-pointer">
                        <input type="checkbox" id="enablePushNotifications" name="enablePushNotifications" 
                               {{#if constants.enablePushNotifications}}checked{{/if}} class="sr-only peer">
                        <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        <span class="ml-3 font-medium text-gray-700 dark:text-gray-300">Push Notifications</span>
                    </label>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <label class="flex items-center cursor-pointer">
                        <input type="checkbox" id="enableAnalytics" name="enableAnalytics" 
                               {{#if constants.enableAnalytics}}checked{{/if}} class="sr-only peer">
                        <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        <span class="ml-3 font-medium text-gray-700 dark:text-gray-300">Analytics</span>
                    </label>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <label class="flex items-center cursor-pointer">
                        <input type="checkbox" id="enableCrashReporting" name="enableCrashReporting" 
                               {{#if constants.enableCrashReporting}}checked{{/if}} class="sr-only peer">
                        <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        <span class="ml-3 font-medium text-gray-700 dark:text-gray-300">Crash Reporting</span>
                    </label>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <label class="flex items-center cursor-pointer">
                        <input type="checkbox" id="enableBetaFeatures" name="enableBetaFeatures" 
                               {{#if constants.enableBetaFeatures}}checked{{/if}} class="sr-only peer">
                        <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        <span class="ml-3 font-medium text-gray-700 dark:text-gray-300">Beta Features</span>
                    </label>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <label class="flex items-center cursor-pointer">
                        <input type="checkbox" id="enableOfflineMode" name="enableOfflineMode" 
                               {{#if constants.enableOfflineMode}}checked{{/if}} class="sr-only peer">
                        <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        <span class="ml-3 font-medium text-gray-700 dark:text-gray-300">Offline Mode</span>
                    </label>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <label class="flex items-center cursor-pointer">
                        <input type="checkbox" id="enableDarkMode" name="enableDarkMode" 
                               {{#if constants.enableDarkMode}}checked{{/if}} class="sr-only peer">
                        <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        <span class="ml-3 font-medium text-gray-700 dark:text-gray-300">Dark Mode</span>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Social Media & Support</h2>
        </div>

        <div class="p-6 space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="supportEmail" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Support Email</label>
                    <input type="email" id="supportEmail" name="supportEmail" value="{{constants.supportEmail}}" 
                           placeholder="<EMAIL>" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                </div>
                <div>
                    <label for="websiteUrl" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Website URL</label>
                    <input type="url" id="websiteUrl" name="websiteUrl" value="{{constants.websiteUrl}}" 
                           placeholder="https://powerup.com" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="privacyPolicyUrl" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Privacy Policy URL</label>
                    <input type="url" id="privacyPolicyUrl" name="privacyPolicyUrl" value="{{constants.privacyPolicyUrl}}" 
                           placeholder="https://powerup.com/privacy" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                </div>
                <div>
                    <label for="termsOfServiceUrl" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Terms of Service URL</label>
                    <input type="url" id="termsOfServiceUrl" name="termsOfServiceUrl" value="{{constants.termsOfServiceUrl}}" 
                           placeholder="https://powerup.com/terms" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                </div>
            </div>
        </div>
    </div>
</div>

<script>
async function saveAppConstants() {
    const form = document.getElementById('appConstantsForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    // Handle checkboxes for feature flags
    const checkboxes = ['enablePushNotifications', 'enableAnalytics', 'enableCrashReporting', 
                       'enableBetaFeatures', 'enableOfflineMode', 'enableDarkMode'];
    
    checkboxes.forEach(checkbox => {
        const element = document.getElementById(checkbox);
        data[checkbox] = element.checked;
    });

    // Convert numeric fields
    data.buildNumber = parseInt(data.buildNumber);
    data.requestTimeout = parseInt(data.requestTimeout);
    data.maxRetries = parseInt(data.maxRetries);

    try {
        const response = await fetch('/admin/api/app-constants', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        if (response.ok) {
            showNotification('App constants updated successfully!', 'success');
        } else {
            throw new Error('Failed to update app constants');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('Failed to update app constants', 'error');
    }
}
</script>
