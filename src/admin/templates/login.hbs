<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        }
                    }
                }
            }
        }
    </script>
    
    <style>
        body {
            background: linear-gradient(135deg, #020617 0%, #0f172a 100%);
            min-height: 100vh;
        }
        
        .gradient-text {
            background: linear-gradient(135deg, #60a5fa, #3b82f6, #2563eb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .login-card {
            background: rgba(30, 41, 59, 0.9);
            backdrop-filter: blur(20px);
            border: 1px solid #334155;
        }
        
        .form-input {
            background: #334155;
            border: 1px solid #475569;
            color: #f9fafb;
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #60a5fa;
            box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #60a5fa, #3b82f6);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(96, 165, 250, 0.3);
        }
    </style>
</head>
<body class="flex items-center justify-center min-h-screen p-4">
    <div class="login-card rounded-xl p-8 w-full max-w-md">
        <div class="text-center mb-8">
            <img src="/assets/images/logo.png" alt="Power Up Logo" class="w-16 h-16 mx-auto mb-4 rounded-lg">
            <h1 class="text-3xl font-bold gradient-text mb-2">Power Up</h1>
            <p class="text-gray-400">Control Panel</p>
        </div>
        
        {{#if error}}
        <div class="mb-6 p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
            <p class="text-red-400 text-sm">{{error}}</p>
        </div>
        {{/if}}
        
        <form method="POST" action="/admin/login" class="space-y-6">
            <div>
                <label for="email" class="block text-sm font-medium text-gray-300 mb-2">Email</label>
                <input
                    type="email"
                    id="email"
                    name="email"
                    value="{{email}}"
                    required
                    class="form-input w-full px-4 py-3 rounded-lg"
                    placeholder="<EMAIL>"
                >
            </div>
            
            <div>
                <label for="password" class="block text-sm font-medium text-gray-300 mb-2">Password</label>
                <input
                    type="password"
                    id="password"
                    name="password"
                    required
                    class="form-input w-full px-4 py-3 rounded-lg"
                    placeholder="Enter your password"
                >
            </div>
            
            <button
                type="submit"
                class="w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors"
            >
                Sign In
            </button>
        </form>
    </div>
</body>
</html>
