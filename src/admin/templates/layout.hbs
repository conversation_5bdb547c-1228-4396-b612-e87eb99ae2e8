<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}}</title>
    <meta name="description" content="Power Up Admin Control Panel">
    
    <!-- Favicons -->
    <link rel="icon" type="image/x-icon" href="/assets/images/logo.png">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                        dark: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Include external libraries -->
    {{#if includeEditorJS}}
    <!-- Editor.js for Notion-like editing -->
    <script>
        // Editor.js loader with proper sequencing
        window.editorJSReady = false;
        window.editorJSTools = {};
        
        // Function to load scripts sequentially
        function loadScript(src, name) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = () => {
                    console.log(`${name} loaded successfully`);
                    resolve();
                };
                script.onerror = () => {
                    console.error(`Failed to load ${name}`);
                    reject(new Error(`Failed to load ${name}`));
                };
                document.head.appendChild(script);
            });
        }
        
        // Load Editor.js and tools sequentially
        async function loadEditorJS() {
            try {
                console.log('Starting Editor.js loading sequence...');
                
                // Load main Editor.js first
                await loadScript('https://unpkg.com/@editorjs/editorjs@latest', 'EditorJS');
                
                // Load tools sequentially with retry logic
                const toolsToLoad = [
                    { url: 'https://unpkg.com/@editorjs/header@latest', name: 'Header', globalName: 'Header' },
                    { url: 'https://unpkg.com/@editorjs/paragraph@latest', name: 'Paragraph', globalName: 'Paragraph' },
                    { url: 'https://unpkg.com/@editorjs/list@latest', name: 'List', globalName: 'List' },
                    { url: 'https://unpkg.com/@editorjs/image@latest', name: 'ImageTool', globalName: 'ImageTool' },
                    { url: 'https://unpkg.com/@editorjs/embed@latest', name: 'Embed', globalName: 'Embed' },
                    { url: 'https://unpkg.com/@editorjs/quote@latest', name: 'Quote', globalName: 'Quote' },
                    { url: 'https://unpkg.com/@editorjs/delimiter@latest', name: 'Delimiter', globalName: 'Delimiter' },
                    { url: 'https://unpkg.com/@editorjs/table@latest', name: 'Table', globalName: 'Table' },
                    { url: 'https://unpkg.com/@editorjs/code@latest', name: 'CodeTool', globalName: 'CodeTool' },
                    { url: 'https://unpkg.com/@editorjs/inline-code@latest', name: 'InlineCode', globalName: 'InlineCode' },
                    { url: 'https://unpkg.com/@editorjs/marker@latest', name: 'Marker', globalName: 'Marker' },
                    { url: 'https://unpkg.com/@editorjs/underline@latest', name: 'Underline', globalName: 'Underline' }
                ];
                
                // Load tools with individual error handling
                const toolPromises = toolsToLoad.map(async (tool) => {
                    try {
                        await loadScript(tool.url, tool.name);
                        // Wait a bit for the tool to be available globally
                        await new Promise(resolve => setTimeout(resolve, 50));
                        return { name: tool.name, globalName: tool.globalName, loaded: true };
                    } catch (error) {
                        console.warn(`Failed to load ${tool.name}:`, error);
                        return { name: tool.name, globalName: tool.globalName, loaded: false };
                    }
                });
                
                const toolResults = await Promise.all(toolPromises);
                
                // Wait additional time for tools to fully initialize
                await new Promise(resolve => setTimeout(resolve, 300));
                
                // Store tool references, only if they're actually available
                window.editorJSTools = {};
                const loadedTools = [];
                const failedTools = [];
                
                toolResults.forEach(result => {
                    const globalTool = window[result.globalName];
                    if (result.loaded && typeof globalTool === 'function') {
                        window.editorJSTools[result.globalName] = globalTool;
                        loadedTools.push(result.name);
                    } else {
                        failedTools.push(result.name);
                        console.warn(`Tool ${result.name} not available as ${result.globalName}:`, typeof globalTool);
                    }
                });
                
                console.log('Editor.js tools loading results:', {
                    EditorJS: typeof EditorJS,
                    loadedTools: loadedTools,
                    failedTools: failedTools,
                    toolReferences: Object.keys(window.editorJSTools).reduce((acc, key) => {
                        acc[key] = typeof window.editorJSTools[key];
                        return acc;
                    }, {})
                });
                
                // Check if we have minimum required tools
                const requiredTools = ['Header', 'Paragraph'];
                const hasRequiredTools = requiredTools.every(tool => window.editorJSTools[tool]);
                
                if (!hasRequiredTools) {
                    throw new Error(`Missing required tools: ${requiredTools.filter(tool => !window.editorJSTools[tool]).join(', ')}`);
                }
                
                window.editorJSReady = true;
                window.dispatchEvent(new CustomEvent('editorToolsReady'));
                
            } catch (error) {
                console.error('Error loading Editor.js:', error);
                window.editorJSReady = false;
                window.dispatchEvent(new CustomEvent('editorToolsError', { detail: error }));
            }
        }
        
        // Start loading when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', loadEditorJS);
        } else {
            loadEditorJS();
        }
    </script>
    <style>
        /* Custom styles for Editor.js */
        .ce-block__content {
            max-width: none !important;
        }
        .ce-toolbar__content {
            max-width: none !important;
        }
        .codex-editor {
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            padding: 1rem;
            min-height: 400px;
            background: white;
            color: #1f2937;
        }
        .dark .codex-editor {
            background: #374151;
            border-color: #4b5563;
            color: #f9fafb;
        }
        
        /* Editor.js text styling */
        .ce-block {
            margin: 0.5rem 0;
        }
        .ce-paragraph {
            line-height: 1.6;
            color: #1f2937;
        }
        .dark .ce-paragraph {
            color: #f9fafb;
        }
        .ce-header {
            font-weight: bold;
            margin: 1rem 0;
            color: #1f2937;
        }
        .dark .ce-header {
            color: #f9fafb;
        }
        .ce-quote {
            border-left: 4px solid #6366f1;
            padding-left: 1rem;
            font-style: italic;
            margin: 1rem 0;
            color: #1f2937;
        }
        .dark .ce-quote {
            border-left-color: #818cf8;
            color: #f9fafb;
        }
        
        /* Input elements in Editor.js */
        .codex-editor [contenteditable="true"] {
            color: #1f2937 !important;
            font-size: 16px;
            line-height: 1.6;
        }
        .dark .codex-editor [contenteditable="true"] {
            color: #f9fafb !important;
        }
        
        /* Ensure text is visible in all Editor.js elements */
        .codex-editor * {
            color: inherit;
        }
        
        /* Specific styling for different block types */
        .ce-block .ce-block__content,
        .ce-block .ce-block__content * {
            color: #1f2937;
        }
        .dark .ce-block .ce-block__content,
        .dark .ce-block .ce-block__content * {
            color: #f9fafb;
        }
        
        /* Placeholder styling */
        .codex-editor .ce-paragraph[data-placeholder]:empty:before {
            color: #9ca3af !important;
            opacity: 0.7;
        }
        .dark .codex-editor .ce-paragraph[data-placeholder]:empty:before {
            color: #6b7280 !important;
        }
        
        /* Toolbar styling */
        .ce-toolbar {
            background: white;
        }
        .dark .ce-toolbar {
            background: #374151;
        }
        
        /* Inline toolbar styling */
        .ce-inline-toolbar {
            background: white;
            border: 1px solid #e5e7eb;
        }
        .dark .ce-inline-toolbar {
            background: #374151;
            border-color: #4b5563;
        }
    </style>
    {{/if}}
    
    <!-- Custom CSS -->
    <style>
        * {
            scroll-behavior: smooth;
        }
        
        body {
            background: #0a0a0a;
            color: #f9fafb;
        }
        
        .gradient-blue {
            background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%);
        }
        
        .gradient-text {
            background: linear-gradient(135deg, #60a5fa, #3b82f6, #2563eb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        /* Sidebar */
        .sidebar {
            background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
            border-right: 1px solid rgba(96, 165, 250, 0.2);
        }
        
        .sidebar-item {
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .sidebar-item:hover,
        .sidebar-item.active {
            background: rgba(96, 165, 250, 0.1);
            border-left-color: #60a5fa;
        }
        
        /* Cards */
        .admin-card {
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(96, 165, 250, 0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .admin-card:hover {
            border-color: rgba(96, 165, 250, 0.5);
            box-shadow: 0 25px 50px rgba(96, 165, 250, 0.1);
        }
        
        /* Buttons */
        .btn-primary {
            background: linear-gradient(135deg, #60a5fa, #3b82f6);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(96, 165, 250, 0.3);
        }
        
        .btn-secondary {
            background: rgba(96, 165, 250, 0.1);
            border: 1px solid rgba(96, 165, 250, 0.3);
            color: #60a5fa;
            transition: all 0.3s ease;
        }
        
        .btn-secondary:hover {
            background: rgba(96, 165, 250, 0.2);
            border-color: rgba(96, 165, 250, 0.5);
        }
        
        /* Form elements */
        .form-input,
        .form-textarea,
        .form-select {
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(96, 165, 250, 0.2);
            color: #f9fafb;
            transition: all 0.3s ease;
        }
        
        .form-input:focus,
        .form-textarea:focus,
        .form-select:focus {
            outline: none;
            border-color: #60a5fa;
            box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
        }
        
        .form-label {
            color: #cbd5e1;
            font-weight: 500;
        }
        
        /* Animated background */
        .animated-bg {
            background: radial-gradient(circle at 20% 80%, rgba(96, 165, 250, 0.05) 0%, transparent 50%),
                       radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
                       radial-gradient(circle at 40% 40%, rgba(37, 99, 235, 0.05) 0%, transparent 50%);
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #1e293b;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #60a5fa;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #3b82f6;
        }
        
        /* Notifications */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            max-width: 400px;
            padding: 16px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
            animation: slideIn 0.3s ease-out;
        }
        
        .notification.success {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.3);
            color: #22c55e;
        }
        
        .notification.error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #ef4444;
        }
        
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        /* Tables */
        .admin-table {
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(168, 85, 247, 0.2);
        }
        
        .admin-table th {
            background: rgba(168, 85, 247, 0.1);
            color: #a855f7;
            font-weight: 600;
        }
        
        .admin-table tbody tr:hover {
            background: rgba(168, 85, 247, 0.05);
        }
        
        /* JSON Editor */
        .json-editor {
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 14px;
        }
    </style>
    
</head>
<body class="bg-gray-900 text-white animated-bg min-h-screen">
    <!-- Navigation -->
    <nav class="fixed top-0 w-full z-50 bg-gray-900/80 backdrop-blur-lg border-b border-blue-500/20">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <img src="/assets/images/logo.png" alt="Power Up Logo" class="w-8 h-8 rounded-lg">
                    <span class="text-xl font-bold gradient-text">Power Up Control Panel</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-400">Welcome, Admin</span>
                    <form method="POST" action="/admin/logout" class="inline">
                        <button type="submit" class="text-red-400 hover:text-red-300 transition-colors">
                            Logout
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex pt-16">
        <!-- Sidebar -->
        <aside class="sidebar fixed left-0 top-16 h-full w-64 p-6">
            <nav class="space-y-2">
                <a href="/admin/dashboard" class="sidebar-item flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    </svg>
                    <span>Dashboard</span>
                </a>
                
                <a href="/admin/landing-page" class="sidebar-item flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                    <span>Landing Page</span>
                </a>
                
                <a href="/admin/app-constants" class="sidebar-item flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                    <span>App Constants</span>
                </a>
                
                <a href="/admin/help" class="sidebar-item flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>Help Center</span>
                </a>
                
                <a href="/admin/community" class="sidebar-item flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    <span>Community</span>
                </a>
                
                <a href="/admin/blog" class="sidebar-item flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                    </svg>
                    <span>Blog Management</span>
                </a>
                
                <a href="/admin/monetization" class="sidebar-item flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>Monetization</span>
                </a>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 ml-64 p-8">
            {{{body}}}
        </main>
    </div>

    <!-- Notification System -->
    <script>
        // Show notifications from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const success = urlParams.get('success');
        const error = urlParams.get('error');
        
        if (success) {
            showNotification(success, 'success');
        }
        if (error) {
            showNotification(error, 'error');
        }
        
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-current opacity-70 hover:opacity-100">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 5000);
        }
        
        // Auto-dismiss notifications
        document.addEventListener('DOMContentLoaded', function() {
            const notifications = document.querySelectorAll('.notification');
            notifications.forEach(notification => {
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 5000);
            });
        });
        
        // Active sidebar item highlighting
        const currentPath = window.location.pathname;
        const sidebarItems = document.querySelectorAll('.sidebar-item');
        sidebarItems.forEach(item => {
            if (item.getAttribute('href') === currentPath) {
                item.classList.add('active');
            }
        });
    </script>
</body>
</html>
