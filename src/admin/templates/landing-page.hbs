<div class="space-y-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 dark:text-white">Landing Page Management</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">Manage the content displayed on your landing page</p>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Hero Section</h2>
                <button class="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium" onclick="saveLandingPage()">
                    Save Changes
                </button>
            </div>
        </div>

        <div class="p-6">
            <form id="landingPageForm" class="space-y-6">
                <div>
                    <label for="heroTitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Hero Title</label>
                    <input type="text" id="heroTitle" name="heroTitle" value="{{content.heroTitle}}" 
                           placeholder="Welcome to Power Up" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                </div>

                <div>
                    <label for="heroSubtitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Hero Subtitle</label>
                    <textarea id="heroSubtitle" name="heroSubtitle" rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white" 
                              placeholder="Transform your life with our powerful habit tracking app">{{content.heroSubtitle}}</textarea>
                </div>

                <div>
                    <label for="heroImage" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Hero Image URL</label>
                    <input type="url" id="heroImage" name="heroImage" value="{{content.heroImage}}" 
                           placeholder="https://example.com/hero-image.jpg" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                </div>

                <div>
                    <label for="ctaText" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Call to Action Text</label>
                    <input type="text" id="ctaText" name="ctaText" value="{{content.ctaText}}" 
                           placeholder="Get Started Today" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                </div>

                <div>
                    <label for="ctaLink" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Call to Action Link</label>
                    <input type="url" id="ctaLink" name="ctaLink" value="{{content.ctaLink}}" 
                           placeholder="https://play.google.com/store/apps/details?id=com.powerup" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                </div>
            </form>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Features Section</h2>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <div class="space-y-4">
                        <div>
                            <label for="feature1Title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Feature 1 Title</label>
                            <input type="text" id="feature1Title" name="feature1Title" value="{{content.feature1Title}}" 
                                   placeholder="Habit Tracking" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white">
                        </div>
                        <div>
                            <label for="feature1Description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Feature 1 Description</label>
                            <textarea id="feature1Description" name="feature1Description" rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white">{{content.feature1Description}}</textarea>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <div class="space-y-4">
                        <div>
                            <label for="feature2Title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Feature 2 Title</label>
                            <input type="text" id="feature2Title" name="feature2Title" value="{{content.feature2Title}}" 
                                   placeholder="Goal Setting" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white">
                        </div>
                        <div>
                            <label for="feature2Description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Feature 2 Description</label>
                            <textarea id="feature2Description" name="feature2Description" rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white">{{content.feature2Description}}</textarea>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <div class="space-y-4">
                        <div>
                            <label for="feature3Title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Feature 3 Title</label>
                            <input type="text" id="feature3Title" name="feature3Title" value="{{content.feature3Title}}" 
                                   placeholder="Progress Analytics" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white">
                        </div>
                        <div>
                            <label for="feature3Description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Feature 3 Description</label>
                            <textarea id="feature3Description" name="feature3Description" rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white">{{content.feature3Description}}</textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white">About Section</h2>
        </div>

        <div class="p-6 space-y-6">
            <div>
                <label for="aboutTitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">About Title</label>
                <input type="text" id="aboutTitle" name="aboutTitle" value="{{content.aboutTitle}}" 
                       placeholder="About Power Up" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
            </div>

            <div>
                <label for="aboutContent" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">About Content</label>
                <textarea id="aboutContent" name="aboutContent" rows="6" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">{{content.aboutContent}}</textarea>
            </div>
        </div>
    </div>
</div>

<script>
async function saveLandingPage() {
    const form = document.getElementById('landingPageForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    try {
        const response = await fetch('/admin/api/landing-page', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        if (response.ok) {
            showNotification('Landing page content updated successfully!', 'success');
        } else {
            throw new Error('Failed to update landing page content');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('Failed to update landing page content', 'error');
    }
}
</script>
