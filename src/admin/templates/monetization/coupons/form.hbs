<div class="space-y-8">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <a href="/admin/monetization/coupons" class="text-gray-400 hover:text-white transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </a>
            <h1 class="text-3xl font-bold gradient-text">
                {{#if coupon}}Edit Coupon Code{{else}}Create New Coupon Code{{/if}}
            </h1>
        </div>
    </div>

    <form id="couponForm" method="POST" action="{{#if coupon}}/admin/monetization/coupons/{{coupon.id}}{{else}}/admin/monetization/coupons{{/if}}" class="space-y-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Form -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
                    <h2 class="text-xl font-semibold text-white mb-6">Basic Information</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="code" class="form-label required">Coupon Code</label>
                            <input type="text" id="code" name="code" required 
                                   value="{{coupon.code}}" 
                                   class="form-input w-full font-mono uppercase"
                                   placeholder="SAVE20"
                                   pattern="[A-Z0-9]+"
                                   title="Only uppercase letters and numbers allowed">
                            <p class="text-sm text-gray-400 mt-1">Only uppercase letters and numbers (e.g., SAVE20, WELCOME2024)</p>
                        </div>
                        
                        <div>
                            <label for="description" class="form-label">Description</label>
                            <input type="text" id="description" name="description" 
                                   value="{{coupon.description}}" 
                                   class="form-input w-full"
                                   placeholder="20% off for new customers">
                        </div>
                    </div>
                </div>

                <!-- Discount Configuration -->
                <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
                    <h2 class="text-xl font-semibold text-white mb-6">Discount Configuration</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="discountType" class="form-label required">Discount Type</label>
                            <select id="discountType" name="discountType" required class="form-select w-full">
                                <option value="">Select discount type</option>
                                <option value="percentage" {{#if (eq coupon.discountType 'percentage')}}selected{{/if}}>Percentage (%)</option>
                                <option value="fixed" {{#if (eq coupon.discountType 'fixed')}}selected{{/if}}>Fixed Amount ($)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="discountValue" class="form-label required">Discount Value</label>
                            <div class="relative">
                                <input type="number" id="discountValue" name="discountValue" required 
                                       value="{{coupon.discountValue}}" 
                                       class="form-input w-full pl-8"
                                       step="0.01" min="0"
                                       placeholder="20">
                                <div id="discountSymbol" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                                    %
                                </div>
                            </div>
                            <p id="discountHelp" class="text-sm text-gray-400 mt-1">Enter the percentage value (0-100)</p>
                        </div>
                    </div>

                    <div class="mt-6">
                        <label for="minimumOrderValue" class="form-label">Minimum Order Value</label>
                        <div class="relative">
                            <input type="number" id="minimumOrderValue" name="minimumOrderValue" 
                                   value="{{coupon.minimumOrderValue}}" 
                                   class="form-input w-full pl-8"
                                   step="0.01" min="0"
                                   placeholder="0.00">
                            <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                                $
                            </div>
                        </div>
                        <p class="text-sm text-gray-400 mt-1">Minimum order value required to use this coupon (optional)</p>
                    </div>
                </div>

                <!-- Usage Limits -->
                <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
                    <h2 class="text-xl font-semibold text-white mb-6">Usage Limits</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="maxUses" class="form-label">Maximum Uses</label>
                            <input type="number" id="maxUses" name="maxUses" 
                                   value="{{coupon.maxUses}}" 
                                   class="form-input w-full"
                                   min="1"
                                   placeholder="100">
                            <p class="text-sm text-gray-400 mt-1">Leave empty for unlimited uses</p>
                        </div>
                        
                        <div>
                            <label for="maxUsesPerUser" class="form-label">Max Uses Per User</label>
                            <input type="number" id="maxUsesPerUser" name="maxUsesPerUser" 
                                   value="{{coupon.maxUsesPerUser}}" 
                                   class="form-input w-full"
                                   min="1"
                                   placeholder="1">
                            <p class="text-sm text-gray-400 mt-1">Maximum times a single user can use this coupon</p>
                        </div>
                    </div>

                    {{#if coupon}}
                    <div class="mt-6">
                        <label class="form-label">Current Usage</label>
                        <div class="bg-gray-700 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-gray-300">Used {{coupon.usedCount}} times</span>
                                {{#if coupon.maxUses}}
                                <span class="text-gray-300">{{coupon.maxUses}} max</span>
                                {{/if}}
                            </div>
                            {{#if coupon.maxUses}}
                            <div class="w-full bg-gray-600 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: {{multiply (divide coupon.usedCount coupon.maxUses) 100}}%"></div>
                            </div>
                            {{/if}}
                        </div>
                    </div>
                    {{/if}}
                </div>

                <!-- Validity Period -->
                <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
                    <h2 class="text-xl font-semibold text-white mb-6">Validity Period</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="validFrom" class="form-label">Valid From</label>
                            <input type="datetime-local" id="validFrom" name="validFrom" 
                                   value="{{formatDateForInput coupon.validFrom}}" 
                                   class="form-input w-full">
                            <p class="text-sm text-gray-400 mt-1">Leave empty to make active immediately</p>
                        </div>
                        
                        <div>
                            <label for="expiryDate" class="form-label">Expiry Date</label>
                            <input type="datetime-local" id="expiryDate" name="expiryDate" 
                                   value="{{formatDateForInput coupon.expiryDate}}" 
                                   class="form-input w-full">
                            <p class="text-sm text-gray-400 mt-1">Leave empty for no expiry</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Status -->
                <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
                    <h2 class="text-xl font-semibold text-white mb-6">Status</h2>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="flex items-center space-x-3 cursor-pointer">
                                <input type="hidden" name="active" value="false">
                                <input type="checkbox" id="active" name="active" value="true"
                                       {{#if (or (not coupon) coupon.active)}}checked{{/if}}
                                       class="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2">
                                <span class="text-white">Active</span>
                            </label>
                            <p class="text-sm text-gray-400 mt-1">When enabled, users can apply this coupon code</p>
                        </div>

                        {{#if coupon}}
                        <div class="pt-4 border-t border-gray-700">
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Created:</span>
                                    <span class="text-white">{{formatDate coupon.createdAt}}</span>
                                </div>
                                {{#if coupon.updatedAt}}
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Updated:</span>
                                    <span class="text-white">{{formatDate coupon.updatedAt}}</span>
                                </div>
                                {{/if}}
                            </div>
                        </div>
                        {{/if}}
                    </div>
                </div>

                <!-- Preview -->
                <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
                    <h2 class="text-xl font-semibold text-white mb-6">Preview</h2>
                    
                    <div id="couponPreview" class="bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-4 text-white">
                        <div class="text-center">
                            <div class="text-2xl font-bold font-mono mb-2" id="previewCode">COUPON CODE</div>
                            <div class="text-lg mb-2" id="previewDiscount">0% OFF</div>
                            <div class="text-sm opacity-90" id="previewDescription">Enter coupon details</div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
                    <h2 class="text-xl font-semibold text-white mb-6">Actions</h2>
                    
                    <div class="space-y-3">
                        <button type="submit" class="w-full btn-primary">
                            {{#if coupon}}Update Coupon{{else}}Create Coupon{{/if}}
                        </button>
                        
                        <a href="/admin/monetization/coupons" class="block w-full btn-secondary text-center">
                            Cancel
                        </a>
                        
                        {{#if coupon}}
                        <button type="button" onclick="confirmDeleteCoupon()" class="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                            Delete Coupon
                        </button>
                        {{/if}}
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

{{#if coupon}}
<!-- Delete Coupon Modal -->
<div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
    <div class="bg-gray-800 border border-red-500/20 rounded-xl p-6 max-w-md w-full mx-4">
        <div class="text-center">
            <svg class="mx-auto h-12 w-12 text-red-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            <h3 class="text-lg font-medium text-white mb-2">Delete Coupon Code</h3>
            <p class="text-gray-400 mb-6">Are you sure you want to delete this coupon code? This action cannot be undone.</p>
            
            <div class="flex space-x-3">
                <button onclick="closeDeleteModal()" class="flex-1 btn-secondary">
                    Cancel
                </button>
                <form action="/admin/monetization/coupons/{{coupon.id}}/delete" method="POST" class="flex-1">
                    <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                        Delete Coupon
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{{/if}}

<script>
// Form validation and dynamic updates
document.addEventListener('DOMContentLoaded', function() {
    const codeInput = document.getElementById('code');
    const discountTypeSelect = document.getElementById('discountType');
    const discountValueInput = document.getElementById('discountValue');
    const descriptionInput = document.getElementById('description');
    
    // Auto-uppercase coupon code
    codeInput.addEventListener('input', function() {
        this.value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
        updatePreview();
    });
    
    // Update discount symbol and validation based on type
    discountTypeSelect.addEventListener('change', function() {
        const symbol = document.getElementById('discountSymbol');
        const help = document.getElementById('discountHelp');
        
        if (this.value === 'percentage') {
            symbol.textContent = '%';
            help.textContent = 'Enter the percentage value (0-100)';
            discountValueInput.max = '100';
            discountValueInput.placeholder = '20';
        } else if (this.value === 'fixed') {
            symbol.textContent = '$';
            help.textContent = 'Enter the fixed discount amount';
            discountValueInput.removeAttribute('max');
            discountValueInput.placeholder = '10.00';
        }
        updatePreview();
    });
    
    // Update preview on input changes
    discountValueInput.addEventListener('input', updatePreview);
    descriptionInput.addEventListener('input', updatePreview);
    
    // Initialize discount type on page load
    if (discountTypeSelect.value) {
        discountTypeSelect.dispatchEvent(new Event('change'));
    }
    
    // Initial preview update
    updatePreview();
});

function updatePreview() {
    const code = document.getElementById('code').value || 'COUPON CODE';
    const discountType = document.getElementById('discountType').value;
    const discountValue = document.getElementById('discountValue').value;
    const description = document.getElementById('description').value || 'Enter coupon details';
    
    document.getElementById('previewCode').textContent = code;
    
    let discountText = '0% OFF';
    if (discountValue && discountType) {
        if (discountType === 'percentage') {
            discountText = `${discountValue}% OFF`;
        } else {
            discountText = `$${discountValue} OFF`;
        }
    }
    
    document.getElementById('previewDiscount').textContent = discountText;
    document.getElementById('previewDescription').textContent = description;
}

// Form validation
document.getElementById('couponForm').addEventListener('submit', function(e) {
    const code = document.getElementById('code').value;
    const discountType = document.getElementById('discountType').value;
    const discountValue = parseFloat(document.getElementById('discountValue').value);
    
    // Validate coupon code
    if (!code || code.length < 3) {
        e.preventDefault();
        alert('Coupon code must be at least 3 characters long');
        return;
    }
    
    // Validate discount value
    if (!discountValue || discountValue <= 0) {
        e.preventDefault();
        alert('Discount value must be greater than 0');
        return;
    }
    
    if (discountType === 'percentage' && discountValue > 100) {
        e.preventDefault();
        alert('Percentage discount cannot be more than 100%');
        return;
    }
    
    // Validate dates
    const validFrom = document.getElementById('validFrom').value;
    const expiryDate = document.getElementById('expiryDate').value;
    
    if (validFrom && expiryDate && new Date(validFrom) >= new Date(expiryDate)) {
        e.preventDefault();
        alert('Expiry date must be after the valid from date');
        return;
    }
});

{{#if coupon}}
function confirmDeleteCoupon() {
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteModal();
    }
});
{{/if}}
</script>
