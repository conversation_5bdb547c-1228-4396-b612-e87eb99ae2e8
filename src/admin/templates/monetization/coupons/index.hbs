<div class="space-y-8">
    <div class="flex items-center justify-between">
        <h1 class="text-3xl font-bold gradient-text">Coupon Codes</h1>
        <div class="flex items-center space-x-3">
            <a href="/admin/monetization/coupons/new" class="btn-primary px-6 py-3 rounded-lg font-medium flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                Create New Coupon
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">Total Coupons</p>
                    <p class="text-2xl font-bold text-blue-400">{{coupons.length}}</p>
                </div>
                <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2zM10 8.5a.5.5 0 11-1 0 .5.5 0 011 0zm5 5a.5.5 0 11-1 0 .5.5 0 011 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 border border-green-500/20 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">Active Coupons</p>
                    <p class="text-2xl font-bold text-green-400">{{#each coupons}}{{#if this.active}}{{@index}}{{/if}}{{/each}}</p>
                </div>
                <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 border border-purple-500/20 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">Used Coupons</p>
                    <p class="text-2xl font-bold text-purple-400">{{#each coupons}}{{this.usedCount}}{{/each}}</p>
                </div>
                <div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 border border-yellow-500/20 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">Expired Coupons</p>
                    <p class="text-2xl font-bold text-yellow-400">{{#each coupons}}{{#if (and (not this.active) this.expiryDate)}}{{@index}}{{/if}}{{/each}}</p>
                </div>
                <div class="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div class="flex items-center space-x-4">
                <div>
                    <label for="statusFilter" class="form-label">Filter by Status</label>
                    <select id="statusFilter" class="form-select bg-gray-700 border-gray-600 text-white rounded-lg px-3 py-2">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="expired">Expired</option>
                    </select>
                </div>
                <div>
                    <label for="typeFilter" class="form-label">Filter by Type</label>
                    <select id="typeFilter" class="form-select bg-gray-700 border-gray-600 text-white rounded-lg px-3 py-2">
                        <option value="">All Types</option>
                        <option value="percentage">Percentage</option>
                        <option value="fixed">Fixed Amount</option>
                    </select>
                </div>
                <div>
                    <label for="searchInput" class="form-label">Search</label>
                    <input type="text" id="searchInput" placeholder="Search by code or description..." class="form-input bg-gray-700 border-gray-600 text-white rounded-lg px-3 py-2">
                </div>
            </div>
        </div>
    </div>

    <!-- Coupons Table -->
    <div class="bg-gray-800 border border-blue-500/20 rounded-xl overflow-hidden">
        <div class="px-6 py-4 bg-gray-700/50 border-b border-gray-700">
            <h2 class="text-lg font-semibold text-white">All Coupon Codes</h2>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-blue-500/10">
                    <tr>
                        <th class="px-6 py-4 text-left text-xs font-medium text-blue-400 uppercase tracking-wider">Code</th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-blue-400 uppercase tracking-wider">Type</th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-blue-400 uppercase tracking-wider">Discount</th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-blue-400 uppercase tracking-wider">Usage</th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-blue-400 uppercase tracking-wider">Expiry Date</th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-blue-400 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-blue-400 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-700" id="couponsTableBody">
                    {{#each coupons}}
                    <tr class="coupon-row hover:bg-gray-700/30 transition-colors" 
                        data-status="{{#if this.active}}active{{else}}inactive{{/if}}" 
                        data-type="{{this.discountType}}" 
                        data-code="{{this.code}}" 
                        data-description="{{this.description}}">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-lg bg-purple-500/20 flex items-center justify-center">
                                        <svg class="w-5 h-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2zM10 8.5a.5.5 0 11-1 0 .5.5 0 011 0zm5 5a.5.5 0 11-1 0 .5.5 0 011 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-bold text-white font-mono">{{this.code}}</div>
                                    <div class="text-sm text-gray-400">{{this.description}}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {{#if (eq this.discountType 'percentage')}}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Percentage
                            </span>
                            {{else}}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Fixed Amount
                            </span>
                            {{/if}}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white font-semibold">
                            {{#if (eq this.discountType 'percentage')}}
                            {{this.discountValue}}%
                            {{else}}
                            ${{this.discountValue}}
                            {{/if}}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">
                                {{this.usedCount}}{{#if this.maxUses}} / {{this.maxUses}}{{/if}}
                            </div>
                            {{#if this.maxUses}}
                            <div class="w-full bg-gray-700 rounded-full h-1.5 mt-1">
                                <div class="bg-blue-500 h-1.5 rounded-full" style="width: {{multiply (divide this.usedCount this.maxUses) 100}}%"></div>
                            </div>
                            {{/if}}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                            {{#if this.expiryDate}}
                            {{formatDate this.expiryDate}}
                            {{else}}
                            <span class="text-gray-500">No expiry</span>
                            {{/if}}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {{#if this.active}}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Active
                            </span>
                            {{else}}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Inactive
                            </span>
                            {{/if}}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <a href="/admin/monetization/coupons/{{this.id}}/edit" class="text-blue-400 hover:text-blue-300 transition-colors">
                                Edit
                            </a>
                            <button onclick="confirmDeleteCoupon('{{this.id}}', '{{this.code}}')" class="text-red-400 hover:text-red-300 transition-colors">
                                Delete
                            </button>
                            {{#if this.active}}
                            <button onclick="toggleCouponStatus('{{this.id}}', false)" class="text-yellow-400 hover:text-yellow-300 transition-colors">
                                Deactivate
                            </button>
                            {{else}}
                            <button onclick="toggleCouponStatus('{{this.id}}', true)" class="text-green-400 hover:text-green-300 transition-colors">
                                Activate
                            </button>
                            {{/if}}
                        </td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Delete Coupon Modal -->
<div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
    <div class="bg-gray-800 border border-red-500/20 rounded-xl p-6 max-w-md w-full mx-4">
        <div class="text-center">
            <svg class="mx-auto h-12 w-12 text-red-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            <h3 class="text-lg font-medium text-white mb-2">Delete Coupon Code</h3>
            <p class="text-gray-400 mb-6">Are you sure you want to delete the coupon code "<span id="deleteCouponCode" class="font-mono text-white"></span>"? This action cannot be undone.</p>
            
            <div class="flex space-x-3">
                <button onclick="closeDeleteModal()" class="flex-1 btn-secondary">
                    Cancel
                </button>
                <form id="deleteForm" method="POST" class="flex-1">
                    <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                        Delete Coupon
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Filtering functionality
document.getElementById('statusFilter').addEventListener('change', filterCoupons);
document.getElementById('typeFilter').addEventListener('change', filterCoupons);
document.getElementById('searchInput').addEventListener('input', filterCoupons);

function filterCoupons() {
    const statusFilter = document.getElementById('statusFilter').value;
    const typeFilter = document.getElementById('typeFilter').value;
    const searchInput = document.getElementById('searchInput').value.toLowerCase();
    
    const rows = document.querySelectorAll('.coupon-row');
    
    rows.forEach(row => {
        const status = row.dataset.status;
        const type = row.dataset.type;
        const code = row.dataset.code.toLowerCase();
        const description = row.dataset.description.toLowerCase();
        
        const statusMatch = !statusFilter || status === statusFilter;
        const typeMatch = !typeFilter || type === typeFilter;
        const searchMatch = !searchInput || code.includes(searchInput) || description.includes(searchInput);
        
        if (statusMatch && typeMatch && searchMatch) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// Delete coupon modal
function confirmDeleteCoupon(couponId, couponCode) {
    document.getElementById('deleteForm').action = `/admin/monetization/coupons/${couponId}/delete`;
    document.getElementById('deleteCouponCode').textContent = couponCode;
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}

// Toggle coupon status
function toggleCouponStatus(couponId, activate) {
    const action = activate ? 'activate' : 'deactivate';
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/admin/monetization/coupons/${couponId}/${action}`;
    document.body.appendChild(form);
    form.submit();
}

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteModal();
    }
});
</script>
