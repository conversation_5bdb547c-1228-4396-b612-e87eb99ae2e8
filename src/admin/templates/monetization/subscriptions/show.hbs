<div class="space-y-8">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <a href="/admin/monetization/subscriptions" class="text-gray-400 hover:text-white transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </a>
            <h1 class="text-3xl font-bold gradient-text">Subscription Details</h1>
        </div>
        <div class="flex items-center space-x-3">
            {{#if (eq subscription.status 'active')}}
            <button onclick="confirmCancelSubscription()" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                Cancel Subscription
            </button>
            {{/if}}
        </div>
    </div>

    <!-- Subscription Overview -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Subscription Details -->
        <div class="lg:col-span-2 space-y-6">
            <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
                <h2 class="text-xl font-semibold text-white mb-6">Subscription Information</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="form-label">Subscription ID</label>
                        <p class="text-white font-mono text-sm bg-gray-700 rounded-lg px-3 py-2">{{subscription.id}}</p>
                    </div>
                    
                    <div>
                        <label class="form-label">Status</label>
                        <div class="flex items-center space-x-2">
                            {{#if (eq subscription.status 'active')}}
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Active
                            </span>
                            {{else if (eq subscription.status 'expired')}}
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                Expired
                            </span>
                            {{else if (eq subscription.status 'cancelled')}}
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                </svg>
                                Cancelled
                            </span>
                            {{else}}
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                                {{subscription.status}}
                            </span>
                            {{/if}}
                        </div>
                    </div>
                    
                    <div>
                        <label class="form-label">Start Date</label>
                        <p class="text-white">{{formatDate subscription.startDate}}</p>
                    </div>
                    
                    <div>
                        <label class="form-label">End Date</label>
                        <p class="text-white">{{formatDate subscription.endDate}}</p>
                    </div>
                    
                    <div>
                        <label class="form-label">Price</label>
                        <p class="text-white text-lg font-semibold">${{subscription.price}}</p>
                    </div>
                    
                    <div>
                        <label class="form-label">Created At</label>
                        <p class="text-gray-300">{{formatDate subscription.createdAt}}</p>
                    </div>
                </div>
            </div>

            <!-- User Information -->
            <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
                <h2 class="text-xl font-semibold text-white mb-6">User Information</h2>
                
                <div class="flex items-center space-x-4 mb-4">
                    <div class="h-16 w-16 rounded-full bg-blue-500/20 flex items-center justify-center">
                        <span class="text-blue-400 font-bold text-xl">{{substr subscription.user.email 0 2}}</span>
                    </div>
                    <div>
                        <h3 class="text-white font-semibold">{{subscription.user.name}}</h3>
                        <p class="text-gray-400">{{subscription.user.email}}</p>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="form-label">User ID</label>
                        <p class="text-white font-mono text-sm">{{subscription.user.id}}</p>
                    </div>
                    <div>
                        <label class="form-label">Registration Date</label>
                        <p class="text-gray-300">{{formatDate subscription.user.createdAt}}</p>
                    </div>
                </div>
            </div>

            <!-- Payment History -->
            {{#if subscription.payments}}
            <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
                <h2 class="text-xl font-semibold text-white mb-6">Payment History</h2>
                
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-blue-500/10">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-blue-400 uppercase tracking-wider">Date</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-blue-400 uppercase tracking-wider">Amount</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-blue-400 uppercase tracking-wider">Status</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-blue-400 uppercase tracking-wider">Transaction ID</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-700">
                            {{#each subscription.payments}}
                            <tr class="hover:bg-gray-700/30">
                                <td class="px-4 py-3 text-sm text-gray-300">{{formatDate this.createdAt}}</td>
                                <td class="px-4 py-3 text-sm text-white font-medium">${{this.amount}}</td>
                                <td class="px-4 py-3 text-sm">
                                    {{#if (eq this.status 'completed')}}
                                    <span class="text-green-400">Completed</span>
                                    {{else if (eq this.status 'pending')}}
                                    <span class="text-yellow-400">Pending</span>
                                    {{else}}
                                    <span class="text-red-400">Failed</span>
                                    {{/if}}
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-300 font-mono">{{this.transactionId}}</td>
                            </tr>
                            {{/each}}
                        </tbody>
                    </table>
                </div>
            </div>
            {{/if}}
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Plan Details -->
            <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
                <h2 class="text-xl font-semibold text-white mb-6">Plan Details</h2>
                
                <div class="space-y-4">
                    <div>
                        <label class="form-label">Plan Name</label>
                        <p class="text-white font-semibold">{{subscription.plan.name}}</p>
                    </div>
                    
                    <div>
                        <label class="form-label">Billing Cycle</label>
                        <p class="text-gray-300">{{subscription.plan.billingCycle}}</p>
                    </div>
                    
                    <div>
                        <label class="form-label">Plan Price</label>
                        <p class="text-white font-semibold">${{subscription.plan.price}}</p>
                    </div>
                    
                    <div>
                        <label class="form-label">Features</label>
                        <ul class="text-gray-300 space-y-1">
                            {{#each subscription.plan.features}}
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                {{this}}
                            </li>
                            {{/each}}
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
                <h2 class="text-xl font-semibold text-white mb-6">Quick Actions</h2>
                
                <div class="space-y-3">
                    <a href="/admin/monetization/subscriptions" class="block w-full btn-secondary text-center">
                        Back to Subscriptions
                    </a>
                    
                    {{#if (eq subscription.status 'active')}}
                    <button onclick="confirmCancelSubscription()" class="block w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                        Cancel Subscription
                    </button>
                    {{/if}}
                    
                    <a href="/admin/monetization/plans/{{subscription.plan.id}}/edit" class="block w-full btn-secondary text-center">
                        Edit Plan
                    </a>
                </div>
            </div>

            <!-- Subscription Timeline -->
            <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
                <h2 class="text-xl font-semibold text-white mb-6">Timeline</h2>
                
                <div class="space-y-4">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-white font-medium">Subscription Created</p>
                            <p class="text-gray-400 text-sm">{{formatDate subscription.createdAt}}</p>
                        </div>
                    </div>
                    
                    {{#if subscription.startDate}}
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-white font-medium">Subscription Started</p>
                            <p class="text-gray-400 text-sm">{{formatDate subscription.startDate}}</p>
                        </div>
                    </div>
                    {{/if}}
                    
                    {{#if subscription.endDate}}
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-yellow-500/20 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-white font-medium">{{#if (eq subscription.status 'active')}}Will Expire{{else}}Expired{{/if}}</p>
                            <p class="text-gray-400 text-sm">{{formatDate subscription.endDate}}</p>
                        </div>
                    </div>
                    {{/if}}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cancel Subscription Modal -->
<div id="cancelModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
    <div class="bg-gray-800 border border-red-500/20 rounded-xl p-6 max-w-md w-full mx-4">
        <div class="text-center">
            <svg class="mx-auto h-12 w-12 text-red-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
            <h3 class="text-lg font-medium text-white mb-2">Cancel Subscription</h3>
            <p class="text-gray-400 mb-6">Are you sure you want to cancel this subscription? This action cannot be undone.</p>
            
            <div class="flex space-x-3">
                <button onclick="closeCancelModal()" class="flex-1 btn-secondary">
                    Cancel
                </button>
                <form action="/admin/monetization/subscriptions/{{subscription.id}}/cancel" method="POST" class="flex-1">
                    <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                        Confirm Cancel
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmCancelSubscription() {
    document.getElementById('cancelModal').classList.remove('hidden');
}

function closeCancelModal() {
    document.getElementById('cancelModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('cancelModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeCancelModal();
    }
});
</script>
