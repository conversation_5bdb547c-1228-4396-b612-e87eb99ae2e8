<div class="space-y-8">
    <div class="flex items-center justify-between">
        <h1 class="text-3xl font-bold gradient-text">Subscriptions</h1>
        <div class="text-sm text-gray-400">
            Manage all user subscriptions
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">Total Subscriptions</p>
                    <p class="text-2xl font-bold text-blue-400">{{subscriptions.length}}</p>
                </div>
                <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 border border-green-500/20 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">Active Subscriptions</p>
                    <p class="text-2xl font-bold text-green-400">{{#each subscriptions}}{{#if (eq this.status 'active')}}{{@index}}{{/if}}{{/each}}</p>
                </div>
                <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 border border-yellow-500/20 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">Expired Subscriptions</p>
                    <p class="text-2xl font-bold text-yellow-400">{{#each subscriptions}}{{#if (eq this.status 'expired')}}{{@index}}{{/if}}{{/each}}</p>
                </div>
                <div class="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 border border-red-500/20 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">Cancelled Subscriptions</p>
                    <p class="text-2xl font-bold text-red-400">{{#each subscriptions}}{{#if (eq this.status 'cancelled')}}{{@index}}{{/if}}{{/each}}</p>
                </div>
                <div class="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Actions -->
    <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div class="flex items-center space-x-4">
                <div>
                    <label for="statusFilter" class="form-label">Filter by Status</label>
                    <select id="statusFilter" class="form-select bg-gray-700 border-gray-600 text-white rounded-lg px-3 py-2">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="expired">Expired</option>
                        <option value="cancelled">Cancelled</option>
                        <option value="pending">Pending</option>
                    </select>
                </div>
                <div>
                    <label for="planFilter" class="form-label">Filter by Plan</label>
                    <select id="planFilter" class="form-select bg-gray-700 border-gray-600 text-white rounded-lg px-3 py-2">
                        <option value="">All Plans</option>
                        {{#each subscriptions}}
                        <option value="{{this.plan.id}}">{{this.plan.name}}</option>
                        {{/each}}
                    </select>
                </div>
                <div>
                    <label for="searchInput" class="form-label">Search</label>
                    <input type="text" id="searchInput" placeholder="Search by user email..." class="form-input bg-gray-700 border-gray-600 text-white rounded-lg px-3 py-2">
                </div>
            </div>
        </div>
    </div>

    <!-- Subscriptions Table -->
    <div class="bg-gray-800 border border-blue-500/20 rounded-xl overflow-hidden">
        <div class="px-6 py-4 bg-gray-700/50 border-b border-gray-700">
            <h2 class="text-lg font-semibold text-white">All Subscriptions</h2>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-blue-500/10">
                    <tr>
                        <th class="px-6 py-4 text-left text-xs font-medium text-blue-400 uppercase tracking-wider">User</th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-blue-400 uppercase tracking-wider">Plan</th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-blue-400 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-blue-400 uppercase tracking-wider">Start Date</th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-blue-400 uppercase tracking-wider">End Date</th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-blue-400 uppercase tracking-wider">Price</th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-blue-400 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-700" id="subscriptionsTableBody">
                    {{#each subscriptions}}
                    <tr class="subscription-row hover:bg-gray-700/30 transition-colors" data-status="{{this.status}}" data-plan="{{this.plan.id}}" data-user="{{this.user.email}}">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                                        <span class="text-blue-400 font-medium text-sm">{{substr this.user.email 0 2}}</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-white">{{this.user.email}}</div>
                                    <div class="text-sm text-gray-400">{{this.user.name}}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-white">{{this.plan.name}}</div>
                            <div class="text-sm text-gray-400">{{this.plan.billingCycle}}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {{#if (eq this.status 'active')}}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Active
                            </span>
                            {{else if (eq this.status 'expired')}}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                Expired
                            </span>
                            {{else if (eq this.status 'cancelled')}}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Cancelled
                            </span>
                            {{else}}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                {{this.status}}
                            </span>
                            {{/if}}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                            {{formatDate this.startDate}}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                            {{formatDate this.endDate}}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white font-medium">
                            ${{this.price}}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <a href="/admin/monetization/subscriptions/{{this.id}}" class="text-blue-400 hover:text-blue-300 transition-colors">
                                View
                            </a>
                            {{#if (eq this.status 'active')}}
                            <button onclick="confirmCancelSubscription('{{this.id}}')" class="text-red-400 hover:text-red-300 transition-colors">
                                Cancel
                            </button>
                            {{/if}}
                        </td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Cancel Subscription Modal -->
<div id="cancelModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
    <div class="bg-gray-800 border border-red-500/20 rounded-xl p-6 max-w-md w-full mx-4">
        <div class="text-center">
            <svg class="mx-auto h-12 w-12 text-red-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
            <h3 class="text-lg font-medium text-white mb-2">Cancel Subscription</h3>
            <p class="text-gray-400 mb-6">Are you sure you want to cancel this subscription? This action cannot be undone.</p>
            
            <div class="flex space-x-3">
                <button onclick="closeCancelModal()" class="flex-1 btn-secondary">
                    Cancel
                </button>
                <form id="cancelForm" method="POST" class="flex-1">
                    <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                        Confirm Cancel
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Filtering functionality
document.getElementById('statusFilter').addEventListener('change', filterSubscriptions);
document.getElementById('planFilter').addEventListener('change', filterSubscriptions);
document.getElementById('searchInput').addEventListener('input', filterSubscriptions);

function filterSubscriptions() {
    const statusFilter = document.getElementById('statusFilter').value;
    const planFilter = document.getElementById('planFilter').value;
    const searchInput = document.getElementById('searchInput').value.toLowerCase();
    
    const rows = document.querySelectorAll('.subscription-row');
    
    rows.forEach(row => {
        const status = row.dataset.status;
        const plan = row.dataset.plan;
        const user = row.dataset.user.toLowerCase();
        
        const statusMatch = !statusFilter || status === statusFilter;
        const planMatch = !planFilter || plan === planFilter;
        const searchMatch = !searchInput || user.includes(searchInput);
        
        if (statusMatch && planMatch && searchMatch) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// Cancel subscription modal
function confirmCancelSubscription(subscriptionId) {
    document.getElementById('cancelForm').action = `/admin/monetization/subscriptions/${subscriptionId}/cancel`;
    document.getElementById('cancelModal').classList.remove('hidden');
}

function closeCancelModal() {
    document.getElementById('cancelModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('cancelModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeCancelModal();
    }
});
</script>
