<div class="space-y-8">
    <div class="flex items-center justify-between">
        <h1 class="text-3xl font-bold gradient-text">Monetization Analytics</h1>
        <div class="text-sm text-gray-400">
            Real-time monetization insights and performance metrics
        </div>
    </div>

    <!-- Key Metrics Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">Total Revenue</p>
                    <p class="text-2xl font-bold text-blue-400">${{stats.totalRevenue}}</p>
                </div>
                <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 border border-green-500/20 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">Active Subscriptions</p>
                    <p class="text-2xl font-bold text-green-400">{{stats.activeSubscriptions}}</p>
                </div>
                <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 border border-purple-500/20 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">Total Plans</p>
                    <p class="text-2xl font-bold text-purple-400">{{stats.totalPlans}}</p>
                </div>
                <div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 border border-yellow-500/20 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">Active Coupons</p>
                    <p class="text-2xl font-bold text-yellow-400">{{stats.activeCoupons}}</p>
                </div>
                <div class="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2zM10 8.5a.5.5 0 11-1 0 .5.5 0 011 0zm5 5a.5.5 0 11-1 0 .5.5 0 011 0z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Revenue Chart Placeholder -->
        <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
            <h2 class="text-xl font-semibold text-white mb-6">Revenue Trend</h2>
            
            <div class="bg-gray-700 rounded-lg p-8 text-center">
                <svg class="w-16 h-16 text-gray-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2-2z"></path>
                </svg>
                <p class="text-gray-400">Revenue chart integration</p>
                <p class="text-sm text-gray-500 mt-2">Connect with Chart.js or similar library</p>
            </div>
        </div>

        <!-- Subscription Status Breakdown -->
        <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
            <h2 class="text-xl font-semibold text-white mb-6">Subscription Status</h2>
            
            <div class="space-y-4">
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                        <span class="text-white">Active</span>
                    </div>
                    <span class="text-green-400 font-bold">{{stats.activeSubscriptions}}</span>
                </div>
                
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                        <span class="text-white">Expired</span>
                    </div>
                    <span class="text-yellow-400 font-bold">{{stats.expiredSubscriptions}}</span>
                </div>
                
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                        <span class="text-white">Cancelled</span>
                    </div>
                    <span class="text-red-400 font-bold">{{stats.cancelledSubscriptions}}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Plan Performance -->
    <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
        <h2 class="text-xl font-semibold text-white mb-6">Plan Performance</h2>
        
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-blue-500/10">
                    <tr>
                        <th class="px-6 py-4 text-left text-xs font-medium text-blue-400 uppercase tracking-wider">Plan Name</th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-blue-400 uppercase tracking-wider">Price</th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-blue-400 uppercase tracking-wider">Active Subscriptions</th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-blue-400 uppercase tracking-wider">Monthly Revenue</th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-blue-400 uppercase tracking-wider">Conversion Rate</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-700">
                    {{#each planPerformance}}
                    <tr class="hover:bg-gray-700/30">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-white">{{this.name}}</div>
                            <div class="text-sm text-gray-400">{{this.type}}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white font-semibold">${{this.price}}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{this.activeSubscriptions}}</div>
                            {{#if this.monthlyGrowth}}
                            <div class="text-sm text-green-400">+{{this.monthlyGrowth}} this month</div>
                            {{/if}}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white font-semibold">${{this.monthlyRevenue}}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {{#if (gte this.conversionRate 10)}}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {{this.conversionRate}}%
                            </span>
                            {{else if (gte this.conversionRate 5)}}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{this.conversionRate}}%
                            </span>
                            {{else}}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                {{this.conversionRate}}%
                            </span>
                            {{/if}}
                        </td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Coupon Usage Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
            <h2 class="text-xl font-semibold text-white mb-6">Top Performing Coupons</h2>
            
            <div class="space-y-4">
                {{#each topCoupons}}
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <div>
                        <div class="text-white font-mono font-bold">{{this.code}}</div>
                        <div class="text-sm text-gray-400">
                            {{#if (eq this.type 'percentage')}}
                            {{this.value}}% off
                            {{else}}
                            ${{this.value}} off
                            {{/if}}
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-white font-semibold">{{this.usedCount}} uses</div>
                        <div class="text-sm text-green-400">${{this.totalSaved}} saved</div>
                    </div>
                </div>
                {{/each}}
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
            <h2 class="text-xl font-semibold text-white mb-6">Recent Activity</h2>
            
            <div class="space-y-4">
                {{#each recentActivity}}
                <div class="flex items-center space-x-3 p-3 bg-gray-700 rounded-lg">
                    <div class="w-8 h-8 bg-{{this.color}}-500/20 rounded-full flex items-center justify-center">
                        {{#if (eq this.type 'subscription')}}
                        <svg class="w-4 h-4 text-{{this.color}}-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        {{else if (eq this.type 'coupon')}}
                        <svg class="w-4 h-4 text-{{this.color}}-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2zM10 8.5a.5.5 0 11-1 0 .5.5 0 011 0zm5 5a.5.5 0 11-1 0 .5.5 0 011 0z"></path>
                        </svg>
                        {{else if (eq this.type 'payment')}}
                        <svg class="w-4 h-4 text-{{this.color}}-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                        {{else if (eq this.type 'expiry')}}
                        <svg class="w-4 h-4 text-{{this.color}}-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                        </svg>
                        {{/if}}
                    </div>
                    <div class="flex-1">
                        <div class="text-white text-sm">{{this.message}}</div>
                        <div class="text-gray-400 text-xs">{{this.timeAgo}}</div>
                    </div>
                </div>
                {{/each}}
            </div>
        </div>
    </div>

    <!-- Export Options -->
    <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
        <h2 class="text-xl font-semibold text-white mb-6">Export Analytics</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <a href="/admin/monetization/analytics/export/csv" class="btn-secondary px-6 py-3 rounded-lg font-medium flex items-center justify-center" download="monetization-analytics.csv">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Export CSV
            </a>
            
            <a href="/admin/monetization/analytics/export/report" class="btn-secondary px-6 py-3 rounded-lg font-medium flex items-center justify-center" target="_blank">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a4 4 0 01-4-4V5a4 4 0 714-4h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a4 4 0 01-4 4z"></path>
                </svg>
                Generate Report
            </a>
        </div>
    </div>
</div>

<script>
// Add any interactive features for analytics
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh analytics every 5 minutes
    setInterval(function() {
        // Could implement AJAX refresh of key metrics
        console.log('Analytics refresh interval');
    }, 300000);
});
</script>
