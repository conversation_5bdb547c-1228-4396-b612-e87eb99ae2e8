<div class="space-y-8">
    <div class="flex items-center justify-between">
        <h1 class="text-3xl font-bold gradient-text">Subscription Plans</h1>
        <a href="/admin/monetization/plans/new" class="btn-primary px-6 py-3 rounded-lg font-medium">
            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Create New Plan
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">Total Plans</p>
                    <p class="text-2xl font-bold text-blue-400">{{plans.length}}</p>
                </div>
                <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 border border-green-500/20 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">Active Plans</p>
                    <p class="text-2xl font-bold text-green-400">{{activePlansCount}}</p>
                </div>
                <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 border border-purple-500/20 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">iOS Plans</p>
                    <p class="text-2xl font-bold text-purple-400">{{iosPlansCount}}</p>
                </div>
                <div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 border border-yellow-500/20 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">Android Plans</p>
                    <p class="text-2xl font-bold text-yellow-400">{{androidPlansCount}}</p>
                </div>
                <div class="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Plans Table -->
    <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-bold text-white">All Subscription Plans</h2>
            <div class="text-sm text-gray-400">
                {{plans.length}} total plans
            </div>
        </div>

        {{#if plans}}
        <div class="overflow-x-auto">
            <table class="w-full admin-table">
                <thead>
                    <tr class="text-left border-b border-gray-700">
                        <th class="pb-3 px-4 text-purple-400">Name</th>
                        <th class="pb-3 px-4 text-purple-400">Type</th>
                        <th class="pb-3 px-4 text-purple-400">Price</th>
                        <th class="pb-3 px-4 text-purple-400">Platform</th>
                        <th class="pb-3 px-4 text-purple-400">Trial</th>
                        <th class="pb-3 px-4 text-purple-400">Status</th>
                        <th class="pb-3 px-4 text-purple-400">Actions</th>
                    </tr>
                </thead>
                <tbody class="text-gray-300">
                    {{#each plans}}
                    <tr class="border-b border-gray-700 hover:bg-gray-700/30">
                        <td class="py-4 px-4">
                            <div>
                                <div class="font-medium text-white">{{name}}</div>
                                {{#if description}}
                                <div class="text-sm text-gray-400">{{truncate description 50}}</div>
                                {{/if}}
                            </div>
                        </td>
                        <td class="py-4 px-4">
                            <span class="px-2 py-1 bg-indigo-500/20 text-indigo-400 rounded text-xs capitalize">{{type}}</span>
                        </td>
                        <td class="py-4 px-4">
                            <span class="text-lg font-bold text-green-400">{{currency}} {{price}}</span>
                        </td>
                        <td class="py-4 px-4">
                            <span class="px-2 py-1 bg-blue-500/20 text-blue-400 rounded text-xs uppercase">{{platform}}</span>
                        </td>
                        <td class="py-4 px-4">
                            {{#if freeTrialDays}}
                                <span class="px-2 py-1 bg-orange-500/20 text-orange-400 rounded text-xs">{{freeTrialDays}} days</span>
                            {{else}}
                                <span class="text-gray-500">No trial</span>
                            {{/if}}
                        </td>
                        <td class="py-4 px-4">
                            {{#if active}}
                                <span class="px-2 py-1 bg-green-500/20 text-green-400 rounded text-xs">Active</span>
                            {{else}}
                                <span class="px-2 py-1 bg-red-500/20 text-red-400 rounded text-xs">Inactive</span>
                            {{/if}}
                        </td>
                        <td class="py-4 px-4">
                            <div class="flex space-x-3">
                                <a href="/admin/monetization/plans/{{id}}/edit" class="text-blue-400 hover:text-blue-300 transition-colors" title="Edit">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </a>
                                <form method="POST" action="/admin/monetization/plans/{{id}}/delete" class="inline" onsubmit="return confirm('Are you sure you want to delete this plan?')">
                                    <button type="submit" class="text-red-400 hover:text-red-300 transition-colors" title="Delete">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>
        </div>
        {{else}}
        <div class="text-center py-12">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <h3 class="text-lg font-medium text-gray-300 mb-2">No subscription plans found</h3>
            <p class="text-gray-400 mb-4">Get started by creating your first subscription plan.</p>
            <a href="/admin/monetization/plans/new" class="btn-primary px-6 py-3 rounded-lg font-medium">
                Create New Plan
            </a>
        </div>
        {{/if}}
    </div>
</div>

<script>
// Count calculations for the statistics
document.addEventListener('DOMContentLoaded', function() {
    const plans = {{{json plans}}};
    if (plans) {
        const activePlans = plans.filter(p => p.active);
        const iosPlans = plans.filter(p => p.platform === 'ios');
        const androidPlans = plans.filter(p => p.platform === 'android');
        
        // Update counts if elements exist
        const activePlansEl = document.querySelector('.text-green-400');
        const iosPlansEl = document.querySelectorAll('.text-purple-400')[1];
        const androidPlansEl = document.querySelectorAll('.text-yellow-400')[1];
        
        if (activePlansEl) activePlansEl.textContent = activePlans.length;
        if (iosPlansEl) iosPlansEl.textContent = iosPlans.length;
        if (androidPlansEl) androidPlansEl.textContent = androidPlans.length;
    }
});
</script>
