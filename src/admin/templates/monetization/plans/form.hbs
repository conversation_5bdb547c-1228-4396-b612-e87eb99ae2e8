<div class="space-y-8">
    <div class="flex items-center justify-between">
        <h1 class="text-3xl font-bold gradient-text">
            {{#if isNew}}
                Create New Subscription Plan
            {{else}}
                Edit Subscription Plan
            {{/if}}
        </h1>
        <a href="/admin/monetization/plans" class="btn-secondary px-6 py-3 rounded-lg font-medium">
            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Plans
        </a>
    </div>

    <!-- Form -->
    <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
        <form method="POST" action="{{#if isNew}}/admin/monetization/plans{{else}}/admin/monetization/plans/{{plan.id}}{{/if}}" class="space-y-6">
            {{#unless isNew}}
            <input type="hidden" name="_method" value="PUT">
            {{/unless}}
            
            <!-- Basic Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="name" class="form-label block text-sm font-medium mb-2">Plan Name *</label>
                    <input type="text" id="name" name="name" value="{{#if plan}}{{plan.name}}{{/if}}" required
                           class="form-input w-full px-4 py-3 rounded-lg focus:ring-2 focus:ring-blue-500"
                           placeholder="e.g., Premium Monthly">
                </div>

                <div>
                    <label for="price" class="form-label block text-sm font-medium mb-2">Price *</label>
                    <input type="number" id="price" name="price" value="{{#if plan}}{{plan.price}}{{/if}}" step="0.01" min="0" required
                           class="form-input w-full px-4 py-3 rounded-lg focus:ring-2 focus:ring-blue-500"
                           placeholder="9.99">
                </div>
            </div>

            <!-- Plan Type and Platform -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="type" class="form-label block text-sm font-medium mb-2">Plan Type *</label>
                    <select id="type" name="type" required
                            class="form-select w-full px-4 py-3 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="">Select plan type</option>
                        <option value="weekly" {{#if plan}}{{#eq plan.type 'weekly'}}selected{{/eq}}{{/if}}>Weekly</option>
                        <option value="monthly" {{#if plan}}{{#eq plan.type 'monthly'}}selected{{/eq}}{{/if}}>Monthly</option>
                        <option value="yearly" {{#if plan}}{{#eq plan.type 'yearly'}}selected{{/eq}}{{/if}}>Yearly</option>
                    </select>
                </div>

                <div>
                    <label for="platform" class="form-label block text-sm font-medium mb-2">Platform *</label>
                    <select id="platform" name="platform" required
                            class="form-select w-full px-4 py-3 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="">Select platform</option>
                        <option value="ios" {{#if plan}}{{#eq plan.platform 'ios'}}selected{{/eq}}{{/if}}>iOS (App Store)</option>
                        <option value="android" {{#if plan}}{{#eq plan.platform 'android'}}selected{{/eq}}{{/if}}>Android (Google Play)</option>
                        <option value="web" {{#if plan}}{{#eq plan.platform 'web'}}selected{{/eq}}{{/if}}>Web</option>
                    </select>
                </div>
            </div>

            <!-- Platform-specific IDs -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="productId" class="form-label block text-sm font-medium mb-2">Product ID *</label>
                    <input type="text" id="productId" name="productId" value="{{#if plan}}{{plan.productId}}{{/if}}" required
                           class="form-input w-full px-4 py-3 rounded-lg focus:ring-2 focus:ring-blue-500"
                           placeholder="Platform-specific product ID">
                    <p class="text-xs text-gray-400 mt-1">
                        <span id="productIdHelp">Platform-specific product identifier</span>
                    </p>
                </div>

                <div>
                    <label for="currency" class="form-label block text-sm font-medium mb-2">Currency</label>
                    <select id="currency" name="currency"
                            class="form-select w-full px-4 py-3 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="USD" {{#if plan}}{{#eq plan.currency 'USD'}}selected{{/eq}}{{else}}selected{{/if}}>USD ($)</option>
                        <option value="EUR" {{#if plan}}{{#eq plan.currency 'EUR'}}selected{{/eq}}{{/if}}>EUR (€)</option>
                        <option value="GBP" {{#if plan}}{{#eq plan.currency 'GBP'}}selected{{/eq}}{{/if}}>GBP (£)</option>
                        <option value="CAD" {{#if plan}}{{#eq plan.currency 'CAD'}}selected{{/eq}}{{/if}}>CAD ($)</option>
                        <option value="AUD" {{#if plan}}{{#eq plan.currency 'AUD'}}selected{{/eq}}{{/if}}>AUD ($)</option>
                    </select>
                </div>
            </div>

            <!-- Trial Period -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="freeTrialDays" class="form-label block text-sm font-medium mb-2">Free Trial Days</label>
                    <input type="number" id="freeTrialDays" name="freeTrialDays" value="{{#if plan}}{{plan.freeTrialDays}}{{/if}}" min="0"
                           class="form-input w-full px-4 py-3 rounded-lg focus:ring-2 focus:ring-blue-500"
                           placeholder="7">
                    <p class="text-xs text-gray-400 mt-1">Leave empty or 0 for no trial period</p>
                </div>

                <div class="space-y-6">
                    <!-- Has Free Trial Tier Toggle -->
                    <div class="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg border border-gray-600 cursor-pointer hover:bg-gray-700/70 transition-colors" onclick="toggleSwitch('hasFreeTrialTier')">
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                <svg class="w-5 h-5 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <label for="hasFreeTrialTier" class="text-sm font-medium text-white cursor-pointer">Has Free Trial Tier</label>
                                <p class="text-xs text-gray-400">Enable trial functionality for this plan</p>
                            </div>
                        </div>
                        <div class="relative">
                            <input type="checkbox" id="hasFreeTrialTier" name="hasFreeTrialTier" value="true" 
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-orange-500 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-500 cursor-pointer"></div>
                        </div>
                    </div>
                    
                    <!-- Active Plan Toggle -->
                    <div class="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg border border-gray-600 cursor-pointer hover:bg-gray-700/70 transition-colors" onclick="toggleSwitch('active')">
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <label for="active" class="text-sm font-medium text-white cursor-pointer">Active Plan</label>
                                <p class="text-xs text-gray-400">Make this plan available to users</p>
                            </div>
                        </div>
                        <div class="relative">
                            <input type="checkbox" id="active" name="active" value="true" 
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-green-500 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-500 cursor-pointer"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div>
                <label for="description" class="form-label block text-sm font-medium mb-2">Description</label>
                <textarea id="description" name="description" rows="4"
                          class="form-textarea w-full px-4 py-3 rounded-lg focus:ring-2 focus:ring-blue-500"
                          placeholder="Describe the features and benefits of this plan...">{{#if plan}}{{plan.description}}{{/if}}</textarea>
            </div>

            <!-- Features -->
            <div>
                <label class="form-label block text-sm font-medium mb-2">Features</label>
                <div id="featuresContainer" class="space-y-3">
                    <!-- Features will be dynamically added here -->
                </div>
                <button type="button" id="addFeatureBtn" class="mt-3 btn-secondary px-4 py-2 rounded-lg text-sm font-medium">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Feature
                </button>
                <p class="text-xs text-gray-400 mt-1">Add features one by one. Click "Add Feature" to add more.</p>
            </div>

            <!-- Submit Buttons -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-700">
                <a href="/admin/monetization/plans" class="btn-secondary px-6 py-3 rounded-lg font-medium">
                    Cancel
                </a>
                <button type="submit" class="btn-primary px-6 py-3 rounded-lg font-medium">
                    {{#if isNew}}
                        Create Plan
                    {{else}}
                        Update Plan
                    {{/if}}
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Debug: Log the plan data being passed
    const planData = {{{json plan}}};
    console.log('Plan data received:', planData);
    
    // Initialize form with plan data if available
    if (planData) {
        // Set select values
        if (planData.type) {
            document.getElementById('type').value = planData.type;
        }
        if (planData.platform) {
            document.getElementById('platform').value = planData.platform;
        }
        if (planData.currency) {
            document.getElementById('currency').value = planData.currency;
        }
        
        // Set checkbox values properly
        const activeCheckbox = document.getElementById('active');
        const trialCheckbox = document.getElementById('hasFreeTrialTier');
        
        // Set active status (default to true for new plans)
        activeCheckbox.checked = planData.active !== false;
        
        // Set trial tier status
        trialCheckbox.checked = planData.hasFreeTrialTier === true;
        
        console.log('Set active to:', activeCheckbox.checked);
        console.log('Set trial tier to:', trialCheckbox.checked);
    } else {
        // Set defaults for new plans
        document.getElementById('active').checked = true;
        document.getElementById('currency').value = 'USD';
        console.log('Set defaults for new plan');
    }
    
    // Features management
    const featuresContainer = document.getElementById('featuresContainer');
    const addFeatureBtn = document.getElementById('addFeatureBtn');
    let featureIndex = 0;
    
    function createFeatureInput(value = '') {
        const featureDiv = document.createElement('div');
        featureDiv.className = 'flex items-center space-x-3';
        featureDiv.innerHTML = `
            <input type="text" name="features[]" value="${value}" 
                   class="form-input flex-1 px-4 py-3 rounded-lg focus:ring-2 focus:ring-blue-500"
                   placeholder="e.g., Unlimited access">
            <button type="button" class="remove-feature-btn text-red-400 hover:text-red-300 p-2" title="Remove feature">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </button>
        `;
        
        // Add remove functionality
        featureDiv.querySelector('.remove-feature-btn').addEventListener('click', function() {
            featureDiv.remove();
            updateRemoveButtons();
        });
        
        return featureDiv;
    }
    
    function updateRemoveButtons() {
        const removeButtons = featuresContainer.querySelectorAll('.remove-feature-btn');
        // Always allow removal if there's more than one feature, or if there's one feature and it's not empty
        removeButtons.forEach(btn => {
            const input = btn.parentElement.querySelector('input');
            const allInputs = featuresContainer.querySelectorAll('input[name="features[]"]');
            btn.style.display = allInputs.length > 1 || input.value.trim() ? 'block' : 'none';
        });
    }
    
    function addFeature(value = '') {
        const featureInput = createFeatureInput(value);
        featuresContainer.appendChild(featureInput);
        updateRemoveButtons();
        return featureInput;
    }
    
    // Initialize features from plan data
    if (planData && planData.features && Array.isArray(planData.features)) {
        planData.features.forEach(feature => {
            addFeature(feature);
        });
    }
    
    // If no features exist, add one empty input
    if (featuresContainer.children.length === 0) {
        addFeature();
    }
    
    // Add feature button event
    addFeatureBtn.addEventListener('click', function() {
        const newFeature = addFeature();
        newFeature.querySelector('input').focus();
    });
    
    // Platform validation and dynamic placeholders
    const platformSelect = document.getElementById('platform');
    const productIdInput = document.getElementById('productId');
    const productIdHelp = document.getElementById('productIdHelp');
    
    function updateProductIdField() {
        const platform = platformSelect.value;
        
        switch (platform) {
            case 'ios':
                productIdInput.placeholder = 'com.yourapp.premium.monthly';
                productIdHelp.textContent = 'Apple App Store product identifier (reverse domain format)';
                break;
            case 'android':
                productIdInput.placeholder = 'premium_monthly';
                productIdHelp.textContent = 'Google Play Store product identifier';
                break;
            case 'web':
                productIdInput.placeholder = 'web_premium_monthly';
                productIdHelp.textContent = 'Internal web product identifier';
                break;
            default:
                productIdInput.placeholder = 'Platform-specific product ID';
                productIdHelp.textContent = 'Platform-specific product identifier';
        }
    }
    
    platformSelect.addEventListener('change', updateProductIdField);
    updateProductIdField(); // Initial setup
    
    // Type and trial period logic
    const typeSelect = document.getElementById('type');
    const freeTrialDaysInput = document.getElementById('freeTrialDays');
    const hasFreeTrialTierCheckbox = document.getElementById('hasFreeTrialTier');
    
    function updateTrialFields() {
        const type = typeSelect.value;
        
        // Set default trial days based on plan type
        if (!freeTrialDaysInput.value) {
            switch (type) {
                case 'weekly':
                    freeTrialDaysInput.placeholder = '3';
                    break;
                case 'monthly':
                    freeTrialDaysInput.placeholder = '7';
                    break;
                case 'yearly':
                    freeTrialDaysInput.placeholder = '14';
                    break;
                default:
                    freeTrialDaysInput.placeholder = '7';
            }
        }
    }
    
    typeSelect.addEventListener('change', updateTrialFields);
    updateTrialFields(); // Initial setup
    
    // Sync trial checkbox with trial days
    freeTrialDaysInput.addEventListener('input', function() {
        const hasTrialDays = parseInt(this.value) > 0;
        hasFreeTrialTierCheckbox.checked = hasTrialDays;
        console.log('Trial days changed:', this.value, 'Setting trial tier to:', hasTrialDays);
    });
    
    hasFreeTrialTierCheckbox.addEventListener('change', function() {
        console.log('Trial tier checkbox changed to:', this.checked);
        if (!this.checked) {
            freeTrialDaysInput.value = '0';
        } else if (!freeTrialDaysInput.value || freeTrialDaysInput.value === '0') {
            freeTrialDaysInput.value = freeTrialDaysInput.placeholder || '7';
        }
    });
    
    // Add click handlers for the toggle switches (since they're custom styled)
    window.toggleSwitch = function(switchId) {
        const checkbox = document.getElementById(switchId);
        checkbox.checked = !checkbox.checked;
        console.log(`${switchId} toggle clicked, new state:`, checkbox.checked);
        
        // If it's the trial tier switch, trigger sync with trial days
        if (switchId === 'hasFreeTrialTier') {
            checkbox.dispatchEvent(new Event('change'));
        }
    };
    
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        // Add hidden inputs for unchecked checkboxes to ensure they're sent as false
        const activeCheckbox = document.getElementById('active');
        const trialCheckbox = document.getElementById('hasFreeTrialTier');
        
        // Remove any existing hidden inputs for these fields
        const existingHiddenActive = form.querySelector('input[type="hidden"][name="active"]');
        const existingHiddenTrial = form.querySelector('input[type="hidden"][name="hasFreeTrialTier"]');
        if (existingHiddenActive) existingHiddenActive.remove();
        if (existingHiddenTrial) existingHiddenTrial.remove();
        
        // Add hidden inputs for unchecked checkboxes
        if (!activeCheckbox.checked) {
            const hiddenActive = document.createElement('input');
            hiddenActive.type = 'hidden';
            hiddenActive.name = 'active';
            hiddenActive.value = 'false';
            form.appendChild(hiddenActive);
        }
        
        if (!trialCheckbox.checked) {
            const hiddenTrial = document.createElement('input');
            hiddenTrial.type = 'hidden';
            hiddenTrial.name = 'hasFreeTrialTier';
            hiddenTrial.value = 'false';
            form.appendChild(hiddenTrial);
        }
        
        console.log('Form submission - Active:', activeCheckbox.checked, 'Trial:', trialCheckbox.checked);
        
        // Validate required fields
        const requiredFields = [
            { field: document.getElementById('name'), name: 'Plan Name' },
            { field: document.getElementById('type'), name: 'Plan Type' },
            { field: document.getElementById('platform'), name: 'Platform' },
            { field: document.getElementById('price'), name: 'Price' },
            { field: document.getElementById('productId'), name: 'Product ID' }
        ];
        
        for (const { field, name } of requiredFields) {
            if (!field.value.trim()) {
                e.preventDefault();
                alert(`${name} is required.`);
                field.focus();
                return false;
            }
        }
        
        // Validate price
        const price = parseFloat(document.getElementById('price').value);
        if (isNaN(price) || price < 0) {
            e.preventDefault();
            alert('Please enter a valid price.');
            document.getElementById('price').focus();
            return false;
        }
        
        // Validate trial days
        const trialDays = parseInt(freeTrialDaysInput.value) || 0;
        if (trialDays < 0) {
            e.preventDefault();
            alert('Trial days cannot be negative.');
            freeTrialDaysInput.focus();
            return false;
        }
        
        // Clean up empty features before submission, but keep at least one for proper array handling
        const featureInputs = document.querySelectorAll('input[name="features[]"]');
        const nonEmptyFeatures = Array.from(featureInputs).filter(input => input.value.trim());
        
        // Remove all empty features
        featureInputs.forEach(input => {
            if (!input.value.trim()) {
                input.parentElement.remove();
            }
        });
        
        // If no features remain, add a hidden empty feature input to ensure features[] is sent
        if (nonEmptyFeatures.length === 0) {
            const hiddenFeature = document.createElement('input');
            hiddenFeature.type = 'hidden';
            hiddenFeature.name = 'features[]';
            hiddenFeature.value = '';
            form.appendChild(hiddenFeature);
            console.log('Added hidden empty feature input to ensure features[] is sent');
        }
        
        console.log('Form validation passed, submitting with', nonEmptyFeatures.length, 'features...');
    });
});
</script>
