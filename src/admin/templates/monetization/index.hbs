<div class="space-y-8">
    <div class="flex items-center justify-between">
        <h1 class="text-3xl font-bold gradient-text">Monetization Dashboard</h1>
        <div class="text-sm text-gray-400">
            Manage subscriptions, plans, and revenue
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">Total Plans</p>
                    <p class="text-2xl font-bold text-blue-400">{{stats.totalPlans}}</p>
                </div>
                <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 border border-green-500/20 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">Active Subscriptions</p>
                    <p class="text-2xl font-bold text-green-400">{{stats.activeSubscriptions}}</p>
                </div>
                <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 border border-purple-500/20 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">Total Coupons</p>
                    <p class="text-2xl font-bold text-purple-400">{{stats.totalCoupons}}</p>
                </div>
                <div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 border border-yellow-500/20 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-400">Total Revenue</p>
                    <p class="text-2xl font-bold text-yellow-400">${{stats.totalRevenue}}</p>
                </div>
                <div class="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
        <h2 class="text-xl font-bold mb-6 text-white">Quick Actions</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <a href="/admin/monetization/plans" class="block text-center py-3 px-4 bg-gray-700 hover:bg-gray-600 text-white font-medium rounded-lg transition-colors">
                <i class="fas fa-list mb-2"></i><br>
                Manage Plans
            </a>
            <a href="/admin/monetization/subscriptions" class="block text-center py-3 px-4 bg-gray-700 hover:bg-gray-600 text-white font-medium rounded-lg transition-colors">
                <i class="fas fa-users mb-2"></i><br>
                View Subscriptions
            </a>
            <a href="/admin/monetization/coupons" class="block text-center py-3 px-4 bg-gray-700 hover:bg-gray-600 text-white font-medium rounded-lg transition-colors">
                <i class="fas fa-tags mb-2"></i><br>
                Manage Coupons
            </a>
            <a href="/admin/monetization/analytics" class="block text-center py-3 px-4 bg-gray-700 hover:bg-gray-600 text-white font-medium rounded-lg transition-colors">
                <i class="fas fa-chart-bar mb-2"></i><br>
                View Analytics
            </a>
        </div>
    </div>

    <!-- Recent Plans -->
    {{#if plans}}
    <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-bold text-white">Recent Subscription Plans</h2>
            <a href="/admin/monetization/plans" class="text-blue-400 hover:text-blue-300 text-sm">View All</a>
        </div>
        <div class="overflow-x-auto">
            <table class="w-full admin-table">
                <thead>
                    <tr class="text-left border-b border-gray-700">
                        <th class="pb-3 text-purple-400">Name</th>
                        <th class="pb-3 text-purple-400">Price</th>
                        <th class="pb-3 text-purple-400">Duration</th>
                        <th class="pb-3 text-purple-400">Platform</th>
                        <th class="pb-3 text-purple-400">Status</th>
                    </tr>
                </thead>
                <tbody class="text-gray-300">
                    {{#each plans}}
                    <tr class="border-b border-gray-700 hover:bg-gray-700/30">
                        <td class="py-3">{{name}}</td>
                        <td class="py-3">${{price}}</td>
                        <td class="py-3">{{duration}} {{durationType}}</td>
                        <td class="py-3">
                            <span class="px-2 py-1 bg-blue-500/20 text-blue-400 rounded text-xs">{{platform}}</span>
                        </td>
                        <td class="py-3">
                            {{#if active}}
                                <span class="px-2 py-1 bg-green-500/20 text-green-400 rounded text-xs">Active</span>
                            {{else}}
                                <span class="px-2 py-1 bg-red-500/20 text-red-400 rounded text-xs">Inactive</span>
                            {{/if}}
                        </td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>
        </div>
    </div>
    {{/if}}

    <!-- Recent Subscriptions -->
    {{#if subscriptions}}
    <div class="bg-gray-800 border border-blue-500/20 rounded-xl p-6">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-bold text-white">Recent Subscriptions</h2>
            <a href="/admin/monetization/subscriptions" class="text-blue-400 hover:text-blue-300 text-sm">View All</a>
        </div>
        <div class="overflow-x-auto">
            <table class="w-full admin-table">
                <thead>
                    <tr class="text-left border-b border-gray-700">
                        <th class="pb-3 text-purple-400">User</th>
                        <th class="pb-3 text-purple-400">Plan</th>
                        <th class="pb-3 text-purple-400">Status</th>
                        <th class="pb-3 text-purple-400">Start Date</th>
                        <th class="pb-3 text-purple-400">End Date</th>
                    </tr>
                </thead>
                <tbody class="text-gray-300">
                    {{#each subscriptions}}
                    <tr class="border-b border-gray-700 hover:bg-gray-700/30">
                        <td class="py-3">{{userId}}</td>
                        <td class="py-3">{{planName}}</td>
                        <td class="py-3">
                            {{#eq status 'active'}}
                                <span class="px-2 py-1 bg-green-500/20 text-green-400 rounded text-xs">Active</span>
                            {{else}}
                                <span class="px-2 py-1 bg-gray-500/20 text-gray-400 rounded text-xs">{{status}}</span>
                            {{/eq}}
                        </td>
                        <td class="py-3">{{formatDate startDate}}</td>
                        <td class="py-3">{{formatDate endDate}}</td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>
        </div>
    </div>
    {{/if}}
</div>
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h5 class="card-title">Total Subscriptions</h5>
                                        <h3>{{totalSubscriptions}}</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h5 class="card-title">Coupon Codes</h5>
                                        <h3>{{totalCoupons}}</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-ticket-alt fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h5 class="card-title">Revenue</h5>
                                        <h3>$0</h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-chart-line fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Quick Actions</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <a href="/admin/monetization/plans" class="btn btn-outline-primary w-100">
                                            <i class="fas fa-list me-2"></i>Manage Plans
                                        </a>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <a href="/admin/monetization/subscriptions" class="btn btn-outline-success w-100">
                                            <i class="fas fa-users me-2"></i>View Subscriptions
                                        </a>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <a href="/admin/monetization/coupons" class="btn btn-outline-warning w-100">
                                            <i class="fas fa-ticket-alt me-2"></i>Manage Coupons
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between">
                                <h5 class="mb-0">Recent Subscriptions</h5>
                                <a href="/admin/monetization/subscriptions" class="btn btn-sm btn-outline-primary">View All</a>
                            </div>
                            <div class="card-body">
                                {{#if subscriptions}}
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>User</th>
                                                    <th>Plan</th>
                                                    <th>Status</th>
                                                    <th>Created</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {{#each subscriptions}}
                                                <tr>
                                                    <td>{{user.email}}</td>
                                                    <td>{{plan.name}}</td>
                                                    <td>
                                                        <span class="badge bg-{{#if (eq status 'active')}}success{{else}}secondary{{/if}}">
                                                            {{status}}
                                                        </span>
                                                    </td>
                                                    <td>{{formatDate createdAt}}</td>
                                                </tr>
                                                {{/each}}
                                            </tbody>
                                        </table>
                                    </div>
                                {{else}}
                                    <p class="text-muted">No subscriptions yet.</p>
                                {{/if}}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between">
                                <h5 class="mb-0">Active Plans</h5>
                                <a href="/admin/monetization/plans" class="btn btn-sm btn-outline-primary">View All</a>
                            </div>
                            <div class="card-body">
                                {{#if plans}}
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Type</th>
                                                    <th>Price</th>
                                                    <th>Platform</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {{#each plans}}
                                                <tr>
                                                    <td>{{name}}</td>
                                                    <td>{{type}}</td>
                                                    <td>${{price}}</td>
                                                    <td>{{platform}}</td>
                                                </tr>
                                                {{/each}}
                                            </tbody>
                                        </table>
                                    </div>
                                {{else}}
                                    <p class="text-muted">No plans created yet.</p>
                                {{/if}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
