<div class="space-y-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 dark:text-white">Blog Management</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">Create, edit, and manage blog posts</p>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm hover:shadow-md transition-shadow">
            <div class="flex items-center space-x-4">
                <div class="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                    </svg>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white" id="totalPosts">{{stats.totalPosts}}</h3>
                    <p class="text-gray-600 dark:text-gray-400">Total Posts</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm hover:shadow-md transition-shadow">
            <div class="flex items-center space-x-4">
                <div class="bg-green-100 dark:bg-green-900/30 p-3 rounded-lg">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white" id="publishedPosts">{{stats.publishedPosts}}</h3>
                    <p class="text-gray-600 dark:text-gray-400">Published</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm hover:shadow-md transition-shadow">
            <div class="flex items-center space-x-4">
                <div class="bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white" id="draftPosts">{{stats.draftPosts}}</h3>
                    <p class="text-gray-600 dark:text-gray-400">Drafts</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm hover:shadow-md transition-shadow">
            <div class="flex items-center space-x-4">
                <div class="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                    </svg>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white" id="totalViews">{{stats.totalViews}}</h3>
                    <p class="text-gray-600 dark:text-gray-400">Total Views</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Bar -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
        <button class="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium flex items-center gap-2" onclick="showCreatePostModal()">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
            </svg>
            Create New Post
        </button>
        
        <div class="flex flex-col sm:flex-row gap-3">
            <div class="relative">
                <input type="text" id="searchPosts" placeholder="Search posts..." 
                       class="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                       onkeyup="filterPosts()">
                <svg class="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                </svg>
            </div>
            <select id="statusFilter" onchange="filterPosts()" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <option value="">All Statuses</option>
                <option value="published">Published</option>
                <option value="draft">Draft</option>
                <option value="archived">Archived</option>
            </select>
            <select id="categoryFilter" onchange="filterPosts()" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <option value="">All Categories</option>
                <option value="wellness">Wellness</option>
                <option value="ai-technology">AI Technology</option>
                <option value="habits">Habits</option>
                <option value="productivity">Productivity</option>
                <option value="mental-health">Mental Health</option>
                <option value="fitness">Fitness</option>
                <option value="nutrition">Nutrition</option>
                <option value="company-news">Company News</option>
            </select>
        </div>
    </div>

    <!-- Blog Posts Grid -->
    <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm">
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 p-6" id="postsGrid">
            {{#each posts}}
            <div class="bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-200" 
                 data-id="{{id}}" data-status="{{status}}" data-category="{{category}}" data-title="{{title}}">
                <div class="flex justify-between items-start mb-4">
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">{{title}}</h3>
                        <div class="flex items-center gap-2 mb-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {{#if (eq status 'published')}}bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400{{/if}}
                                {{#if (eq status 'draft')}}bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400{{/if}}
                                {{#if (eq status 'archived')}}bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300{{/if}}">
                                {{status}}
                            </span>
                            {{#if isFeatured}}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                                Featured
                            </span>
                            {{/if}}
                        </div>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">{{categoryDisplayName category}}</p>
                    </div>
                    <div class="flex gap-2 ml-4">
                        <button onclick="editPost('{{id}}')" 
                                class="p-2 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/30 rounded-lg transition-colors" 
                                title="Edit Post">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                            </svg>
                        </button>
                        <button onclick="deletePost('{{id}}', '{{title}}')" 
                                class="p-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/30 rounded-lg transition-colors" 
                                title="Delete Post">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </button>
                    </div>
                </div>
                
                {{#if excerpt}}
                <p class="text-gray-600 dark:text-gray-300 text-sm line-clamp-3 mb-4">{{excerpt}}</p>
                {{/if}}
                
                {{#if tags}}
                <div class="flex flex-wrap gap-1 mb-4">
                    {{#each tags}}
                    <span class="inline-block bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded text-xs">{{this}}</span>
                    {{/each}}
                </div>
                {{/if}}
                
                <div class="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-600">
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        <div>{{formatDate updatedAt}}</div>
                        <div>by {{author.name}}</div>
                    </div>
                    <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                        <div class="flex items-center gap-1">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                            </svg>
                            <span>{{views}}</span>
                        </div>
                        <div class="flex items-center gap-1">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                            </svg>
                            <span>{{likes}}</span>
                        </div>
                    </div>
                </div>
            </div>
            {{/each}}
        </div>
        
        {{#unless posts}}
        <div class="text-center py-20">
            <div class="text-gray-500 dark:text-gray-400 mb-4">
                <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">No blog posts yet</h3>
            <p class="text-gray-600 dark:text-gray-400">Create your first blog post to get started.</p>
        </div>
        {{/unless}}
    </div>
</div>

<!-- Create/Edit Post Modal -->
<div id="postModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50 p-4">
    <div class="bg-white dark:bg-gray-800 rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 id="modalTitle" class="text-xl font-semibold text-gray-900 dark:text-white">Create New Post</h2>
            <button class="text-gray-400 hover:text-gray-600 text-2xl font-bold" onclick="closePostModal()">&times;</button>
        </div>
        
        <div class="p-6">
            <form id="postForm" class="space-y-6">
                <input type="hidden" id="postId" name="id">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="postTitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Title *</label>
                        <input type="text" id="postTitle" name="title" required 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>
                    
                    <div>
                        <label for="postSlug" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Slug</label>
                        <input type="text" id="postSlug" name="slug" 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Leave empty to auto-generate from title</p>
                    </div>
                </div>

                <div>
                    <label for="postExcerpt" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Excerpt</label>
                    <textarea id="postExcerpt" name="excerpt" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"></textarea>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Content *</label>
                    <div class="space-y-4">
                        <!-- Notion-like Editor -->
                        <div id="editorContainer" class="bg-white dark:bg-gray-700 rounded-lg border border-gray-300 dark:border-gray-600">
                            <div id="notionEditor"></div>
                        </div>
                        
                        <!-- Hidden input to store content -->
                        <input type="hidden" id="postContentHidden" name="content">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="postCategory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Category *</label>
                        <select id="postCategory" name="category" required 
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            <option value="wellness" selected>Wellness</option>
                            <option value="ai-technology">AI Technology</option>
                            <option value="habits">Habits</option>
                            <option value="productivity">Productivity</option>
                            <option value="mental-health">Mental Health</option>
                            <option value="fitness">Fitness</option>
                            <option value="nutrition">Nutrition</option>
                            <option value="company-news">Company News</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="postStatus" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status *</label>
                        <select id="postStatus" name="status" required 
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            <option value="draft">Draft</option>
                            <option value="published">Published</option>
                            <option value="archived">Archived</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="postFeaturedImage" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Featured Image URL</label>
                        <input type="url" id="postFeaturedImage" name="featuredImage" 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="postTags" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tags</label>
                        <input type="text" id="postTags" name="tags" 
                               placeholder="Enter tags separated by commas"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>
                    
                    <div>
                        <label for="postMetaDescription" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Meta Description</label>
                        <input type="text" id="postMetaDescription" name="metaDescription" 
                               placeholder="SEO meta description"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>
                </div>

                <div>
                    <label class="flex items-center cursor-pointer">
                        <input type="checkbox" id="postFeatured" name="isFeatured" class="sr-only peer">
                        <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        <span class="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">Featured Post</span>
                    </label>
                </div>
            </form>
        </div>
        
        <div class="flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
            <button type="button" onclick="closePostModal()" 
                    class="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                Cancel
            </button>
            <button type="submit" form="postForm"
                    class="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200">
                Save Post
            </button>
        </div>
    </div>
</div>

<script>
let currentEditId = null;
let editor = null;

// Notification function
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Initialize Notion-like Editor
function initNotionEditor() {
    if (editor) return;
    
    // Check if Editor.js tools are ready
    if (!window.editorJSReady) {
        console.log('Editor.js tools not ready yet, waiting...');
        showNotification('Loading editor...', 'info');
        
        // Listen for tools ready event
        const onToolsReady = () => {
            window.removeEventListener('editorToolsReady', onToolsReady);
            window.removeEventListener('editorToolsError', onToolsError);
            setTimeout(() => initNotionEditor(), 100);
        };
        
        const onToolsError = (event) => {
            window.removeEventListener('editorToolsReady', onToolsReady);
            window.removeEventListener('editorToolsError', onToolsError);
            console.error('Editor.js tools failed to load:', event.detail);
            showFallbackEditor();
        };
        
        window.addEventListener('editorToolsReady', onToolsReady);
        window.addEventListener('editorToolsError', onToolsError);
        return;
    }
    
    // Check if main EditorJS is loaded
    if (typeof EditorJS === 'undefined') {
        console.error('EditorJS is not loaded');
        showFallbackEditor();
        return;
    }
    
    // Use the stored tool references
    const tools = window.editorJSTools;
    if (!tools || !tools.Header || !tools.Paragraph) {
        console.error('Editor.js essential tools not properly loaded');
        showFallbackEditor();
        return;
    }
    
    try {
        // Build tools configuration dynamically based on what's available
        const editorTools = {
            header: {
                class: tools.Header,
                config: {
                    placeholder: 'Enter a header',
                    levels: [1, 2, 3, 4, 5, 6],
                    defaultLevel: 2
                }
            },
            paragraph: {
                class: tools.Paragraph,
                inlineToolbar: true,
                config: {
                    placeholder: 'Start writing or type \'/\' for commands...'
                }
            }
        };
        
        // Add optional tools only if they're available
        if (tools.List) {
            editorTools.list = {
                class: tools.List,
                inlineToolbar: true,
                config: {
                    defaultStyle: 'unordered'
                }
            };
        } else {
            console.warn('List tool not available, skipping...');
        }
        
        if (tools.ImageTool) {
            editorTools.image = {
                class: tools.ImageTool,
                config: {
                    endpoints: {
                        byFile: '/admin/api/upload/image',
                        byUrl: '/admin/api/upload/image-by-url'
                    }
                }
            };
        }
        
        if (tools.Embed) {
            editorTools.embed = {
                class: tools.Embed,
                config: {
                    services: {
                        youtube: true,
                        coub: true,
                        vimeo: true
                    }
                }
            };
        }
        
        if (tools.Quote) {
            editorTools.quote = {
                class: tools.Quote,
                inlineToolbar: true,
                shortcut: 'CMD+SHIFT+O',
                config: {
                    quotePlaceholder: 'Enter a quote',
                    captionPlaceholder: 'Quote\'s author',
                }
            };
        }
        
        if (tools.Delimiter) {
            editorTools.delimiter = tools.Delimiter;
        }
        
        if (tools.Table) {
            editorTools.table = {
                class: tools.Table,
                inlineToolbar: true,
                config: {
                    rows: 2,
                    cols: 3,
                }
            };
        }
        
        if (tools.CodeTool) {
            editorTools.code = {
                class: tools.CodeTool,
                config: {
                    placeholder: 'Enter code here...'
                }
            };
        }
        
        if (tools.InlineCode) {
            editorTools.inlineCode = {
                class: tools.InlineCode,
                shortcut: 'CMD+SHIFT+M',
            };
        }
        
        if (tools.Marker) {
            editorTools.marker = {
                class: tools.Marker,
                shortcut: 'CMD+SHIFT+H',
            };
        }
        
        if (tools.Underline) {
            editorTools.underline = {
                class: tools.Underline,
                shortcut: 'CMD+U',
            };
        }
        
        console.log('Initializing Editor.js with tools:', Object.keys(editorTools));
        
        editor = new EditorJS({
            holder: 'notionEditor',
            placeholder: 'Type \'/\' for commands, or start writing...',
            autofocus: true,
            tools: editorTools,
            onReady: () => {
                console.log('Editor.js is ready to work!');
                showNotification('Editor loaded successfully!', 'success');
            },
            onChange: (api, event) => {
                // Auto-save content
                saveEditorContent();
            }
        });
    } catch (error) {
        console.error('Failed to initialize Editor.js:', error);
        showFallbackEditor();
    }
}

// Fallback editor for when Editor.js fails to load
function showFallbackEditor() {
    const editorContainer = document.getElementById('editorContainer');
    if (editorContainer) {
        editorContainer.innerHTML = `
            <div class="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg mb-4">
                <p class="text-yellow-800 dark:text-yellow-200 text-sm">
                    <strong>Editor Loading Issue:</strong> Falling back to simple text editor.
                </p>
            </div>
            <textarea id="fallbackEditor" rows="20" 
                      placeholder="Write your blog post content here..." 
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-mono text-sm"></textarea>
        `;
        
        // Add event listener for fallback editor
        const fallbackEditor = document.getElementById('fallbackEditor');
        if (fallbackEditor) {
            fallbackEditor.addEventListener('input', function() {
                document.getElementById('postContentHidden').value = this.value;
            });
        }
    }
}

// Save editor content to hidden input
async function saveEditorContent() {
    if (!editor) return;
    
    try {
        const outputData = await editor.save();
        document.getElementById('postContentHidden').value = JSON.stringify(outputData);
    } catch (error) {
        console.log('Saving failed: ', error);
    }
}

function showCreatePostModal() {
    console.log('showCreatePostModal called');
    const modal = document.getElementById('postModal');
    const modalTitle = document.getElementById('modalTitle');
    const postForm = document.getElementById('postForm');
    const postId = document.getElementById('postId');
    
    if (!modal || !modalTitle || !postForm || !postId) {
        console.error('Modal elements not found');
        return;
    }
    
    modalTitle.textContent = 'Create New Post';
    postForm.reset();
    postId.value = '';
    currentEditId = null;
    
    // Reset editor
    if (editor) {
        editor.clear();
    }
    document.getElementById('postContentHidden').value = '';
    
    modal.classList.remove('hidden');
    modal.classList.add('flex');
    modal.style.display = 'flex';
    
    // Initialize editor after modal is shown
    setTimeout(() => {
        initNotionEditor();
    }, 200);
    
    console.log('Modal should be visible now');
}

async function editPost(id) {
    document.getElementById('modalTitle').textContent = 'Edit Post';
    currentEditId = id;
    
    try {
        const response = await fetch(`/admin/api/blog/${id}`);
        const post = await response.json();
        
        document.getElementById('postId').value = post.id;
        document.getElementById('postTitle').value = post.title;
        document.getElementById('postSlug').value = post.slug;
        document.getElementById('postExcerpt').value = post.excerpt || '';
        document.getElementById('postCategory').value = post.category;
        document.getElementById('postStatus').value = post.status;
        document.getElementById('postFeaturedImage').value = post.featuredImage || '';
        document.getElementById('postTags').value = (post.tags || []).join(', ');
        document.getElementById('postMetaDescription').value = post.metaDescription || '';
        document.getElementById('postFeatured').checked = post.isFeatured || false;
        
        document.getElementById('postModal').classList.remove('hidden');
        document.getElementById('postModal').classList.add('flex');
        document.getElementById('postModal').style.display = 'flex';
        
        // Initialize editor and set content
        setTimeout(async () => {
            initNotionEditor();
            
            // Wait for editor to be ready before loading content
            const waitForEditor = () => {
                return new Promise((resolve) => {
                    if (editor && editor.readOnly !== undefined) {
                        resolve();
                    } else {
                        setTimeout(() => {
                            waitForEditor().then(resolve);
                        }, 100);
                    }
                });
            };
            
            await waitForEditor();
            
            // Load content into editor
            if (post.content) {
                if (editor) {
                    try {
                        const contentData = typeof post.content === 'string' 
                            ? JSON.parse(post.content) 
                            : post.content;
                        
                        if (contentData && contentData.blocks) {
                            await editor.render(contentData);
                        } else {
                            // Fallback for plain text/HTML content
                            await editor.render({
                                blocks: [{
                                    type: "paragraph",
                                    data: {
                                        text: post.content
                                    }
                                }]
                            });
                        }
                    } catch (e) {
                        console.error('Error rendering content:', e);
                        // Fallback for plain text content
                        await editor.render({
                            blocks: [{
                                type: "paragraph",
                                data: {
                                    text: post.content
                                }
                            }]
                        });
                    }
                } else {
                    // Use fallback editor
                    const fallbackEditor = document.getElementById('fallbackEditor');
                    if (fallbackEditor) {
                        const contentText = typeof post.content === 'string' 
                            ? post.content 
                            : JSON.stringify(post.content, null, 2);
                        fallbackEditor.value = contentText;
                        document.getElementById('postContentHidden').value = contentText;
                    }
                }
            }
        }, 200);
        
    } catch (error) {
        console.error('Error:', error);
        showNotification('Failed to load post', 'error');
    }
}

function closePostModal() {
    const modal = document.getElementById('postModal');
    modal.classList.add('hidden');
    modal.classList.remove('flex');
    modal.style.display = 'none';
    currentEditId = null;
}

async function savePost(event) {
    // Prevent form default submission
    if (event) {
        event.preventDefault();
    }
    
    const form = document.getElementById('postForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    
    // Get content from editor
    if (editor) {
        try {
            const outputData = await editor.save();
            data.content = JSON.stringify(outputData);
        } catch (error) {
            console.error('Error saving editor content:', error);
            showNotification('Failed to save content', 'error');
            return;
        }
    } else {
        // Fallback to textarea content
        const fallbackEditor = document.getElementById('fallbackEditor');
        if (fallbackEditor) {
            data.content = fallbackEditor.value;
        } else {
            data.content = document.getElementById('postContentHidden').value || '';
        }
    }
    
    // Convert tags string to array
    if (data.tags && data.tags.trim()) {
        data.tags = data.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
    } else {
        data.tags = []; // Ensure it's always an array, even if empty
    }
    
    // Ensure category is never empty
    if (!data.category || data.category.trim() === '') {
        data.category = 'wellness'; // Default to wellness if category is somehow empty
    }
    
    data.isFeatured = document.getElementById('postFeatured').checked;
    
    // Remove empty id field for new posts (let TypeORM auto-generate UUID)
    if (!currentEditId && (!data.id || data.id.trim() === '')) {
        delete data.id;
    }
    
    try {
        let response;
        if (currentEditId) {
            response = await fetch(`/admin/api/blog/${currentEditId}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            });
        } else {
            response = await fetch('/admin/api/blog', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            });
        }
        
        if (response.ok) {
            showNotification(currentEditId ? 'Post updated successfully' : 'Post created successfully', 'success');
            closePostModal();
            location.reload();
        } else {
            const error = await response.json();
            showNotification(error.message || 'Failed to save post', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('Failed to save post', 'error');
    }
}

async function deletePost(id, title) {
    if (!confirm(`Are you sure you want to delete "${title}"?`)) {
        return;
    }
    
    try {
        const response = await fetch(`/admin/api/blog/${id}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            showNotification('Post deleted successfully', 'success');
            location.reload();
        } else {
            showNotification('Failed to delete post', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('Failed to delete post', 'error');
    }
}

function filterPosts() {
    const search = document.getElementById('searchPosts').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const categoryFilter = document.getElementById('categoryFilter').value;
    const posts = document.querySelectorAll('#postsGrid > div');
    
    posts.forEach(post => {
        const title = post.dataset.title.toLowerCase();
        const status = post.dataset.status;
        const category = post.dataset.category;
        
        const matchesSearch = title.includes(search);
        const matchesStatus = !statusFilter || status === statusFilter;
        const matchesCategory = !categoryFilter || category === categoryFilter;
        
        if (matchesSearch && matchesStatus && matchesCategory) {
            post.style.display = 'block';
        } else {
            post.style.display = 'none';
        }
    });
}

// Auto-generate slug from title
document.addEventListener('DOMContentLoaded', function() {
    const titleInput = document.getElementById('postTitle');
    if (titleInput) {
        titleInput.addEventListener('input', function() {
            const title = this.value;
            const slug = title
                .toLowerCase()
                .replace(/[^\w\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim();
            
            const slugInput = document.getElementById('postSlug');
            if (slugInput && !slugInput.value) {
                slugInput.value = slug;
            }
        });
    }
    
    // Add form submit handler
    const postForm = document.getElementById('postForm');
    if (postForm) {
        postForm.addEventListener('submit', function(event) {
            event.preventDefault();
            savePost(event);
        });
    }
});

// Close modal when clicking outside
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('postModal');
    if (modal) {
        modal.addEventListener('click', function(event) {
            if (event.target === this) {
                closePostModal();
            }
        });
    }
});
</script>
