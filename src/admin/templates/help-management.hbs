<div class="space-y-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 dark:text-white">Help Management</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">Manage help articles and support content</p>
        </div>
    </div>

    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
        <button class="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium flex items-center gap-2" onclick="showCreateArticleModal()">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Create New Article
        </button>
        <div class="relative w-full sm:w-80">
            <input type="text" id="searchInput" placeholder="Search articles..." onkeyup="filterArticles()" 
                   class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
            <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6" id="articlesGrid">
        {{#each articles}}
        <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow duration-200 p-6" data-id="{{id}}" data-title="{{title}}">
            <div class="flex justify-between items-start mb-4">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white line-clamp-2">{{title}}</h3>
                <div class="flex gap-2 ml-4">
                    <button onclick="editArticle('{{id}}')" class="text-blue-600 hover:text-blue-700 p-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                    </button>
                    <button onclick="deleteArticle('{{id}}')" class="text-red-600 hover:text-red-700 p-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
            
            <div class="flex justify-between items-center mb-3">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-blue-500 to-blue-600 text-white">
                    {{category}}
                </span>
                <span class="text-sm text-gray-500 dark:text-gray-400">Order: {{order}}</span>
            </div>
            
            <div class="text-gray-600 dark:text-gray-300 text-sm line-clamp-3 mb-4">
                {{substring content 0 150}}{{#if (gt content.length 150)}}...{{/if}}
            </div>
            
            <div class="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-600">
                <span class="text-sm text-gray-500 dark:text-gray-400">
                    Updated: {{formatDate updatedAt}}
                </span>
                {{#if isActive}}
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Active
                    </span>
                {{else}}
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        Inactive
                    </span>
                {{/if}}
            </div>
        </div>
        {{/each}}
    </div>

    {{#unless articles}}
    <div class="text-center py-20">
        <svg class="mx-auto h-16 w-16 text-gray-400 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No help articles found</h3>
        <p class="text-gray-500 dark:text-gray-400 mb-6">Create your first help article to get started</p>
        <button class="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-3 rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium" onclick="showCreateArticleModal()">Create Article</button>
    </div>
    {{/unless}}
</div>

<!-- Create/Edit Article Modal -->
<div id="articleModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 id="modalTitle" class="text-xl font-semibold text-gray-800 dark:text-white">Create New Article</h2>
            <button class="text-gray-400 hover:text-gray-600 text-2xl font-bold" onclick="closeArticleModal()">&times;</button>
        </div>
        
        <div class="p-6">
            <form id="articleForm" class="space-y-6">
                <input type="hidden" id="articleId" name="id">
                
                <div>
                    <label for="articleTitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Title *</label>
                    <input type="text" id="articleTitle" name="title" required 
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="articleCategory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Category *</label>
                        <select id="articleCategory" name="category" required 
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            <option value="">Select Category</option>
                            <option value="Getting Started">Getting Started</option>
                            <option value="Account Management">Account Management</option>
                            <option value="Habit Tracking">Habit Tracking</option>
                            <option value="Goal Setting">Goal Setting</option>
                            <option value="Progress Analytics">Progress Analytics</option>
                            <option value="Troubleshooting">Troubleshooting</option>
                            <option value="FAQ">FAQ</option>
                        </select>
                    </div>
                    <div>
                        <label for="articleOrder" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Display Order</label>
                        <input type="number" id="articleOrder" name="order" min="1" value="1" 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>
                </div>

                <div>
                    <label for="articleContent" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Content *</label>
                    <textarea id="articleContent" name="content" rows="10" required 
                              placeholder="Write your help article content here..." 
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"></textarea>
                </div>

                <div>
                    <label class="flex items-center cursor-pointer">
                        <input type="checkbox" id="articleActive" name="isActive" checked class="sr-only peer">
                        <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        <span class="ml-3 font-medium text-gray-700 dark:text-gray-300">Active</span>
                    </label>
                </div>
            </form>
        </div>
        
        <div class="flex justify-end gap-3 p-6 border-t border-gray-200 dark:border-gray-700">
            <button type="button" class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors" onclick="closeArticleModal()">Cancel</button>
            <button type="button" class="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium" onclick="saveArticle()">Save Article</button>
        </div>
    </div>
</div>

<script>
let currentEditId = null;

function showCreateArticleModal() {
    currentEditId = null;
    document.getElementById('modalTitle').textContent = 'Create New Article';
    document.getElementById('articleForm').reset();
    document.getElementById('articleId').value = '';
    document.getElementById('articleActive').checked = true;
    document.getElementById('articleModal').style.display = 'block';
}

function editArticle(id) {
    currentEditId = id;
    document.getElementById('modalTitle').textContent = 'Edit Article';
    
    // Fetch article data
    fetch(`/admin/api/help/${id}`)
        .then(response => response.json())
        .then(article => {
            document.getElementById('articleId').value = article.id;
            document.getElementById('articleTitle').value = article.title;
            document.getElementById('articleCategory').value = article.category;
            document.getElementById('articleOrder').value = article.order;
            document.getElementById('articleContent').value = article.content;
            document.getElementById('articleActive').checked = article.isActive;
            document.getElementById('articleModal').style.display = 'block';
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Failed to load article', 'error');
        });
}

function closeArticleModal() {
    document.getElementById('articleModal').style.display = 'none';
    currentEditId = null;
}

async function saveArticle() {
    const form = document.getElementById('articleForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    
    data.isActive = document.getElementById('articleActive').checked;
    data.order = parseInt(data.order) || 1;

    try {
        const url = currentEditId ? `/admin/api/help/${currentEditId}` : '/admin/api/help';
        const method = currentEditId ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        if (response.ok) {
            showNotification(`Article ${currentEditId ? 'updated' : 'created'} successfully!`, 'success');
            closeArticleModal();
            setTimeout(() => window.location.reload(), 1000);
        } else {
            throw new Error(`Failed to ${currentEditId ? 'update' : 'create'} article`);
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification(`Failed to ${currentEditId ? 'update' : 'create'} article`, 'error');
    }
}

async function deleteArticle(id) {
    if (!confirm('Are you sure you want to delete this article?')) {
        return;
    }

    try {
        const response = await fetch(`/admin/api/help/${id}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            showNotification('Article deleted successfully!', 'success');
            document.querySelector(`[data-id="${id}"]`).remove();
        } else {
            throw new Error('Failed to delete article');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('Failed to delete article', 'error');
    }
}

function filterArticles() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const articles = document.querySelectorAll('.article-card');

    articles.forEach(article => {
        const title = article.getAttribute('data-title').toLowerCase();
        const content = article.querySelector('.article-content').textContent.toLowerCase();
        
        if (title.includes(searchTerm) || content.includes(searchTerm)) {
            article.style.display = 'block';
        } else {
            article.style.display = 'none';
        }
    });
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('articleModal');
    if (event.target === modal) {
        closeArticleModal();
    }
}
</script>
