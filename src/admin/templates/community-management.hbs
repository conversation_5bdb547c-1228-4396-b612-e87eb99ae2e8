<div class="space-y-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 dark:text-white">Community Management</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">Manage community challenges and user engagement</p>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm hover:shadow-md transition-shadow">
            <div class="flex items-center space-x-4">
                <div class="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg">
                    <i class="icon-trophy text-blue-600 dark:text-blue-400 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white">{{stats.totalChallenges}}</h3>
                    <p class="text-gray-600 dark:text-gray-400">Total Challenges</p>
                </div>
            </div>
        </div>
        <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm hover:shadow-md transition-shadow">
            <div class="flex items-center space-x-4">
                <div class="bg-green-100 dark:bg-green-900/30 p-3 rounded-lg">
                    <i class="icon-users text-green-600 dark:text-green-400 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white">{{stats.activeChallenges}}</h3>
                    <p class="text-gray-600 dark:text-gray-400">Active Challenges</p>
                </div>
            </div>
        </div>
        <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm hover:shadow-md transition-shadow">
            <div class="flex items-center space-x-4">
                <div class="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg">
                    <i class="icon-star text-blue-600 dark:text-blue-400 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white">{{stats.totalParticipants}}</h3>
                    <p class="text-gray-600 dark:text-gray-400">Total Participants</p>
                </div>
            </div>
        </div>
        <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm hover:shadow-md transition-shadow">
            <div class="flex items-center space-x-4">
                <div class="bg-orange-100 dark:bg-orange-900/30 p-3 rounded-lg">
                    <i class="icon-calendar text-orange-600 dark:text-orange-400 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white">{{stats.thisMonthChallenges}}</h3>
                    <p class="text-gray-600 dark:text-gray-400">This Month</p>
                </div>
            </div>
        </div>
    </div>

    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <button class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg shadow-sm transition-colors" onclick="showCreateChallengeModal()">
            <i class="icon-plus mr-2"></i> Create New Challenge
        </button>
        <div class="flex flex-col sm:flex-row gap-3">
            <select id="statusFilter" onchange="filterChallenges()" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="completed">Completed</option>
                <option value="draft">Draft</option>
            </select>
            <select id="categoryFilter" onchange="filterChallenges()" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <option value="">All Categories</option>
                <option value="fitness">Fitness</option>
                <option value="health">Health</option>
                <option value="productivity">Productivity</option>
                <option value="mindfulness">Mindfulness</option>
                <option value="learning">Learning</option>
                <option value="social">Social</option>
            </select>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm">
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 p-6" id="challengesGrid">
            {{#each challenges}}
            <div class="bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow" data-id="{{id}}" data-status="{{status}}" data-category="{{category}}">
                <div class="flex justify-between items-start mb-4">
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{name}}</h3>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {{#if (eq status 'active')}}bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400{{/if}}
                            {{#if (eq status 'completed')}}bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400{{/if}}
                            {{#if (eq status 'draft')}}bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300{{/if}}">
                            {{status}}
                        </span>
                    </div>
                    <div class="flex space-x-2">
                        <button class="p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors" onclick="editChallenge({{id}})">
                            <i class="icon-edit"></i>
                        </button>
                        <button class="p-2 text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors" onclick="deleteChallenge({{id}})">
                            <i class="icon-delete"></i>
                        </button>
                    </div>
                </div>

                <div class="flex flex-wrap gap-2 mb-4">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">{{category}}</span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">{{duration}} days</span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        {{#if (eq difficulty 'beginner')}}bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400{{/if}}
                        {{#if (eq difficulty 'intermediate')}}bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400{{/if}}
                        {{#if (eq difficulty 'advanced')}}bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400{{/if}}">
                        {{difficulty}}
                    </span>
                </div>

                <div class="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3">
                    {{substring description 0 120}}{{#if (gt description.length 120)}}...{{/if}}
                </div>

                <div class="flex justify-between items-center text-sm text-gray-600 dark:text-gray-400 mb-4">
                    <div class="flex items-center space-x-1">
                        <i class="icon-users"></i>
                        <span>{{participantCount}} participants</span>
                    </div>
                    <div class="flex items-center space-x-1">
                        <i class="icon-trophy"></i>
                        <span>{{completionCount}} completed</span>
                    </div>
                </div>

                <div class="border-t border-gray-200 dark:border-gray-700 pt-4 space-y-2">
                    <div class="text-sm">
                        <span class="font-medium text-gray-900 dark:text-white">Start:</span> 
                        <span class="text-gray-600 dark:text-gray-400">{{formatDate startDate}}</span>
                    </div>
                    <div class="text-sm">
                        <span class="font-medium text-gray-900 dark:text-white">End:</span> 
                        <span class="text-gray-600 dark:text-gray-400">{{formatDate endDate}}</span>
                    </div>
                </div>

                {{#if rewards}}
                <div class="mt-4">
                    <h4 class="font-medium text-gray-900 dark:text-white mb-2">Rewards:</h4>
                    <ul class="list-disc list-inside text-sm text-gray-600 dark:text-gray-400 space-y-1">
                        {{#each rewards}}
                        <li>{{this}}</li>
                        {{/each}}
                    </ul>
                </div>
                {{/if}}
            </div>
            {{/each}}
        </div>

        {{#unless challenges}}
        <div class="flex flex-col items-center justify-center py-12 text-center">
            <i class="icon-trophy text-4xl text-gray-400 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No challenges found</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-6">Create your first community challenge to get started</p>
            <button class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg shadow-sm transition-colors" onclick="showCreateChallengeModal()">Create Challenge</button>
        </div>
        {{/unless}}
    </div>
</div>

<!-- Create/Edit Challenge Modal -->
<div id="challengeModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 id="modalTitle" class="text-xl font-semibold text-gray-900 dark:text-white">Create New Challenge</h2>
            <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 text-2xl" onclick="closeChallengeModal()">&times;</button>
        </div>
        <div class="p-6">
            <form id="challengeForm" class="space-y-6">
                <input type="hidden" id="challengeId" name="id">
                
                <div>
                    <label for="challengeName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Challenge Name *</label>
                    <input type="text" id="challengeName" name="name" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" required 
                           placeholder="30-Day Fitness Challenge">
                </div>

                <div>
                    <label for="challengeDescription" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description *</label>
                    <textarea id="challengeDescription" name="description" rows="4" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" required 
                              placeholder="Join us for a 30-day journey to improve your fitness..."></textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="challengeCategory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Category *</label>
                        <select id="challengeCategory" name="category" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                            <option value="">Select Category</option>
                            <option value="fitness">Fitness</option>
                            <option value="health">Health</option>
                            <option value="productivity">Productivity</option>
                            <option value="mindfulness">Mindfulness</option>
                            <option value="learning">Learning</option>
                            <option value="social">Social</option>
                        </select>
                    </div>
                    <div>
                        <label for="challengeDuration" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Duration (days) *</label>
                        <input type="number" id="challengeDuration" name="duration" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                               required min="1" max="365" value="30">
                    </div>
                    <div>
                        <label for="challengeDifficulty" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Difficulty *</label>
                        <select id="challengeDifficulty" name="difficulty" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                            <option value="">Select Difficulty</option>
                            <option value="beginner">Beginner</option>
                            <option value="intermediate">Intermediate</option>
                            <option value="advanced">Advanced</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="challengeStartDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Start Date *</label>
                        <input type="datetime-local" id="challengeStartDate" name="startDate" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                    </div>
                    <div>
                        <label for="challengeEndDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">End Date *</label>
                        <input type="datetime-local" id="challengeEndDate" name="endDate" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                    </div>
                </div>

                <div>
                    <label for="challengeRules" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Rules & Guidelines</label>
                    <textarea id="challengeRules" name="rules" rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                              placeholder="1. Complete daily tasks..."></textarea>
                </div>

                <div>
                    <label for="challengeRewards" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Rewards (one per line)</label>
                    <textarea id="challengeRewards" name="rewards" rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                              placeholder="Digital badge&#10;Points: 100&#10;Exclusive content access"></textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="challengeMaxParticipants" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Max Participants</label>
                        <input type="number" id="challengeMaxParticipants" name="maxParticipants" 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" min="1" placeholder="Leave empty for unlimited">
                    </div>
                    <div>
                        <label for="challengeStatus" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status *</label>
                        <select id="challengeStatus" name="status" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                            <option value="draft">Draft</option>
                            <option value="active">Active</option>
                            <option value="completed">Completed</option>
                        </select>
                    </div>
                </div>

                <div class="flex items-center">
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="challengeFeatured" name="isFeatured" class="sr-only peer">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        <span class="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">Featured Challenge</span>
                    </label>
                </div>
            </form>
        </div>
        <div class="flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
            <button type="button" class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors" onclick="closeChallengeModal()">Cancel</button>
            <button type="button" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg shadow-sm transition-colors" onclick="saveChallenge()">Save Challenge</button>
        </div>
    </div>
</div>

<script>
let currentEditId = null;

function showCreateChallengeModal() {
    currentEditId = null;
    document.getElementById('modalTitle').textContent = 'Create New Challenge';
    document.getElementById('challengeForm').reset();
    document.getElementById('challengeId').value = '';
    document.getElementById('challengeStatus').value = 'draft';
    document.getElementById('challengeModal').classList.remove('hidden');
}

function editChallenge(id) {
    currentEditId = id;
    document.getElementById('modalTitle').textContent = 'Edit Challenge';
    
    // Fetch challenge data
    fetch(`/admin/api/community/challenges/${id}`)
        .then(response => response.json())
        .then(challenge => {
            document.getElementById('challengeId').value = challenge.id;
            document.getElementById('challengeName').value = challenge.name;
            document.getElementById('challengeDescription').value = challenge.description;
            document.getElementById('challengeCategory').value = challenge.category;
            document.getElementById('challengeDuration').value = challenge.duration;
            document.getElementById('challengeDifficulty').value = challenge.difficulty;
            document.getElementById('challengeStartDate').value = formatDateForInput(challenge.startDate);
            document.getElementById('challengeEndDate').value = formatDateForInput(challenge.endDate);
            document.getElementById('challengeRules').value = challenge.rules || '';
            document.getElementById('challengeRewards').value = challenge.rewards ? challenge.rewards.join('\n') : '';
            document.getElementById('challengeMaxParticipants').value = challenge.maxParticipants || '';
            document.getElementById('challengeStatus').value = challenge.status;
            document.getElementById('challengeFeatured').checked = challenge.isFeatured || false;
            document.getElementById('challengeModal').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Failed to load challenge', 'error');
        });
}

function closeChallengeModal() {
    document.getElementById('challengeModal').classList.add('hidden');
    currentEditId = null;
}

async function saveChallenge() {
    const form = document.getElementById('challengeForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    
    // Process rewards
    if (data.rewards) {
        data.rewards = data.rewards.split('\n').filter(r => r.trim()).map(r => r.trim());
    }
    
    data.isFeatured = document.getElementById('challengeFeatured').checked;
    data.duration = parseInt(data.duration);
    data.maxParticipants = data.maxParticipants ? parseInt(data.maxParticipants) : null;

    try {
        const url = currentEditId ? `/admin/api/community/challenges/${currentEditId}` : '/admin/api/community/challenges';
        const method = currentEditId ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        if (response.ok) {
            showNotification(`Challenge ${currentEditId ? 'updated' : 'created'} successfully!`, 'success');
            closeChallengeModal();
            setTimeout(() => window.location.reload(), 1000);
        } else {
            throw new Error(`Failed to ${currentEditId ? 'update' : 'create'} challenge`);
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification(`Failed to ${currentEditId ? 'update' : 'create'} challenge`, 'error');
    }
}

async function deleteChallenge(id) {
    if (!confirm('Are you sure you want to delete this challenge?')) {
        return;
    }

    try {
        const response = await fetch(`/admin/api/community/challenges/${id}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            showNotification('Challenge deleted successfully!', 'success');
            document.querySelector(`[data-id="${id}"]`).remove();
        } else {
            throw new Error('Failed to delete challenge');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('Failed to delete challenge', 'error');
    }
}

function filterChallenges() {
    const statusFilter = document.getElementById('statusFilter').value;
    const categoryFilter = document.getElementById('categoryFilter').value;
    const challenges = document.querySelectorAll('.challenge-card');

    challenges.forEach(challenge => {
        const status = challenge.getAttribute('data-status');
        const category = challenge.getAttribute('data-category');
        
        const statusMatch = !statusFilter || status === statusFilter;
        const categoryMatch = !categoryFilter || category === categoryFilter;
        
        if (statusMatch && categoryMatch) {
            challenge.style.display = 'block';
        } else {
            challenge.style.display = 'none';
        }
    });
}

function formatDateForInput(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toISOString().slice(0, 16);
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('challengeModal');
    if (event.target === modal) {
        closeChallengeModal();
    }
}
</script>
