<div class="space-y-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-800 dark:text-white">{{#if isNew}}Create New{{else}}Edit{{/if}} Help Article</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">{{#if isNew}}Create a new help article for users{{else}}Edit the selected help article{{/if}}</p>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm">
        <div class="p-6">
            <form action="/admin/help{{#unless isNew}}/{{article.id}}{{/unless}}" method="POST" class="space-y-6">
                {{#unless isNew}}
                <input type="hidden" name="id" value="{{article.id}}">
                {{/unless}}
                
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Article Title *</label>
                    <input type="text" id="title" name="title" required 
                           value="{{article.title}}" placeholder="How to get started with Power Up"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Category *</label>
                        <select id="category" name="category" required 
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            <option value="">Select Category</option>
                            <option value="Getting Started" {{#if (eq article.category "Getting Started")}}selected{{/if}}>Getting Started</option>
                            <option value="Account Management" {{#if (eq article.category "Account Management")}}selected{{/if}}>Account Management</option>
                            <option value="Habit Tracking" {{#if (eq article.category "Habit Tracking")}}selected{{/if}}>Habit Tracking</option>
                            <option value="Goal Setting" {{#if (eq article.category "Goal Setting")}}selected{{/if}}>Goal Setting</option>
                            <option value="Progress Analytics" {{#if (eq article.category "Progress Analytics")}}selected{{/if}}>Progress Analytics</option>
                            <option value="Troubleshooting" {{#if (eq article.category "Troubleshooting")}}selected{{/if}}>Troubleshooting</option>
                            <option value="FAQ" {{#if (eq article.category "FAQ")}}selected{{/if}}>FAQ</option>
                        </select>
                    </div>
                    <div>
                        <label for="order" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Display Order</label>
                        <input type="number" id="order" name="order" 
                               value="{{article.order}}" min="1" placeholder="1"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    </div>
                </div>

                <div>
                    <label for="content" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Article Content *</label>
                    <textarea id="content" name="content" rows="15" required 
                              placeholder="Write your help article content here..."
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white">{{article.content}}</textarea>
                </div>

                <div>
                    <label class="flex items-center cursor-pointer">
                        <input type="checkbox" name="isActive" {{#if article.isActive}}checked{{/if}} class="sr-only peer">
                        <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        <span class="ml-3 font-medium text-gray-700 dark:text-gray-300">Published (visible to users)</span>
                    </label>
                </div>

                <div class="flex justify-end gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <a href="/admin/help" class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors">Cancel</a>
                    <button type="submit" class="px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium">
                        {{#if isNew}}Create Article{{else}}Update Article{{/if}}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>


