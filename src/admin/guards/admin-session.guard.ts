import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Request } from 'express';

@Injectable()
export class AdminSessionGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request: Request = context.switchToHttp().getRequest();
    
    // Check if admin session exists
    if (request.session && request.session.adminId) {
      return true;
    }
    
    throw new UnauthorizedException('Admin session required');
  }
}
