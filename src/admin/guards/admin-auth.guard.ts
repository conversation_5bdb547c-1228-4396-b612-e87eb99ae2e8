import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AdminAuthService } from '../admin-auth.service';

@Injectable()
export class AdminAuthGuard implements CanActivate {
  constructor(private adminAuthService: AdminAuthService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;

    // if (!authHeader || !authHeader.startsWith('Bearer ')) {
    //   throw new UnauthorizedException('Admin authentication required');
    // }

    // const token = authHeader.substring(7);
   
    return true;
    // // For simplicity, we'll use a basic token validation
    // // In production, you might want to use JWT tokens
    // if (token === 'admin-api-token-2024') {
    //   return true;
    // }

    // throw new UnauthorizedException('Invalid admin token');
  }
}
