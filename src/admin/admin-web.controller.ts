import { Controller, Get, Post, Put, Delete, Body, Render, Res, Req, Session, UseGuards, Redirect, Param, Query } from '@nestjs/common';
import { AdminService } from './admin.service';
import { AdminAuthService } from './admin-auth.service';
import { AdminSessionGuard } from './guards/admin-session.guard';
import type { Request, Response } from 'express';
import { SearchBlogPostDto } from '../blog/dto/search-blog-post.dto';
import PDFDocument from 'pdfkit';

// Add monetization imports
import { SubscriptionPlanService } from '../monetization/services/subscription-plan.service';
import { SubscriptionService } from '../monetization/services/subscription.service';
import { CouponCodeService } from '../monetization/services/coupon-code.service';
import { CreateSubscriptionPlanDto, UpdateSubscriptionPlanDto } from '../monetization/dto/subscription-plan.dto';
import { CreateCouponCodeDto, UpdateCouponCodeDto } from '../monetization/dto/coupon-code.dto';

@Controller('admin')
export class AdminWebController {
  constructor(
    private readonly adminService: AdminService,
    private readonly adminAuthService: AdminAuthService,
    // Add monetization services
    private readonly subscriptionPlanService: SubscriptionPlanService,
    private readonly subscriptionService: SubscriptionService,
    private readonly couponCodeService: CouponCodeService,
  ) {}

  // Root admin route - check auth and redirect
  @Get()
  async adminRoot(@Session() session: any, @Res() res: Response) {
    if (session.adminId) {
      res.redirect('/admin/dashboard');
    } else {
      res.redirect('/admin/login');
    }
  }

  // Login page
  @Get('login')
  async loginPage(@Session() session: any, @Res() res: Response) {
    if (session.adminId) {
      res.redirect('/admin/dashboard');
      return;
    }
    res.render('login', { 
      title: 'Admin Login - Power Up Control Panel',
      layout: false // Disable layout for login page
    });
  }

  // Handle login
  @Post('login')
  async login(
    @Body() body: { email: string; password: string },
    @Session() session: any,
    @Res() res: Response,
  ) {
    const admin = await this.adminAuthService.validateAdmin(body.email, body.password);
    
    if (admin) {
      session.adminId = admin.id;
      res.redirect('/admin/dashboard');
    } else {
      res.render('login', {
        title: 'Admin Login - Power Up Control Panel',
        error: 'Invalid email or password',
        email: body.email,
        layout: false // Disable layout for login page
      });
    }
  }

  // Logout
  @Post('logout')
  async logout(@Session() session: any, @Res() res: Response) {
    session.adminId = null;
    res.redirect('/admin/login');
  }

  // Dashboard
  @Get('dashboard')
  @UseGuards(AdminSessionGuard)
  @Render('dashboard')
  async dashboard() {
    const stats = await this.adminService.getDashboardStats();
    return {
      title: 'Dashboard - Power Up Control Panel',
      stats,
    };
  }

  // Landing Page Management
  @Get('landing-page')
  @UseGuards(AdminSessionGuard)
  @Render('landing-page')
  async landingPageManagement() {
    const content = await this.adminService.getLandingPageContent();
    return {
      title: 'Landing Page Management - Power Up Control Panel',
      sections: content,
    };
  }

  @Post('landing-page/:section')
  @UseGuards(AdminSessionGuard)
  async updateLandingPageContent(
    @Param('section') section: string,
    @Body() body: any,
    @Res() res: Response,
  ) {
    try {
      await this.adminService.updateLandingPageContent(section, body.content);
      res.redirect('/admin/landing-page?success=Content updated successfully');
    } catch (error) {
      res.redirect('/admin/landing-page?error=Failed to update content');
    }
  }

  // App Constants Management
  @Get('app-constants')
  @UseGuards(AdminSessionGuard)
  @Render('app-constants')
  async appConstantsManagement() {
    const constants = await this.adminService.getAppConstants();
    return {
      title: 'App Constants Management - Power Up Control Panel',
      constants,
    };
  }

  @Post('app-constants')
  @UseGuards(AdminSessionGuard)
  async updateAppConstants(
    @Body() body: any,
    @Res() res: Response,
  ) {
    try {
      await this.adminService.updateAppConstants(body);
      res.redirect('/admin/app-constants?success=Constants updated successfully');
    } catch (error) {
      res.redirect('/admin/app-constants?error=Failed to update constants');
    }
  }

  // Help Module Management
  @Get('help')
  @UseGuards(AdminSessionGuard)
  @Render('help-management')
  async helpManagement() {
    const articles = await this.adminService.getHelpArticles();
    return {
      title: 'Help Management - Power Up Control Panel',
      articles,
    };
  }

  @Get('help/new')
  @UseGuards(AdminSessionGuard)
  @Render('help-editor')
  async newHelpArticle() {
    return {
      title: 'New Help Article - Power Up Control Panel',
      isNew: true,
    };
  }

  @Get('help/:id/edit')
  @UseGuards(AdminSessionGuard)
  @Render('help-editor')
  async editHelpArticle(@Param('id') id: string) {
    const article = await this.adminService.getHelpArticle(id);
    return {
      title: 'Edit Help Article - Power Up Control Panel',
      article,
      isNew: false,
    };
  }

  @Post('help')
  @UseGuards(AdminSessionGuard)
  async createHelpArticle(@Body() body: any, @Res() res: Response) {
    try {
      await this.adminService.createHelpArticle(body);
      res.redirect('/admin/help?success=Article created successfully');
    } catch (error) {
      res.redirect('/admin/help?error=Failed to create article');
    }
  }

  @Post('help/:id')
  @UseGuards(AdminSessionGuard)
  async updateHelpArticle(
    @Param('id') id: string,
    @Body() body: any,
    @Res() res: Response,
  ) {
    try {
      await this.adminService.updateHelpArticle(id, body);
      res.redirect('/admin/help?success=Article updated successfully');
    } catch (error) {
      res.redirect('/admin/help?error=Failed to update article');
    }
  }

  // Community Management
  @Get('community')
  @UseGuards(AdminSessionGuard)
  @Render('community-management')
  async communityManagement() {
    const communityData = await this.adminService.getCommunityData();
    return {
      title: 'Community Management - Power Up Control Panel',
      ...communityData,
    };
  }

  @Get('community/challenges/:id/edit')
  @UseGuards(AdminSessionGuard)
  @Render('challenge-editor')
  async editChallenge(@Param('id') id: string) {
    const challenge = await this.adminService.getChallenge(id);
    return {
      title: 'Edit Challenge - Power Up Control Panel',
      challenge,
    };
  }

  @Post('community/challenges/:id')
  @UseGuards(AdminSessionGuard)
  async updateChallenge(
    @Param('id') id: string,
    @Body() body: any,
    @Res() res: Response,
  ) {
    try {
      await this.adminService.updateChallenge(id, body);
      res.redirect('/admin/community?success=Challenge updated successfully');
    } catch (error) {
      res.redirect('/admin/community?error=Failed to update challenge');
    }
  }

  // Blog Management
  @Get('blog')
  @UseGuards(AdminSessionGuard)
  @Render('blog-management')
  async blogManagement(@Query() searchDto: SearchBlogPostDto) {
    const { posts, total } = await this.adminService.getAllBlogPosts(searchDto);
    const stats = await this.adminService.getBlogStats();
    return {
      title: 'Blog Management - Power Up Control Panel',
      posts,
      total,
      stats,
      searchParams: searchDto,
      includeEditorJS: true, // Enable Editor.js for Notion-like editing
    };
  }

  @Post('blog')
  @UseGuards(AdminSessionGuard)
  async createBlogPost(@Body() body: any, @Session() session: any, @Res() res: Response) {
    try {
      await this.adminService.createBlogPost(body, session.adminId);
      res.redirect('/admin/blog?success=Blog post created successfully');
    } catch (error) {
      res.redirect('/admin/blog?error=Failed to create blog post');
    }
  }

  @Post('blog/:id')
  @UseGuards(AdminSessionGuard)
  async updateBlogPost(
    @Param('id') id: string,
    @Body() body: any,
    @Res() res: Response,
  ) {
    try {
      await this.adminService.updateBlogPost(id, body);
      res.redirect('/admin/blog?success=Blog post updated successfully');
    } catch (error) {
      res.redirect('/admin/blog?error=Failed to update blog post');
    }
  }

  // API routes for blog management (session-based for frontend JavaScript)
  @Get('api/blog')
  @UseGuards(AdminSessionGuard)
  async apiBlogGetAll(@Query() searchDto: SearchBlogPostDto, @Res() res: Response) {
    try {
      const result = await this.adminService.getAllBlogPosts(searchDto);
      res.json(result);
    } catch (error) {
      res.status(500).json({ message: 'Failed to fetch blog posts', error: error.message });
    }
  }

  @Get('api/blog/:id')
  @UseGuards(AdminSessionGuard)
  async apiBlogGetOne(@Param('id') id: string, @Res() res: Response) {
    try {
      const post = await this.adminService.getBlogPost(id);
      res.json(post);
    } catch (error) {
      res.status(404).json({ message: 'Blog post not found', error: error.message });
    }
  }

  @Post('api/blog')
  @UseGuards(AdminSessionGuard)
  async apiBlogCreate(@Body() body: any, @Session() session: any, @Res() res: Response) {
    try {
      const post = await this.adminService.createBlogPost(body, session.adminId);
      res.json(post);
    } catch (error) {
      res.status(400).json({ message: 'Failed to create blog post', error: error.message });
    }
  }

  @Put('api/blog/:id')
  @UseGuards(AdminSessionGuard)
  async apiBlogUpdate(@Param('id') id: string, @Body() body: any, @Res() res: Response) {
    try {
      const post = await this.adminService.updateBlogPost(id, body);
      res.json(post);
    } catch (error) {
      res.status(400).json({ message: 'Failed to update blog post', error: error.message });
    }
  }

  @Delete('api/blog/:id')
  @UseGuards(AdminSessionGuard)
  async apiBlogDelete(@Param('id') id: string, @Res() res: Response) {
    try {
      await this.adminService.deleteBlogPost(id);
      res.json({ message: 'Blog post deleted successfully' });
    } catch (error) {
      res.status(400).json({ message: 'Failed to delete blog post', error: error.message });
    }
  }

  // Monetization Management
  @Get('monetization')
  @UseGuards(AdminSessionGuard)
  @Render('monetization/index')
  async monetizationDashboard() {
    const [plans, subscriptions, coupons] = await Promise.all([
      this.subscriptionPlanService.findAll(),
      this.subscriptionService.findAll(),
      this.couponCodeService.findAll(),
    ]);

    // Calculate basic analytics
    const activeSubscriptions = subscriptions.filter(s => s.status === 'active');
    const totalRevenue = activeSubscriptions.reduce((sum, s) => sum + (s.price || 0), 0);
    const activeCoupons = coupons.filter(c => c.active);

    return {
      title: 'Monetization Dashboard - Power Up Control Panel',
      plans: plans.slice(0, 5), // Show only first 5 for overview
      subscriptions: subscriptions.slice(0, 10), // Show recent 10
      coupons: coupons.slice(0, 5), // Show only first 5 for overview
      stats: {
        totalPlans: plans.length,
        activePlans: plans.filter(p => p.active).length,
        totalSubscriptions: subscriptions.length,
        activeSubscriptions: activeSubscriptions.length,
        totalCoupons: coupons.length,
        activeCoupons: activeCoupons.length,
        totalRevenue,
      },
    };
  }

  // Subscription Plans Management
  @Get('monetization/plans')
  @UseGuards(AdminSessionGuard)
  @Render('monetization/plans/index')
  async monetizationPlans() {
    const plans = await this.subscriptionPlanService.findAll();
    return {
      title: 'Subscription Plans - Power Up Control Panel',
      plans,
    };
  }

  @Get('monetization/plans/new')
  @UseGuards(AdminSessionGuard)
  @Render('monetization/plans/form')
  async newMonetizationPlan() {
    return {
      title: 'Create New Subscription Plan - Power Up Control Panel',
      isNew: true,
    };
  }

  @Post('monetization/plans')
  @UseGuards(AdminSessionGuard)
  async createMonetizationPlan(@Body() body: any, @Res() res: Response) {
    try {
      // Handle features array - filter out empty values
      let features: string[] = [];
      if (body['features[]']) {
        if (Array.isArray(body['features[]'])) {
          features = body['features[]'].filter(f => f && f.trim());
        } else {
          features = [body['features[]']].filter(f => f && f.trim());
        }
      } else if (body.features) {
        if (Array.isArray(body.features)) {
          features = body.features.filter(f => f && f.trim());
        } else {
          features = [body.features].filter(f => f && f.trim());
        }
      }
      
      // Handle boolean fields more carefully
      let hasFreeTrialTier = body.hasFreeTrialTier === 'true' || body.hasFreeTrialTier === true;
      let active = body.active === 'true' || body.active === true;
      
      // Handle cases where checkbox value might be an array (hidden + checkbox)
      if (Array.isArray(body.hasFreeTrialTier)) {
        const hasTrue = body.hasFreeTrialTier.includes('true');
        hasFreeTrialTier = hasTrue;
      }
      if (Array.isArray(body.active)) {
        const hasTrue = body.active.includes('true');
        active = hasTrue;
      }
      
      // Transform form data to proper types
      const createPlanDto: CreateSubscriptionPlanDto = {
        name: body.name,
        description: body.description,
        type: body.type,
        platform: body.platform,
        price: parseFloat(body.price),
        currency: body.currency || 'USD',
        productId: body.productId,
        hasFreeTrialTier: hasFreeTrialTier,
        freeTrialDays: parseInt(body.freeTrialDays) || 0,
        active: active,
        features: features,
        sortOrder: parseInt(body.sortOrder) || 0,
      };
      
      console.log('Transformed data for create:', createPlanDto);
      
      await this.subscriptionPlanService.create(createPlanDto);
      res.redirect('/admin/monetization/plans?success=Plan created successfully');
    } catch (error) {
      console.error('Error creating plan:', error);
      res.redirect('/admin/monetization/plans/new?error=' + encodeURIComponent(error.message));
    }
  }

  @Get('monetization/plans/:id/edit')
  @UseGuards(AdminSessionGuard)
  @Render('monetization/plans/form')
  async editMonetizationPlan(@Param('id') id: string) {
    const plan = await this.subscriptionPlanService.findOne(id);
    return {
      title: 'Edit Subscription Plan - Power Up Control Panel',
      plan,
      isNew: false,
    };
  }

  @Post('monetization/plans/:id')
  @UseGuards(AdminSessionGuard)
  async updateMonetizationPlan(@Param('id') id: string, @Body() body: any, @Res() res: Response) {
    try {
      console.log('Raw form data received:', body);
      console.log('Form data keys:', Object.keys(body));
      
      // Handle features array - filter out empty values
      let features: string[] = [];
      if (body['features[]']) {
        if (Array.isArray(body['features[]'])) {
          features = body['features[]'].filter(f => f && f.trim());
        } else {
          features = [body['features[]']].filter(f => f && f.trim());
        }
      } else if (body.features) {
        if (Array.isArray(body.features)) {
          features = body.features.filter(f => f && f.trim());
        } else {
          features = [body.features].filter(f => f && f.trim());
        }
      }
      
      // Handle boolean fields more carefully
      // For checkboxes, if the field exists and is 'true', set to true, otherwise false
      let hasFreeTrialTier = body.hasFreeTrialTier === 'true' || body.hasFreeTrialTier === true;
      let active = body.active === 'true' || body.active === true;
      
      // Handle cases where checkbox value might be an array (hidden + checkbox)
      if (Array.isArray(body.hasFreeTrialTier)) {
        const hasTrue = body.hasFreeTrialTier.includes('true');
        hasFreeTrialTier = hasTrue;
      }
      if (Array.isArray(body.active)) {
        const hasTrue = body.active.includes('true');
        active = hasTrue;
      }
      
      // Transform form data to proper types
      const updatePlanDto: UpdateSubscriptionPlanDto = {
        name: body.name,
        description: body.description,
        price: body.price ? parseFloat(body.price) : undefined,
        currency: body.currency,
        productId: body.productId,
        hasFreeTrialTier: hasFreeTrialTier,
        freeTrialDays: body.freeTrialDays ? parseInt(body.freeTrialDays) : 0,
        active: active,
        features: features,
        sortOrder: body.sortOrder ? parseInt(body.sortOrder) : undefined,
      };
      
      console.log('Transformed data for update:', updatePlanDto);
      console.log('Boolean values - hasFreeTrialTier:', updatePlanDto.hasFreeTrialTier, 'active:', updatePlanDto.active);
      console.log('Features array:', updatePlanDto.features);
      
      await this.subscriptionPlanService.update(id, updatePlanDto);
      res.redirect('/admin/monetization/plans?success=Plan updated successfully');
    } catch (error) {
      console.error('Error updating plan:', error);
      res.redirect(`/admin/monetization/plans/${id}/edit?error=` + encodeURIComponent(error.message));
    }
  }

  @Post('monetization/plans/:id/delete')
  @UseGuards(AdminSessionGuard)
  async deleteMonetizationPlan(@Param('id') id: string, @Res() res: Response) {
    try {
      await this.subscriptionPlanService.remove(id);
      res.redirect('/admin/monetization/plans?success=Plan deleted successfully');
    } catch (error) {
      res.redirect('/admin/monetization/plans?error=' + encodeURIComponent(error.message));
    }
  }

  // Subscriptions Management
  @Get('monetization/subscriptions')
  @UseGuards(AdminSessionGuard)
  @Render('monetization/subscriptions/index')
  async monetizationSubscriptions() {
    const subscriptions = await this.subscriptionService.findAll();
    return {
      title: 'Subscriptions - Power Up Control Panel',
      subscriptions,
    };
  }

  @Get('monetization/subscriptions/:id')
  @UseGuards(AdminSessionGuard)
  @Render('monetization/subscriptions/show')
  async showMonetizationSubscription(@Param('id') id: string) {
    const subscription = await this.subscriptionService.findOne(id);
    return {
      title: 'Subscription Details - Power Up Control Panel',
      subscription,
    };
  }

  @Post('monetization/subscriptions/:id/cancel')
  @UseGuards(AdminSessionGuard)
  async cancelMonetizationSubscription(@Param('id') id: string, @Res() res: Response) {
    try {
      await this.subscriptionService.cancel(id);
      res.redirect('/admin/monetization/subscriptions?success=Subscription cancelled successfully');
    } catch (error) {
      res.redirect('/admin/monetization/subscriptions?error=' + encodeURIComponent(error.message));
    }
  }

  // Coupon Codes Management
  @Get('monetization/coupons')
  @UseGuards(AdminSessionGuard)
  @Render('monetization/coupons/index')
  async monetizationCoupons() {
    const coupons = await this.couponCodeService.findAll();
    return {
      title: 'Coupon Codes - Power Up Control Panel',
      coupons,
    };
  }

  @Get('monetization/coupons/new')
  @UseGuards(AdminSessionGuard)
  @Render('monetization/coupons/form')
  async newMonetizationCoupon() {
    return {
      title: 'Create New Coupon Code - Power Up Control Panel',
      isNew: true,
    };
  }

  @Post('monetization/coupons')
  @UseGuards(AdminSessionGuard)
  async createMonetizationCoupon(@Body() body: any, @Res() res: Response) {
    try {
      // Transform form data to proper types
      const createCouponDto: CreateCouponCodeDto = {
        code: body.code.toUpperCase(),
        name: body.name || body.code,
        description: body.description,
        type: body.type,
        value: body.value ? parseFloat(body.value) : undefined,
        maxUses: body.maxUses ? parseInt(body.maxUses) : undefined,
        validFrom: body.validFrom,
        validUntil: body.validUntil,
        firstTimeOnly: body.firstTimeOnly === 'true',
        applicablePlans: body.applicablePlans ? (Array.isArray(body.applicablePlans) ? body.applicablePlans : [body.applicablePlans]) : undefined,
        active: body.active === 'true',
        minimumPurchaseAmount: body.minimumPurchaseAmount ? parseFloat(body.minimumPurchaseAmount) : undefined,
        freeTrialDays: body.freeTrialDays ? parseInt(body.freeTrialDays) : undefined,
      };
      
      console.log('Creating coupon with data:', createCouponDto);
      await this.couponCodeService.create(createCouponDto);
      res.redirect('/admin/monetization/coupons?success=Coupon created successfully');
    } catch (error) {
      console.error('Error creating coupon:', error);
      res.redirect('/admin/monetization/coupons/new?error=' + encodeURIComponent(error.message));
    }
  }

  @Get('monetization/coupons/:id/edit')
  @UseGuards(AdminSessionGuard)
  @Render('monetization/coupons/form')
  async editMonetizationCoupon(@Param('id') id: string) {
    const coupon = await this.couponCodeService.findOne(id);
    return {
      title: 'Edit Coupon Code - Power Up Control Panel',
      coupon,
      isNew: false,
    };
  }

  @Post('monetization/coupons/:id')
  @UseGuards(AdminSessionGuard)
  async updateMonetizationCoupon(@Param('id') id: string, @Body() body: any, @Res() res: Response) {
    try {
      // Transform form data to proper types
      const updateCouponDto: UpdateCouponCodeDto = {
        name: body.name,
        description: body.description,
        value: body.value ? parseFloat(body.value) : undefined,
        maxUses: body.maxUses ? parseInt(body.maxUses) : undefined,
        validFrom: body.validFrom,
        validUntil: body.validUntil,
        firstTimeOnly: body.firstTimeOnly === 'true',
        applicablePlans: body.applicablePlans ? (Array.isArray(body.applicablePlans) ? body.applicablePlans : [body.applicablePlans]) : undefined,
        active: body.active === 'true',
        minimumPurchaseAmount: body.minimumPurchaseAmount ? parseFloat(body.minimumPurchaseAmount) : undefined,
        freeTrialDays: body.freeTrialDays ? parseInt(body.freeTrialDays) : undefined,
      };
      
      console.log('Updating coupon with data:', updateCouponDto);
      await this.couponCodeService.update(id, updateCouponDto);
      res.redirect('/admin/monetization/coupons?success=Coupon updated successfully');
    } catch (error) {
      console.error('Error updating coupon:', error);
      res.redirect(`/admin/monetization/coupons/${id}/edit?error=` + encodeURIComponent(error.message));
    }
  }

  @Post('monetization/coupons/:id/delete')
  @UseGuards(AdminSessionGuard)
  async deleteMonetizationCoupon(@Param('id') id: string, @Res() res: Response) {
    try {
      await this.couponCodeService.remove(id);
      res.redirect('/admin/monetization/coupons?success=Coupon deleted successfully');
    } catch (error) {
      res.redirect('/admin/monetization/coupons?error=' + encodeURIComponent(error.message));
    }
  }

  @Post('monetization/coupons/:id/activate')
  @UseGuards(AdminSessionGuard)
  async activateMonetizationCoupon(@Param('id') id: string, @Res() res: Response) {
    try {
      await this.couponCodeService.update(id, { active: true });
      res.redirect('/admin/monetization/coupons?success=Coupon activated successfully');
    } catch (error) {
      res.redirect('/admin/monetization/coupons?error=' + encodeURIComponent(error.message));
    }
  }

  @Post('monetization/coupons/:id/deactivate')
  @UseGuards(AdminSessionGuard)
  async deactivateMonetizationCoupon(@Param('id') id: string, @Res() res: Response) {
    try {
      await this.couponCodeService.update(id, { active: false });
      res.redirect('/admin/monetization/coupons?success=Coupon deactivated successfully');
    } catch (error) {
      res.redirect('/admin/monetization/coupons?error=' + encodeURIComponent(error.message));
    }
  }

  // Monetization Analytics
  @Get('monetization/analytics')
  @UseGuards(AdminSessionGuard)
  @Render('monetization/analytics')
  async monetizationAnalytics() {
    const [plans, subscriptions, coupons] = await Promise.all([
      this.subscriptionPlanService.findAll(),
      this.subscriptionService.findAll(),
      this.couponCodeService.findAll(),
    ]);

    // Calculate basic stats
    const activeSubscriptions = subscriptions.filter(s => s.status === 'active');
    const expiredSubscriptions = subscriptions.filter(s => s.status === 'expired');
    const cancelledSubscriptions = subscriptions.filter(s => s.status === 'cancelled');
    const totalRevenue = activeSubscriptions.reduce((sum, s) => sum + (s.price || 0), 0);
    const activeCoupons = coupons.filter(c => c.active);

    // Calculate plan performance
    const planPerformance = plans.map(plan => {
      const planSubscriptions = subscriptions.filter(s => s.planId === plan.id);
      const activePlanSubscriptions = planSubscriptions.filter(s => s.status === 'active');
      const monthlyRevenue = activePlanSubscriptions.reduce((sum, s) => sum + (s.price || 0), 0);
      
      return {
        id: plan.id,
        name: plan.name,
        billingCycle: plan.type, // Use 'type' instead of 'billingCycle'
        price: plan.price,
        activeSubscriptions: activePlanSubscriptions.length,
        monthlyRevenue: monthlyRevenue.toFixed(2),
        conversionRate: planSubscriptions.length > 0 ? ((activePlanSubscriptions.length / planSubscriptions.length) * 100).toFixed(1) : '0.0',
        monthlyGrowth: Math.floor(Math.random() * 20) + 1, // TODO: Calculate actual growth
      };
    });

    // Get top performing coupons
    const topCoupons = coupons
      .filter(c => c.usedCount > 0)
      .sort((a, b) => b.usedCount - a.usedCount)
      .slice(0, 5)
      .map(coupon => ({
        ...coupon,
        discountType: coupon.type, // Map type to discountType for template
        discountValue: coupon.value, // Map value to discountValue for template
        totalSaved: (coupon.usedCount * (coupon.type === 'percentage' ? 50 : coupon.value || 0)).toFixed(0) // Simplified calculation
      }));

    // Generate recent activity (mock data for now - TODO: implement real activity tracking)
    const recentActivity = [
      { type: 'subscription', color: 'green', message: 'New subscription: Pro Plan', timeAgo: '2 minutes ago' },
      { type: 'coupon', color: 'purple', message: 'Coupon used: SAVE20', timeAgo: '5 minutes ago' },
      { type: 'payment', color: 'blue', message: 'Payment received: $19.99', timeAgo: '12 minutes ago' },
      { type: 'expiry', color: 'yellow', message: 'Subscription expired', timeAgo: '1 hour ago' },
    ];

    return {
      title: 'Monetization Analytics - Power Up Control Panel',
      stats: {
        totalPlans: plans.length,
        activePlans: plans.filter(p => p.active).length,
        totalSubscriptions: subscriptions.length,
        activeSubscriptions: activeSubscriptions.length,
        expiredSubscriptions: expiredSubscriptions.length,
        cancelledSubscriptions: cancelledSubscriptions.length,
        totalCoupons: coupons.length,
        activeCoupons: activeCoupons.length,
        usedCoupons: coupons.filter(c => c.usedCount > 0).length,
        totalRevenue: totalRevenue.toFixed(2),
      },
      planPerformance,
      topCoupons,
      recentActivity,
      plans,
      subscriptions,
      coupons,
    };
  }

  // Export Analytics Endpoints
  @Get('monetization/analytics/export/csv')
  @UseGuards(AdminSessionGuard)
  async exportAnalyticsCSV(@Res() res: Response) {
    const [plans, subscriptions, coupons] = await Promise.all([
      this.subscriptionPlanService.findAll(),
      this.subscriptionService.findAll(),
      this.couponCodeService.findAll(),
    ]);

    // Create CSV content
    const csvData: (string | number)[][] = [];
    
    // Add header
    csvData.push(['Plan Name', 'Type', 'Price', 'Active Subscriptions', 'Total Revenue', 'Status']);
    
    // Add plan data
    plans.forEach(plan => {
      const planSubscriptions = subscriptions.filter(s => s.planId === plan.id && s.status === 'active');
      const planRevenue = planSubscriptions.reduce((sum, s) => sum + (s.price || 0), 0);
      
      csvData.push([
        plan.name,
        plan.type,
        plan.price,
        planSubscriptions.length,
        planRevenue.toFixed(2),
        plan.active ? 'Active' : 'Inactive'
      ]);
    });

    // Convert to CSV string
    const csvContent = csvData.map(row => row.map(field => `"${field}"`).join(',')).join('\n');

    // Set headers and send response
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename="monetization-analytics.csv"');
    res.send(csvContent);
  }

  @Get('monetization/analytics/export/report')
  @UseGuards(AdminSessionGuard)
  async generateAnalyticsReport(@Res() res: Response) {
    const [plans, subscriptions, coupons] = await Promise.all([
      this.subscriptionPlanService.findAll(),
      this.subscriptionService.findAll(),
      this.couponCodeService.findAll(),
    ]);

    // Calculate comprehensive stats
    const activeSubscriptions = subscriptions.filter(s => s.status === 'active');
    const expiredSubscriptions = subscriptions.filter(s => s.status === 'expired');
    const cancelledSubscriptions = subscriptions.filter(s => s.status === 'cancelled');
    const totalRevenue = activeSubscriptions.reduce((sum, s) => sum + (s.price || 0), 0);
    const activeCoupons = coupons.filter(c => c.active);

    // Create PDF document
    const doc = new PDFDocument({ margin: 50 });
    
    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', 'attachment; filename="monetization-analytics-report.pdf"');
    
    // Pipe PDF to response
    doc.pipe(res);

    // Add header
    doc.fontSize(24)
       .font('Helvetica-Bold')
       .text('Monetization Analytics Report', 50, 50, { align: 'center' });
    
    doc.fontSize(12)
       .font('Helvetica')
       .text(`Generated on ${new Date().toLocaleDateString()}`, 50, 85, { align: 'center' });

    // Add summary statistics
    doc.fontSize(18)
       .font('Helvetica-Bold')
       .text('Executive Summary', 50, 130);

    let yPosition = 160;
    const statsData = [
      { label: 'Total Revenue', value: `$${totalRevenue.toFixed(2)}` },
      { label: 'Active Subscriptions', value: activeSubscriptions.length.toString() },
      { label: 'Expired Subscriptions', value: expiredSubscriptions.length.toString() },
      { label: 'Cancelled Subscriptions', value: cancelledSubscriptions.length.toString() },
      { label: 'Total Plans', value: plans.length.toString() },
      { label: 'Active Coupons', value: activeCoupons.length.toString() },
    ];

    // Create stats grid
    const statsPerRow = 2;
    const statWidth = 250;
    const statHeight = 60;
    
    statsData.forEach((stat, index) => {
      const col = index % statsPerRow;
      const row = Math.floor(index / statsPerRow);
      const x = 50 + (col * statWidth);
      const y = yPosition + (row * statHeight);
      
      // Draw stat box
      doc.rect(x, y, statWidth - 20, statHeight - 10)
         .stroke('#E5E7EB');
      
      doc.fontSize(10)
         .font('Helvetica')
         .fillColor('#6B7280')
         .text(stat.label, x + 10, y + 10, { width: statWidth - 40 });
      
      doc.fontSize(16)
         .font('Helvetica-Bold')
         .fillColor('#111827')
         .text(stat.value, x + 10, y + 25, { width: statWidth - 40 });
    });

    yPosition += Math.ceil(statsData.length / statsPerRow) * statHeight + 40;

    // Plan Performance Section
    doc.fontSize(18)
       .font('Helvetica-Bold')
       .fillColor('#111827')
       .text('Plan Performance', 50, yPosition);

    yPosition += 30;

    // Table headers
    const tableHeaders = ['Plan Name', 'Type', 'Price', 'Active Subs', 'Revenue', 'Status'];
    const colWidths = [120, 80, 70, 80, 80, 70];
    let xPos = 50;

    // Draw header row
    doc.rect(50, yPosition, 500, 25)
       .fillAndStroke('#F3F4F6', '#E5E7EB');

    tableHeaders.forEach((header, index) => {
      doc.fontSize(10)
         .font('Helvetica-Bold')
         .fillColor('#374151')
         .text(header, xPos + 5, yPosition + 8, { width: colWidths[index] - 10 });
      xPos += colWidths[index];
    });

    yPosition += 25;

    // Plan data rows
    plans.forEach((plan, index) => {
      const planSubscriptions = subscriptions.filter(s => s.planId === plan.id && s.status === 'active');
      const planRevenue = planSubscriptions.reduce((sum, s) => sum + (s.price || 0), 0);
      
      // Alternate row colors
      const fillColor = index % 2 === 0 ? '#FFFFFF' : '#F9FAFB';
      doc.rect(50, yPosition, 500, 20)
         .fillAndStroke(fillColor, '#E5E7EB');

      const rowData = [
        plan.name,
        plan.type,
        `$${plan.price}`,
        planSubscriptions.length.toString(),
        `$${planRevenue.toFixed(2)}`,
        plan.active ? 'Active' : 'Inactive'
      ];

      xPos = 50;
      rowData.forEach((data, colIndex) => {
        doc.fontSize(9)
           .font('Helvetica')
           .fillColor('#374151')
           .text(data, xPos + 5, yPosition + 6, { 
             width: colWidths[colIndex] - 10,
             ellipsis: true 
           });
        xPos += colWidths[colIndex];
      });

      yPosition += 20;
      
      // Check if we need a new page
      if (yPosition > 700) {
        doc.addPage();
        yPosition = 50;
      }
    });

    yPosition += 30;

    // Coupon Performance Section
    if (yPosition > 600) {
      doc.addPage();
      yPosition = 50;
    }

    doc.fontSize(18)
       .font('Helvetica-Bold')
       .fillColor('#111827')
       .text('Coupon Performance', 50, yPosition);

    yPosition += 30;

    // Coupon table headers
    const couponHeaders = ['Code', 'Type', 'Value', 'Used Count', 'Status'];
    const couponColWidths = [100, 100, 80, 80, 80];
    xPos = 50;

    // Draw coupon header row
    doc.rect(50, yPosition, 440, 25)
       .fillAndStroke('#F3F4F6', '#E5E7EB');

    couponHeaders.forEach((header, index) => {
      doc.fontSize(10)
         .font('Helvetica-Bold')
         .fillColor('#374151')
         .text(header, xPos + 5, yPosition + 8, { width: couponColWidths[index] - 10 });
      xPos += couponColWidths[index];
    });

    yPosition += 25;

    // Coupon data rows
    coupons.forEach((coupon, index) => {
      // Alternate row colors
      const fillColor = index % 2 === 0 ? '#FFFFFF' : '#F9FAFB';
      doc.rect(50, yPosition, 440, 20)
         .fillAndStroke(fillColor, '#E5E7EB');

      const valueText = coupon.type === 'percentage' ? `${coupon.value}%` : `$${coupon.value}`;
      const couponRowData = [
        coupon.code,
        coupon.type,
        valueText,
        coupon.usedCount.toString(),
        coupon.active ? 'Active' : 'Inactive'
      ];

      xPos = 50;
      couponRowData.forEach((data, colIndex) => {
        doc.fontSize(9)
           .font('Helvetica')
           .fillColor('#374151')
           .text(data, xPos + 5, yPosition + 6, { 
             width: couponColWidths[colIndex] - 10,
             ellipsis: true 
           });
        xPos += couponColWidths[colIndex];
      });

      yPosition += 20;
      
      // Check if we need a new page
      if (yPosition > 700) {
        doc.addPage();
        yPosition = 50;
      }
    });

    // Add footer
    const pageCount = doc.bufferedPageRange().count;
    for (let i = 0; i < pageCount; i++) {
      doc.switchToPage(i);
      doc.fontSize(8)
         .font('Helvetica')
         .fillColor('#6B7280')
         .text(`Page ${i + 1} of ${pageCount} | Power Up Analytics Report`, 50, 750, { 
           align: 'center',
           width: 500 
         });
    }

    // Finalize PDF
    doc.end();
  }



}
