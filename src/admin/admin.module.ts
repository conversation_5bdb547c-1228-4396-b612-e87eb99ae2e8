import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NestExpressApplication } from '@nestjs/platform-express';
import { AdminController } from './admin.controller';
import { AdminWebController } from './admin-web.controller';
import { UploadController } from './upload.controller';
import { AdminService } from './admin.service';
import { AdminAuthService } from './admin-auth.service';
import { AdminSessionGuard } from './guards/admin-session.guard';
import { AdminUser } from './entities/admin-user.entity';
import { LandingPageContent } from './entities/landing-page-content.entity';
import { AppConstants } from './entities/app-constants.entity';
import { HelpModule } from '../help/help.module';
import { ChallengesModule } from '../challenges/challenges.module';
import { BlogModule } from '../blog/blog.module';
import { MonetizationModule } from '../monetization/monetization.module';
import { SubscriptionPlanService } from '../monetization/services/subscription-plan.service';
import { SubscriptionService } from '../monetization/services/subscription.service';
import { CouponCodeService } from '../monetization/services/coupon-code.service';
import { SubscriptionPlan } from '../monetization/entities/subscription-plan.entity';
import { Subscription } from '../monetization/entities/subscription.entity';
import { CouponCode } from '../monetization/entities/coupon-code.entity';
import { Transaction } from '../monetization/entities/transaction.entity';
import { User } from '../users/entities/user.entity';
import { join } from 'path';
import { create } from 'express-handlebars';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AdminUser, 
      LandingPageContent, 
      AppConstants,
      SubscriptionPlan,
      Subscription,
      CouponCode,
      Transaction,
      User,
    ]),
    HelpModule,
    ChallengesModule,
    BlogModule,
    MonetizationModule,
  ],
  controllers: [AdminWebController, AdminController, UploadController],
  providers: [
    AdminService, 
    AdminAuthService, 
    AdminSessionGuard,
  ],
  exports: [AdminService, AdminAuthService],
})
export class AdminModule {
  static configureHandlebars(app: NestExpressApplication) {
    // Configure Handlebars view engine for admin pages
    const hbs = create({
      layoutsDir: join(process.cwd(), 'src/admin/templates'),
      defaultLayout: 'layout', // Use layout.hbs as default layout for all admin pages
      extname: '.hbs',
      helpers: {
        eq: (a: any, b: any) => a === b,
        ne: (a: any, b: any) => a !== b,
        json: (context: any) => JSON.stringify(context),
        formatDate: (dateString: string) => {
          if (!dateString) return '';
          try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return dateString;
            return date.toLocaleDateString('en-US', { 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            });
          } catch (error) {
            return dateString;
          }
        },
        truncate: (str: string, length: number) => {
          if (!str || typeof str !== 'string') return str || '';
          if (!length || length <= 0) return str;
          return str.length > length ? str.substring(0, length) + '...' : str;
        },
        replace: (str: string, search: string, replace: string) => {
          if (!str || typeof str !== 'string') return str || '';
          if (!search || typeof search !== 'string') return str;
          if (typeof replace !== 'string') replace = '';
          return str.replace(new RegExp(search, 'g'), replace);
        },
        substring: (str: string, start: number, end: number) => {
          if (!str || typeof str !== 'string') return str || '';
          return str.substring(start, end);
        },
        gt: (a: any, b: any) => a > b,
        join: (arr: any[], separator: string) => {
          if (!Array.isArray(arr)) return '';
          return arr.join(separator || ', ');
        },
        formatDateForInput: (dateString: string) => {
          if (!dateString) return '';
          try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return '';
            return date.toISOString().slice(0, 16);
          } catch (error) {
            return '';
          }
        },
      },
    });

    app.engine('.hbs', hbs.engine);
    app.set('view engine', '.hbs');
    app.set('views', join(process.cwd(), 'src/admin/templates'));
  }
}
