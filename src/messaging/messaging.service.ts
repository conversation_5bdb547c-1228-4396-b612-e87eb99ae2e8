import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Message } from './entities/message.entity';
import { Conversation } from './entities/conversation.entity';
import { User, AuthProvider } from '../users/entities/user.entity';
import { FileService } from '../common/services/file.service';
import { File, FileType } from '../common/entities/file.entity';

export type MessageType = 
  | 'direct-message'
  | 'group-message'
  | 'challenge-message'
  | 'system-message';

// Interface for backward compatibility during migration
export interface InAppMessage {
  id?: string;
  senderId: string;
  recipientId: string | null;
  groupId?: string;
  challengeId?: string;
  type: MessageType;
  content: string;
  attachments?: string[];
  createdAt?: Date;
  readAt?: Date | null;
  metadata?: Record<string, any>;
}

@Injectable()
export class MessagingService {
  private readonly logger = new Logger(MessagingService.name);
  
  constructor(
    @InjectRepository(Message)
    private readonly messageRepository: Repository<Message>,
    @InjectRepository(Conversation)
    private readonly conversationRepository: Repository<Conversation>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly fileService: FileService
  ) {}

  /**
   * Send direct message to a user
   */
  async sendDirectMessage(
    senderId: string, 
    recipientId: string, 
    content: string,
    attachments?: string[],
  ): Promise<string> {
    // Find sender and recipient users
    const [sender, recipient] = await Promise.all([
      this.userRepository.findOne({ where: { id: senderId } }),
      this.userRepository.findOne({ where: { id: recipientId } })
    ]);

    if (!sender || !recipient) {
      throw new NotFoundException('Sender or recipient not found');
    }
    
    // Create or find conversation between these two users
    // Create conversation ID using sorted user IDs to ensure consistency
    const participantIds = [senderId, recipientId].sort();
    
    let conversation = await this.conversationRepository.findOne({
      where: { 
        isGroup: false,
        participants: { id: In(participantIds) }
      },
      relations: ['participants']
    });
    
    // Create conversation if it doesn't exist
    if (!conversation) {
      conversation = this.conversationRepository.create({
        name: `${sender.firstName} & ${recipient.firstName}`,
        isGroup: false,
        participants: [sender, recipient],
        lastMessageTime: new Date()
      });
      
      conversation = await this.conversationRepository.save(conversation);
    }
    
    // Process attachments if any
    let fileAttachments: File[] = [];
    if (attachments && attachments.length > 0) {
      fileAttachments = await Promise.all(
        attachments.map(attachmentId => 
          this.fileService.getFile(attachmentId)
        )
      );
    }
    
    // Create message
    const newMessage = this.messageRepository.create({
      content,
      type: 'direct-message',
      read: false,
    });
    
    // Set relationships separately after creating the entity
    newMessage.sender = sender;
    newMessage.senderId = senderId;
    newMessage.conversation = conversation;
    newMessage.conversationId = conversation.id;    
    newMessage.attachments = fileAttachments;

    // Save the message
    const message = await this.messageRepository.save(newMessage);
    
    // Update conversation with last message info
    conversation.lastMessage = content;
    conversation.lastMessageTime = new Date();
    conversation.lastMessageSenderId = senderId;
    
    await this.conversationRepository.save(conversation);
    
    return message.id;
  }

  /**
   * Send message to a group
   */
  async sendGroupMessage(
    senderId: string,
    groupId: string,
    content: string,
    attachments?: string[]
  ): Promise<string> {
    // Find the sender
    const sender = await this.userRepository.findOne({ where: { id: senderId } });
    if (!sender) {
      throw new NotFoundException('Sender not found');
    }
    
    // Find the group conversation
    const conversation = await this.conversationRepository.findOne({ 
      where: { id: groupId, isGroup: true },
      relations: ['participants']
    });
    
    if (!conversation) {
      throw new NotFoundException('Group conversation not found');
    }

    // Check if sender is a participant
    const isMember = conversation.participants.some(p => p.id === senderId);
    if (!isMember) {
      throw new NotFoundException('User is not a member of this group');
    }
    
    // Process attachments if any
    let fileAttachments: File[] = [];
    if (attachments && attachments.length > 0) {
      fileAttachments = await Promise.all(
        attachments.map(attachmentId => 
          this.fileService.getFile(attachmentId)
        )
      );
    }
    
    // Create message
    const newMessage = this.messageRepository.create({
      content,
      type: 'group-message',
      read: false,
    });
    
    // Set relationships separately after creating the entity
    newMessage.sender = sender;
    newMessage.senderId = senderId;
    newMessage.conversation = conversation;
    newMessage.conversationId = conversation.id;
    newMessage.attachments = fileAttachments;
    
    // Save the message
    const message = await this.messageRepository.save(newMessage);
    
    // Update conversation with last message info
    conversation.lastMessage = content;
    conversation.lastMessageTime = new Date();
    conversation.lastMessageSenderId = senderId;
    
    await this.conversationRepository.save(conversation);
    
    return message.id;
  }

  /**
   * Send message to a challenge
   */
  async sendChallengeMessage(
    senderId: string,
    challengeId: string,
    content: string,
    attachments?: string[]
  ): Promise<string> {
    // Find the sender
    const sender = await this.userRepository.findOne({ where: { id: senderId } });
    if (!sender) {
      throw new NotFoundException('Sender not found');
    }
    
    // Find or create a conversation for this challenge
    let conversation = await this.conversationRepository.findOne({ 
      where: { challengeId },
      relations: ['participants']
    });
    
    // Create conversation if it doesn't exist
    if (!conversation) {
      // Find all users in the challenge (this would depend on your challenge structure)
      // For now, we'll just add the sender
      const newConversation = this.conversationRepository.create({
        name: `Challenge ${challengeId}`,
        isGroup: true,
        challengeId,
        lastMessageTime: new Date()
      });
      
      // Save the conversation first to get an ID
      const savedConversation = await this.conversationRepository.save(newConversation);
      
      // Now set the participants
      savedConversation.participants = [sender];
      conversation = await this.conversationRepository.save(savedConversation);
    }

    // Process attachments if any
    let fileAttachments: File[] = [];
    if (attachments && attachments.length > 0) {
      fileAttachments = await Promise.all(
        attachments.map(attachmentId => 
          this.fileService.getFile(attachmentId)
        )
      );
    }
    
    // Create message
    const newMessage = this.messageRepository.create({
      content,
      type: 'challenge-message',
      read: false,
    });
    
    // Set relationships separately after creating the entity
    newMessage.sender = sender;
    newMessage.senderId = senderId;
    newMessage.conversation = conversation;
    newMessage.conversationId = conversation.id;
    newMessage.attachments = fileAttachments;
    
    // Save the message
    const message = await this.messageRepository.save(newMessage);
    
    // Update conversation with last message info
    conversation.lastMessage = content;
    conversation.lastMessageTime = new Date();
    conversation.lastMessageSenderId = senderId;
    
    await this.conversationRepository.save(conversation);
    
    return message.id;
  }

  /**
   * Send a system message
   */
  async sendSystemMessage(
    recipientId: string,
    content: string,
  ): Promise<string> {
    // Find recipient user
    const recipient = await this.userRepository.findOne({ where: { id: recipientId } });
    if (!recipient) {
      throw new NotFoundException('Recipient not found');
    }
    
    // Find system user or create if it doesn't exist
    let systemUser = await this.userRepository.findOne({ 
      where: { email: '<EMAIL>' } 
    });
    
    if (!systemUser) {
      const newSystemUser = this.userRepository.create({
        email: '<EMAIL>',
        firstName: 'System',
        lastName: 'User',
        provider: AuthProvider.LOCAL
      });
      
      systemUser = await this.userRepository.save(newSystemUser);
    }
    
    // Now systemUser is guaranteed to be non-null
    
    // Find or create a conversation between system and recipient
    let conversation = await this.conversationRepository.findOne({
      where: {
        isGroup: false,
        participants: { 
          id: In([recipient.id, systemUser.id]) 
        }
      },
      relations: ['participants']
    });
    
    // Create conversation if it doesn't exist
    if (!conversation) {
      // Create a new conversation with both users
      const newConversation = this.conversationRepository.create({
        name: 'System Messages',
        isGroup: false,
        lastMessageTime: new Date()
      });
      
      // Save the conversation first to get an ID
      const savedConversation = await this.conversationRepository.save(newConversation);
      
      // Now load the conversation with its ID and set the participants
      savedConversation.participants = [systemUser, recipient];
      conversation = await this.conversationRepository.save(savedConversation);
    }
    
    // Create message
    const newMessage = this.messageRepository.create({
      content,
      type: 'system-message',
      read: false,
    });
    
    // Set relationships separately after creating the entity
    newMessage.sender = systemUser;
    newMessage.senderId = systemUser.id;
    newMessage.conversation = conversation;
    newMessage.conversationId = conversation.id;
    
    // Save the message
    const message = await this.messageRepository.save(newMessage);
    
    // Update conversation with last message info
    conversation.lastMessage = content;
    conversation.lastMessageTime = new Date();
    conversation.lastMessageSenderId = systemUser.id;
    
    await this.conversationRepository.save(conversation);
    
    return message.id;
  }

  /**
   * Get direct messages between two users
   */
  async getDirectMessages(
    userId1: string,
    userId2: string,
    limit = 20
  ): Promise<Message[]> {
    // Sort user IDs to ensure consistent conversation lookup
    const userIds = [userId1, userId2].sort();
    
    // Find the conversation between these users
    const conversation = await this.conversationRepository.findOne({
      where: {
        isGroup: false,
        participants: { id: In(userIds) }
      }
    });
    
    if (!conversation) {
      return [];
    }
    
    // Get messages from this conversation
    return this.messageRepository.find({
      where: { conversationId: conversation.id },
      relations: ['sender'],
      order: { createdAt: 'DESC' },
      take: limit
    });
  }

  /**
   * Get messages from a group
   */
  async getGroupMessages(
    groupId: string,
    limit = 20
  ): Promise<Message[]> {
    return this.messageRepository.find({
      where: { 
        conversationId: groupId,
        type: 'group-message'
      },
      relations: ['sender'],
      order: { createdAt: 'DESC' },
      take: limit
    });
  }

  /**
   * Get messages from a challenge
   */
  async getChallengeMessages(
    challengeId: string,
    limit = 20
  ): Promise<Message[]> {
    // Find conversation for this challenge
    const conversation = await this.conversationRepository.findOne({
      where: { challengeId }
    });
    
    if (!conversation) {
      return [];
    }
    
    return this.messageRepository.find({
      where: { 
        conversationId: conversation.id,
        type: 'challenge-message'
      },
      relations: ['sender'],
      order: { createdAt: 'DESC' },
      take: limit
    });
  }

  /**
   * Get all conversations for a user
   */
  async getUserConversations(userId: string): Promise<Conversation[]> {
    return this.conversationRepository.find({
      where: { participants: { id: userId } },
      relations: ['participants'],
      order: { lastMessageTime: 'DESC' }
    });
  }
  
  /**
   * Mark message as read
   */
  async markMessageAsRead(messageId: string): Promise<void> {
    await this.messageRepository.update(
      { id: messageId },
      { read: true }
    );
  }
  
  /**
   * Get unread message count for a user
   */
  async getUnreadMessageCount(userId: string): Promise<number> {
    const conversations = await this.getUserConversations(userId);
    
    if (conversations.length === 0) {
      return 0;
    }
    
    const conversationIds = conversations.map(c => c.id);
    
    return this.messageRepository.count({
      where: {
        conversationId: In(conversationIds),
        senderId: In([userId]), // Exclude messages sent by the user
        read: false
      }
    });
  }
}
