import { Modu<PERSON> } from '@nestjs/common';
import { MessagingService } from './messaging.service';
import { MessagingController } from './messaging.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { Conversation } from './entities/conversation.entity';
import { Message } from './entities/message.entity';
import { User } from '../users/entities/user.entity';
import { FileModule } from '../common/modules/file.module';
import { File } from '../common/entities/file.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Conversation, Message, User, File]),
    ConfigModule,
    FileModule,
  ],
  providers: [MessagingService],
  exports: [MessagingService],
  controllers: [MessagingController],
})
export class MessagingModule {}
