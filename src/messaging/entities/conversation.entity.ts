import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToMany, JoinTable, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Message } from './message.entity';

@Entity('conversations')
export class Conversation {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  name: string;

  @Column({ default: false })
  isGroup: boolean;

  @ManyToMany(() => User)
  @JoinTable({
    name: 'conversation_participants',
    joinColumn: { name: 'conversation_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'user_id', referencedColumnName: 'id' },
  })
  participants: User[];

  @OneToMany(() => Message, message => message.conversation)
  messages: Message[];

  @Column({ name: 'last_message', nullable: true })
  lastMessage: string;

  @Column({ name: 'last_message_time', type: 'timestamp', nullable: true })
  lastMessageTime: Date;

  @Column({ name: 'last_message_sender_id', nullable: true })
  lastMessageSenderId: string;
  
  @Column({ name: 'challenge_id', nullable: true })
  challengeId: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
