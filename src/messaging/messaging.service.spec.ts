import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { MessagingService } from './messaging.service';
import { Message } from './entities/message.entity';
import { Conversation } from './entities/conversation.entity';
import { User } from '../users/entities/user.entity';
import { FileService } from '../common/services/file.service';

describe('MessagingService', () => {
  let service: MessagingService;
  let messageRepository: any;
  let conversationRepository: any;
  let userRepository: any;
  let fileService: FileService;

  const mockMessageRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    remove: jest.fn(),
  };

  const mockConversationRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    remove: jest.fn(),
  };

  const mockUserRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
  };

  const mockFileService = {
    getFile: jest.fn(),
    saveFile: jest.fn(),
    deleteFile: jest.fn(),
    getSignedUrl: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();
    
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MessagingService,
        {
          provide: getRepositoryToken(Message),
          useValue: mockMessageRepository,
        },
        {
          provide: getRepositoryToken(Conversation),
          useValue: mockConversationRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: FileService,
          useValue: mockFileService,
        },
      ],
    }).compile();

    service = module.get<MessagingService>(MessagingService);
    messageRepository = module.get(getRepositoryToken(Message));
    conversationRepository = module.get(getRepositoryToken(Conversation));
    userRepository = module.get(getRepositoryToken(User));
    fileService = module.get(FileService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendDirectMessage', () => {
    const senderId = '123';
    const recipientId = '456';
    const content = 'Hello!';
    const attachments = ['file1', 'file2'];
    const mockSender = { id: senderId, firstName: 'John', lastName: 'Doe' };
    const mockRecipient = { id: recipientId, firstName: 'Jane', lastName: 'Smith' };
    const mockConversation = {
      id: 'conv123',
      name: 'John & Jane',
      isGroup: false,
      participants: [mockSender, mockRecipient],
      lastMessageTime: expect.any(Date),
      lastMessage: content,
      lastMessageSenderId: senderId
    };
    const mockMessage = {
      id: 'msg123',
      content,
      type: 'direct-message',
      read: false,
      sender: mockSender,
      senderId,
      conversation: mockConversation,
      conversationId: mockConversation.id,
      attachments: []
    };

    beforeEach(() => {
      mockUserRepository.findOne.mockImplementation(({ where: { id } }) => {
        if (id === senderId) return Promise.resolve(mockSender);
        if (id === recipientId) return Promise.resolve(mockRecipient);
        return Promise.resolve(null);
      });
      mockConversationRepository.findOne.mockResolvedValue(null);
      mockConversationRepository.create.mockReturnValue(mockConversation);
      mockConversationRepository.save.mockResolvedValue(mockConversation);
      mockMessageRepository.create.mockReturnValue(mockMessage);
      mockMessageRepository.save.mockResolvedValue(mockMessage);
      mockFileService.getFile.mockImplementation(fileId => 
        Promise.resolve({ id: fileId, type: 'image' })
      );
    });

    it('should send a direct message successfully', async () => {
      const result = await service.sendDirectMessage(senderId, recipientId, content);
      
      expect(result).toBe(mockMessage.id);
      expect(mockUserRepository.findOne).toHaveBeenCalledTimes(2);
      expect(mockConversationRepository.findOne).toHaveBeenCalled();
      expect(mockConversationRepository.create).toHaveBeenCalled();
      expect(mockConversationRepository.save).toHaveBeenCalledWith(expect.objectContaining({
        lastMessage: content,
        lastMessageSenderId: senderId,
        lastMessageTime: expect.any(Date)
      }));
      expect(mockMessageRepository.create).toHaveBeenCalled();
      expect(mockMessageRepository.save).toHaveBeenCalled();
    });

    it('should handle attachments correctly', async () => {
      await service.sendDirectMessage(senderId, recipientId, content, attachments);
      
      expect(mockFileService.getFile).toHaveBeenCalledTimes(attachments.length);
      expect(mockMessageRepository.save).toHaveBeenCalledWith(expect.objectContaining({
        attachments: attachments.map(id => ({ id, type: 'image' }))
      }));
    });

    it('should find existing conversation if it exists', async () => {
      mockConversationRepository.findOne.mockResolvedValue(mockConversation);
      
      await service.sendDirectMessage(senderId, recipientId, content);
      
      expect(mockConversationRepository.create).not.toHaveBeenCalled();
      expect(mockConversationRepository.save).toHaveBeenCalledTimes(1); // Only for updating last message
    });

    it('should throw NotFoundException if sender not found', async () => {
      mockUserRepository.findOne.mockImplementation(({ where: { id } }) => {
        if (id === recipientId) return Promise.resolve(mockRecipient);
        return Promise.resolve(null);
      });
      
      await expect(service.sendDirectMessage('invalid-id', recipientId, content))
        .rejects.toThrow('Sender or recipient not found');
    });

    it('should throw NotFoundException if recipient not found', async () => {
      mockUserRepository.findOne.mockImplementation(({ where: { id } }) => {
        if (id === senderId) return Promise.resolve(mockSender);
        return Promise.resolve(null);
      });
      
      await expect(service.sendDirectMessage(senderId, 'invalid-id', content))
        .rejects.toThrow('Sender or recipient not found');
    });
  });

  describe('sendGroupMessage', () => {
    const senderId = '123';
    const groupId = 'group123';
    const content = 'Hello group!';
    const attachments = ['file1'];
    const mockSender = { id: senderId, firstName: 'John', lastName: 'Doe' };
    const mockParticipants = [
      mockSender,
      { id: '456', firstName: 'Jane', lastName: 'Smith' },
      { id: '789', firstName: 'Bob', lastName: 'Johnson' }
    ];
    const mockGroupConversation = {
      id: groupId,
      name: 'Test Group',
      isGroup: true,
      participants: mockParticipants,
      lastMessageTime: expect.any(Date),
      lastMessage: content,
      lastMessageSenderId: senderId
    };
    const mockMessage = {
      id: 'msg123',
      content,
      type: 'group-message',
      read: false,
      sender: mockSender,
      senderId,
      conversation: mockGroupConversation,
      conversationId: groupId,
      attachments: []
    };

    beforeEach(() => {
      mockUserRepository.findOne.mockImplementation(({ where: { id } }) => {
        if (id === senderId) return Promise.resolve(mockSender);
        return Promise.resolve(null);
      });
      mockConversationRepository.findOne.mockResolvedValue(mockGroupConversation);
      mockMessageRepository.create.mockReturnValue(mockMessage);
      mockMessageRepository.save.mockResolvedValue(mockMessage);
      mockFileService.getFile.mockImplementation(fileId => 
        Promise.resolve({ id: fileId, type: 'image' })
      );
    });

    it('should send a group message successfully', async () => {
      const result = await service.sendGroupMessage(senderId, groupId, content);
      
      expect(result).toBe(mockMessage.id);
      expect(mockUserRepository.findOne).toHaveBeenCalledTimes(1);
      expect(mockConversationRepository.findOne).toHaveBeenCalledWith({
        where: { id: groupId, isGroup: true },
        relations: ['participants']
      });
      expect(mockMessageRepository.create).toHaveBeenCalled();
      expect(mockMessageRepository.save).toHaveBeenCalled();
      expect(mockConversationRepository.save).toHaveBeenCalledWith(expect.objectContaining({
        lastMessage: content,
        lastMessageSenderId: senderId,
        lastMessageTime: expect.any(Date)
      }));
    });

    it('should handle attachments correctly in group messages', async () => {
      await service.sendGroupMessage(senderId, groupId, content, attachments);
      
      expect(mockFileService.getFile).toHaveBeenCalledTimes(attachments.length);
      expect(mockMessageRepository.save).toHaveBeenCalledWith(expect.objectContaining({
        attachments: attachments.map(id => ({ id, type: 'image' }))
      }));
    });

    it('should throw NotFoundException if sender not found', async () => {
      mockUserRepository.findOne.mockResolvedValue(null);
      
      await expect(service.sendGroupMessage('invalid-id', groupId, content))
        .rejects.toThrow('Sender not found');
    });

    it('should throw NotFoundException if group not found', async () => {
      mockConversationRepository.findOne.mockResolvedValue(null);
      
      await expect(service.sendGroupMessage(senderId, 'invalid-group', content))
        .rejects.toThrow('Group conversation not found');
    });

    it('should throw if sender is not a group participant', async () => {
      const nonMemberSender = { id: 'nonmember', firstName: 'Non', lastName: 'Member' };
      mockUserRepository.findOne.mockResolvedValue(nonMemberSender);
      
      await expect(service.sendGroupMessage('nonmember', groupId, content))
        .rejects.toThrow('User is not a member of this group');
    });
  });

  describe('getUserConversations', () => {
    const userId = 'user-id';
    const mockUser = { id: userId, firstName: 'Test', lastName: 'User' };
    const mockConversations = [
      {
        id: 'conversation-1',
        name: 'Test & Other1',
        isGroup: false,
        participants: [mockUser, { id: 'other-user-1', firstName: 'Other1', lastName: 'User' }],
        lastMessage: 'Hello there',
        lastMessageSenderId: 'other-user-1',
        lastMessageTime: new Date(),
      },
      {
        id: 'conversation-2',
        name: 'Group Chat',
        isGroup: true,
        participants: [
          mockUser,
          { id: 'other-user-2', firstName: 'Other2', lastName: 'User' },
          { id: 'other-user-3', firstName: 'Other3', lastName: 'User' }
        ],
        lastMessage: 'How are you?',
        lastMessageSenderId: userId,
        lastMessageTime: new Date(),
      },
    ];

    it('should return user conversations', async () => {
      mockConversationRepository.find.mockResolvedValue(mockConversations);
      
      const result = await service.getUserConversations(userId);
      
      expect(mockConversationRepository.find).toHaveBeenCalledWith({
        where: { participants: { id: userId } },
        relations: ['participants'],
        order: { lastMessageTime: 'DESC' }
      });
      expect(result).toEqual(mockConversations);
    });

    it('should return empty array if no conversations found', async () => {
      mockConversationRepository.find.mockResolvedValue([]);
      
      const result = await service.getUserConversations(userId);
      
      expect(result).toEqual([]);
    });
  });
});
