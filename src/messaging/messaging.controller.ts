import { Body, Controller, Get, Param, Post, Put, UseGuards } from '@nestjs/common';
import { MessagingService } from './messaging.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { SendDirectMessageDto } from './dto/send-direct-message.dto';
import { SendGroupMessageDto } from './dto/send-group-message.dto';
import { SendChallengeMessageDto } from './dto/send-challenge-message.dto';
import { GetUser } from '../auth/decorators/get-user.decorator';

@ApiTags('messaging')
@Controller('api/messaging')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
export class MessagingController {
  constructor(private readonly messagingService: MessagingService) {}

  @Post('direct')
  @ApiOperation({ summary: 'Send a direct message to a user' })
  @ApiResponse({ 
    status: 201, 
    description: 'Message sent successfully' 
  })
  async sendDirectMessage(
    @GetUser('id') userId: string,
    @Body() sendMessageDto: SendDirectMessageDto
  ) {
    const messageId = await this.messagingService.sendDirectMessage(
      userId,
      sendMessageDto.recipientId,
      sendMessageDto.content,
      sendMessageDto.attachments,
    );
    
    return { 
      success: true,
      messageId,
      message: 'Message sent successfully' 
    };
  }

  @Post('group')
  @ApiOperation({ summary: 'Send a message to a group' })
  @ApiResponse({ 
    status: 201, 
    description: 'Message sent successfully' 
  })
  async sendGroupMessage(
    @GetUser('id') userId: string,
    @Body() sendMessageDto: SendGroupMessageDto
  ) {
    const messageId = await this.messagingService.sendGroupMessage(
      userId,
      sendMessageDto.groupId,
      sendMessageDto.content,
      sendMessageDto.attachments,
    );
    
    return { 
      success: true,
      messageId,
      message: 'Group message sent successfully' 
    };
  }

  @Post('challenge')
  @ApiOperation({ summary: 'Send a message in a challenge' })
  @ApiResponse({ 
    status: 201, 
    description: 'Message sent successfully' 
  })
  async sendChallengeMessage(
    @GetUser('id') userId: string,
    @Body() sendMessageDto: SendChallengeMessageDto
  ) {
    const messageId = await this.messagingService.sendChallengeMessage(
      userId,
      sendMessageDto.challengeId,
      sendMessageDto.content,
      sendMessageDto.attachments,
    );
    
    return { 
      success: true,
      messageId,
      message: 'Challenge message sent successfully' 
    };
  }

  @Get('conversations')
  @ApiOperation({ summary: 'Get user conversations' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns user conversations' 
  })
  async getUserConversations(@GetUser('id') userId: string) {
    const conversations = await this.messagingService.getUserConversations(userId);
    return conversations;
  }

  @Get('direct/:recipientId')
  @ApiOperation({ summary: 'Get direct messages with a user' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns direct messages' 
  })
  async getDirectMessages(
    @GetUser('id') userId: string,
    @Param('recipientId') recipientId: string
  ) {
    const messages = await this.messagingService.getDirectMessages(userId, recipientId);
    return messages;
  }

  @Get('group/:groupId')
  @ApiOperation({ summary: 'Get messages in a group' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns group messages' 
  })
  async getGroupMessages(@Param('groupId') groupId: string) {
    const messages = await this.messagingService.getGroupMessages(groupId);
    return messages;
  }

  @Get('challenge/:challengeId')
  @ApiOperation({ summary: 'Get messages in a challenge' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns challenge messages' 
  })
  async getChallengeMessages(@Param('challengeId') challengeId: string) {
    const messages = await this.messagingService.getChallengeMessages(challengeId);
    return messages;
  }

  @Put('read/:messageId')
  @ApiOperation({ summary: 'Mark a message as read' })
  @ApiResponse({ 
    status: 200, 
    description: 'Message marked as read' 
  })
  async markMessageAsRead(@Param('messageId') messageId: string) {
    await this.messagingService.markMessageAsRead(messageId);
    return { 
      success: true,
      message: 'Message marked as read' 
    };
  }
}
