import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsArray, IsOptional } from 'class-validator';

export class SendDirectMessageDto {
  @ApiProperty({ 
    description: 'ID of the recipient user',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @IsNotEmpty()
  @IsString()
  recipientId: string;

  @ApiProperty({ 
    description: 'Message content',
    example: 'Hello, how are you doing today?'
  })
  @IsNotEmpty()
  @IsString()
  content: string;

  @ApiProperty({ 
    description: 'Optional attachment URLs',
    example: ['https://example.com/image.jpg'],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  attachments?: string[];
}
