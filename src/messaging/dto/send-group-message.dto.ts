import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsArray, IsOptional } from 'class-validator';

export class SendGroupMessageDto {
  @ApiProperty({ 
    description: 'ID of the group',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @IsNotEmpty()
  @IsString()
  groupId: string;

  @ApiProperty({ 
    description: 'Message content',
    example: 'Hello everyone! How is the challenge going?'
  })
  @IsNotEmpty()
  @IsString()
  content: string;

  @ApiProperty({ 
    description: 'Optional attachment URLs',
    example: ['https://example.com/image.jpg'],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  attachments?: string[];
}
