import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsArray, IsOptional } from 'class-validator';

export class SendChallengeMessageDto {
  @ApiProperty({ 
    description: 'ID of the challenge',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @IsNotEmpty()
  @IsString()
  challengeId: string;

  @ApiProperty({ 
    description: 'Message content',
    example: 'Just completed my daily goal for the challenge!'
  })
  @IsNotEmpty()
  @IsString()
  content: string;

  @ApiProperty({ 
    description: 'Optional attachment URLs',
    example: ['https://example.com/image.jpg'],
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  attachments?: string[];
}
