import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { MessagingModule } from '../messaging/messaging.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { HabitsModule } from '../habits/habits.module';
import { TasksModule } from '../tasks/tasks.module';
import { SkillPlansModule } from '../skill-plans/skill-plans.module';
import { ChallengesModule } from '../challenges/challenges.module';
import { MonetizationModule } from '../monetization/monetization.module';
import { Podcast } from './entities/podcast.entity';
import { PodcastsService } from './podcasts.service';
import { PodcastsController } from './podcasts.controller';
import { PodcastGenerationProcessor } from './podcast-generation.processor';
import { SubscriptionGuard } from '../auth/guards/subscription.guard';

@Module({
  imports: [
    TypeOrmModule.forFeature([Podcast]),
    BullModule.registerQueue({
      name: 'podcast-generation',
    }),
    ConfigModule,
    ScheduleModule.forRoot(),
    MessagingModule,
    NotificationsModule,
    HabitsModule,
    TasksModule,
    SkillPlansModule,
    ChallengesModule,
    MonetizationModule,
  ],
  providers: [PodcastsService, PodcastGenerationProcessor, SubscriptionGuard],
  controllers: [PodcastsController],
  exports: [PodcastsService],
})
export class PodcastsModule {}
