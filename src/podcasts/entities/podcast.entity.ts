import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';

@Entity('podcasts')
export class Podcast {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'text' })
  transcript: string;

  @Column()
  audioUrl: string;

  @Column({ type: 'int' })
  durationSeconds: number;

  @Column({ default: 'pending' })
  status: 'pending' | 'completed' | 'error';

  @Column({ type: 'text', nullable: true })
  error?: string;

  @Column({ type: 'jsonb' })
  metadata: {
    topic: string;
    mood: string;
    language?: string;
    goals: string[];
    habits: string[];
    generationPrompt: string;
    listened?: boolean;
    listenedAt?: string;
  };

  @ManyToOne(() => User)
  user: User;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
