import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThan, MoreThanOrEqual } from 'typeorm';
import { InjectQueue } from '@nestjs/bull';
import type { Queue } from 'bull';
import { Podcast } from './entities/podcast.entity';
import { User } from '../users/entities/user.entity';
import { ConfigService } from '@nestjs/config';
import { NotificationsService } from '../notifications/notifications.service';
import { NotificationType } from '../notifications/types/notification-types';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import { getAbsolutePath } from '../common/utils/file-utils';

@Injectable()
export class PodcastsService {
  private podcastsUploadDir: string;
  private readonly isTestEnvironment: boolean;

  constructor(
    @InjectRepository(Podcast)
    private podcastsRepository: Repository<Podcast>,
    @InjectQueue('podcast-generation')
    private podcastGenerationQueue: Queue,
    private configService: ConfigService,
    private notificationsService: NotificationsService,
  ) {
    // Get the podcast upload directory from config and ensure it exists
    this.podcastsUploadDir = getAbsolutePath(
      this.configService.get<string>('uploads.podcastsDir') ||
        'uploads/podcasts',
    );
    
    this.isTestEnvironment = process.env.NODE_ENV === 'test';
  }

  async generatePodcastContent(context: {
    user: User;
    // Optional preferences that can be set by user (future feature)
    topic?: string;
    mood?: string;
    language?: string;
  }): Promise<Podcast> {
    // Check weekly limit - user can only generate one podcast per week
    const startOfWeek = new Date();
    startOfWeek.setHours(0, 0, 0, 0);
    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay()); // Start of current week (Sunday)
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(endOfWeek.getDate() + 7); // End of current week

    const existingWeekPodcast = await this.podcastsRepository.findOne({
      where: {
        user: { id: context.user.id },
        createdAt: MoreThanOrEqual(startOfWeek),
      },
    });

    if (existingWeekPodcast) {
      throw new ForbiddenException(
        'You can only generate one podcast per week. Please try again next week.'
      );
    }

    if (this.isTestEnvironment) {
      const mockPodcast = this.podcastsRepository.create({
        id: uuidv4(),
        title: 'Test Generated Podcast',
        description: 'A test generated podcast description',
        transcript: 'Test podcast transcript content',
        audioUrl: 'https://example.com/test-podcast.mp3',
        durationSeconds: 300,
        metadata: {
          topic: context.topic || 'test-podcast',
          mood: context.mood || 'neutral',
          language: context.language || 'English',
          goals: [], // Will be populated from user data
          habits: [], // Will be populated from user data
          generationPrompt: 'test prompt',
        },
        user: context.user,
      });

      return this.podcastsRepository.save(mockPodcast);
    }

    // Create a filename for the podcast audio
    const filename = `podcast_${new Date().getTime()}.wav`;
    const baseUrl = this.configService.get<string>('app.baseUrl', 'https://powerup.apperx.com');
    const audioWebUrl = `${baseUrl}/uploads/podcasts/${filename}`;

    // Create podcast record with placeholder content (will be generated in processor)
    const podcast = this.podcastsRepository.create({
      title: `Weekly Podcast - Week of ${startOfWeek.toLocaleDateString()}`,
      description: 'Generating personalized podcast content...',
      transcript: '',
      audioUrl: audioWebUrl,
      status: 'pending',
      durationSeconds: 0,
      metadata: {
        topic: context.topic || 'weekly-progress-review',
        mood: context.mood || 'neutral',
        language: context.language || 'English',
        goals: [], // Will be automatically fetched from user data
        habits: [], // Will be automatically fetched from user data
        generationPrompt: '',
      },
      user: context.user,
    });

    // Save initial podcast record
    const savedPodcast = await this.podcastsRepository.save(podcast);

    // Queue content and audio generation with simplified context
    await this.podcastGenerationQueue.add('generate-content-and-audio', {
      podcastId: savedPodcast.id,
      context: {
        user: context.user,
        topic: context.topic,
        mood: context.mood,
        language: context.language,
        // Goals and habits will be automatically fetched from user data
      },
    });
    console.log('Podcast generation job added to queue:', savedPodcast.id);
    return savedPodcast;
  }

  async findAll(userId: string): Promise<Podcast[]> {
    return this.podcastsRepository.find({
      where: { user: { id: userId } },
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string, userId: string): Promise<Podcast> {
    const podcast = await this.podcastsRepository.findOne({
      where: { id, user: { id: userId } },
    });

    if (!podcast) {
      throw new NotFoundException('Podcast not found');
    }

    return podcast;
  }

  async remove(id: string, userId: string): Promise<void> {
    const podcast = await this.findOne(id, userId);
    await this.podcastsRepository.remove(podcast);
  }

  async getWeeklyPodcast(userId: string): Promise<Podcast> {
    const startOfWeek = new Date();
    startOfWeek.setHours(0, 0, 0, 0);
    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay()); // Start of current week (Sunday)

    // Check if a podcast was already generated this week
    const existingPodcast = await this.podcastsRepository.findOne({
      where: {
        user: { id: userId },
        createdAt: MoreThanOrEqual(startOfWeek),
      },
      order: { createdAt: 'DESC' },
    });

    if (existingPodcast) {
      return existingPodcast;
    }

    // For tests, return a mock podcast
    if (this.isTestEnvironment) {
      const mockPodcast = this.podcastsRepository.create({
        id: uuidv4(),
        title: 'Weekly Test Podcast',
        description: 'A test podcast description',
        transcript: 'Test podcast transcript content',
        audioUrl: 'https://example.com/test-podcast.mp3',
        durationSeconds: 300,
        metadata: {
          topic: 'test-podcast',
          mood: 'neutral',
          goals: [],
          habits: [],
          generationPrompt: '',
        },
        user: { id: userId } as User,
      });

      return this.podcastsRepository.save(mockPodcast);
    }

    // Generate a new podcast if none exists for this week
    return this.generatePodcastContent({
      user: { id: userId } as User,
      topic: 'weekly-progress-review',
      language: 'English',
    });
  }

  async getPodcastHistory(userId: string): Promise<Podcast[]> {
    const startOfWeek = new Date();
    startOfWeek.setHours(0, 0, 0, 0);
    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay()); // Start of current week

    return this.podcastsRepository.find({
      where: {
        user: { id: userId },
        createdAt: LessThan(startOfWeek),
      },
      order: { createdAt: 'DESC' },
    });
  }

  async markAsListened(id: string, userId: string): Promise<Podcast> {
    const podcast = await this.findOne(id, userId);

    if (!podcast) {
      throw new NotFoundException('Podcast not found');
    }

    // Get current metadata and update listened status
    const metadata = {
      ...podcast.metadata,
      listened: true,
      listenedAt: new Date().toISOString(),
    };

    // Update the podcast with new metadata
    podcast.metadata = metadata;
    return this.podcastsRepository.save(podcast);
  }
}
