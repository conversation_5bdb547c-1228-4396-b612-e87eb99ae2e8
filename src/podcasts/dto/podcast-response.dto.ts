import { ApiProperty } from '@nestjs/swagger';

export class PodcastResponseDto {
  @ApiProperty({
    description: 'Unique podcast ID',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  id: string;

  @ApiProperty({
    description: 'Podcast title',
    example: 'Your Daily Mindfulness Coach'
  })
  title: string;

  @ApiProperty({
    description: 'Podcast description',
    example: 'A personalized meditation session focusing on stress reduction'
  })
  description: string;

  @ApiProperty({
    description: 'Podcast audio URL',
    example: 'https://storage.example.com/podcasts/123e4567-e89b-12d3-a456-426614174000.mp3'
  })
  audioUrl: string;

  @ApiProperty({
    description: 'Podcast duration in seconds',
    example: 600
  })
  durationSeconds: number;

  @ApiProperty({
    description: 'Podcast generation date',
    example: '2025-05-19T10:00:00Z'
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Whether the podcast has been listened to',
    example: false
  })
  listened: boolean;

  @ApiProperty({
    description: 'User ID who owns the podcast',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  userId: string;
}
