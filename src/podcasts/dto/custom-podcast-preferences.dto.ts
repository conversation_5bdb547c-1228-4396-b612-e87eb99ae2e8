import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional } from 'class-validator';

export class CustomPodcastPreferencesDto {
  @ApiProperty({
    description: 'Optional podcast topic preference',
    example: 'Morning Motivation',
    required: false
  })
  @IsString()
  @IsOptional()
  topic?: string;

  @ApiProperty({
    description: 'Optional mood preference to influence podcast tone',
    example: 'energetic',
    required: false
  })
  @IsString()
  @IsOptional()
  mood?: string;

  @ApiProperty({
    description: 'Optional language preference for podcast content',
    example: 'English',
    required: false
  })
  @IsString()
  @IsOptional()
  language?: string;
}
