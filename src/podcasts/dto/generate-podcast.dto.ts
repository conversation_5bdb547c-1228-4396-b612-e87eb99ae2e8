import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsArray } from 'class-validator';

class HabitInfoDto {
  @ApiProperty({
    description: 'Habit name',
    example: 'Morning Meditation'
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Current habit streak',
    example: 7
  })
  streak: number;
}

export class GeneratePodcastDto {
  @ApiProperty({
    description: 'Podcast topic',
    example: 'Mindfulness',
    required: false
  })
  @IsString()
  @IsOptional()
  topic?: string;

  @ApiProperty({
    description: 'User mood',
    example: 'Energetic',
    required: false
  })
  @IsString()
  @IsOptional()
  mood?: string;

  @ApiProperty({
    description: 'User goals',
    example: ['Learn Spanish', 'Run a marathon'],
    required: false,
    type: [String]
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  goals?: string[];

  @ApiProperty({
    description: 'User habits with streak information',
    example: [{ name: 'Morning Meditation', streak: 7 }],
    required: false,
    type: [HabitInfoDto]
  })
  @IsArray()
  @IsOptional()
  habits?: HabitInfoDto[];

  @ApiProperty({
    description: 'User preferred language for podcast content',
    example: 'English',
    required: false
  })
  @IsString()
  @IsOptional()
  language?: string;
}
