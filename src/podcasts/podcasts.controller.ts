import { Controller, Get, Post, Delete, Param, Body, UseGuards, Request, HttpCode } from '@nestjs/common';
import { PodcastsService } from './podcasts.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { SubscriptionGuard } from '../auth/guards/subscription.guard';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiCreatedResponse, ApiOkResponse, ApiBody } from '@nestjs/swagger';
import { PodcastResponseDto } from './dto/podcast-response.dto';
import { ListenedResponseDto } from './dto/listened-response.dto';
import { CustomPodcastPreferencesDto } from './dto/custom-podcast-preferences.dto';

@ApiTags('podcasts')
@Controller('api/podcasts')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class PodcastsController {
  constructor(private readonly podcastsService: PodcastsService) {}

  @Post('generate')
  // @UseGuards(SubscriptionGuard)
  @ApiOperation({ 
    summary: 'Generate a new personalized podcast',
    description: 'Generate a new personalized podcast using your current progress data. Requires active subscription or free trial. Users can only generate one podcast per week.'
  })
  @ApiCreatedResponse({
    description: 'The podcast has been successfully generated based on your current progress',
    type: PodcastResponseDto
  })
  generate(@Request() req) {
    return this.podcastsService.generatePodcastContent({
      user: req.user,
    });
  }

  @Post('generate/custom')
  @UseGuards(SubscriptionGuard)
  @ApiOperation({ 
    summary: 'Generate a personalized podcast with custom preferences',
    description: 'Generate a personalized podcast with optional custom mood, topic, or language preferences. Your habit, task, and progress data will still be automatically included.'
  })
  @ApiBody({ type: CustomPodcastPreferencesDto })
  @ApiCreatedResponse({
    description: 'The podcast has been successfully generated with custom preferences',
    type: PodcastResponseDto
  })
  generateCustom(@Request() req, @Body() preferences: CustomPodcastPreferencesDto) {
    return this.podcastsService.generatePodcastContent({
      user: req.user,
      ...preferences,
    });
  }

  @Get('weekly')
  @UseGuards(SubscriptionGuard)
  @ApiOperation({ 
    summary: 'Get weekly personalized podcast',
    description: 'Get the weekly personalized podcast. Requires active subscription or free trial.'
  })
  @ApiOkResponse({
    description: 'Returns the weekly personalized podcast',
    type: PodcastResponseDto
  })
  getWeeklyPodcast(@Request() req) {
    return this.podcastsService.getWeeklyPodcast(req.user.id);
  }

  @Get('history')
  @ApiOperation({ summary: 'Get podcast listening history' })
  @ApiOkResponse({
    description: 'Returns the list of listened podcasts',
    type: [PodcastResponseDto]
  })
  getHistory(@Request() req) {
    return this.podcastsService.getPodcastHistory(req.user.id);
  }

  @Post(':id/listened')
  @ApiOperation({ summary: 'Mark a podcast as listened' })
  @ApiOkResponse({
    description: 'Returns the updated podcast marked as listened',
    type: PodcastResponseDto
  })
  @HttpCode(200)
  async markAsListened(@Request() req, @Param('id') id: string) {
    return this.podcastsService.markAsListened(id, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: 'Get all podcasts for current user' })
  @ApiOkResponse({
    description: 'Returns all podcasts for the current user',
    type: [PodcastResponseDto]
  })
  findAll(@Request() req) {
    return this.podcastsService.findAll(req.user.id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific podcast' })
  @ApiOkResponse({
    description: 'Returns a specific podcast by ID',
    type: PodcastResponseDto
  })
  findOne(@Request() req, @Param('id') id: string) {
    return this.podcastsService.findOne(id, req.user.id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a podcast' })
  @ApiOkResponse({
    description: 'The podcast has been successfully deleted',
  })
  remove(@Request() req, @Param('id') id: string) {
    return this.podcastsService.remove(id, req.user.id);
  }
}
