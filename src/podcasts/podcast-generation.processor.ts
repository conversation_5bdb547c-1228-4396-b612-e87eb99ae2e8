import { Process, Processor } from '@nestjs/bull';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThan } from 'typeorm';
import type { Job } from 'bull';
import { ConfigService } from '@nestjs/config';
import { Podcast } from './entities/podcast.entity';
import * as path from 'path';
import * as fs from 'fs';
import { NotificationsService } from '../notifications/notifications.service';
import type { NotificationType } from '../notifications/types/notification-types';
import { Cron, CronExpression } from '@nestjs/schedule';
import {
  ensureDirectoryExists,
  getAbsolutePath,
} from '../common/utils/file-utils';
import { GoogleGenAI } from '@google/genai';
import * as wav from 'wav';
import { User } from '../users/entities/user.entity';
import { HabitsService } from '../habits/habits.service';
import { TasksService } from '../tasks/tasks.service';
import { SkillPlansService } from '../skill-plans/skill-plans.service';
import { ChallengesService } from '../challenges/challenges.service';

@Injectable()
@Processor('podcast-generation')
export class PodcastGenerationProcessor {
  private readonly podcastsUploadDir: string;
  private readonly genAI: GoogleGenAI;

  constructor(
    @InjectRepository(Podcast)
    private readonly podcastsRepository: Repository<Podcast>,
    private readonly configService: ConfigService,
    private readonly notificationsService: NotificationsService,
    private readonly habitsService: HabitsService,
    private readonly tasksService: TasksService,
    private readonly skillPlansService: SkillPlansService,
    private readonly challengesService: ChallengesService,
  ) {
    // Initialize upload directory
    this.podcastsUploadDir = getAbsolutePath(
      this.configService.get<string>('uploads.podcastsDir', 'uploads/podcasts'),
    );

    // Ensure directory exists
    if (!fs.existsSync(this.podcastsUploadDir)) {
      fs.mkdirSync(this.podcastsUploadDir, { recursive: true });
    }

    // Initialize Google GenAI client
    const geminiApiKey = this.configService.get<string>('gemini.apiKey');
    if (!geminiApiKey) {
      throw new Error('GEMINI_API_KEY is not configured');
    }
    
    this.genAI = new GoogleGenAI({ apiKey: geminiApiKey });
  }

  private async generatePodcastTranscript(context: {
    user: User;
    topic?: string;
    mood?: string;
    language?: string;
    goals?: string[];
    habits?: any[];
    tasks?: any[];
    skillPlans?: any[];
    challenges?: any[];
    userInsights?: any;
  }): Promise<string> {
    try {
      const prompt = this.buildPrompt(context);
      
      const response = await this.genAI.models.generateContent({
        model: "gemini-2.0-flash",
        contents: prompt,
      });
      
      const transcript = response.text?.trim();
      if (!transcript) {
        throw new Error('No transcript generated from Gemini');
      }
      
      return transcript;
    } catch (error) {
      console.error('Error generating podcast transcript:', error);
      throw error;
    }
  }

  private async saveWaveFile(
    filename: string,
    pcmData: Buffer,
    channels = 1,
    rate = 24000,
    sampleWidth = 2,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const writer = new wav.FileWriter(filename, {
        channels,
        sampleRate: rate,
        bitDepth: sampleWidth * 8,
      });

      writer.on('finish', resolve);
      writer.on('error', reject);

      writer.write(pcmData);
      writer.end();
    });
  }

  private buildPrompt(context: {
    user: User;
    topic?: string;
    mood?: string;
    language?: string;
    goals?: string[];
    habits?: any[];
    tasks?: any[];
    skillPlans?: any[];
    challenges?: any[];
    userInsights?: any;
  }): string {
    const user = context.user;
    const insights = context.userInsights || {};
    const mood = context.mood || 'neutral';
    const language = context.language || 'English';

    // Core podcast structure
    const basePrompt = [
      '# EXPERT WEEKLY PODCAST GENERATION INSTRUCTIONS',
      '',
      'You are an elite podcast script writer specializing in personalized weekly progress review content.',
      'Create a compelling 5-7 minute podcast conversation between two expert life coaches:',
      '- **Alex**: Warm, encouraging, focuses on weekly achievements and celebration',
      '- **Jordan**: Analytical, strategic, provides weekly insights and next week planning',
      '',
      `## LISTENER PROFILE: ${user.firstName}`,
      `- Member since: ${this.formatMembershipDuration(insights.daysSinceMemberStart || 0)}`,
      `- Total XP: ${insights.totalXp || 0} points`,
      `- Badges earned: ${insights.badgeCount || 0}`,
      `- Current mood/state: ${mood}`,
      `- Language preference: ${language}`,
    ];

    // Psychological context section
    const psychologicalContext = [
      '',
      '## PSYCHOLOGICAL CONTEXT & PERSONALIZATION',
    ];

    // Momentum and energy assessment
    if (insights.currentMomentum) {
      psychologicalContext.push(`- Current momentum: ${insights.currentMomentum}`);
      if (insights.currentMomentum === 'high') {
        psychologicalContext.push('  → Leverage this energy for bigger challenges');
      } else if (insights.currentMomentum === 'low') {
        psychologicalContext.push('  → Focus on gentle encouragement and small wins');
      }
    }

    // Consistency and productivity insights
    if (insights.consistencyLevel) {
      psychologicalContext.push(`- Consistency level: ${insights.consistencyLevel}`);
    }
    
    if (insights.productivityScore) {
      psychologicalContext.push(`- Productivity score: ${insights.productivityScore}/100`);
    }

    // Emotional state indicators
    if (insights.needsEncouragement) {
      psychologicalContext.push('- **NEEDS ENCOURAGEMENT**: Show extra support and empathy');
    }
    
    if (insights.celebrationWorthy) {
      psychologicalContext.push('- **CELEBRATION MODE**: Acknowledge achievements enthusiastically');
    }
    
    if (insights.stressIndicators) {
      psychologicalContext.push('- **STRESS DETECTED**: Provide stress-relief strategies and reassurance');
    }

    // Habits deep dive
    const habitsSection = this.buildHabitsSection(context.habits || []);
    
    // Tasks and productivity section
    const tasksSection = this.buildTasksSection(context.tasks || []);
    
    // Learning and growth section
    const learningSection = this.buildLearningSection(context.skillPlans || [], context.challenges || []);

    // Recent achievements and recognition
    const achievementsSection = this.buildAchievementsSection(insights);

    // Strategic guidance section
    const strategySection = [
      '',
      '## STRATEGIC GUIDANCE ELEMENTS',
      '',
      '### Conversation Flow Requirements:',
      '1. **Opening (45s)**: Warm welcome using their name, acknowledge weekly progress',
      '2. **Weekly Review (90s)**: Celebrate wins and analyze patterns from the past week',
      '3. **Insights (90s)**: Provide data-driven observations about their weekly journey',
      '4. **Strategy (90s)**: Offer 2-3 specific, actionable recommendations for next week',
      '5. **Motivation (45s)**: Inspiring close that energizes them for the week ahead',
      '',
      '### Psychological Techniques to Employ:',
      '- **Weekly reflection**: Help them see patterns and growth over the week',
      '- **Progress visualization**: Show weekly trends and momentum',
      '- **Goal setting**: Specific weekly objectives and milestones',
      '- **Habit analysis**: Weekly consistency patterns and improvements',
      '- **Achievement recognition**: Celebrate weekly accomplishments and streaks',
    ];

    // Content guidelines
    const contentGuidelines = [
      '',
      '## CONTENT QUALITY STANDARDS',
      '',
      '### Voice and Tone:',
      `- Language: ${language} (use natural, conversational ${language})`,
      '- Alex: Warm, empathetic, celebratory, uses emotional language',
      '- Jordan: Professional but friendly, data-informed, solution-focused',
      '- Both: Authentic, non-patronizing, respectful of their intelligence',
      '',
      '### Specific Requirements:',
      '- Use their actual habit names, task titles, and skill plan names',
      '- Reference specific weekly numbers (streaks, completion rates, progress percentages)',
      '- Focus on weekly trends and patterns rather than daily fluctuations',
      '- Include natural conversation flow with brief interruptions and agreements',
      '- End with specific goals and action plan for the upcoming week',
    ];

    // Technical format
    const formatSection = [
      '',
      '## OUTPUT FORMAT',
      '',
      'Structure your response as a natural conversation:',
      '',
      'Alex: [Opening statement using their name and acknowledging their current state]',
      'Jordan: [Building on Alex\'s point with specific insights from their data]',
      'Alex: [Emotional support and recognition of progress]',
      'Jordan: [Strategic advice and actionable next steps]',
      'Alex: [Encouraging wrap-up that motivates action]',
      'Jordan: [Final inspiring thought that ties to their long-term vision]',
      '',
      '**GENERATE THE COMPLETE CONVERSATION NOW:**',
    ];

    // Combine all sections
    const fullPrompt = [
      ...basePrompt,
      ...psychologicalContext,
      ...habitsSection,
      ...tasksSection,
      ...learningSection,
      ...achievementsSection,
      ...strategySection,
      ...contentGuidelines,
      ...formatSection,
    ].join('\n');

    return fullPrompt;
  }

  private formatMembershipDuration(days: number): string {
    if (days < 7) return `${days} day${days !== 1 ? 's' : ''}`;
    if (days < 30) return `${Math.floor(days / 7)} week${Math.floor(days / 7) !== 1 ? 's' : ''}`;
    if (days < 365) return `${Math.floor(days / 30)} month${Math.floor(days / 30) !== 1 ? 's' : ''}`;
    return `${Math.floor(days / 365)} year${Math.floor(days / 365) !== 1 ? 's' : ''}`;
  }

  private buildHabitsSection(habits: any[]): string[] {
    if (!habits || habits.length === 0) {
      return [
        '',
        '## HABITS ANALYSIS',
        '- No active habits tracked yet',
        '- **Strategy**: Introduce the power of starting with micro-habits',
      ];
    }

    const section = [
      '',
      '## HABITS ANALYSIS',
      '',
    ];

    // Categorize habits by performance
    const strongHabits = habits.filter(h => h.currentStreak >= 7 || h.weeklyCompletionRate >= 0.8);
    const strugglingHabits = habits.filter(h => h.currentStreak === 0 || h.weeklyCompletionRate < 0.3);
    const riskHabits = habits.filter(h => h.isStreakAtRisk);

    // Strong habits (celebration)
    if (strongHabits.length > 0) {
      section.push('### 🌟 STRONG PERFORMANCE:');
      strongHabits.forEach(habit => {
        section.push(`- **${habit.name}**: ${habit.currentStreak} day streak (${Math.round(habit.weeklyCompletionRate * 100)}% weekly rate)`);
      });
      section.push('  → **Leverage**: Use these as anchor habits for new behaviors');
    }

    // At-risk habits (gentle intervention)
    if (riskHabits.length > 0) {
      section.push('### ⚠️ STREAKS AT RISK:');
      riskHabits.forEach(habit => {
        section.push(`- **${habit.name}**: ${habit.currentStreak} day streak (not completed today)`);
      });
      section.push('  → **Priority**: Address these THIS WEEK to maintain momentum');
    }

    // Struggling habits (strategic support)
    if (strugglingHabits.length > 0) {
      section.push('### 🎯 IMPROVEMENT OPPORTUNITIES:');
      strugglingHabits.slice(0, 2).forEach(habit => {
        section.push(`- **${habit.name}**: Needs strategic reset (${Math.round(habit.weeklyCompletionRate * 100)}% completion)`);
      });
      section.push('  → **Strategy**: Consider habit stacking or environmental design');
    }

    // Habit categories insight
    const categories = habits.reduce((acc, h) => {
      acc[h.category] = (acc[h.category] || 0) + 1;
      return acc;
    }, {});
    
    const topCategory = Object.entries(categories).sort(([,a], [,b]) => (b as number) - (a as number))[0];
    if (topCategory) {
      section.push(`### 📊 FOCUS AREA: ${topCategory[0]} habits (${topCategory[1]} active)`);
    }

    return section;
  }

  private buildTasksSection(tasks: any[]): string[] {
    if (!tasks || tasks.length === 0) {
      return [
        '',
        '## TASK MANAGEMENT',
        '- No active tasks in system',
        '- **Opportunity**: Set up task tracking for better productivity insights',
      ];
    }

    const section = [
      '',
      '## TASK MANAGEMENT ANALYSIS',
      '',
    ];

    const completed = tasks.filter(t => t.completed);
    const pending = tasks.filter(t => !t.completed);
    const overdue = tasks.filter(t => t.isOverdue);
    const urgent = pending.filter(t => t.priority === 'high');

    // Overall performance
    section.push(`### 📈 CURRENT STATUS:`);
    section.push(`- Completed: ${completed.length}/${tasks.length} tasks (${Math.round(completed.length / tasks.length * 100)}%)`);
    
    if (overdue.length > 0) {
      section.push(`- **OVERDUE**: ${overdue.length} task${overdue.length !== 1 ? 's' : ''} need immediate attention`);
      overdue.slice(0, 2).forEach(task => {
        section.push(`  • ${task.title} (${task.category}, ${task.priority} priority)`);
      });
    }

    if (urgent.length > 0) {
      section.push(`### 🚨 HIGH PRIORITY PENDING:`);
      urgent.slice(0, 3).forEach(task => {
        const daysUntilDue = task.daysUntilDue;
        const urgencyNote = daysUntilDue <= 1 ? 'URGENT' : daysUntilDue <= 3 ? 'Soon' : 'Planned';
        section.push(`- **${task.title}** (${urgencyNote}${daysUntilDue ? `, ${daysUntilDue} days` : ''})`);
      });
    }

    // Task categories analysis
    const tasksByCategory = tasks.reduce((acc, t) => {
      acc[t.category] = (acc[t.category] || []).concat(t);
      return acc;
    }, {});

    if (Object.keys(tasksByCategory).length > 1) {
      section.push(`### 📊 FOCUS DISTRIBUTION:`);
      Object.entries(tasksByCategory).forEach(([category, catTasks]) => {
        const taskArray = catTasks as any[];
        const catCompleted = taskArray.filter(t => t.completed).length;
        section.push(`- ${category}: ${catCompleted}/${taskArray.length} completed`);
      });
    }

    return section;
  }

  private buildLearningSection(skillPlans: any[], challenges: any[]): string[] {
    const section = [
      '',
      '## LEARNING & GROWTH ANALYSIS',
      '',
    ];

    // Skill Plans
    if (skillPlans && skillPlans.length > 0) {
      section.push('### 📚 SKILL DEVELOPMENT:');
      
      const activeSkillPlans = skillPlans.filter(sp => sp.isActive);
      const completedSkillPlans = skillPlans.filter(sp => sp.progress === 100);
      
      activeSkillPlans.slice(0, 3).forEach(plan => {
        const progressText = `${plan.progress}% (${plan.completedSteps}/${plan.totalSteps} steps)`;
        const activityText = plan.recentActivity ? '🔥 Active' : 'Paused';
        section.push(`- **${plan.name}**: ${progressText} [${activityText}]`);
        section.push(`  Category: ${plan.category} | Difficulty: ${plan.difficulty}`);
      });

      if (completedSkillPlans.length > 0) {
        section.push(`\n### 🎓 COMPLETED PROGRAMS: ${completedSkillPlans.length}`);
      }
    }

    // Challenges
    if (challenges && challenges.length > 0) {
      section.push('\n### 🏆 COMMUNITY CHALLENGES:');
      
      const activeChallenges = challenges.filter(c => c.isActive);
      activeChallenges.slice(0, 2).forEach(challenge => {
        const timeLeft = challenge.daysRemaining > 0 ? `${challenge.daysRemaining} days left` : 'Ending soon';
        section.push(`- **${challenge.name}**: ${challenge.progress}% progress (${timeLeft})`);
        section.push(`  Category: ${challenge.category} | Difficulty: ${challenge.difficulty}`);
      });
    }

    if (skillPlans.length === 0 && challenges.length === 0) {
      section.push('- No active learning programs');
      section.push('- **Opportunity**: Explore skill-building plans to accelerate growth');
    }

    return section;
  }

  private buildAchievementsSection(insights: any): string[] {
    const section = [
      '',
      '## RECENT ACHIEVEMENTS & RECOGNITION',
      '',
    ];

    if (insights.recentAchievements && insights.recentAchievements.length > 0) {
      section.push('### 🏅 NOTABLE ACCOMPLISHMENTS:');
      insights.recentAchievements.forEach(achievement => {
        section.push(`- ${achievement}`);
      });
    }

    if (insights.habitStrengths && insights.habitStrengths.length > 0) {
      section.push('\n### 💪 HABIT STRENGTHS:');
      insights.habitStrengths.forEach(strength => {
        section.push(`- Excelling at: ${strength}`);
      });
    }

    if (insights.improvementAreas && insights.improvementAreas.length > 0) {
      section.push('\n### 🎯 GROWTH OPPORTUNITIES:');
      insights.improvementAreas.forEach(area => {
        section.push(`- Focus area: ${area}`);
      });
    }

    if ((!insights.recentAchievements || insights.recentAchievements.length === 0) &&
        (!insights.habitStrengths || insights.habitStrengths.length === 0)) {
      section.push('- Building momentum phase - every step counts!');
      section.push('- **Focus**: Establish foundation habits for future achievements');
    }

    return section;
  }

  @Process('generate-content-and-audio')
  async handleContentAndAudioGeneration(
    job: Job<{
      podcastId: string;
      context: {
        user: User;
        topic?: string;
        mood?: string;
        language?: string;
      };
    }>,
  ) {
    const podcast = await this.podcastsRepository.findOne({
      where: { id: job.data.podcastId },
      relations: ['user'],
    });

    if (!podcast) {
      throw new Error('Podcast not found');
    }

    try {
      // Create enhanced context using only authenticated user data
      const baseContext = {
        user: podcast.user,
        // Optional overrides from job data (if provided by user preferences)
        topic: job.data.context?.topic,
        mood: job.data.context?.mood,
        language: job.data.context?.language || 'English',
      };

      // Fetch comprehensive user data and create enriched context
      const enrichedContext = await this.enrichUserContext(baseContext);

      // Generate content using Gemini with enhanced prompt
      console.log(
        `Generating personalized content for podcast ${podcast.id} using enhanced user context...`,
      );
      const transcript = await this.generatePodcastTranscript(enrichedContext);

      // Update podcast with generated content
      podcast.description = transcript.substring(0, 200) + '...';
      podcast.transcript = transcript;
      podcast.metadata = {
        ...podcast.metadata,
        generationPrompt: this.buildPrompt(enrichedContext),
        // Add enhancement metadata using type assertion to bypass strict typing
        ...(({
          generatedAt: new Date().toISOString(),
          version: '2.0',
          enhancedContext: true,
        } as any)),
      };

      await this.podcastsRepository.save(podcast);
      console.log(`Enhanced content generated for podcast ${podcast.id}`);

      // Now generate audio
      await this.generateAudio(podcast, transcript);
    } catch (error) {
      console.error('Error generating podcast content and audio:', error);

      // Update podcast status to error
      podcast.status = 'error';
      podcast.error = error.message;
      await this.podcastsRepository.save(podcast);

      throw error;
    }
  }

  private async enrichUserContext(context: {
    user: User;
    topic?: string;
    mood?: string;
    language?: string;
    goals?: string[];
    habits?: { name: string; streak: number }[];
  }) {
    try {
      // Fetch comprehensive user data
      const [
        userHabits,
        userTasks,
        userSkillPlans,
        allChallenges
      ] = await Promise.all([
        this.habitsService.findAllByUser(context.user),
        this.tasksService.findAll(context.user.id),
        this.skillPlansService.findAll({ creatorId: context.user.id }),
        this.challengesService.findAll()
      ]);

      // Process habits with rich context
      const today = new Date().toISOString().split('T')[0];
      const yesterday = new Date(Date.now() - 86400000).toISOString().split('T')[0];
      const weekAgo = new Date(Date.now() - 7 * 86400000).toISOString().split('T')[0];

      const habitsData = userHabits.map(habit => ({
        name: habit.name,
        currentStreak: habit.currentStreak,
        longestStreak: habit.longestStreak,
        completedToday: habit.completion[today] || false,
        completedYesterday: habit.completion[yesterday] || false,
        weeklyCompletionRate: this.calculateWeeklyCompletionRate(habit.completion),
        schedule: habit.schedule || [],
        xpReward: habit.xpReward || 0,
        isStreakAtRisk: habit.currentStreak > 0 && !habit.completion[today],
        category: this.categorizeHabit(habit.name),
      }));

      // Process tasks with priority and timing insights
      const now = new Date();
      const tasksData = userTasks.map(task => ({
        title: task.title,
        priority: task.priority,
        completed: task.completed,
        isOverdue: !task.completed && new Date(task.dueDate) < now,
        daysUntilDue: !task.completed ? Math.ceil((new Date(task.dueDate).getTime() - now.getTime()) / (1000 * 60 * 60 * 24)) : null,
        category: this.categorizeTask(task.title, task.description),
      }));

      // Process skill plans with detailed progress
      const skillPlansData = userSkillPlans.map(plan => ({
        name: plan.name,
        progress: plan.progress,
        category: plan.metadata?.category || 'General',
        difficulty: plan.metadata?.difficulty || 'intermediate',
        estimatedDuration: plan.metadata?.estimatedDuration || '',
        tags: plan.metadata?.tags || [],
        completedSteps: plan.steps?.filter(step => step.isCompleted).length || 0,
        totalSteps: plan.steps?.length || 0,
        isActive: plan.progress > 0 && plan.progress < 100,
        recentActivity: this.hasRecentSkillPlanActivity(plan),
      }));

      // Process challenges with engagement metrics
      const userChallenges = allChallenges.filter(challenge => 
        challenge.participants?.some(participant => participant.id === context.user.id)
      );
      
      const challengesData = userChallenges.map(challenge => {
        const userProgress = challenge.progress[context.user.id];
        const isActive = new Date(challenge.startDate) <= now && new Date(challenge.endDate) >= now;
        
        return {
          name: challenge.name,
          progress: userProgress?.currentProgress || 0,
          isActive,
          daysRemaining: isActive ? Math.ceil((new Date(challenge.endDate).getTime() - now.getTime()) / (1000 * 60 * 60 * 24)) : 0,
          difficulty: this.assessChallengeDifficulty(challenge),
          category: this.categorizeChallenge(challenge.name, challenge.description),
        };
      });

      // Advanced user insights
      const userInsights = {
        totalXp: context.user.xp || 0,
        badges: context.user.badges || [],
        badgeCount: (context.user.badges || []).length,
        memberSince: context.user.createdAt,
        daysSinceMemberStart: Math.floor((now.getTime() - new Date(context.user.createdAt).getTime()) / (1000 * 60 * 60 * 24)),
        
        // Progress patterns
        habitStrengths: this.identifyHabitStrengths(habitsData),
        improvementAreas: this.identifyImprovementAreas(habitsData, tasksData),
        recentAchievements: this.getRecentAchievements(habitsData, skillPlansData, challengesData),
        currentMomentum: this.assessCurrentMomentum(habitsData, tasksData, skillPlansData),
        
        // Productivity insights
        productivityScore: this.calculateProductivityScore(habitsData, tasksData),
        consistencyLevel: this.assessConsistencyLevel(habitsData),
        goalAlignment: this.assessGoalAlignment(habitsData, skillPlansData, challengesData),
        
        // Motivational context
        needsEncouragement: this.needsEncouragement(habitsData, tasksData),
        celebrationWorthy: this.hasCelebrationWorthy(habitsData, skillPlansData, challengesData),
        stressIndicators: this.detectStressIndicators(tasksData, habitsData),
      };

      return {
        ...context,
        habits: habitsData,
        tasks: tasksData,
        skillPlans: skillPlansData,
        challenges: challengesData,
        userInsights,
        // Infer mood if not provided
        mood: context.mood || this.inferMoodFromData(habitsData, tasksData, userInsights),
        // Default language
        language: context.language || 'English',
      };
    } catch (error) {
      console.error('Error enriching user context:', error);
      // Return basic context if enrichment fails
      return {
        ...context,
        habits: context.habits || [],
        tasks: [],
        skillPlans: [],
        challenges: [],
        userInsights: {
          totalXp: context.user.xp || 0,
          badges: context.user.badges || [],
          needsEncouragement: true,
          celebrationWorthy: false,
        },
        mood: context.mood || 'neutral',
        language: context.language || 'English',
      };
    }
  }

  private calculateWeeklyCompletionRate(completion: { [date: string]: boolean }): number {
    const now = new Date();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    let completedDays = 0;
    let totalDays = 0;
    
    for (let i = 0; i < 7; i++) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const dateStr = date.toISOString().split('T')[0];
      if (completion[dateStr]) completedDays++;
      totalDays++;
    }
    
    return totalDays > 0 ? completedDays / totalDays : 0;
  }

  private categorizeHabit(name: string): string {
    const categories = {
      health: ['exercise', 'workout', 'run', 'walk', 'gym', 'yoga', 'meditation', 'sleep', 'water', 'diet'],
      learning: ['read', 'study', 'learn', 'course', 'book', 'practice', 'skill'],
      productivity: ['plan', 'organize', 'clean', 'work', 'focus', 'task'],
      social: ['call', 'meet', 'family', 'friend', 'connect', 'social'],
      creative: ['write', 'draw', 'paint', 'music', 'create', 'art'],
      mindfulness: ['meditate', 'journal', 'reflect', 'gratitude', 'mindful'],
    };

    const lowerName = name.toLowerCase();
    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => lowerName.includes(keyword))) {
        return category;
      }
    }
    return 'general';
  }

  private categorizeTask(title: string, description?: string): string {
    const text = `${title} ${description || ''}`.toLowerCase();
    
    if (text.includes('work') || text.includes('job') || text.includes('meeting')) return 'work';
    if (text.includes('health') || text.includes('doctor') || text.includes('exercise')) return 'health';
    if (text.includes('family') || text.includes('friend') || text.includes('social')) return 'personal';
    if (text.includes('learn') || text.includes('study') || text.includes('course')) return 'learning';
    if (text.includes('home') || text.includes('clean') || text.includes('organize')) return 'household';
    
    return 'general';
  }

  private categorizeChallenge(name: string, description: string): string {
    const text = `${name} ${description}`.toLowerCase();
    
    if (text.includes('fitness') || text.includes('exercise') || text.includes('health')) return 'fitness';
    if (text.includes('learning') || text.includes('skill') || text.includes('study')) return 'learning';
    if (text.includes('productivity') || text.includes('work') || text.includes('focus')) return 'productivity';
    if (text.includes('mindfulness') || text.includes('meditation') || text.includes('mental')) return 'mindfulness';
    
    return 'general';
  }

  private hasRecentSkillPlanActivity(plan: any): boolean {
    if (!plan.steps) return false;
    
    const threeDaysAgo = new Date(Date.now() - 3 * 24 * 60 * 60 * 1000);
    return plan.steps.some(step => 
      step.completedAt && new Date(step.completedAt) > threeDaysAgo
    );
  }

  private assessChallengeDifficulty(challenge: any): string {
    const duration = new Date(challenge.endDate).getTime() - new Date(challenge.startDate).getTime();
    const durationDays = duration / (1000 * 60 * 60 * 24);
    
    if (durationDays <= 7) return 'easy';
    if (durationDays <= 30) return 'medium';
    return 'hard';
  }

  private identifyHabitStrengths(habits: any[]): string[] {
    return habits
      .filter(h => h.currentStreak >= 7 || h.weeklyCompletionRate >= 0.8)
      .map(h => h.name)
      .slice(0, 3);
  }

  private identifyImprovementAreas(habits: any[], tasks: any[]): string[] {
    const areas: string[] = [];
    
    const strugglingHabits = habits.filter(h => h.currentStreak === 0 && h.weeklyCompletionRate < 0.3);
    if (strugglingHabits.length > 0) areas.push('habit consistency');
    
    const overdueTasks = tasks.filter(t => t.isOverdue);
    if (overdueTasks.length > 2) areas.push('task management');
    
    const highPriorityPending = tasks.filter(t => !t.completed && t.priority === 'high');
    if (highPriorityPending.length > 0) areas.push('prioritization');
    
    return areas.slice(0, 2);
  }

  private getRecentAchievements(habits: any[], skillPlans: any[], challenges: any[]): string[] {
    const achievements: string[] = [];
    
    // Habit streaks
    const goodStreaks = habits.filter(h => h.currentStreak >= 5);
    if (goodStreaks.length > 0) {
      achievements.push(`${goodStreaks.length} habit${goodStreaks.length > 1 ? 's' : ''} with 5+ day streaks`);
    }
    
    // Skill plan progress
    const activeSkillPlans = skillPlans.filter(sp => sp.isActive && sp.progress > 0);
    if (activeSkillPlans.length > 0) {
      achievements.push(`Active in ${activeSkillPlans.length} skill development plan${activeSkillPlans.length > 1 ? 's' : ''}`);
    }
    
    // Challenge participation
    const activeChallenges = challenges.filter(c => c.isActive);
    if (activeChallenges.length > 0) {
      achievements.push(`Participating in ${activeChallenges.length} community challenge${activeChallenges.length > 1 ? 's' : ''}`);
    }
    
    return achievements.slice(0, 3);
  }

  private assessCurrentMomentum(habits: any[], tasks: any[], skillPlans: any[]): 'high' | 'medium' | 'low' {
    let score = 0;
    
    // Habit momentum
    const recentHabitSuccess = habits.filter(h => h.completedToday || h.completedYesterday).length / Math.max(habits.length, 1);
    score += recentHabitSuccess * 40;
    
    // Task momentum
    const completedTasks = tasks.filter(t => t.completed).length / Math.max(tasks.length, 1);
    score += completedTasks * 30;
    
    // Skill plan momentum
    const activeSkillPlans = skillPlans.filter(sp => sp.isActive && sp.recentActivity).length / Math.max(skillPlans.length, 1);
    score += activeSkillPlans * 30;
    
    if (score >= 70) return 'high';
    if (score >= 40) return 'medium';
    return 'low';
  }

  private calculateProductivityScore(habits: any[], tasks: any[]): number {
    let score = 0;
    
    // Habit consistency (40% weight)
    const avgCompletionRate = habits.reduce((sum, h) => sum + h.weeklyCompletionRate, 0) / Math.max(habits.length, 1);
    score += avgCompletionRate * 40;
    
    // Task completion (35% weight)
    const taskCompletionRate = tasks.filter(t => t.completed).length / Math.max(tasks.length, 1);
    score += taskCompletionRate * 35;
    
    // Streak performance (25% weight)
    const avgStreak = habits.reduce((sum, h) => sum + Math.min(h.currentStreak, 30), 0) / Math.max(habits.length, 1);
    score += (avgStreak / 30) * 25;
    
    return Math.round(score);
  }

  private assessConsistencyLevel(habits: any[]): 'excellent' | 'good' | 'moderate' | 'needs improvement' {
    const avgWeeklyRate = habits.reduce((sum, h) => sum + h.weeklyCompletionRate, 0) / Math.max(habits.length, 1);
    
    if (avgWeeklyRate >= 0.9) return 'excellent';
    if (avgWeeklyRate >= 0.7) return 'good';
    if (avgWeeklyRate >= 0.5) return 'moderate';
    return 'needs improvement';
  }

  private assessGoalAlignment(habits: any[], skillPlans: any[], challenges: any[]): 'aligned' | 'partially aligned' | 'misaligned' {
    const activeEngagement = habits.filter(h => h.currentStreak > 0).length + 
                            skillPlans.filter(sp => sp.isActive).length + 
                            challenges.filter(c => c.isActive).length;
    
    const totalItems = habits.length + skillPlans.length + challenges.length;
    const alignmentRatio = totalItems > 0 ? activeEngagement / totalItems : 0;
    
    if (alignmentRatio >= 0.6) return 'aligned';
    if (alignmentRatio >= 0.3) return 'partially aligned';
    return 'misaligned';
  }

  private needsEncouragement(habits: any[], tasks: any[]): boolean {
    const strugglingHabits = habits.filter(h => h.isStreakAtRisk || h.currentStreak === 0).length;
    const overdueTasks = tasks.filter(t => t.isOverdue).length;
    
    return strugglingHabits > habits.length * 0.3 || overdueTasks > 2;
  }

  private hasCelebrationWorthy(habits: any[], skillPlans: any[], challenges: any[]): boolean {
    const milestoneHabits = habits.filter(h => h.currentStreak >= 7 && h.currentStreak % 7 === 0);
    const completedSkillPlans = skillPlans.filter(sp => sp.progress === 100);
    const nearCompletionPlans = skillPlans.filter(sp => sp.progress >= 80);
    
    return milestoneHabits.length > 0 || completedSkillPlans.length > 0 || nearCompletionPlans.length > 0;
  }

  private detectStressIndicators(tasks: any[], habits: any[]): boolean {
    const highPriorityOverdue = tasks.filter(t => t.isOverdue && t.priority === 'high').length;
    const manyOverdue = tasks.filter(t => t.isOverdue).length > 3;
    const brokenStreaks = habits.filter(h => h.longestStreak > 7 && h.currentStreak === 0).length;
    
    return highPriorityOverdue > 0 || manyOverdue || brokenStreaks > 1;
  }

  private inferMoodFromData(habits: any[], tasks: any[], insights: any): string {
    if (insights.celebrationWorthy) return 'accomplished';
    if (insights.stressIndicators) return 'overwhelmed';
    if (insights.currentMomentum === 'high') return 'motivated';
    if (insights.needsEncouragement) return 'struggling';
    if (insights.currentMomentum === 'medium') return 'steady';
    return 'neutral';
  }

  private async generateAudio(
    podcast: Podcast,
    transcript: string,
  ): Promise<void> {
    const filename = `podcast_${podcast.id}.wav`;
    const filePath = path.join(this.podcastsUploadDir, filename);

    await ensureDirectoryExists(this.podcastsUploadDir);
    
    const response = await this.genAI.models.generateContent({
      model: "gemini-2.5-flash-preview-tts",
      contents: [{ parts: [{ text: transcript }] }],
      config: {
        responseModalities: ['AUDIO'],
        speechConfig: {
          multiSpeakerVoiceConfig: {
            speakerVoiceConfigs: [
              {
                speaker: 'Alex',
                voiceConfig: {
                  prebuiltVoiceConfig: { voiceName: 'Kore' }
                }
              },
              {
                speaker: 'Jordan',
                voiceConfig: {
                  prebuiltVoiceConfig: { voiceName: 'Puck' }
                }
              }
            ]
          }
        }
      }
    });

    const audioData = response.candidates?.[0]?.content?.parts?.[0]?.inlineData?.data;
    if (!audioData) {
      throw new Error('No audio data received from Gemini TTS');
    }

    const audioBuffer = Buffer.from(audioData, 'base64');
    await this.saveWaveFile(filePath, audioBuffer);
    
    console.log('Audio generated successfully using Gemini TTS');

    // Construct the web URL instead of storing absolute path
    const baseUrl = this.configService.get<string>('app.baseUrl', 'https://powerup.apperx.com');
    const audioWebUrl = `${baseUrl}/podcasts/${filename}`;

    // Update podcast metadata
    podcast.status = 'completed';
    podcast.audioUrl = audioWebUrl;
    podcast.durationSeconds = this.calculateAudioDuration(filePath);

    const savedPodcast = await this.podcastsRepository.save(podcast);

    // Send notification
    await this.notificationsService.sendNotificationWithPreferences(
      podcast.user.id,
      'podcast_available' as NotificationType,
      {
        title: 'Your Weekly Podcast is Ready!',
        body: `Your personalized podcast "${savedPodcast.title}" is now available.`,
        type: 'podcast_available' as NotificationType,
        data: { podcastId: savedPodcast.id },
      },
    );

    console.log(`Podcast audio saved at: ${filePath}`);
  }

  private calculateAudioDuration(filePath: string): number {
    try {
      const audioSize = fs.statSync(filePath).size;
      const sampleRate = 24000;
      const bytesPerSample = 2;
      const channels = 1;
      return Math.floor(audioSize / (sampleRate * bytesPerSample * channels));
    } catch (error) {
      console.warn('Could not calculate audio duration:', error.message);
      return 300; // Default to 5 minutes
    }
  }

  @Cron(CronExpression.EVERY_WEEK)
  async cleanupOldPodcasts() {
    try {
      // Find podcasts older than 60 days (keep more history for weekly podcasts)
      const sixtyDaysAgo = new Date();
      sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);

      const oldPodcasts = await this.podcastsRepository.find({
        where: {
          createdAt: LessThan(sixtyDaysAgo),
          status: 'completed',
        },
      });

      for (const podcast of oldPodcasts) {
        // Extract the file name from the audioUrl
        const filename = podcast.audioUrl.split('/').pop();
        if (!filename) continue;

        const filePath = path.join(this.podcastsUploadDir, filename);

        // Check if file exists and delete it
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
          console.log(`Deleted old podcast file: ${filePath}`);
        }

        // Remove the database record
        await this.podcastsRepository.remove(podcast);
        console.log(`Removed old podcast record: ${podcast.id}`);
      }

      console.log(`Cleaned up ${oldPodcasts.length} old podcast records`);
    } catch (error) {
      console.error('Error during podcast cleanup:', error);
    }
  }
}
