import { Test, TestingModule } from '@nestjs/testing';
import { PodcastsService } from './podcasts.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Podcast } from './entities/podcast.entity';
import { ConfigService } from '@nestjs/config';
import { getQueueToken } from '@nestjs/bull';
import { NotFoundException, ForbiddenException } from '@nestjs/common';
import { NotificationsService } from '../notifications/notifications.service';

describe('PodcastsService', () => {
  let service: PodcastsService;
  let repository: Repository<Podcast>;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    delete: jest.fn(),
    remove: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockQueue = {
    add: jest.fn(),
  };

  const mockNotificationsService = {
    sendNotificationWithPreferences: jest.fn(),
    sendNotification: jest.fn(),
    createForUser: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PodcastsService,
        {
          provide: getRepositoryToken(Podcast),
          useValue: mockRepository,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: getQueueToken('podcast-generation'),
          useValue: mockQueue,
        },
        {
          provide: NotificationsService,
          useValue: mockNotificationsService,
        },
        {
          provide: NotificationsService,
          useValue: mockNotificationsService,
        },
      ],
    }).compile();

    service = module.get<PodcastsService>(PodcastsService);
    repository = module.get<Repository<Podcast>>(getRepositoryToken(Podcast));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getWeeklyPodcast', () => {
    it('should return existing podcast or generate a new one', async () => {
      const userId = '1';
      const startOfWeek = new Date();
      startOfWeek.setHours(0, 0, 0, 0);
      startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay());

      const podcast = {
        id: '1',
        title: 'Weekly Test Podcast',
        status: 'completed',
        user: { id: userId },
        createdAt: startOfWeek,
      };

      mockRepository.findOne.mockResolvedValue(podcast);

      const result = await service.getWeeklyPodcast(userId);

      expect(result).toBeDefined();
      expect(result.id).toBe('1');
      expect(result.title).toBe('Weekly Test Podcast');
    });
  });

  describe('getUserPodcasts', () => {
    it('should return podcast history for the user', async () => {
      const userId = '1';
      const podcasts = [
        {
          id: '1',
          title: 'Weekly Motivation',
          audioUrl: 'https://example.com/podcast1.mp3',
          user: { id: userId },
          createdAt: new Date(),
        },
        {
          id: '2',
          title: 'Progress Review',
          audioUrl: 'https://example.com/podcast2.mp3',
          user: { id: userId },
          createdAt: new Date(),
        },
      ];

      mockRepository.find.mockResolvedValue(podcasts);

      const result = await service.getPodcastHistory(userId);

      expect(result).toBeDefined();
      expect(result).toHaveLength(2);
    });
  });

  describe('findOne', () => {
    it('should return a podcast if found', async () => {
      const podcastId = '1';
      const userId = '1';
      const podcast = {
        id: podcastId,
        title: 'Weekly Motivation',
        user: { id: userId },
      };

      mockRepository.findOne.mockResolvedValue(podcast);

      const result = await service.findOne(podcastId, userId);

      expect(result).toBeDefined();
      expect(result.id).toBe(podcastId);
    });

    it('should throw NotFoundException if podcast not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne('1', '1')).rejects.toThrow(NotFoundException);
    });
  });

  describe('markAsListened', () => {
    it('should mark a podcast as listened', async () => {
      const podcastId = '1';
      const userId = '1';
      const podcast = {
        id: podcastId,
        title: 'Weekly Motivation',
        metadata: {},
        user: { id: userId },
      };

      const updatedPodcast = {
        ...podcast,
        metadata: {
          listened: true,
          listenedAt: expect.any(String),
        },
      };

      mockRepository.findOne.mockResolvedValue(podcast);
      mockRepository.save.mockResolvedValue(updatedPodcast);

      const result = await service.markAsListened(podcastId, userId);

      expect(result).toBeDefined();
      expect(result.metadata.listened).toBe(true);
      expect(result.metadata.listenedAt).toBeDefined();
    });
  });



  describe('remove', () => {
    it('should delete a podcast', async () => {
      const podcastId = '1';
      const userId = '1';
      const podcast = {
        id: podcastId,
        title: 'Weekly Motivation',
        user: { id: userId },
      };

      mockRepository.findOne.mockResolvedValue(podcast);
      mockRepository.remove.mockResolvedValue(podcast);

      await service.remove(podcastId, userId);

      expect(mockRepository.remove).toHaveBeenCalledWith(podcast);
    });

    it('should throw NotFoundException if podcast not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.remove('1', '1')).rejects.toThrow(NotFoundException);
    });
  });

  describe('generatePodcastContent', () => {
    it('should throw ForbiddenException if user already generated podcast today', async () => {
      const user = { id: '1' } as any;
      const existingPodcast = {
        id: '1',
        title: 'Existing Podcast',
        user,
        createdAt: new Date(),
      };

      // Mock repository to return existing podcast for today
      mockRepository.findOne.mockResolvedValue(existingPodcast);

      await expect(
        service.generatePodcastContent({
          user,
          topic: 'test',
        })
      ).rejects.toThrow(ForbiddenException);
      
      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: {
          user: { id: '1' },
          createdAt: expect.any(Object), // MoreThanOrEqual(today)
        },
      });
    });

    it('should allow podcast generation if no podcast exists for today', async () => {
      const user = { id: '1' } as any;
      const mockPodcast = {
        id: '1',
        title: 'Test Podcast',
        user,
      };

      // Mock repository to return null (no existing podcast for today)
      mockRepository.findOne.mockResolvedValue(null);
      mockRepository.create.mockReturnValue(mockPodcast);
      mockRepository.save.mockResolvedValue(mockPodcast);

      const result = await service.generatePodcastContent({
        user,
        topic: 'test',
      });

      expect(result).toBe(mockPodcast);
      expect(mockRepository.save).toHaveBeenCalled();
      // Note: In test environment, the queue is not called because service returns early
    });
  });
});
