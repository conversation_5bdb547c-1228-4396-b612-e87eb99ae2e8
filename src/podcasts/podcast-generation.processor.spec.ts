import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { PodcastGenerationProcessor } from './podcast-generation.processor';
import { ConfigService } from '@nestjs/config';
import { Job } from 'bull';
import { Podcast } from './entities/podcast.entity';
import { NotificationsService } from '../notifications/notifications.service';
import { Voice, VoiceClient } from 'elevenlabs-node';
import { GoogleGenAI } from '@google/genai';
import { NotificationType } from '../notifications/types/notification-types';

const mockVoices = [
  {
    voice_id: 'voice1',
    name: 'Voice 1',
    description: 'Professional male voice',
    preview_url: 'http://example.com/preview1',
    category: 'professional',
    labels: { gender: 'male' }
  },
  {
    voice_id: 'voice2',
    name: 'Voice 2',
    description: 'Friendly female voice',
    preview_url: 'http://example.com/preview2',
    category: 'conversational',
    labels: { gender: 'female' }
  }
];

jest.mock('elevenlabs-node', () => ({
  VoiceClient: jest.fn().mockImplementation(() => ({
    getVoices: jest.fn().mockResolvedValue(mockVoices),
    textToSpeech: jest.fn().mockImplementation(async (voiceId, options) => {
      if (!voiceId || !options?.text) {
        throw new Error('Missing required parameters');
      }
      return Buffer.from('mock audio data');
    })
  }))
}));

jest.mock('@google/generative-ai', () => {
  return {
    GoogleGenerativeAI: jest.fn().mockImplementation(() => ({
      getGenerativeModel: jest.fn().mockReturnValue({
        generateContent: jest.fn().mockResolvedValue({
          response: { text: () => 'voice1' }
        })
      })
    }))
  };
});

describe('PodcastGenerationProcessor', () => {
  let processor: PodcastGenerationProcessor;
  let configService: ConfigService;
  let mockVoiceClient: jest.Mocked<VoiceClient>;
  let mockGenAI: jest.Mocked<GoogleGenAI>;

  const mockPodcastRepository = {
    findOne: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn((key: string) => {
      switch (key) {
        case 'uploads.podcastsDir':
          return 'uploads/podcasts';
        case 'ELEVENLABS_API_KEY':
          return 'mock-elevenlabs-key';
        case 'gemini.apiKey':
          return 'mock-gemini-key';
        default:
          return undefined;
      }
    }),
  };

  const mockNotificationsService = {
    sendNotificationWithPreferences: jest.fn(),
    sendNotification: jest.fn(),
    createForUser: jest.fn()
  };

  beforeEach(async () => {
    // Reset all mocks
    jest.clearAllMocks();

    // Create a real VoiceClient mock that we can spy on methods
    const mockVoiceClientValue = {
      getVoices: jest.fn().mockResolvedValue(mockVoices),
      textToSpeech: jest.fn().mockImplementation(async (voiceId, options) => {
        if (!voiceId || !options?.text) {
          throw new Error('Missing required parameters');
        }
        return Buffer.from('mock audio data');
      })
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PodcastGenerationProcessor,
        {
          provide: getRepositoryToken(Podcast),
          useValue: mockPodcastRepository,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: NotificationsService,
          useValue: mockNotificationsService,
        },
        {
          provide: VoiceClient,
          useValue: mockVoiceClientValue
        }
      ],
    }).compile();

    processor = module.get<PodcastGenerationProcessor>(PodcastGenerationProcessor);
    configService = module.get<ConfigService>(ConfigService);
    mockVoiceClient = module.get<VoiceClient>(VoiceClient) as jest.Mocked<VoiceClient>;
    
    // Create a spy that bypasses the private method
    jest.spyOn(processor as any, 'selectVoiceForPodcast').mockImplementation(() => Promise.resolve('voice1'));
  });

  it('should be defined', () => {
    expect(processor).toBeDefined();
  });

  describe('handleAudioGeneration', () => {
    beforeEach(() => {
      // Reset all mocks before each test
      jest.clearAllMocks();
    });

    it.skip('should successfully generate a podcast audio with voice selection', async () => {
      const jobData = {
        podcastId: '1',
        transcript: 'Test transcript content'
      };

      const mockJob = {
        data: jobData,
      } as Job;

      const mockUser = {
        id: 'user1',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        voicePreference: null // Add this to test dynamic voice selection
      };

      const mockPodcast = {
        id: '1',
        title: 'Test Podcast',
        status: 'pending',
        transcript: 'Test transcript content',
        user: mockUser
      };

      // Set up repository mocks
      mockPodcastRepository.findOne.mockResolvedValue(mockPodcast);
      mockPodcastRepository.save.mockResolvedValue({
        ...mockPodcast,
        status: 'completed',
        audioUrl: '/podcasts/podcast_1.mp3'
      });
      
      // Let the implementation run, we'll just verify the result
      mockVoiceClient.textToSpeech.mockResolvedValue(Buffer.from('mock audio data'));

      await processor.handleAudioGeneration(mockJob);

      // Verify TTS generation
      expect(mockVoiceClient.textToSpeech).toHaveBeenCalledWith(
        'voice1',
        expect.objectContaining({
          text: expect.stringContaining('Test transcript content')
        })
      );

      // Verify notification was sent
      expect(mockNotificationsService.sendNotificationWithPreferences).toHaveBeenCalledWith(
        mockUser.id,
        NotificationType.PODCAST_AVAILABLE,
        expect.objectContaining({
          title: expect.any(String),
          body: expect.stringContaining(mockPodcast.title),
          type: NotificationType.PODCAST_AVAILABLE,
          data: expect.any(Object)
        })
      );
    });

    it.skip('should handle errors during podcast generation', async () => {
      const jobData = {
        podcastId: '1',
        transcript: 'Test transcript content'
      };

      const mockJob = {
        data: jobData,
      } as Job;

      const mockError = new Error('Failed to generate audio');
      
      // Make textToSpeech fail
      mockVoiceClient.getVoices.mockResolvedValueOnce(mockVoices);
      mockVoiceClient.textToSpeech.mockRejectedValueOnce(mockError);

      const mockPodcast = {
        id: '1',
        title: 'Test Podcast',
        status: 'pending',
        transcript: 'Test transcript content',
        user: {
          id: 'user1',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User'
        }
      };

      mockPodcastRepository.findOne.mockResolvedValue(mockPodcast);

      await expect(processor.handleAudioGeneration(mockJob)).rejects.toThrow('Failed to generate audio');

      // Verify podcast was updated with error status
      expect(mockPodcastRepository.save).toHaveBeenCalledWith(expect.objectContaining({
        id: '1',
        status: 'error',
        error: expect.stringContaining('Failed to generate audio')
      }));
    });

    it.skip('should handle voice selection failure gracefully', async () => {
      const jobData = {
        podcastId: '1',
        transcript: 'Test transcript content'
      };

      const mockJob = {
        data: jobData,
      } as Job;

      const mockPodcast = {
        id: '1',
        title: 'Test Podcast',
        status: 'pending',
        user: {
          id: 'user1',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User'
        }
      };

      // Setup complete mock for the entire chain that will definitely fail
      mockPodcastRepository.findOne.mockResolvedValue(mockPodcast);
      
      // Make getVoices fail which should cause an error
      mockVoiceClient.getVoices.mockRejectedValueOnce(new Error('Failed to get voices'));
      mockVoiceClient.textToSpeech.mockRejectedValueOnce(new Error('Failed to get any voices from ElevenLabs'));

      await expect(processor.handleAudioGeneration(mockJob))
        .rejects
        .toThrow('Failed to get any voices from ElevenLabs');
    });

    it('should successfully generate a podcast audio', async () => {
      const jobData = {
        podcastId: '1',
        transcript: 'Test transcript content'
      };

      const mockJob = {
        data: jobData,
      } as Job;

      const mockPodcast = {
        id: '1',
        title: 'Test Podcast',
        status: 'pending',
        user: {
          id: 'user1',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User'
        }
      };

      mockPodcastRepository.findOne.mockResolvedValue(mockPodcast);
      mockPodcastRepository.save.mockResolvedValue({
        ...mockPodcast,
        status: 'completed',
        audioUrl: 'podcasts/podcast_1.mp3'
      });

      await processor.handleAudioGeneration(mockJob);

      expect(mockPodcastRepository.findOne).toHaveBeenCalledWith({
        where: { id: '1' },
        relations: ['user']
      });
      expect(mockPodcastRepository.save).toHaveBeenCalledWith(expect.objectContaining({
        id: '1',
        status: 'completed',
        audioUrl: expect.stringContaining('podcast_1.mp3')
      }));
    });

    it('should handle audio generation failure', async () => {
      const jobData = {
        podcastId: '1',
        transcript: 'Test transcript content'
      };

      const mockJob = {
        data: jobData,
      } as Job;

      const mockPodcast = {
        id: '1',
        title: 'Test Podcast',
        status: 'pending'
      };

      mockPodcastRepository.findOne.mockResolvedValue(mockPodcast);
      mockPodcastRepository.save.mockRejectedValue(new Error('Failed to save'));

      await expect(processor.handleAudioGeneration(mockJob))
        .rejects
        .toThrow('Failed to save');
    });

    it('should handle podcast not found', async () => {
      const jobData = {
        podcastId: '1',
        transcript: 'Test transcript content'
      };

      const mockJob = {
        data: jobData,
      } as Job;

      mockPodcastRepository.findOne.mockResolvedValue(null);

      await expect(processor.handleAudioGeneration(mockJob))
        .rejects
        .toThrow('Podcast not found');
    });
  });
});
