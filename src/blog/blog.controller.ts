import { Controller, Get, Query, Param, NotFoundException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiParam } from '@nestjs/swagger';
import { BlogService } from './blog.service';
import { BlogPostResponseDto, SearchBlogPostDto } from './dto';
import { BlogCategory } from './entities/blog-post.entity';

@ApiTags('blog-api')
@Controller('api/blog')
export class BlogController {
  constructor(private readonly blogService: BlogService) {}

  @Get()
  @ApiOperation({ summary: 'Get all published blog posts' })
  @ApiResponse({ status: 200, description: 'Returns paginated blog posts', type: [BlogPostResponseDto] })
  async getAllPosts(@Query() searchDto: SearchBlogPostDto) {
    return this.blogService.findAll(searchDto);
  }

  @Get('featured')
  @ApiOperation({ summary: 'Get featured blog posts' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of posts to return', example: 5 })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns featured blog posts',
    schema: {
      type: 'object',
      properties: {
        posts: { type: 'array', items: { $ref: '#/components/schemas/BlogPostResponseDto' } },
        total: { type: 'number' }
      }
    }
  })
  async getFeaturedPosts(@Query('limit') limit?: number) {
    const posts = await this.blogService.getFeaturedPosts(limit ? parseInt(limit.toString(), 10) : 5);
    return { posts, total: posts.length };
  }

  @Get('category/:category')
  @ApiOperation({ summary: 'Get blog posts by category' })
  @ApiParam({ name: 'category', enum: BlogCategory, description: 'Blog category' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of posts to return', example: 10 })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns blog posts by category',
    schema: {
      type: 'object',
      properties: {
        posts: { type: 'array', items: { $ref: '#/components/schemas/BlogPostResponseDto' } },
        total: { type: 'number' }
      }
    }
  })
  async getPostsByCategory(
    @Param('category') category: BlogCategory,
    @Query('limit') limit?: number
  ) {
    const posts = await this.blogService.getPostsByCategory(category, limit ? parseInt(limit.toString(), 10) : 10);
    return { posts, total: posts.length };
  }

  @Get(':slug')
  @ApiOperation({ summary: 'Get a blog post by slug' })
  @ApiParam({ name: 'slug', description: 'Blog post slug' })
  @ApiResponse({ status: 200, description: 'Returns the blog post', type: BlogPostResponseDto })
  @ApiResponse({ status: 404, description: 'Blog post not found' })
  async getPostBySlug(@Param('slug') slug: string) {
    return this.blogService.findBySlug(slug);
  }

  @Get(':id/related')
  @ApiOperation({ summary: 'Get related blog posts' })
  @ApiParam({ name: 'id', description: 'Blog post ID' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of related posts to return', example: 3 })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns related blog posts',
    schema: {
      type: 'object',
      properties: {
        posts: { type: 'array', items: { $ref: '#/components/schemas/BlogPostResponseDto' } },
        total: { type: 'number' }
      }
    }
  })
  async getRelatedPosts(
    @Param('id') id: string,
    @Query('limit') limit?: number
  ) {
    const posts = await this.blogService.getRelatedPosts(id, limit ? parseInt(limit.toString(), 10) : 3);
    return { posts, total: posts.length };
  }
}
