import { <PERSON>, Get, Res, Param, Query } from '@nestjs/common';
import { ApiExcludeController } from '@nestjs/swagger';
import type { Response } from 'express';
import { BlogService } from './blog.service';
import { SearchBlogPostDto } from './dto';
import { BlogCategory } from './entities/blog-post.entity';
import { AdminService } from '../admin/admin.service';

@ApiExcludeController()
@Controller()
export class BlogWebController {
  constructor(
    private readonly blogService: BlogService,
    private readonly adminService: AdminService
  ) {}

  private async getBaseUrl(): Promise<string> {
    try {
      const appConstants = await this.adminService.getAppConstants();
      return appConstants?.websiteUrl || '';
    } catch (error) {
      return '';
    }
  }

  @Get('blog')
  async getBlogIndex(@Query() searchDto: SearchBlogPostDto, @Res() res: Response) {
    try {
      const { posts, total } = await this.blogService.findAll(searchDto);
      const featuredPosts = await this.blogService.getFeaturedPosts(3);
      const baseUrl = await this.getBaseUrl();
      
      // Get categories for filter
      const categories = Object.values(BlogCategory);
      
      res.render('blog/index', {
        title: 'Blog - Power Up',
        description: 'Discover insights about wellness, AI technology, and personal development',
        posts,
        featuredPosts,
        categories,
        total,
        currentPage: Math.floor((searchDto.offset || 0) / (searchDto.limit || 10)) + 1,
        totalPages: Math.ceil(total / (searchDto.limit || 10)),
        searchParams: searchDto,
        baseUrl,
        layout: 'public-layout'
      });
    } catch (error) {
      const baseUrl = await this.getBaseUrl();
      res.status(500).render('blog/error', {
        title: 'Error - Power Up Blog',
        error: {
          title: 'Unable to Load Blog',
          message: 'We\'re experiencing technical difficulties. Please try again later.',
          backLink: '/'
        },
        baseUrl,
        layout: 'public-layout'
      });
    }
  }

  @Get('blog/category/:category')
  async getBlogByCategory(
    @Param('category') category: BlogCategory,
    @Query() searchDto: SearchBlogPostDto,
    @Res() res: Response
  ) {
    try {
      const categorySearchDto = { ...searchDto, category };
      const { posts, total } = await this.blogService.findAll(categorySearchDto);
      const categories = Object.values(BlogCategory);
      const baseUrl = await this.getBaseUrl();
      
      res.render('blog/category', {
        title: `${category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ')} - Power Up Blog`,
        description: `Read our latest articles about ${category.replace('-', ' ')}`,
        posts,
        categories,
        currentCategory: category,
        total,
        currentPage: Math.floor((searchDto.offset || 0) / (searchDto.limit || 10)) + 1,
        totalPages: Math.ceil(total / (searchDto.limit || 10)),
        searchParams: searchDto,
        baseUrl,
        layout: 'public-layout'
      });
    } catch (error) {
      const baseUrl = await this.getBaseUrl();
      res.status(404).render('blog/error', {
        title: 'Category Not Found - Power Up Blog',
        error: {
          title: 'Category Not Found',
          message: 'The blog category you\'re looking for doesn\'t exist.',
          backLink: '/blog'
        },
        baseUrl,
        layout: 'public-layout'
      });
    }
  }

  @Get('blog/:slug')
  async getBlogPost(@Param('slug') slug: string, @Res() res: Response) {
    try {
      const post = await this.blogService.findBySlug(slug);
      const relatedPosts = await this.blogService.getRelatedPosts(post.id, 3);
      const baseUrl = await this.getBaseUrl();
      
      res.render('blog/post', {
        title: `${post.title} - Power Up Blog`,
        description: post.metaDescription || post.excerpt || post.content.substring(0, 160),
        post,
        relatedPosts,
        baseUrl,
        layout: 'public-layout'
      });
    } catch (error) {
      const baseUrl = await this.getBaseUrl();
      res.status(404).render('blog/error', {
        title: 'Post Not Found - Power Up Blog',
        error: {
          title: 'Blog Post Not Found',
          message: 'The blog post you\'re looking for could not be found.',
          backLink: '/blog'
        },
        baseUrl,
        layout: 'public-layout'
      });
    }
  }
}
