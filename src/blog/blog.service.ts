import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, ILike, In } from 'typeorm';
import { BlogPost, BlogPostStatus, BlogCategory } from './entities/blog-post.entity';
import { CreateBlogPostDto, UpdateBlogPostDto, SearchBlogPostDto } from './dto';

@Injectable()
export class BlogService {
  constructor(
    @InjectRepository(BlogPost)
    private readonly blogPostRepository: Repository<BlogPost>,
  ) {}

  // Create a new blog post
  async create(createBlogPostDto: CreateBlogPostDto, authorId: string): Promise<BlogPost> {
    const { slug, ...data } = createBlogPostDto;
    
    // Generate slug if not provided
    const finalSlug = slug || this.generateSlug(data.title);
    
    // Check if slug already exists
    const existingPost = await this.blogPostRepository.findOne({ where: { slug: finalSlug } });
    if (existingPost) {
      throw new ConflictException('A blog post with this slug already exists');
    }

    // Ensure tags is properly formatted for PostgreSQL simple-array
    const processedTags = this.processTags(data.tags);

    const blogPost = this.blogPostRepository.create({
      ...data,
      tags: processedTags,
      slug: finalSlug,
      authorId,
      publishedAt: data.status === BlogPostStatus.PUBLISHED ? new Date() : undefined,
    });

    return this.blogPostRepository.save(blogPost);
  }

  // Get all blog posts with search and filtering
  async findAll(searchDto: SearchBlogPostDto = {}): Promise<{ posts: BlogPost[]; total: number }> {
    const {
      query,
      category,
      status,
      tags,
      featured,
      limit = 10,
      offset = 0,
      sortBy = 'publishedAt',
      sortOrder = 'DESC'
    } = searchDto;

    const queryBuilder = this.blogPostRepository
      .createQueryBuilder('post')
      .leftJoinAndSelect('post.author', 'author');

    // Search by title or content
    if (query) {
      queryBuilder.andWhere(
        '(post.title ILIKE :query OR post.content ILIKE :query OR post.excerpt ILIKE :query)',
        { query: `%${query}%` }
      );
    }

    // Filter by category
    if (category) {
      queryBuilder.andWhere('post.category = :category', { category });
    }

    // Filter by status
    if (status) {
      queryBuilder.andWhere('post.status = :status', { status });
    } else {
      // Default to only published posts for public view
      queryBuilder.andWhere('post.status = :status', { status: BlogPostStatus.PUBLISHED });
    }

    // Filter by tags
    if (tags) {
      const tagArray = tags.split(',').map(tag => tag.trim());
      queryBuilder.andWhere('post.tags && :tags', { tags: tagArray });
    }

    // Filter featured posts
    if (featured !== undefined) {
      queryBuilder.andWhere('post.isFeatured = :featured', { featured });
    }

    // Apply sorting
    const validSortFields = ['createdAt', 'publishedAt', 'views', 'likes', 'title'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'publishedAt';
    queryBuilder.orderBy(`post.${sortField}`, sortOrder);

    // Apply pagination
    queryBuilder.skip(offset).take(limit);

    const [posts, total] = await queryBuilder.getManyAndCount();

    return { posts, total };
  }

  // Get a single blog post by ID
  async findOne(id: string): Promise<BlogPost> {
    const post = await this.blogPostRepository.findOne({
      where: { id },
      relations: ['author'],
    });

    if (!post) {
      throw new NotFoundException('Blog post not found');
    }

    return post;
  }

  // Get a single blog post by slug
  async findBySlug(slug: string): Promise<BlogPost> {
    const post = await this.blogPostRepository.findOne({
      where: { slug, status: BlogPostStatus.PUBLISHED },
      relations: ['author'],
    });

    if (!post) {
      throw new NotFoundException('Blog post not found');
    }

    // Increment view count
    await this.incrementViews(post.id);

    return { ...post, views: post.views + 1 };
  }

  // Update a blog post
  async update(id: string, updateBlogPostDto: UpdateBlogPostDto): Promise<BlogPost> {
    const post = await this.findOne(id);
    
    const { slug, status, tags, ...data } = updateBlogPostDto;
    
    // Handle slug update
    if (slug && slug !== post.slug) {
      const existingPost = await this.blogPostRepository.findOne({ where: { slug } });
      if (existingPost && existingPost.id !== id) {
        throw new ConflictException('A blog post with this slug already exists');
      }
    }

    // Process tags for PostgreSQL compatibility
    const processedTags = tags !== undefined ? this.processTags(tags) : undefined;

    // Handle publish date
    const updateData: any = { ...data };
    if (slug) updateData.slug = slug;
    if (tags !== undefined) updateData.tags = processedTags;
    if (status) {
      updateData.status = status;
      if (status === BlogPostStatus.PUBLISHED && !post.publishedAt) {
        updateData.publishedAt = new Date();
      }
    }

    await this.blogPostRepository.update(id, updateData);
    return this.findOne(id);
  }

  // Delete a blog post
  async remove(id: string): Promise<void> {
    const post = await this.findOne(id);
    await this.blogPostRepository.remove(post);
  }

  // Get featured posts
  async getFeaturedPosts(limit: number = 5): Promise<BlogPost[]> {
    return this.blogPostRepository.find({
      where: { 
        isFeatured: true, 
        status: BlogPostStatus.PUBLISHED 
      },
      relations: ['author'],
      order: { publishedAt: 'DESC' },
      take: limit,
    });
  }

  // Get posts by category
  async getPostsByCategory(category: BlogCategory, limit: number = 10): Promise<BlogPost[]> {
    return this.blogPostRepository.find({
      where: { 
        category, 
        status: BlogPostStatus.PUBLISHED 
      },
      relations: ['author'],
      order: { publishedAt: 'DESC' },
      take: limit,
    });
  }

  // Get related posts
  async getRelatedPosts(postId: string, limit: number = 3): Promise<BlogPost[]> {
    const post = await this.findOne(postId);
    
    return this.blogPostRepository
      .createQueryBuilder('post')
      .leftJoinAndSelect('post.author', 'author')
      .where('post.id != :id', { id: postId })
      .andWhere('post.status = :status', { status: BlogPostStatus.PUBLISHED })
      .andWhere('(post.category = :category OR post.tags && :tags)', {
        category: post.category,
        tags: post.tags || []
      })
      .orderBy('post.publishedAt', 'DESC')
      .take(limit)
      .getMany();
  }

  // Increment view count
  async incrementViews(id: string): Promise<void> {
    await this.blogPostRepository.increment({ id }, 'views', 1);
  }

  // Like/unlike a post
  async toggleLike(id: string): Promise<{ liked: boolean; likes: number }> {
    const post = await this.findOne(id);
    // For simplicity, just increment likes (in a real app, you'd track user likes)
    await this.blogPostRepository.increment({ id }, 'likes', 1);
    return { liked: true, likes: post.likes + 1 };
  }

  // Get blog statistics for admin
  async getStats(): Promise<any> {
    const [
      totalPosts,
      publishedPosts,
      draftPosts,
      archivedPosts,
      featuredPosts,
      totalViews,
      totalLikes
    ] = await Promise.all([
      this.blogPostRepository.count(),
      this.blogPostRepository.count({ where: { status: BlogPostStatus.PUBLISHED } }),
      this.blogPostRepository.count({ where: { status: BlogPostStatus.DRAFT } }),
      this.blogPostRepository.count({ where: { status: BlogPostStatus.ARCHIVED } }),
      this.blogPostRepository.count({ where: { isFeatured: true } }),
      this.blogPostRepository
        .createQueryBuilder('post')
        .select('SUM(post.views)', 'sum')
        .getRawOne()
        .then(result => parseInt(result.sum) || 0),
      this.blogPostRepository
        .createQueryBuilder('post')
        .select('SUM(post.likes)', 'sum')
        .getRawOne()
        .then(result => parseInt(result.sum) || 0),
    ]);

    return {
      totalPosts,
      publishedPosts,
      draftPosts,
      archivedPosts,
      featuredPosts,
      totalViews,
      totalLikes,
    };
  }

  // Generate slug from title
  private generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .trim()
      .substring(0, 100); // Limit length
  }

  // Admin-specific methods
  async findAllForAdmin(searchDto: SearchBlogPostDto = {}): Promise<{ posts: BlogPost[]; total: number }> {
    // Remove the status filter for admin - they can see all posts
    const { status, ...adminSearchDto } = searchDto;
    
    const queryBuilder = this.blogPostRepository
      .createQueryBuilder('post')
      .leftJoinAndSelect('post.author', 'author');

    // Apply search filters (without default status filter)
    if (adminSearchDto.query) {
      queryBuilder.andWhere(
        '(post.title ILIKE :query OR post.content ILIKE :query OR post.excerpt ILIKE :query)',
        { query: `%${adminSearchDto.query}%` }
      );
    }

    if (adminSearchDto.category) {
      queryBuilder.andWhere('post.category = :category', { category: adminSearchDto.category });
    }

    // Allow admin to filter by specific status
    if (status) {
      queryBuilder.andWhere('post.status = :status', { status });
    }

    if (adminSearchDto.tags) {
      const tagArray = adminSearchDto.tags.split(',').map(tag => tag.trim());
      queryBuilder.andWhere('post.tags && :tags', { tags: tagArray });
    }

    if (adminSearchDto.featured !== undefined) {
      queryBuilder.andWhere('post.isFeatured = :featured', { featured: adminSearchDto.featured });
    }

    // Apply sorting
    const validSortFields = ['createdAt', 'publishedAt', 'views', 'likes', 'title'];
    const sortField = validSortFields.includes(adminSearchDto.sortBy || '') ? adminSearchDto.sortBy : 'createdAt';
    queryBuilder.orderBy(`post.${sortField}`, adminSearchDto.sortOrder);

    // Apply pagination
    queryBuilder.skip(adminSearchDto.offset).take(adminSearchDto.limit);

    const [posts, total] = await queryBuilder.getManyAndCount();

    return { posts, total };
  }

  // Helper method to process tags for PostgreSQL simple-array compatibility
  private processTags(tags: string[] | undefined | null): string[] | undefined {
    // If tags is undefined or null, return undefined (which TypeORM handles properly)
    if (!tags) {
      return undefined;
    }
    
    // If tags is an empty array, return undefined to avoid PostgreSQL parsing issues
    if (Array.isArray(tags) && tags.length === 0) {
      return undefined;
    }
    
    // If tags has content, ensure all elements are strings and filter out empty ones
    if (Array.isArray(tags)) {
      const filteredTags = tags
        .filter(tag => tag && typeof tag === 'string' && tag.trim() !== '')
        .map(tag => tag.trim());
      
      return filteredTags.length > 0 ? filteredTags : undefined;
    }
    
    return undefined;
  }
}
