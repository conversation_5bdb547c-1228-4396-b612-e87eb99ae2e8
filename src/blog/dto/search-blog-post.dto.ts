import { <PERSON><PERSON><PERSON><PERSON>, Is<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { BlogCategory, BlogPostStatus } from '../entities/blog-post.entity';

export class SearchBlogPostDto {
  @ApiProperty({ description: 'Search query', required: false })
  @IsOptional()
  @IsString()
  query?: string;

  @ApiProperty({ enum: BlogCategory, description: 'Filter by category', required: false })
  @IsOptional()
  @IsEnum(BlogCategory)
  category?: BlogCategory;

  @ApiProperty({ enum: BlogPostStatus, description: 'Filter by status', required: false })
  @IsOptional()
  @IsEnum(BlogPostStatus)
  status?: BlogPostStatus;

  @ApiProperty({ description: 'Filter by tags (comma-separated)', required: false })
  @IsOptional()
  @IsString()
  tags?: string;

  @ApiProperty({ description: 'Show only featured posts', required: false })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  featured?: boolean;

  @ApiProperty({ description: 'Number of posts to return', minimum: 1, maximum: 100, default: 10, required: false })
  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10))
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiProperty({ description: 'Number of posts to skip', minimum: 0, default: 0, required: false })
  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10))
  @IsNumber()
  @Min(0)
  offset?: number = 0;

  @ApiProperty({ description: 'Sort by field', enum: ['createdAt', 'publishedAt', 'views', 'likes', 'title'], default: 'publishedAt', required: false })
  @IsOptional()
  @IsString()
  sortBy?: string = 'publishedAt';

  @ApiProperty({ description: 'Sort order', enum: ['ASC', 'DESC'], default: 'DESC', required: false })
  @IsOptional()
  @IsString()
  sortOrder?: 'ASC' | 'DESC' = 'DESC';
}
