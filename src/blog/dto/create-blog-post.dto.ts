import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>y, IsB<PERSON><PERSON>, <PERSON><PERSON>ength, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { BlogCategory, BlogPostStatus } from '../entities/blog-post.entity';

export class CreateBlogPostDto {
  @ApiProperty({ description: 'Blog post title', example: 'The Future of AI in Personal Wellness' })
  @IsString()
  @MinLength(5)
  @MaxLength(255)
  title: string;

  @ApiProperty({ description: 'Short excerpt/summary', example: 'Discover how AI is revolutionizing personal wellness and habit formation.', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  excerpt?: string;

  @ApiProperty({ description: 'Blog post content in markdown', example: '# Introduction\n\nThis is the content...' })
  @IsString()
  @MinLength(50)
  content: string;

  @ApiProperty({ description: 'URL slug (auto-generated if not provided)', example: 'future-ai-personal-wellness', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  slug?: string;

  @ApiProperty({ enum: BlogCategory, description: 'Blog post category', example: BlogCategory.AI_TECHNOLOGY })
  @IsEnum(BlogCategory)
  @Transform(({ value }) => {
    // Handle empty string or null/undefined by returning default category
    if (!value || value === '' || (typeof value === 'string' && value.trim() === '')) {
      return BlogCategory.WELLNESS; // Default to WELLNESS category
    }
    return value;
  })
  category: BlogCategory;

  @ApiProperty({ enum: BlogPostStatus, description: 'Blog post status', example: BlogPostStatus.DRAFT, required: false })
  @IsOptional()
  @IsEnum(BlogPostStatus)
  status?: BlogPostStatus;

  @ApiProperty({ description: 'Featured image URL', example: 'https://example.com/image.jpg', required: false })
  @IsOptional()
  @IsString()
  featuredImage?: string;

  @ApiProperty({ description: 'Meta description for SEO', example: 'Learn about AI wellness technology trends', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(160)
  metaDescription?: string;

  @ApiProperty({ description: 'Tags array', example: ['AI', 'wellness', 'technology'], required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }) => {
    // Handle empty string or null/undefined by returning empty array
    if (!value || value === '' || (typeof value === 'string' && value.trim() === '')) {
      return [];
    }
    // If it's already an array, return as is
    if (Array.isArray(value)) {
      return value;
    }
    // If it's a string, split by comma and clean up
    if (typeof value === 'string') {
      return value.split(',').map(tag => tag.trim()).filter(tag => tag);
    }
    return [];
  })
  tags?: string[];

  @ApiProperty({ description: 'Mark as featured post', example: false, required: false })
  @IsOptional()
  @IsBoolean()
  isFeatured?: boolean;
}
