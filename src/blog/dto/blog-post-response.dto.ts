import { ApiProperty } from '@nestjs/swagger';
import { Blog<PERSON>ategory, BlogPostStatus } from '../entities/blog-post.entity';

export class BlogPostResponseDto {
  @ApiProperty({ description: 'Blog post ID' })
  id: string;

  @ApiProperty({ description: 'Blog post title' })
  title: string;

  @ApiProperty({ description: 'Short excerpt' })
  excerpt: string;

  @ApiProperty({ description: 'Blog post content' })
  content: string;

  @ApiProperty({ description: 'URL slug' })
  slug: string;

  @ApiProperty({ enum: BlogCategory, description: 'Blog post category' })
  category: BlogCategory;

  @ApiProperty({ enum: BlogPostStatus, description: 'Blog post status' })
  status: BlogPostStatus;

  @ApiProperty({ description: 'Featured image URL' })
  featuredImage: string;

  @ApiProperty({ description: 'Meta description' })
  metaDescription: string;

  @ApiProperty({ description: 'Tags array' })
  tags: string[];

  @ApiProperty({ description: 'View count' })
  views: number;

  @ApiProperty({ description: 'Like count' })
  likes: number;

  @ApiProperty({ description: 'Is featured post' })
  isFeatured: boolean;

  @ApiProperty({ description: 'Published date' })
  publishedAt: Date;

  @ApiProperty({ description: 'Author information' })
  author: {
    id: string;
    email: string;
    name?: string;
  };

  @ApiProperty({ description: 'Created date' })
  createdAt: Date;

  @ApiProperty({ description: 'Updated date' })
  updatedAt: Date;
}
