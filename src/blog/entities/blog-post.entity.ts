import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { AdminUser } from '../../admin/entities/admin-user.entity';

export enum BlogPostStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived'
}

export enum BlogCategory {
  WELLNESS = 'wellness',
  AI_TECHNOLOGY = 'ai-technology',
  HABITS = 'habits',
  PRODUCTIVITY = 'productivity',
  MENTAL_HEALTH = 'mental-health',
  FITNESS = 'fitness',
  NUTRITION = 'nutrition',
  COMPANY_NEWS = 'company-news'
}

@Entity('blog_posts')
export class BlogPost {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  title: string;

  @Column({ length: 500, nullable: true })
  excerpt: string;

  @Column('text')
  content: string;

  @Column({ length: 255, unique: true })
  slug: string;

  @Column({
    type: 'enum',
    enum: BlogCategory,
    default: BlogCategory.WELLNESS
  })
  category: BlogCategory;

  @Column({
    type: 'enum',
    enum: BlogPostStatus,
    default: BlogPostStatus.DRAFT
  })
  status: BlogPostStatus;

  @Column({ nullable: true })
  featuredImage: string;

  @Column({ type: 'text', nullable: true })
  metaDescription: string;

  @Column({ type: 'simple-array', nullable: true })
  tags: string[];

  @Column({ default: 0 })
  views: number;

  @Column({ default: 0 })
  likes: number;

  @Column({ default: false })
  isFeatured: boolean;

  @Column({ type: 'timestamp', nullable: true })
  publishedAt: Date;

  @Column('uuid')
  authorId: string;

  @ManyToOne(() => AdminUser, { eager: true })
  @JoinColumn({ name: 'authorId' })
  author: AdminUser;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
