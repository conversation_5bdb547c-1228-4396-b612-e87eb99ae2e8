import { Injectable } from '@nestjs/common';

export interface EditorJSBlock {
  id?: string;
  type: string;
  data: any;
}

export interface EditorJSData {
  time?: number;
  blocks: EditorJSBlock[];
  version?: string;
}

@Injectable()
export class EditorJsToHtmlService {
  
  /**
   * Convert Editor.js JSON to HTML
   */
  convertToHtml(editorData: string | EditorJSData): string {
    try {
      let data: EditorJSData;
      
      // Parse JSON string if needed
      if (typeof editorData === 'string') {
        data = JSON.parse(editorData);
      } else {
        data = editorData;
      }

      if (!data || !data.blocks || !Array.isArray(data.blocks)) {
        return '';
      }

      return data.blocks.map(block => this.convertBlock(block)).join('');
    } catch (error) {
      console.error('Error converting Editor.js to HTML:', error);
      return '';
    }
  }

  /**
   * Convert a single Editor.js block to HTML
   */
  private convertBlock(block: EditorJSBlock): string {
    switch (block.type) {
      case 'paragraph':
        return this.convertParagraph(block.data);
      
      case 'header':
        return this.convertHeader(block.data);
      
      case 'list':
        return this.convertList(block.data);
      
      case 'quote':
        return this.convertQuote(block.data);
      
      case 'code':
        return this.convertCode(block.data);
      
      case 'delimiter':
        return this.convertDelimiter();
      
      case 'image':
        return this.convertImage(block.data);
      
      case 'embed':
        return this.convertEmbed(block.data);
      
      case 'table':
        return this.convertTable(block.data);
      
      case 'checklist':
        return this.convertChecklist(block.data);
      
      case 'warning':
        return this.convertWarning(block.data);
      
      case 'linkTool':
        return this.convertLinkTool(block.data);
      
      default:
        // For unknown block types, try to extract text content
        if (block.data && block.data.text) {
          return `<p>${this.sanitizeHtml(block.data.text)}</p>`;
        }
        return '';
    }
  }

  private convertParagraph(data: any): string {
    if (!data || !data.text) return '';
    return `<p>${this.processInlineStyles(data.text)}</p>`;
  }

  private convertHeader(data: any): string {
    if (!data || !data.text) return '';
    const level = data.level || 1;
    const validLevel = Math.min(Math.max(level, 1), 6); // Ensure level is between 1-6
    return `<h${validLevel}>${this.processInlineStyles(data.text)}</h${validLevel}>`;
  }

  private convertList(data: any): string {
    if (!data || !data.items || !Array.isArray(data.items)) return '';
    
    const tag = data.style === 'ordered' ? 'ol' : 'ul';
    const items = data.items
      .map(item => `<li>${this.processInlineStyles(item)}</li>`)
      .join('');
    
    return `<${tag}>${items}</${tag}>`;
  }

  private convertQuote(data: any): string {
    if (!data || !data.text) return '';
    const caption = data.caption ? `<cite>${this.sanitizeHtml(data.caption)}</cite>` : '';
    return `<blockquote>${this.processInlineStyles(data.text)}${caption}</blockquote>`;
  }

  private convertCode(data: any): string {
    if (!data || !data.code) return '';
    return `<pre><code>${this.sanitizeHtml(data.code)}</code></pre>`;
  }

  private convertDelimiter(): string {
    return '<hr>';
  }

  private convertImage(data: any): string {
    if (!data || !data.file || !data.file.url) return '';
    
    const alt = data.caption ? this.sanitizeHtml(data.caption) : '';
    const caption = data.caption ? `<figcaption>${this.sanitizeHtml(data.caption)}</figcaption>` : '';
    
    return `<figure><img src="${this.sanitizeHtml(data.file.url)}" alt="${alt}">${caption}</figure>`;
  }

  private convertEmbed(data: any): string {
    if (!data || !data.embed) return '';
    
    const caption = data.caption ? `<figcaption>${this.sanitizeHtml(data.caption)}</figcaption>` : '';
    
    return `<figure>
      <div class="embed-responsive">
        <iframe src="${this.sanitizeHtml(data.embed)}" frameborder="0" allowfullscreen></iframe>
      </div>
      ${caption}
    </figure>`;
  }

  private convertTable(data: any): string {
    if (!data || !data.content || !Array.isArray(data.content)) return '';
    
    const withHeadings = data.withHeadings !== false; // Default to true
    let html = '<table>';
    
    data.content.forEach((row: string[], index: number) => {
      if (withHeadings && index === 0) {
        html += '<thead><tr>';
        row.forEach(cell => {
          html += `<th>${this.sanitizeHtml(cell)}</th>`;
        });
        html += '</tr></thead><tbody>';
      } else {
        if (index === 1 && withHeadings) {
          // First body row after header
        }
        html += '<tr>';
        row.forEach(cell => {
          html += `<td>${this.sanitizeHtml(cell)}</td>`;
        });
        html += '</tr>';
      }
    });
    
    if (withHeadings) {
      html += '</tbody>';
    }
    html += '</table>';
    
    return html;
  }

  private convertChecklist(data: any): string {
    if (!data || !data.items || !Array.isArray(data.items)) return '';
    
    const items = data.items
      .map(item => {
        const checked = item.checked ? 'checked' : '';
        return `<li><input type="checkbox" ${checked} disabled> ${this.sanitizeHtml(item.text)}</li>`;
      })
      .join('');
    
    return `<ul class="checklist">${items}</ul>`;
  }

  private convertWarning(data: any): string {
    if (!data || !data.message) return '';
    
    const title = data.title ? `<strong>${this.sanitizeHtml(data.title)}</strong>` : '';
    
    return `<div class="warning">
      ${title}
      <p>${this.processInlineStyles(data.message)}</p>
    </div>`;
  }

  private convertLinkTool(data: any): string {
    if (!data || !data.link) return '';
    
    const title = data.meta && data.meta.title ? this.sanitizeHtml(data.meta.title) : data.link;
    const description = data.meta && data.meta.description ? 
      `<p>${this.sanitizeHtml(data.meta.description)}</p>` : '';
    const image = data.meta && data.meta.image && data.meta.image.url ? 
      `<img src="${this.sanitizeHtml(data.meta.image.url)}" alt="${title}">` : '';
    
    return `<div class="link-tool">
      <a href="${this.sanitizeHtml(data.link)}" target="_blank" rel="noopener">
        ${image}
        <div class="link-content">
          <h4>${title}</h4>
          ${description}
        </div>
      </a>
    </div>`;
  }

  /**
   * Process inline styles like bold, italic, etc.
   */
  private processInlineStyles(text: string): string {
    if (!text) return '';
    
    // Basic inline style processing
    // This is a simplified version - you might want to implement more sophisticated parsing
    return text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code>$1</code>');
  }

  /**
   * Basic HTML sanitization
   */
  private sanitizeHtml(text: string): string {
    if (!text) return '';
    
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;');
  }
}