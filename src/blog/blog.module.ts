import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NestExpressApplication } from '@nestjs/platform-express';
import { BlogController } from './blog.controller';
import { BlogWebController } from './blog-web.controller';
import { BlogService } from './blog.service';
import { BlogPost } from './entities/blog-post.entity';
import { EditorJsToHtmlService } from './services/editorjs-to-html.service';
import { AdminModule } from '../admin/admin.module';
import { join } from 'path';
import { create } from 'express-handlebars';

@Module({
  imports: [
    TypeOrmModule.forFeature([BlogPost]),
    forwardRef(() => AdminModule)
  ],
  controllers: [BlogWebController, BlogController],
  providers: [BlogService, EditorJsToHtmlService],
  exports: [BlogService, EditorJsToHtmlService]
})
export class BlogModule {
  static configureHandlebars(app: NestExpressApplication) {
    // Get the EditorJs service instance
    const editorJsService = new EditorJsToHtmlService();
    
    // Configure Handlebars view engine for blog pages
    const hbs = create({
      layoutsDir: join(process.cwd(), 'src/templates'),
      defaultLayout: 'public-layout',
      extname: '.hbs',
      helpers: {
        eq: (a: any, b: any) => a === b,
        ne: (a: any, b: any) => a !== b,
        gt: (a: any, b: any) => a > b,
        lt: (a: any, b: any) => a < b,
        and: (a: any, b: any) => a && b,
        or: (a: any, b: any) => a || b,
        formatDate: (dateString: string) => {
          if (!dateString) return '';
          try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return '';
            return date.toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            });
          } catch (error) {
            return '';
          }
        },
        formatDateShort: (dateString: string) => {
          if (!dateString) return '';
          try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return '';
            return date.toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
              year: 'numeric'
            });
          } catch (error) {
            return '';
          }
        },
        truncate: (str: string, length: number) => {
          if (!str || typeof str !== 'string') return str || '';
          if (str.length <= length) return str;
          return str.substring(0, length) + '...';
        },
        capitalize: (str: string) => {
          if (!str || typeof str !== 'string') return str || '';
          return str.charAt(0).toUpperCase() + str.slice(1);
        },
        replace: (str: string, search: string, replace: string) => {
          if (!str || typeof str !== 'string') return str || '';
          if (!search || typeof search !== 'string') return str;
          if (typeof replace !== 'string') replace = '';
          return str.replace(new RegExp(search, 'g'), replace);
        },
        markdownToHtml: (markdown: string) => {
          if (!markdown || typeof markdown !== 'string') return '';
          // Basic markdown to HTML conversion (you might want to use a proper markdown library)
          return markdown
            .replace(/^### (.*$)/gm, '<h3>$1</h3>')
            .replace(/^## (.*$)/gm, '<h2>$1</h2>')
            .replace(/^# (.*$)/gm, '<h1>$1</h1>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/\n/g, '<br>');
        },
        editorJsToHtml: (editorData: string) => {
          return editorJsService.convertToHtml(editorData);
        },
        categoryDisplayName: (category: string) => {
          if (!category) return '';
          return category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        },
        statusColor: (status: string) => {
          switch (status) {
            case 'published': return 'green';
            case 'draft': return 'yellow';
            case 'archived': return 'gray';
            default: return 'gray';
          }
        },
        times: (n: number, block: any) => {
          let result = '';
          for (let i = 0; i < n; i++) {
            result += block.fn(i + 1);
          }
          return result;
        },
        add: (a: number, b: number) => a + b,
        subtract: (a: number, b: number) => a - b,
        multiply: (a: number, b: number) => a * b,
        divide: (a: number, b: number) => Math.floor(a / b),
        paginate: (currentPage: number, totalPages: number, limit: number = 5) => {
          const pages: Array<{ number: number; isCurrent: boolean }> = [];
          const start = Math.max(1, currentPage - Math.floor(limit / 2));
          const end = Math.min(totalPages, start + limit - 1);
          
          for (let i = start; i <= end; i++) {
            pages.push({
              number: i,
              isCurrent: i === currentPage
            });
          }
          
          return pages;
        }
      },
    });

    app.engine('.hbs', hbs.engine);
    app.set('view engine', '.hbs');
    app.set('views', join(process.cwd(), 'src/templates'));
  }
}
