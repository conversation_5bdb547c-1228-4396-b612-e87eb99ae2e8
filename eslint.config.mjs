// Simple ESLint configuration that avoids TypeScript errors
export default [
  {
    ignores: ['eslint.config.mjs'],
    linterOptions: {
      reportUnusedDisableDirectives: true,
    },
    languageOptions: {
      sourceType: 'module',
      ecmaVersion: 2022,
      globals: {
        // Common globals
        console: false,
        module: false,
        require: false,
        process: false,
        // Jest globals
        describe: false,
        expect: false,
        test: false,
        jest: false,
        beforeEach: false,
        afterEach: false,
      },
    },
    rules: {
      'no-unused-vars': 'warn',
      'no-undef': 'error',
      'prefer-const': 'warn',
    },
  },
];