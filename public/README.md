# Power Up Landing Page

This directory contains the static landing page for the Power Up API.

## Files

- `index.html` - Main landing page with responsive design
- `styles.css` - Additional CSS styles
- `manifest.json` - Web app manifest for PWA features
- `robots.txt` - SEO configuration

## Features

- **Responsive Design**: Works on desktop, tablet, and mobile
- **Modern UI**: Clean, gradient-based design with animations
- **Interactive Elements**: Smooth scrolling, hover effects, and dynamic stats
- **API Integration**: Real-time status display from the backend API
- **SEO Optimized**: Meta tags, structured content, and robots.txt
- **PWA Ready**: Web app manifest for progressive web app features

## Accessing the Landing Page

When the server is running, the landing page is available at:
- `http://localhost:3000/` - Main landing page
- `http://localhost:3000/api/health` - API health check
- `http://localhost:3000/api/status` - API status with features list
- `http://localhost:3000/api` - Swagger API documentation

## Technology Stack

- **HTML5** with semantic markup
- **Tailwind CSS** for styling via CDN
- **Vanilla JavaScript** for interactions
- **Express Static Files** served by NestJS

## Customization

The landing page can be customized by:
1. Editing the HTML content in `index.html`
2. Adding custom styles in `styles.css`
3. Updating the color scheme in the Tailwind classes
4. Modifying the JavaScript for additional functionality

The page is designed to showcase the Power Up app's features and encourage downloads while providing a professional web presence for the API.
