<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Power Up - AI-Driven Personal Wellness Coach</title>
    <meta name="description" content="Transform your life with Power Up - the ultimate AI-driven wellness app featuring personalized coaching, habit tracking, daily podcasts, and community challenges.">
    
    <!-- Web App Manifest -->
    <link rel="manifest" href="/manifest.json">
    
    <!-- Favicons -->
    <link rel="icon" type="image/x-icon" href="/assets/images/logo.png">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f3f1ff',
                            100: '#ebe5ff',
                            200: '#d9ceff',
                            300: '#bea6ff',
                            400: '#9f75ff',
                            500: '#843dff',
                            600: '#7c25f7',
                            700: '#6f1de3',
                            800: '#5c18bf',
                            900: '#4d169c',
                        },
                        dark: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <style>
        * {
            scroll-behavior: smooth;
        }
        
        body {
            background: #0a0a0a;
            overflow-x: hidden;
        }
        
        /* Ensure hero section has dark background */
        #hero {
            background: #0a0a0a;
        }
        
        .section-height {
            min-height: 100vh;
        }
        
        .gradient-purple {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #a855f7 100%);
        }
        
        .gradient-text {
            background: linear-gradient(135deg, #a855f7, #ec4899, #f59e0b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        /* Navbar */
        .navbar-transparent {
            background: rgba(10, 10, 10, 0.8);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .navbar-scrolled {
            background: rgba(10, 10, 10, 0.95);
            backdrop-filter: blur(20px);
            box-shadow: 0 4px 20px rgba(168, 85, 247, 0.1);
        }
        
        /* Animations */
        .fade-in-up {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .fade-in-up.animate {
            opacity: 1;
            transform: translateY(0);
        }
        
        .slide-in-left {
            opacity: 0;
            transform: translateX(-50px);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .slide-in-left.animate {
            opacity: 1;
            transform: translateX(0);
        }
        
        .slide-in-right {
            opacity: 0;
            transform: translateX(50px);
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
        
        .slide-in-right.animate {
            opacity: 1;
            transform: translateX(0);
        }
        
        /* Feature Cards */
        .feature-card {
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(168, 85, 247, 0.2);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .feature-card:hover {
            transform: translateY(-10px) scale(1.02);
            border-color: rgba(168, 85, 247, 0.5);
            box-shadow: 0 25px 50px rgba(168, 85, 247, 0.2);
        }
        
        /* Glowing effects */
        .glow-purple {
            box-shadow: 0 0 30px rgba(168, 85, 247, 0.3);
        }
        
        .glow-purple:hover {
            box-shadow: 0 0 40px rgba(168, 85, 247, 0.5);
        }
        
        /* Animated background */
        .animated-bg {
            background: radial-gradient(circle at 20% 80%, rgba(168, 85, 247, 0.1) 0%, transparent 50%),
                       radial-gradient(circle at 80% 20%, rgba(236, 72, 153, 0.1) 0%, transparent 50%),
                       radial-gradient(circle at 40% 40%, rgba(245, 158, 11, 0.1) 0%, transparent 50%);
            animation: backgroundShift 20s ease-in-out infinite;
        }
        
        @keyframes backgroundShift {
            0%, 100% { 
                background: radial-gradient(circle at 20% 80%, rgba(168, 85, 247, 0.1) 0%, transparent 50%),
                           radial-gradient(circle at 80% 20%, rgba(236, 72, 153, 0.1) 0%, transparent 50%),
                           radial-gradient(circle at 40% 40%, rgba(245, 158, 11, 0.1) 0%, transparent 50%);
            }
            50% { 
                background: radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.15) 0%, transparent 50%),
                           radial-gradient(circle at 20% 80%, rgba(236, 72, 153, 0.15) 0%, transparent 50%),
                           radial-gradient(circle at 60% 60%, rgba(245, 158, 11, 0.15) 0%, transparent 50%);
            }
        }
        
        /* Counter animation */
        .counter {
            font-variant-numeric: tabular-nums;
        }
        
        /* SVG animations */
        .bounce-slow {
            animation: bounce 3s infinite;
        }
        
        .pulse-glow {
            animation: pulseGlow 2s ease-in-out infinite alternate;
        }
        
        @keyframes pulseGlow {
            from {
                filter: drop-shadow(0 0 10px rgba(168, 85, 247, 0.5));
            }
            to {
                filter: drop-shadow(0 0 20px rgba(168, 85, 247, 0.8));
            }
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #1e293b;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #a855f7;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #9333ea;
        }
        
        /* Mobile responsive */
        @media (max-width: 768px) {
            .section-height {
                min-height: 100vh;
                padding-top: 80px;
            }
        }
    </style>
</head>
<body class="bg-gray-900 text-white animated-bg">
    <!-- Transparent Navbar -->
    <nav class="navbar-transparent fixed top-0 w-full z-50" id="navbar">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <img src="/assets/images/logo.png" alt="Power Up Logo" class="w-10 h-10 pulse-glow rounded-lg">
                    <span class="text-2xl font-bold gradient-text">Power Up</span>
                </div>
                <div class="hidden md:flex space-x-8">
                    <a href="#hero" class="hover:text-purple-400 transition-colors duration-300">Home</a>
                    <a href="#features" class="hover:text-purple-400 transition-colors duration-300">Features</a>
                    <a href="#how-it-works" class="hover:text-purple-400 transition-colors duration-300">How It Works</a>
                    <a href="#download" class="hover:text-purple-400 transition-colors duration-300">Download</a>
                </div>
                <!-- Mobile menu button -->
                <button class="md:hidden text-white" id="mobile-menu-button">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" id="menu-icon">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                    <svg class="w-6 h-6 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24" id="close-icon">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Mobile menu -->
        <div class="md:hidden hidden bg-gray-900 bg-opacity-95 backdrop-filter backdrop-blur-lg border-t border-purple-500 border-opacity-20" id="mobile-menu">
            <div class="px-6 py-4 space-y-4">
                <a href="#hero" class="block text-white hover:text-purple-400 transition-colors duration-300 py-2">Home</a>
                <a href="#features" class="block text-white hover:text-purple-400 transition-colors duration-300 py-2">Features</a>
                <a href="#how-it-works" class="block text-white hover:text-purple-400 transition-colors duration-300 py-2">How It Works</a>
                <a href="#download" class="block text-white hover:text-purple-400 transition-colors duration-300 py-2">Download</a>
            </div>
        </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="hero" class="section-height flex items-center justify-center relative overflow-hidden">
        <!-- Animated background elements -->
        <div class="absolute inset-0">
            <div class="absolute top-20 left-10 w-20 h-20 bg-purple-500 rounded-full opacity-20 animate-pulse"></div>
            <div class="absolute top-40 right-20 w-32 h-32 bg-pink-500 rounded-full opacity-10 animate-bounce"></div>
            <div class="absolute bottom-20 left-20 w-16 h-16 bg-yellow-500 rounded-full opacity-15 bounce-slow"></div>
        </div>
        
        <div class="container mx-auto px-6 text-center relative z-10">
            <div class="fade-in-up">
                <h1 class="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                    Transform Your Life with 
                    <span class="gradient-text">AI-Powered</span> Wellness
                </h1>
                <p class="text-xl md:text-2xl mb-8 max-w-4xl mx-auto text-gray-300 leading-relaxed">
                    Experience personalized coaching, habit tracking, daily motivational podcasts, and community challenges - all powered by advanced AI technology.
                </p>
                <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                    <button class="group bg-gradient-to-r from-purple-600 to-pink-600 px-8 py-4 rounded-full font-bold text-lg hover:from-purple-500 hover:to-pink-500 transition-all duration-300 glow-purple transform hover:scale-105 flex items-center space-x-3">
                        <svg class="w-6 h-6 group-hover:animate-bounce" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                        </svg>
                        <span>Download for iOS</span>
                    </button>
                    <div class="text-gray-400 text-sm">
                        Free to download • Coming soon to App Store
                    </div>
                </div>
            </div>
            
            <!-- Floating phone mockup -->
            <div class="fade-in-up mt-16" style="animation-delay: 0.3s">
                <div class="relative max-w-sm mx-auto">
                    <div class="bg-gradient-to-r from-gray-800 to-gray-900 rounded-3xl p-3 shadow-2xl glow-purple">
                        <div class="bg-gray-900 rounded-2xl p-6 text-center">
                            <div class="w-full h-96 bg-gradient-to-b from-purple-900 to-gray-900 rounded-xl flex items-center justify-center">
                                <img src="/assets/images/logo.png" alt="Power Up Logo" class="w-20 h-20 pulse-glow rounded-xl">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Scroll indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <svg class="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
            </svg>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="section-height flex items-center bg-gray-900">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16 fade-in-up">
                <h2 class="text-4xl md:text-6xl font-bold mb-6">Everything You Need to <span class="gradient-text">Power Up</span></h2>
                <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                    Our comprehensive wellness platform combines cutting-edge AI with proven wellness techniques
                </p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- AI Coaching -->
                <div class="feature-card rounded-xl p-8 slide-in-left" style="animation-delay: 0.1s">
                    <div class="w-16 h-16 bg-purple-500 bg-opacity-20 rounded-full flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-purple-400" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-white">AI-Powered Coaching</h3>
                    <p class="text-gray-400">
                        Get personalized guidance and insights tailored to your unique goals, habits, and progress patterns.
                    </p>
                </div>

                <!-- Daily Podcasts -->
                <div class="feature-card rounded-xl p-8 slide-in-left" style="animation-delay: 0.2s">
                    <div class="w-16 h-16 bg-blue-500 bg-opacity-20 rounded-full flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-white">Daily Motivational Podcasts</h3>
                    <p class="text-gray-400">
                        Wake up to AI-generated podcasts personalized to your mood, goals, and current challenges.
                    </p>
                </div>

                <!-- Habit Tracking -->
                <div class="feature-card rounded-xl p-8 slide-in-left" style="animation-delay: 0.3s">
                    <div class="w-16 h-16 bg-green-500 bg-opacity-20 rounded-full flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-green-400" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 8V6z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-white">Smart Habit Tracking</h3>
                    <p class="text-gray-400">
                        Build lasting habits with intelligent tracking, streak monitoring, and adaptive reminders.
                    </p>
                </div>

                <!-- Task Management -->
                <div class="feature-card rounded-xl p-8 slide-in-right" style="animation-delay: 0.4s">
                    <div class="w-16 h-16 bg-orange-500 bg-opacity-20 rounded-full flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-orange-400" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-white">Intelligent Task Management</h3>
                    <p class="text-gray-400">
                        Organize your life with smart task prioritization and productivity insights.
                    </p>
                </div>

                <!-- Community Challenges -->
                <div class="feature-card rounded-xl p-8 slide-in-right" style="animation-delay: 0.5s">
                    <div class="w-16 h-16 bg-pink-500 bg-opacity-20 rounded-full flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-pink-400" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-white">Community Challenges</h3>
                    <p class="text-gray-400">
                        Join engaging challenges and connect with like-minded individuals on similar wellness journeys.
                    </p>
                </div>

                <!-- Skill Development -->
                <div class="feature-card rounded-xl p-8 slide-in-right" style="animation-delay: 0.6s">
                    <div class="w-16 h-16 bg-indigo-500 bg-opacity-20 rounded-full flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-indigo-400" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-white">Personalized Skill Plans</h3>
                    <p class="text-gray-400">
                        Access curated learning paths and track your progress in developing new skills.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section id="how-it-works" class="section-height flex items-center bg-gray-800">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16 fade-in-up">
                <h2 class="text-4xl md:text-6xl font-bold mb-6">How <span class="gradient-text">Power Up</span> Works</h2>
                <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                    Simple steps to transform your daily routine into a powerful wellness journey
                </p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-12">
                <div class="text-center fade-in-up" style="animation-delay: 0.1s">
                    <div class="w-24 h-24 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-6 glow-purple">
                        <span class="text-3xl text-white font-bold">1</span>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-white">Set Your Goals</h3>
                    <p class="text-gray-400">
                        Tell us about your wellness goals, current habits, and what you want to achieve.
                    </p>
                </div>
                
                <div class="text-center fade-in-up" style="animation-delay: 0.2s">
                    <div class="w-24 h-24 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 glow-purple">
                        <span class="text-3xl text-white font-bold">2</span>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-white">Get AI Insights</h3>
                    <p class="text-gray-400">
                        Our AI analyzes your patterns and creates personalized recommendations and content.
                    </p>
                </div>
                
                <div class="text-center fade-in-up" style="animation-delay: 0.3s">
                    <div class="w-24 h-24 bg-gradient-to-r from-green-600 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6 glow-purple">
                        <span class="text-3xl text-white font-bold">3</span>
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-white">Track & Improve</h3>
                    <p class="text-gray-400">
                        Monitor your progress, celebrate wins, and continuously improve with AI guidance.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="section-height flex items-center bg-gradient-to-r from-purple-900 via-gray-900 to-pink-900">
        <div class="container mx-auto px-6">
            <div class="grid md:grid-cols-4 gap-8 text-center">
                <div class="fade-in-up" style="animation-delay: 0.1s">
                    <div class="text-5xl font-bold mb-2 counter gradient-text" id="stat-users">0</div>
                    <div class="text-lg text-gray-300">Active Users</div>
                </div>
                <div class="fade-in-up" style="animation-delay: 0.2s">
                    <div class="text-5xl font-bold mb-2 counter gradient-text" id="stat-habits">0</div>
                    <div class="text-lg text-gray-300">Habits Tracked</div>
                </div>
                <div class="fade-in-up" style="animation-delay: 0.3s">
                    <div class="text-5xl font-bold mb-2 counter gradient-text" id="stat-podcasts">0</div>
                    <div class="text-lg text-gray-300">Podcasts Generated</div>
                </div>
                <div class="fade-in-up" style="animation-delay: 0.4s">
                    <div class="text-5xl font-bold mb-2 counter gradient-text" id="stat-satisfaction">0</div>
                    <div class="text-lg text-gray-300">User Satisfaction</div>
                </div>
            </div>
            
            <!-- API Status -->
            <div class="mt-12 text-center fade-in-up" style="animation-delay: 0.5s">
                <div class="inline-flex items-center bg-white bg-opacity-10 rounded-full px-6 py-3 backdrop-filter backdrop-blur-lg">
                    <div class="w-3 h-3 bg-green-400 rounded-full mr-3 animate-pulse" id="status-indicator"></div>
                    <span class="text-sm font-medium text-white" id="api-status">API Status: Checking...</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Download Section -->
    <section id="download" class="section-height flex items-center bg-gray-900">
        <div class="container mx-auto px-6 text-center">
            <div class="fade-in-up">
                <h2 class="text-4xl md:text-6xl font-bold mb-6">Ready to <span class="gradient-text">Power Up</span> Your Life?</h2>
                <p class="text-xl text-gray-400 mb-8 max-w-3xl mx-auto">
                    Join thousands of users who have transformed their wellness journey with AI-powered coaching.
                </p>
                
                <div class="flex flex-col sm:flex-row gap-6 justify-center items-center mb-8">
                    <button class="group bg-gradient-to-r from-purple-600 to-pink-600 px-12 py-5 rounded-full font-bold text-xl hover:from-purple-500 hover:to-pink-500 transition-all duration-300 glow-purple transform hover:scale-105 flex items-center space-x-3">
                        <svg class="w-8 h-8 group-hover:animate-bounce" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                        </svg>
                        <span>Download for iOS</span>
                    </button>
                </div>
                
                <p class="text-gray-500">Free to download • Coming soon to App Store</p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 border-t border-purple-500 border-opacity-20">
        <div class="container mx-auto px-6 py-12">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center mb-4">
                        <img src="/assets/images/logo.png" alt="Power Up Logo" class="w-8 h-8 mr-2 rounded-lg">
                        <span class="text-xl font-bold gradient-text">Power Up</span>
                    </div>
                    <p class="text-gray-400">
                        Transform your life with AI-driven wellness coaching.
                    </p>
                </div>
                
                <div>
                    <h4 class="font-bold mb-4 text-white">Features</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-purple-400 transition">AI Coaching</a></li>
                        <li><a href="#" class="hover:text-purple-400 transition">Habit Tracking</a></li>
                        <li><a href="#" class="hover:text-purple-400 transition">Daily Podcasts</a></li>
                        <li><a href="#" class="hover:text-purple-400 transition">Community</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-bold mb-4 text-white">Support</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="/help" class="hover:text-purple-400 transition">Help Center</a></li>
                        <li><a href="/help/contact" class="hover:text-purple-400 transition">Contact Us</a></li>
                        <li><a href="/help/privacy" class="hover:text-purple-400 transition">Privacy Policy</a></li>
                        <li><a href="/help/terms" class="hover:text-purple-400 transition">Terms of Service</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-bold mb-4 text-white">Connect</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-purple-400 transition">Twitter</a></li>
                        <li><a href="#" class="hover:text-purple-400 transition">Instagram</a></li>
                        <li><a href="#" class="hover:text-purple-400 transition">LinkedIn</a></li>
                        <li><a href="#" class="hover:text-purple-400 transition">Blog</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2025 Power Up. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript for animations and interactions -->
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 100) {
                navbar.classList.add('navbar-scrolled');
                navbar.classList.remove('navbar-transparent');
            } else {
                navbar.classList.add('navbar-transparent');
                navbar.classList.remove('navbar-scrolled');
            }
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate');
                }
            });
        }, observerOptions);

        // Observe all animation elements
        document.querySelectorAll('.fade-in-up, .slide-in-left, .slide-in-right').forEach(el => {
            observer.observe(el);
        });

        // Counter animation
        function animateCounter(element, target, duration = 2000) {
            const start = 0;
            const increment = target / (duration / 16);
            let current = start;
            
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                
                if (target >= 1000000) {
                    element.textContent = (current / 1000000).toFixed(1) + 'M+';
                } else if (target >= 1000) {
                    element.textContent = Math.floor(current / 1000) + 'K+';
                } else {
                    element.textContent = Math.floor(current) + '%';
                }
            }, 16);
        }

        // API Status Check
        async function checkApiStatus() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                
                const statusElement = document.getElementById('api-status');
                const indicatorElement = document.getElementById('status-indicator');
                
                if (data.status === 'ok') {
                    statusElement.textContent = 'API Status: Operational';
                    indicatorElement.classList.remove('bg-red-400', 'bg-yellow-400');
                    indicatorElement.classList.add('bg-green-400');
                } else {
                    throw new Error('API not healthy');
                }
            } catch (error) {
                const statusElement = document.getElementById('api-status');
                const indicatorElement = document.getElementById('status-indicator');
                
                statusElement.textContent = 'API Status: Offline';
                indicatorElement.classList.remove('bg-green-400', 'bg-yellow-400');
                indicatorElement.classList.add('bg-red-400');
            }
        }

        // Initialize when page loads
        window.addEventListener('load', function() {
            checkApiStatus();
            
            // Animate stats when they come into view
            const statsObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        animateCounter(document.getElementById('stat-users'), 10000);
                        animateCounter(document.getElementById('stat-habits'), 1000000);
                        animateCounter(document.getElementById('stat-podcasts'), 50000);
                        animateCounter(document.getElementById('stat-satisfaction'), 95);
                        statsObserver.unobserve(entry.target);
                    }
                });
            });
            
            const statsSection = document.querySelector('#stat-users').closest('section');
            if (statsSection) {
                statsObserver.observe(statsSection);
            }
        });

        // Refresh API status every 30 seconds
        setInterval(checkApiStatus, 30000);

        // Mobile menu toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            const menuIcon = document.getElementById('menu-icon');
            const closeIcon = document.getElementById('close-icon');
            
            if (mobileMenu.classList.contains('hidden')) {
                mobileMenu.classList.remove('hidden');
                menuIcon.classList.add('hidden');
                closeIcon.classList.remove('hidden');
            } else {
                mobileMenu.classList.add('hidden');
                menuIcon.classList.remove('hidden');
                closeIcon.classList.add('hidden');
            }
        });

        // Close mobile menu when clicking on a link
        document.querySelectorAll('#mobile-menu a').forEach(link => {
            link.addEventListener('click', function() {
                const mobileMenu = document.getElementById('mobile-menu');
                const menuIcon = document.getElementById('menu-icon');
                const closeIcon = document.getElementById('close-icon');
                
                mobileMenu.classList.add('hidden');
                menuIcon.classList.remove('hidden');
                closeIcon.classList.add('hidden');
            });
        });
    </script>
</body>
</html>
