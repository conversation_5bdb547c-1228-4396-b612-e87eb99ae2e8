import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import request from 'supertest';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { AppModule } from './../src/app.module';
import { testDbConfig } from './test-db';
// Import entities
import { User } from './../src/users/entities/user.entity';
import { Habit } from './../src/habits/entities/habit.entity';
import { Task } from './../src/tasks/entities/task.entity';
import { Challenge } from './../src/challenges/entities/challenge.entity';
import { Podcast } from './../src/podcasts/entities/podcast.entity';
import { SkillPlan } from './../src/skill-plans/entities/skill-plan.entity';
import { SkillPlanStep } from './../src/skill-plans/entities/skill-plan-step.entity';
import { UserProgress } from './../src/analytics/entities/user-progress.entity';

jest.setTimeout(30000); // Increase timeout for e2e tests

describe('Power Up API (e2e)', () => {
  let app: INestApplication;
  let jwtToken: string;
  let userId: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: 'test/.env.test',
          load: [() => ({
            jwt: {
              secret: 'test-secret-key',
              expiresIn: '1h',
            },
          })],
        }),
        TypeOrmModule.forRoot({
          ...testDbConfig,
          entities: [User, Habit, Task, Challenge, Podcast, SkillPlan, SkillPlanStep, UserProgress],
        }),
        AppModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.setGlobalPrefix('api');
    app.useGlobalPipes(new ValidationPipe());
    await app.init();
  });

  afterAll(async () => {
    if (app) {
      await app.close();
      // Give time for connections to close properly
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  });

  describe('App Health (e2e)', () => {
    it('should check API health status', () => {
      return request(app.getHttpServer())
        .get('/api/health')
        .expect(200)
        .expect({ status: 'ok' });
    });
  });

  describe('Auth (e2e)', () => {
    const testUser = {
      email: '<EMAIL>',
      password: 'Test123!',
      firstName: 'Test',
      lastName: 'User'
    };

    it('should register a new user', () => {
      return request(app.getHttpServer())
        .post('/api/users/register')
        .send(testUser)
        .expect(201)
        .expect(res => {
          expect(res.body).toHaveProperty('id');
          expect(res.body.email).toBe(testUser.email);
          expect(res.body).not.toHaveProperty('password');
          userId = res.body.id;
        });
    });

    it('should not register with existing email', () => {
      return request(app.getHttpServer())
        .post('/api/users/register')
        .send(testUser)
        .expect(409);
    });

    it('should login with email and password', () => {
      return request(app.getHttpServer())
        .post('/api/users/login')
        .send({
          email: testUser.email,
          password: testUser.password,
        })
        .expect(200)
        .expect(res => {
          expect(res.body).toHaveProperty('accessToken');
          jwtToken = res.body.accessToken;
        });
    });

    it('should not login with invalid credentials', () => {
      return request(app.getHttpServer())
        .post('/api/users/login')
        .send({
          email: testUser.email,
          password: 'wrongpassword',
        })
        .expect(401);
    });
  });

  describe('Users (e2e)', () => {
    it('should get user profile', () => {
      return request(app.getHttpServer())
        .get('/api/users/profile')
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(res.body.email).toBe('<EMAIL>');
          expect(res.body.firstName).toBe('Test');
          expect(res.body.lastName).toBe('User');
        });
    });

    it('should update user profile', () => {
      const updatedProfile = {
        firstName: 'Updated',
        lastName: 'Name'
      };

      return request(app.getHttpServer())
        .put('/api/users/profile')
        .set('Authorization', `Bearer ${jwtToken}`)
        .send(updatedProfile)
        .expect(200)
        .expect(res => {
          expect(res.body.firstName).toBe(updatedProfile.firstName);
          expect(res.body.lastName).toBe(updatedProfile.lastName);
        });
    });

    it('should get user by id', () => {
      return request(app.getHttpServer())
        .get(`/api/users/${userId}`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(res.body.id).toBe(userId);
          expect(res.body).not.toHaveProperty('password');
        });
    });
  });

  describe('Habits (e2e)', () => {
    let habitId: string;

    const testHabit = {
      name: 'Daily Exercise',
      description: 'Exercise for 30 minutes',
      schedule: ['Monday', 'Wednesday', 'Friday'],
      reminderSettings: {
        enabled: true,
        time: '09:00',
        days: ['Monday', 'Wednesday', 'Friday']
      }
    };

    it('should create a new habit', () => {
      return request(app.getHttpServer())
        .post('/api/habits')
        .set('Authorization', `Bearer ${jwtToken}`)
        .send(testHabit)
        .expect(201)
        .expect(res => {
          expect(res.body).toHaveProperty('id');
          habitId = res.body.id;
          expect(res.body.name).toBe(testHabit.name);
        });
    });

    it('should get all habits', () => {
      return request(app.getHttpServer())
        .get('/api/habits')
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(Array.isArray(res.body)).toBe(true);
          expect(res.body.length).toBeGreaterThan(0);
        });
    });

    it('should get a specific habit', () => {
      return request(app.getHttpServer())
        .get(`/api/habits/${habitId}`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(res.body.id).toBe(habitId);
          expect(res.body.name).toBe(testHabit.name);
        });
    });

    it('should update a habit', () => {
      const updatedHabit = {
        name: 'Updated Exercise Routine',
        description: 'Exercise for 45 minutes'
      };

      return request(app.getHttpServer())
        .put(`/api/habits/${habitId}`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .send(updatedHabit)
        .expect(200)
        .expect(res => {
          expect(res.body.name).toBe(updatedHabit.name);
          expect(res.body.description).toBe(updatedHabit.description);
        });
    });

    it('should mark habit as complete', () => {
      return request(app.getHttpServer())
        .post(`/api/habits/${habitId}/complete`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(res.body).toHaveProperty('lastCompletedAt');
        });
    });
  });

  describe('Tasks (e2e)', () => {
    let taskId: string;

    const testTask = {
      title: 'Complete Project',
      description: 'Finish the project documentation',
      dueDate: new Date(Date.now() + 86400000).toISOString(), // Tomorrow
      priority: 'high'
    };

    it('should create a new task', () => {
      return request(app.getHttpServer())
        .post('/api/tasks')
        .set('Authorization', `Bearer ${jwtToken}`)
        .send(testTask)
        .expect(201)
        .expect(res => {
          expect(res.body).toHaveProperty('id');
          taskId = res.body.id;
          expect(res.body.title).toBe(testTask.title);
        });
    });

    it('should get all tasks', () => {
      return request(app.getHttpServer())
        .get('/api/tasks')
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(Array.isArray(res.body)).toBe(true);
          expect(res.body.length).toBeGreaterThan(0);
        });
    });

    it('should get upcoming tasks', () => {
      return request(app.getHttpServer())
        .get('/api/tasks?filter=upcoming')
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(Array.isArray(res.body)).toBe(true);
        });
    });

    it('should get overdue tasks', () => {
      return request(app.getHttpServer())
        .get('/api/tasks?filter=overdue')
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(Array.isArray(res.body)).toBe(true);
        });
    });

    it('should get a specific task', () => {
      return request(app.getHttpServer())
        .get(`/api/tasks/${taskId}`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(res.body.id).toBe(taskId);
          expect(res.body.title).toBe(testTask.title);
        });
    });

    it('should update a task', () => {
      const updatedTask = {
        title: 'Updated Project Task',
        description: 'Updated project documentation'
      };

      return request(app.getHttpServer())
        .put(`/api/tasks/${taskId}`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .send(updatedTask)
        .expect(200)
        .expect(res => {
          expect(res.body.title).toBe(updatedTask.title);
          expect(res.body.description).toBe(updatedTask.description);
        });
    });

    it('should mark task as complete', () => {
      return request(app.getHttpServer())
        .post(`/api/tasks/${taskId}/complete`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(res.body.completed).toBe(true);
          expect(res.body).toHaveProperty('completedAt');
        });
    });

    it('should delete a task', () => {
      return request(app.getHttpServer())
        .delete(`/api/tasks/${taskId}`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200);
    });
  });

  describe('Challenges (e2e)', () => {
    let challengeId: string;

    const testChallenge = {
      name: 'Fitness Challenge',
      description: '30 days of daily exercise',
      startDate: new Date().toISOString(),
      endDate: new Date(Date.now() + 30 * 86400000).toISOString(), // 30 days from now
      type: 'fitness'
    };

    it('should create a new challenge', () => {
      return request(app.getHttpServer())
        .post('/api/challenges')
        .set('Authorization', `Bearer ${jwtToken}`)
        .send(testChallenge)
        .expect(201)
        .expect(res => {
          expect(res.body).toHaveProperty('id');
          challengeId = res.body.id;
          expect(res.body.name).toBe(testChallenge.name);
        });
    });

    it('should get all challenges', () => {
      return request(app.getHttpServer())
        .get('/api/challenges')
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(Array.isArray(res.body)).toBe(true);
          expect(res.body.length).toBeGreaterThan(0);
        });
    });

    it('should get active challenges', () => {
      return request(app.getHttpServer())
        .get('/api/challenges?filter=active')
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(Array.isArray(res.body)).toBe(true);
        });
    });

    it('should get a specific challenge', () => {
      return request(app.getHttpServer())
        .get(`/api/challenges/${challengeId}`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(res.body.id).toBe(challengeId);
          expect(res.body.name).toBe(testChallenge.name);
        });
    });

    it('should join a challenge', () => {
      return request(app.getHttpServer())
        .post(`/api/challenges/${challengeId}/join`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(res.body.joined).toBe(true);
        });
    });

    it('should update challenge progress', () => {
      return request(app.getHttpServer())
        .post(`/api/challenges/${challengeId}/progress`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .send({ progress: 50 })
        .expect(200)
        .expect(res => {
          expect(res.body.progress).toBe(50);
        });
    });

    it('should leave a challenge', () => {
      return request(app.getHttpServer())
        .post(`/api/challenges/${challengeId}/leave`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200);
    });
  });

  describe('Podcasts (e2e)', () => {
    let podcastId: string;

    const testPodcast = {
      topic: 'Productivity Tips',
      duration: 10,
      format: 'short'
    };

    it('should generate a new podcast', () => {
      return request(app.getHttpServer())
        .post('/api/podcasts/generate')
        .set('Authorization', `Bearer ${jwtToken}`)
        .send(testPodcast)
        .expect(201)
        .expect(res => {
          expect(res.body).toHaveProperty('id');
          podcastId = res.body.id;
          expect(res.body).toHaveProperty('audioUrl');
        });
    });

    it('should get daily podcast', () => {
      return request(app.getHttpServer())
        .get('/api/podcasts/daily')
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(res.body).toHaveProperty('audioUrl');
          expect(res.body).toHaveProperty('transcript');
        });
    });

    it('should get podcast history', () => {
      return request(app.getHttpServer())
        .get('/api/podcasts/history')
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(Array.isArray(res.body)).toBe(true);
        });
    });

    it('should mark podcast as listened', () => {
      return request(app.getHttpServer())
        .post(`/api/podcasts/${podcastId}/listened`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(res.body.listened).toBe(true);
        });
    });
  });

  describe('Skill Plans (e2e)', () => {
    let planId: string;
    let stepId: string;

    beforeAll(async () => {
      const res = await request(app.getHttpServer())
        .post('/api/skill-plans')
        .set('Authorization', `Bearer ${jwtToken}`)
        .send({
          name: 'Initial Plan',
          description: 'Test Plan',
          isPublic: true,
          metadata: {
            category: 'Test',
            difficulty: 'beginner',
            estimatedDuration: '1 week',
            tags: ['test']
          },
          steps: [{
            title: 'Step 1',
            description: 'First Step',
            order: 1,
            tasks: [{ 
              description: 'Task 1',
              isCompleted: false 
            }]
          }]
        });
      planId = res.body.id;
      stepId = res.body.steps[0].id;
    });

    const testPlan = {
      name: 'Learn TypeScript',
      description: 'Master TypeScript in 30 days',
      isPublic: true,
      metadata: {
        category: 'Programming',
        difficulty: 'intermediate',
        estimatedDuration: '4 weeks',
        tags: ['typescript', 'programming', 'web development']
      },
      steps: [
        {
          title: 'Basics',
          description: 'Learn TypeScript basics',
          order: 1,
          tasks: [
            { description: 'Setup development environment', isCompleted: false },
            { description: 'Learn basic types', isCompleted: false }
          ]
        },
        {
          title: 'Advanced Topics',
          description: 'Advanced TypeScript concepts',
          order: 2,
          tasks: [
            { description: 'Generics', isCompleted: false },
            { description: 'Decorators', isCompleted: false }
          ]
        }
      ]
    };

    it('should create a skill plan', () => {
      return request(app.getHttpServer())
        .post('/api/skill-plans')
        .set('Authorization', `Bearer ${jwtToken}`)
        .send({
          ...testPlan,
          creator: userId
        })
        .expect(201)
        .expect(res => {
          expect(res.body).toHaveProperty('id');
          planId = res.body.id;
          expect(res.body.name).toBe(testPlan.name);
          expect(Array.isArray(res.body.steps)).toBe(true);
          stepId = res.body.steps[0].id;
        });
    });

    it('should get all skill plans', () => {
      return request(app.getHttpServer())
        .get('/api/skill-plans')
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(Array.isArray(res.body)).toBe(true);
          expect(res.body.some(plan => plan.name === 'Initial Plan')).toBe(true);
        });
    });

    it('should get public skill plans', () => {
      return request(app.getHttpServer())
        .get('/api/skill-plans?isPublic=true')
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(Array.isArray(res.body)).toBe(true);
          const publicPlan = res.body.find(plan => plan.name === 'Initial Plan');
          expect(publicPlan).toBeDefined();
          expect(publicPlan.isPublic).toBe(true);
        });
    });

    it('should update skill plan step', async () => {
      const updatedStep = {
        title: 'TypeScript Fundamentals',
        description: 'Learn the core concepts of TypeScript',
        order: 1,
        tasks: [{ title: 'Updated Task', completed: false }]
      };

      const response = await request(app.getHttpServer())
        .put(`/api/skill-plans/${planId}/steps/${stepId}`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .send(updatedStep)
        .expect(200);

      expect(response.body.title).toBe(updatedStep.title);
    });

    it('should complete a step task', async () => {
      const taskIndex = 0;
      const response = await request(app.getHttpServer())
        .put(`/api/skill-plans/${planId}/steps/${stepId}/tasks/${taskIndex}/complete`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200);

      expect(response.body.tasks[taskIndex].isCompleted).toBe(true);
    });

    it('should update skill plan progress', async () => {
      const response = await request(app.getHttpServer())
        .post(`/api/skill-plans/${planId}/progress`)
        .set('Authorization', `Bearer ${jwtToken}`)
        .send({
          order: 1,  // Using step order
          completed: true
        })
        .expect(200);

      expect(response.body).toHaveProperty('progress');
      expect(response.body.progress).toBeGreaterThanOrEqual(0);
      expect(response.body.progress).toBeLessThanOrEqual(100);
    });
  });

  describe('Analytics (e2e)', () => {
    it('should get user progress overview', () => {
      return request(app.getHttpServer())
        .get('/api/analytics/progress')
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(res.body).toHaveProperty('habits');
          expect(res.body).toHaveProperty('tasks');
          expect(res.body).toHaveProperty('challenges');
          expect(res.body).toHaveProperty('skillPlans');
        });
    });

    it('should get habit analytics', () => {
      return request(app.getHttpServer())
        .get('/api/analytics/habits')
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(res.body).toHaveProperty('completionRate');
          expect(res.body).toHaveProperty('streaks');
          expect(res.body).toHaveProperty('totalCompleted');
        });
    });

    it('should get productivity analytics', () => {
      return request(app.getHttpServer())
        .get('/api/analytics/productivity')
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(res.body).toHaveProperty('focusTimeStats');
          expect(res.body).toHaveProperty('dailyBreakdown');
        });
    });

    it('should record focus session', () => {
      return request(app.getHttpServer())
        .post('/api/analytics/focus-session')
        .set('Authorization', `Bearer ${jwtToken}`)
        .send({ minutes: 30 })
        .expect(201);
    });

    it('should record mood entry', () => {
      return request(app.getHttpServer())
        .post('/api/analytics/mood')
        .set('Authorization', `Bearer ${jwtToken}`)
        .send({ 
          mood: 'great'  // Using valid mood enum value
        })
        .expect(201);
    });

    it('should get weekly stats', () => {
      return request(app.getHttpServer())
        .get('/api/analytics/weekly-stats')
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(res.body).toHaveProperty('habitCompletionRate');
          expect(res.body).toHaveProperty('avgDailyConsistency');
          expect(res.body).toHaveProperty('totalFocusTime');
        });
    });

    it('should get habit correlations', () => {
      return request(app.getHttpServer())
        .get('/api/analytics/habit-correlations')
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(res.body).toHaveProperty('correlations');
        });
    });

    it('should get streak milestones', () => {
      return request(app.getHttpServer())
        .get('/api/analytics/streak-milestones')
        .set('Authorization', `Bearer ${jwtToken}`)
        .expect(200)
        .expect(res => {
          expect(res.body).toHaveProperty('streakMilestones');
        });
    });
  });
});
