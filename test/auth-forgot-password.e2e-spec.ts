import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('Forget Password API (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/auth/forgot-password (POST)', () => {
    it('should return success message for valid email format', () => {
      return request(app.getHttpServer())
        .post('/auth/forgot-password')
        .send({ email: '<EMAIL>' })
        .expect(200)
        .expect((res) => {
          expect(res.body.message).toBe(
            'If an account with that email exists, a password reset link has been sent.'
          );
        });
    });

    it('should return validation error for invalid email', () => {
      return request(app.getHttpServer())
        .post('/auth/forgot-password')
        .send({ email: 'invalid-email' })
        .expect(400);
    });

    it('should return validation error for missing email', () => {
      return request(app.getHttpServer())
        .post('/auth/forgot-password')
        .send({})
        .expect(400);
    });
  });

  describe('/auth/reset-password (POST)', () => {
    it('should return validation error for missing token', () => {
      return request(app.getHttpServer())
        .post('/auth/reset-password')
        .send({ newPassword: 'newpassword123' })
        .expect(400);
    });

    it('should return validation error for weak password', () => {
      return request(app.getHttpServer())
        .post('/auth/reset-password')
        .send({ 
          token: 'some-token',
          newPassword: '123' // Too short
        })
        .expect(400);
    });

    it('should return error for invalid token format', () => {
      return request(app.getHttpServer())
        .post('/auth/reset-password')
        .send({ 
          token: 'invalid-token',
          newPassword: 'validPassword123'
        })
        .expect(400);
    });
  });

  describe('/auth/forgot-password-firebase (POST)', () => {
    it('should return success message for Firebase method', () => {
      return request(app.getHttpServer())
        .post('/auth/forgot-password-firebase')
        .send({ email: '<EMAIL>' })
        .expect(200)
        .expect((res) => {
          expect(res.body.message).toBe(
            'If an account with that email exists, a password reset link has been sent.'
          );
        });
    });
  });
});
