-- Manual SQL script to fix production schema issues
-- Run this if migrations continue to fail

DO $$
BEGIN
    -- Check if device_tokens table exists, if not create it
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'device_tokens') THEN
        CREATE TABLE device_tokens (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID NOT NULL,
            device_token VARCHAR NOT NULL,
            device_type VARCHAR NOT NULL,
            device_name VARCHAR,
            is_active BOOLEAN DEFAULT true,
            created_at TIMESTAMP DEFAULT now(),
            updated_at TIMESTAMP DEFAULT now(),
            last_used_at TIMESTAMP
        );
        
        -- Add foreign key constraint
        ALTER TABLE device_tokens 
        ADD CONSTRAINT FK_17e1f528b993c6d55def4cf5bea 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
    END IF;

    -- Check if notification_preferences table exists, if not create it
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notification_preferences') THEN
        CREATE TABLE notification_preferences (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID UNIQUE NOT NULL,
            task_reminders BOOLEAN DEFAULT true,
            habit_reminders BOOLEAN DEFAULT true,
            streak_alerts BOOLEAN DEFAULT true,
            milestone_celebrations BOOLEAN DEFAULT true,
            challenge_updates BOOLEAN DEFAULT true,
            new_messages BOOLEAN DEFAULT true,
            podcast_ready BOOLEAN DEFAULT true,
            created_at TIMESTAMP DEFAULT now(),
            updated_at TIMESTAMP DEFAULT now()
        );
        
        -- Add foreign key constraint
        ALTER TABLE notification_preferences 
        ADD CONSTRAINT FK_notification_preferences_user_id 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
    END IF;

    -- Add missing columns to users table if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'firebase_uid') THEN
        ALTER TABLE users ADD COLUMN firebase_uid VARCHAR;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'provider') THEN
        ALTER TABLE users ADD COLUMN provider VARCHAR DEFAULT 'local';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'picture') THEN
        ALTER TABLE users ADD COLUMN picture VARCHAR;
    END IF;

    -- Make password nullable if it's not already
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'password' AND is_nullable = 'NO') THEN
        ALTER TABLE users ALTER COLUMN password DROP NOT NULL;
    END IF;

    -- Handle enum types
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'users_provider_enum') THEN
        -- Rename existing enum if auth_provider doesn't exist
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'auth_provider') THEN
            ALTER TYPE users_provider_enum RENAME TO auth_provider;
        END IF;
    ELSIF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'auth_provider') THEN
        -- Create auth_provider enum
        CREATE TYPE auth_provider AS ENUM ('local', 'google', 'apple');
    END IF;

    -- Update column type if it's varchar
    IF EXISTS (SELECT 1 FROM information_schema.columns 
             WHERE table_name = 'users' 
             AND column_name = 'provider' 
             AND data_type = 'character varying') THEN
        ALTER TABLE users ALTER COLUMN provider TYPE auth_provider USING 
            CASE 
                WHEN provider = 'local' THEN 'local'::auth_provider
                WHEN provider = 'google' THEN 'google'::auth_provider
                WHEN provider = 'apple' THEN 'apple'::auth_provider
                ELSE 'local'::auth_provider
            END;
    END IF;

    -- Fix help_articles category NULL values before migration
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'help_articles') THEN
        UPDATE help_articles 
        SET category = 'getting-started' 
        WHERE category IS NULL OR category = '';
    END IF;

    -- Fix feedback type NULL values before migration
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'feedback') THEN
        UPDATE feedback 
        SET type = 'bug' 
        WHERE type IS NULL OR type = '';
    END IF;

    -- Fix admin_users NULL values before migration
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'admin_users') THEN
        UPDATE admin_users 
        SET email = '<EMAIL>' 
        WHERE email IS NULL OR email = '';
        
        UPDATE admin_users 
        SET "firstName" = 'Admin' 
        WHERE "firstName" IS NULL OR "firstName" = '';
        
        UPDATE admin_users 
        SET "lastName" = 'User' 
        WHERE "lastName" IS NULL OR "lastName" = '';
    END IF;

    -- Mark migration as completed (add to migrations table)
    INSERT INTO migrations (timestamp, name) 
    VALUES (1715498894265, 'AddNotificationsAndDeviceTokens1715498894265')
    ON CONFLICT DO NOTHING;

END
$$;
