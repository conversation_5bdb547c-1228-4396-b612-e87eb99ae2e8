#!/bin/bash

# Firebase Setup Script for Production Environments
# This script helps set up secure environment variables for Firebase
# instead of using the firebase-service-account.json file

# Color codes for better readability
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Firebase Production Setup Script${NC}"
echo "===================================="
echo
echo -e "${YELLOW}This script will help you securely set up Firebase for production environments${NC}"
echo "by extracting credentials from your firebase-service-account.json file"
echo "and setting them as environment variables."
echo

# Check if firebase-service-account.json exists
if [ ! -f "firebase-service-account.json" ]; then
  echo -e "${RED}Error: firebase-service-account.json not found!${NC}"
  echo "Please place the file in the project root directory."
  exit 1
fi

echo -e "${GREEN}Found firebase-service-account.json...${NC}"
echo

# Extract fields from the JSON file
PROJECT_ID=$(grep -o '"project_id": *"[^"]*"' firebase-service-account.json | cut -d'"' -f4)
PRIVATE_KEY_ID=$(grep -o '"private_key_id": *"[^"]*"' firebase-service-account.json | cut -d'"' -f4)
PRIVATE_KEY=$(grep -o '"private_key": *"[^"]*"' firebase-service-account.json | cut -d'"' -f4)
CLIENT_EMAIL=$(grep -o '"client_email": *"[^"]*"' firebase-service-account.json | cut -d'"' -f4)
CLIENT_ID=$(grep -o '"client_id": *"[^"]*"' firebase-service-account.json | cut -d'"' -f4)
AUTH_URI=$(grep -o '"auth_uri": *"[^"]*"' firebase-service-account.json | cut -d'"' -f4)
TOKEN_URI=$(grep -o '"token_uri": *"[^"]*"' firebase-service-account.json | cut -d'"' -f4)
AUTH_PROVIDER=$(grep -o '"auth_provider_x509_cert_url": *"[^"]*"' firebase-service-account.json | cut -d'"' -f4)
CLIENT_CERT=$(grep -o '"client_x509_cert_url": *"[^"]*"' firebase-service-account.json | cut -d'"' -f4)

# Create environment variables for .env file
echo "Creating environment variables..."
echo

cat << EOF > firebase.env
# Firebase Configuration
FIREBASE_PROJECT_ID=$PROJECT_ID
FIREBASE_PRIVATE_KEY_ID=$PRIVATE_KEY_ID
FIREBASE_PRIVATE_KEY="$PRIVATE_KEY"
FIREBASE_CLIENT_EMAIL=$CLIENT_EMAIL
FIREBASE_CLIENT_ID=$CLIENT_ID
FIREBASE_AUTH_URI=$AUTH_URI
FIREBASE_TOKEN_URI=$TOKEN_URI
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=$AUTH_PROVIDER
FIREBASE_CLIENT_X509_CERT_URL=$CLIENT_CERT
FIREBASE_DATABASE_URL=https://$PROJECT_ID.firebaseio.com
FIREBASE_STORAGE_BUCKET=$PROJECT_ID.appspot.com
EOF

echo -e "${GREEN}Created firebase.env file with environment variables!${NC}"
echo
echo -e "${YELLOW}IMPORTANT:${NC} For production deployment, add these variables to your environment"
echo "or inject them into your containers/VM instances."
echo
echo "You can source this file in development with: source firebase.env"
echo
echo -e "${RED}WARNING:${NC} firebase.env contains sensitive information."
echo "Do NOT commit it to your repository!"
echo "Add firebase.env to your .gitignore file."
echo

# Add to .gitignore if it exists
if [ -f ".gitignore" ]; then
  if ! grep -q "firebase.env" .gitignore; then
    echo "Adding firebase.env to .gitignore..."
    echo "firebase.env" >> .gitignore
    echo -e "${GREEN}Added firebase.env to .gitignore!${NC}"
  else
    echo -e "${GREEN}firebase.env already in .gitignore!${NC}"
  fi
else
  echo -e "${YELLOW}Warning: .gitignore not found.${NC}"
  echo "Please make sure to add firebase.env to your .gitignore."
fi

echo
echo -e "${GREEN}Setup completed!${NC}"
echo
