#!/bin/sh

# Load test environment variables
set -a
source test/.env.test
set +a

# Wait for PostgreSQL to be ready
echo "Waiting for PostgreSQL to be ready..."
until pg_isready -h localhost -U postgres; do
  echo "Waiting for PostgreSQL to start..."
  sleep 1
done

# Drop the database if it exists and create a new one
echo "Setting up test database..."
psql -h localhost -U postgres -c "DROP DATABASE IF EXISTS power_up_test;"
psql -h localhost -U postgres -c "CREATE DATABASE power_up_test;"

echo "Test database setup complete!"
