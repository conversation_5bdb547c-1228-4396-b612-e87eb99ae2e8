-- Quick fix: Mark migration as completed if reset_code columns already exist
-- Use this if you want to skip the migration entirely

DO $$
BEGIN
    -- Check if the reset_code column already exists (main change from this migration)
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'reset_code') THEN
        -- Mark migration as completed
        INSERT INTO migrations (timestamp, name) 
        VALUES (1749808858809, 'AddResetCodeToUsers1749808858809')
        ON CONFLICT DO NOTHING;
        
        RAISE NOTICE 'Migration AddResetCodeToUsers1749808858809 marked as completed';
    ELSE
        RAISE NOTICE 'Reset code column does not exist, migration should be run normally';
    END IF;
END
$$;
