-- Safe migration script to handle the help_articles category issue
-- Run this before running the migration

DO $$
BEGIN
    -- Update any NULL categories in help_articles to a default value
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'help_articles') THEN
        UPDATE help_articles 
        SET category = 'getting-started' 
        WHERE category IS NULL OR category = '';
    END IF;

    -- Update any NULL types in feedback to a default value
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'feedback') THEN
        UPDATE feedback 
        SET type = 'bug' 
        WHERE type IS NULL OR type = '';
    END IF;

    -- Update any NULL or empty values in admin_users to defaults
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'admin_users') THEN
        UPDATE admin_users 
        SET email = '<EMAIL>' 
        WHERE email IS NULL OR email = '';
        
        UPDATE admin_users 
        SET "firstName" = 'Admin' 
        WHERE "firstName" IS NULL OR "firstName" = '';
        
        UPDATE admin_users 
        SET "lastName" = 'User' 
        WHERE "lastName" IS NULL OR "lastName" = '';
    END IF;

    -- Mark this migration as completed if the changes were already applied
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'reset_code') THEN
        INSERT INTO migrations (timestamp, name) 
        VALUES (1749808858809, 'AddResetCodeToUsers1749808858809')
        ON CONFLICT DO NOTHING;
    END IF;
END
$$;
