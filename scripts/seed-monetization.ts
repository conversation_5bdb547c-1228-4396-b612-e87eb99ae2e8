import { DataSource } from 'typeorm';
import { SubscriptionPlan, SubscriptionPlanType, SubscriptionPlatform } from '../src/monetization/entities/subscription-plan.entity';
import { CouponCode, CouponType } from '../src/monetization/entities/coupon-code.entity';

export async function seedMonetizationData(dataSource: DataSource) {
  const subscriptionPlanRepository = dataSource.getRepository(SubscriptionPlan);
  const couponCodeRepository = dataSource.getRepository(CouponCode);

  // Clear existing data
  await subscriptionPlanRepository.delete({});
  await couponCodeRepository.delete({});

  // Create subscription plans
  const plans = [
    // iOS Plans
    {
      name: 'Power Up Weekly - iOS',
      description: 'Weekly subscription for iOS users with premium features',
      type: SubscriptionPlanType.WEEKLY,
      platform: SubscriptionPlatform.IOS,
      price: 4.99,
      currency: 'USD',
      productId: 'com.powerup.weekly',
      hasFreeTrialTier: true,
      freeTrialDays: 7,
      active: true,
      features: ['AI-powered habit tracking', 'Advanced analytics', 'Unlimited habits', 'Priority support'],
      sortOrder: 1,
    },
    {
      name: 'Power Up Monthly - iOS',
      description: 'Monthly subscription for iOS users with premium features',
      type: SubscriptionPlanType.MONTHLY,
      platform: SubscriptionPlatform.IOS,
      price: 14.99,
      currency: 'USD',
      productId: 'com.powerup.monthly',
      hasFreeTrialTier: true,
      freeTrialDays: 14,
      active: true,
      features: ['AI-powered habit tracking', 'Advanced analytics', 'Unlimited habits', 'Priority support', 'Custom themes'],
      sortOrder: 2,
    },
    {
      name: 'Power Up Yearly - iOS',
      description: 'Yearly subscription for iOS users with premium features (Best Value)',
      type: SubscriptionPlanType.YEARLY,
      platform: SubscriptionPlatform.IOS,
      price: 99.99,
      currency: 'USD',
      productId: 'com.powerup.yearly',
      hasFreeTrialTier: true,
      freeTrialDays: 30,
      active: true,
      features: ['AI-powered habit tracking', 'Advanced analytics', 'Unlimited habits', 'Priority support', 'Custom themes', 'Export data'],
      sortOrder: 3,
    },
    // Android Plans
    {
      name: 'Power Up Weekly - Android',
      description: 'Weekly subscription for Android users with premium features',
      type: SubscriptionPlanType.WEEKLY,
      platform: SubscriptionPlatform.ANDROID,
      price: 4.99,
      currency: 'USD',
      productId: 'com.powerup.weekly.android',
      hasFreeTrialTier: true,
      freeTrialDays: 7,
      active: true,
      features: ['AI-powered habit tracking', 'Advanced analytics', 'Unlimited habits', 'Priority support'],
      sortOrder: 4,
    },
    {
      name: 'Power Up Monthly - Android',
      description: 'Monthly subscription for Android users with premium features',
      type: SubscriptionPlanType.MONTHLY,
      platform: SubscriptionPlatform.ANDROID,
      price: 14.99,
      currency: 'USD',
      productId: 'com.powerup.monthly.android',
      hasFreeTrialTier: true,
      freeTrialDays: 14,
      active: true,
      features: ['AI-powered habit tracking', 'Advanced analytics', 'Unlimited habits', 'Priority support', 'Custom themes'],
      sortOrder: 5,
    },
    {
      name: 'Power Up Yearly - Android',
      description: 'Yearly subscription for Android users with premium features (Best Value)',
      type: SubscriptionPlanType.YEARLY,
      platform: SubscriptionPlatform.ANDROID,
      price: 99.99,
      currency: 'USD',
      productId: 'com.powerup.yearly.android',
      hasFreeTrialTier: true,
      freeTrialDays: 30,
      active: true,
      features: ['AI-powered habit tracking', 'Advanced analytics', 'Unlimited habits', 'Priority support', 'Custom themes', 'Export data'],
      sortOrder: 6,
    },
  ];

  for (const planData of plans) {
    const plan = subscriptionPlanRepository.create(planData);
    await subscriptionPlanRepository.save(plan);
  }

  // Create coupon codes
  const coupons = [
    {
      code: 'WELCOME20',
      name: 'Welcome Discount',
      description: '20% off for new users',
      type: CouponType.PERCENTAGE,
      value: 20,
      maxUses: 1000,
      validFrom: new Date('2025-01-01'),
      validUntil: new Date('2025-12-31'),
      firstTimeOnly: true,
      active: true,
    },
    {
      code: 'SAVE50',
      name: 'Save $5 on Monthly Plans',
      description: '$5 off monthly subscriptions',
      type: CouponType.FIXED_AMOUNT,
      value: 5.00,
      maxUses: 500,
      validFrom: new Date('2025-01-01'),
      validUntil: new Date('2025-06-30'),
      minimumPurchaseAmount: 10.00,
      active: true,
    },
    {
      code: 'FREETRIAL',
      name: 'Extended Free Trial',
      description: 'Get an extra 7 days of free trial',
      type: CouponType.FREE_TRIAL_EXTENSION,
      freeTrialDays: 7,
      maxUses: 100,
      validFrom: new Date('2025-01-01'),
      validUntil: new Date('2025-03-31'),
      firstTimeOnly: true,
      active: true,
    },
    {
      code: 'HOLIDAY50',
      name: 'Holiday Special',
      description: '50% off for holiday season',
      type: CouponType.PERCENTAGE,
      value: 50,
      maxUses: 200,
      validFrom: new Date('2025-12-15'),
      validUntil: new Date('2026-01-15'),
      active: true,
    },
  ];

  for (const couponData of coupons) {
    const coupon = couponCodeRepository.create(couponData);
    await couponCodeRepository.save(coupon);
  }

  console.log('✅ Monetization data seeded successfully');
  console.log(`📋 Created ${plans.length} subscription plans`);
  console.log(`🎫 Created ${coupons.length} coupon codes`);
}
