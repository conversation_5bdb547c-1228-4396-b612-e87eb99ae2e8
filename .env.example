# Server
PORT=3000
NODE_ENV=development
BASE_URL=https://powerup.apperx.com

# Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=
DATABASE_NAME=power_up

# JWT
JWT_SECRET=development-secret-key-change-in-production
JWT_EXPIRES_IN=1d

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# Google AI
GEMINI_API_KEY=AIzaSyClwqvsMbQ_MJD7IiebarKRXO0-cWdd3Zo

# Storage
FILE_UPLOADS_DIR=uploads/files

# Firebase
FIREBASE_SERVICE_ACCOUNT_PATH=firebase-service-account.json
FIREBASE_DATABASE_URL=https://powerup-e51d2.firebaseio.com
FIREBASE_PROJECT_ID=powerup-e51d2
FIREBASE_STORAGE_BUCKET=powerup-e51d2.appspot.com

# SMTP Email
SMTP_HOST=smtp.example.com
SMTP_PORT=587
EMAIL_SECURE=true
EMAIL_USER=
EMAIL_PASSWORD=
EMAIL_FROM=