{"name": "power-up-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "test:setup": "chmod +x ./scripts/test-setup.sh && ./scripts/test-setup.sh", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "firebase:init": "ts-node -r tsconfig-paths/register src/commands/cli.ts init-firebase", "migration:generate": "typeorm-ts-node-commonjs migration:generate -d src/data-source.ts", "migration:create": "typeorm-ts-node-commonjs migration:create", "migration:run": "typeorm-ts-node-commonjs migration:run -d src/data-source.ts", "migration:revert": "typeorm-ts-node-commonjs migration:revert -d src/data-source.ts", "migration:show": "typeorm-ts-node-commonjs migration:show -d src/data-source.ts", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:firebase": "jest --testMatch \"**/src/**/firebase*.spec.ts\""}, "dependencies": {"@google/genai": "^1.4.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/bull": "^11.0.2", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^11.1.1", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/platform-socket.io": "^11.1.1", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.1.1", "@types/express-session": "^1.18.2", "@types/multer": "^1.4.13", "@types/passport-local": "^1.0.38", "@types/pdfkit": "^0.13.9", "@types/wav": "^1.0.4", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "bull": "^4.16.5", "bullmq": "^5.52.2", "cache-manager-redis-yet": "^5.1.5", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "connect-redis": "^7.1.1", "dotenv": "^16.5.0", "express-handlebars": "^8.0.3", "express-session": "^1.18.1", "firebase-admin": "^13.4.0", "handlebars": "^4.7.8", "helmet": "^8.1.0", "ioredis": "^5.6.1", "moment": "^2.30.1", "nest-commander": "^3.17.0", "nodemailer": "^7.0.3", "openai": "^4.100.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pdfkit": "^0.17.1", "pg": "^8.16.0", "redis": "^5.0.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "typeorm": "^0.3.24", "uuid": "^11.1.0", "wav": "^1.0.2"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/moment": "^2.13.0", "@types/node": "^22.10.7", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/socket.io": "^3.0.2", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "better-sqlite3": "^11.10.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "sqlite3": "^5.1.7", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "engines": {"node": "22"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}