<p align="center">
  <img src="https://via.placeholder.com/200x200?text=Power+Up" width="200" alt="Power Up Logo" />
</p>

<h1 align="center">Power Up API</h1>

<p align="center">
  A wellness application API designed to enhance users' lives through AI-driven personalized coaching.
</p>

<p align="center">
  <a href="#about">About</a> •
  <a href="#features">Features</a> •
  <a href="#architecture">Architecture</a> •
  <a href="#api-endpoints">API Endpoints</a> •
  <a href="#installation">Installation</a> •
  <a href="#running-the-app">Running the App</a> •
  <a href="#firebase-integration">Firebase Integration</a> •
  <a href="#testing">Testing</a> •
  <a href="#webhooks">Webhooks</a>
</p>

## About

Power Up is a comprehensive wellness application that aims to provide a holistic wellness experience by integrating AI-driven coaching with various self-improvement tools. This repository contains the NestJS-based backend API that powers the Power Up mobile applications.

## Features

Power Up API provides backend support for the following features:

- **AI-Powered Personalization**
  - Daily personalized podcast generation
  - AI-driven progress reports
  - Content adaptation based on user mood and goals
  - Personalized coaching based on habits and tasks

- **Task and Habit Management**
  - Create and track tasks and habits
  - Progress metrics for completion
  - Reminder system
  - Smart calendar integration

- **Community Features**
  - Group challenges
  - Leaderboards for friendly competition
  - Community interaction support

- **Skill-Building Plans**
  - Pre-built skill-building plans
  - Custom plan creation
  - Progress tracking

- **Focus Timer**
  - Customizable focus sessions
  - Ambient sound support
  - AI voice prompt integration

- **Gamification**
  - Streaks for consistent habit completion
  - Badges for milestones
  - XP system for tracking progress
  - Redeemable rewards

## Architecture

The Power Up backend is built using the following technology stack:

- **Framework**: NestJS (Node.js)
- **Database**: PostgreSQL for persistent data
- **Cache & Real-time Database**: Redis
- **Authentication & Notifications**: Firebase
- **Message Queue**: BullMQ
- **AI Integration**: OpenAI, Google Cloud AI

### System Components

The backend follows a modular architecture with the following services:

- User Service
- Habit Service  
- Task Service
- Podcast Service
- Challenge Service
- Calendar Service  
- Notification Service
- AI Service

## API Endpoints

The API exposes the following key endpoints:

### Authentication

- `POST /auth/register` - Register a new user
- `POST /auth/login` - Authenticate a user
- `POST /auth/refresh` - Refresh authentication token

### User Management

- `GET /users/profile` - Get current user profile
- `PUT /users/profile` - Update user profile
- `GET /users/preferences` - Get user preferences

### Firebase Integration

For detailed information about Firebase integration (Authentication, Notifications, and Messaging), please see the [Firebase Integration section](#firebase-integration).
- `PUT /users/preferences` - Update user preferences

### Habits

- `POST /habits` - Create a new habit
- `GET /habits` - Get all habits for the current user
- `GET /habits/:id` - Get a specific habit
- `PUT /habits/:id` - Update a habit
- `DELETE /habits/:id` - Delete a habit
- `POST /habits/:id/complete` - Mark a habit as complete for today

### Tasks

- `POST /tasks` - Create a new task
- `GET /tasks` - Get all tasks for the current user
- `GET /tasks/:id` - Get a specific task
- `PUT /tasks/:id` - Update a task
- `DELETE /tasks/:id` - Delete a task
- `PUT /tasks/:id/status` - Update task status

### Podcasts

- `GET /podcasts/daily` - Get the daily personalized podcast
- `GET /podcasts/history` - Get podcast history
- `POST /podcasts/generate` - Manually trigger podcast generation

### Challenges

- `GET /challenges` - Get all available challenges
- `GET /challenges/:id` - Get challenge details
- `POST /challenges/:id/join` - Join a challenge
- `GET /challenges/my` - Get user's active challenges
- `GET /challenges/:id/leaderboard` - Get challenge leaderboard

### Skill Plans

- `GET /skill-plans` - Get available skill plans
- `GET /skill-plans/:id` - Get a specific skill plan details
- `POST /skill-plans/:id/enroll` - Enroll in a skill plan
- `GET /skill-plans/my` - Get enrolled skill plans
- `PUT /skill-plans/my/:id/progress` - Update progress in a skill plan

## Installation

### Prerequisites

- Node.js (v16.x or higher)
- PNPM package manager
- PostgreSQL
- Redis

### Setup Steps

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/power-up-api.git
   cd power-up-api
   ```

2. Install dependencies:
   ```bash
   pnpm install
   ```

3. Setup environment variables:
   ```bash
   cp example.env .env
   # Edit the .env file with your configuration
   ```

4. Setup the database:
   ```bash
   # Make sure PostgreSQL is running
   # Create a database named 'power_up'
   ```

5. Setup Redis (choose one option):
   
   **Option A: Using Docker (Recommended for development)**
   ```bash
   # Start Redis with Docker Compose
   docker-compose -f docker-compose.redis.yml up -d
   ```
   
   **Option B: Local Redis installation**
   ```bash
   # macOS (using Homebrew)
   brew install redis
   brew services start redis
   
   # Ubuntu/Debian
   sudo apt update && sudo apt install redis-server
   sudo systemctl start redis-server
   ```

6. Run database migrations:
   ```bash
   pnpm run migration:run
   ```

## Running the App

```bash
# Development mode
pnpm run start:dev

# Production mode
pnpm run start:prod

# Using Docker Compose (recommended for development)
docker-compose up
```

## Testing

```bash
# Unit tests
pnpm run test

# End-to-end tests
pnpm run test:e2e

# Test coverage reports
pnpm run test:cov

# Database tests (using Docker)
bash scripts/test-setup.sh
pnpm run test:e2e
```

## Webhooks

Power Up API supports integration with Firebase webhooks for real-time synchronization between Firebase services and your application database.

### Firebase Authentication Webhooks

The API provides endpoints to handle authentication events from Firebase, including:
- User creation
- User deletion

For detailed setup instructions and implementation details, see the [Firebase Webhooks Documentation](./docs/firebase-webhooks.md).

### Setting Up Webhooks

1. Create a Firebase Cloud Function that forwards Auth events to your API
2. Configure secure authentication between Firebase Functions and your API
3. Deploy the Cloud Function to your Firebase project

See the example implementation in the [Firebase Webhooks Documentation](./docs/firebase-webhooks.md).
