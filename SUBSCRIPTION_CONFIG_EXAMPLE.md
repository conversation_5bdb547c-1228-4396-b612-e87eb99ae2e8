# Subscription Configuration Template
# Add these environment variables to your .env file

# App Store Configuration
APP_STORE_SHARED_SECRET=your_app_store_shared_secret_here
APP_STORE_ISSUER_ID=your_app_store_issuer_id_here
APP_STORE_KEY_ID=your_app_store_key_id_here
APP_STORE_PRIVATE_KEY_PATH=path/to/your/app_store_private_key.p8

# Google Play Configuration
GOOGLE_PLAY_SERVICE_ACCOUNT_KEY=path/to/your/google_play_service_account.json
GOOGLE_PLAY_PACKAGE_NAME=com.yourapp.package
GOOGLE_PLAY_APPLICATION_NAME=YourAppName

# Webhook Security
APP_STORE_WEBHOOK_SECRET=your_app_store_webhook_secret
GOOGLE_PLAY_WEBHOOK_SECRET=your_google_play_webhook_secret

# Notification Settings
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_SMS_NOTIFICATIONS=false

# Email Configuration (if using @nestjs-modules/mailer)
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your_app_password
MAIL_FROM=<EMAIL>

# Push Notification Configuration (Firebase)
FIREBASE_ADMIN_SDK_PATH=path/to/your/firebase-admin-sdk.json
FIREBASE_PROJECT_ID=your_firebase_project_id

# SMS Configuration (optional)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Analytics and Monitoring
ENABLE_ANALYTICS_REPORTING=true
ANALYTICS_WEBHOOK_URL=https://your-analytics-service.com/webhook

# Database Settings (for subscription-specific configs)
SUBSCRIPTION_GRACE_PERIOD_DAYS=3
SUBSCRIPTION_RETRY_ATTEMPTS=3
SUBSCRIPTION_NOTIFICATION_DAYS_BEFORE_EXPIRY=7,3,1

# Cache Settings
SUBSCRIPTION_CACHE_TTL=300
SUBSCRIPTION_CACHE_PREFIX=sub:

# Rate Limiting
WEBHOOK_RATE_LIMIT_WINDOW=60000
WEBHOOK_RATE_LIMIT_MAX=100

# Security
WEBHOOK_IP_WHITELIST=127.0.0.1,::1
ENABLE_WEBHOOK_SIGNATURE_VERIFICATION=true

# Feature Flags
ENABLE_AUTOMATIC_RENEWAL_REMINDERS=true
ENABLE_SUBSCRIPTION_ANALYTICS=true
ENABLE_COUPON_SYSTEM=true
ENABLE_FREE_TRIAL_EXTENSIONS=false

# Development/Testing
SUBSCRIPTION_TEST_MODE=false
MOCK_PAYMENT_PROVIDERS=false
ENABLE_SUBSCRIPTION_LOGS=true
