# Firebase Integration for Power Up API

This document explains how Firebase has been integrated into the Power Up API for authentication, notifications, and in-app messaging.

## Features

- **Social Authentication**: Implemented Google and Apple authentication using Firebase
- **Push Notifications**: Integrated Firebase Cloud Messaging for sending push notifications
- **In-App Messaging**: Used Firebase Firestore for real-time messaging

## Setup Instructions

### 1. Configure the Firebase Service Account

The Firebase service account is already set up in the file `firebase-service-account.json`. This file should be kept secure and not committed to public repositories.

### 2. Environment Configuration

Add the following variables to your `.env` file:

```
# Firebase
FIREBASE_SERVICE_ACCOUNT_PATH=firebase-service-account.json
FIREBASE_DATABASE_URL=https://powerup-e51d2.firebaseio.com
FIREBASE_PROJECT_ID=powerup-e51d2
```

### 3. Database Migrations

Run database migrations to create the necessary tables for device tokens and notification preferences:

```bash
npm run migration:run
```

## Usage

### Authentication

#### Social Login (Google/Apple)

```typescript
// Frontend code (React Native example with Firebase)
import auth from '@react-native-firebase/auth';
import { GoogleSignin } from '@react-native-google-signin/google-signin';

// For Google Sign-In
async function onGoogleButtonPress() {
  // Get the user ID token
  const { idToken } = await GoogleSignin.signIn();

  // Send to backend
  const response = await fetch('https://api.power-up.com/users/social-login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ idToken }),
  });
  
  return response.json();
}
```

### Push Notifications

#### Sending Notifications

```typescript
// From another service (e.g., podcast service)
import { NotificationsService } from '../notifications/notifications.service';

@Injectable()
export class PodcastsService {
  constructor(private notificationsService: NotificationsService) {}
  
  async notifyPodcastReady(userId: string, podcastTitle: string) {
    await this.notificationsService.sendNotificationWithPreferences(
      userId,
      'podcast-ready',
      {
        title: 'New Podcast Ready',
        body: `Your daily personalized podcast "${podcastTitle}" is ready to listen!`,
        type: 'podcast-ready',
        data: {
          podcastTitle,
        },
      }
    );
  }
}
```

### In-App Messaging

#### Sending Direct Messages

```typescript
// From a controller
import { MessagingService } from '../messaging/messaging.service';

@Injectable()
export class SomeService {
  constructor(private messagingService: MessagingService) {}
  
  async sendMessage(senderId: string, recipientId: string, content: string) {
    return this.messagingService.sendDirectMessage(
      senderId,
      recipientId,
      content
    );
  }
}
```

## Client Integration

The mobile client should:

1. Integrate Firebase SDK
2. Set up Push Notification handling
3. Register device tokens with the backend
4. Implement social authentication flows

## Security Considerations

- Firebase service account keys are sensitive and should be protected
- Environment variables should be properly secured in production
- User authentication tokens should be validated on every request
- Device tokens should be verified and managed securely
