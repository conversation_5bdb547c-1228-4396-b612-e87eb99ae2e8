# Firebase Integration with Power-Up API - Mobile Client Guide

This document provides guidance and code examples for integrating the Power-Up API's Firebase features with your mobile application. The examples are primarily in Dart/Flutter but the concepts apply to any mobile platform.

## Table of Contents

1. [Setup](#setup)
2. [Authentication](#authentication)
3. [Push Notifications](#push-notifications)
4. [In-App Messaging](#in-app-messaging)
5. [Troubleshooting](#troubleshooting)

## Setup

### Flutter Project Configuration

1. Add the Firebase dependencies to your `pubspec.yaml`:

```yaml
dependencies:
  flutter:
    sdk: flutter
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  firebase_messaging: ^14.7.10
  http: ^1.1.2
  shared_preferences: ^2.2.2
```

2. Set up platform-specific configurations:

#### For Android:
- Download `google-services.json` from your Firebase console
- Place it in the `android/app/` directory

#### For iOS:
- Download `GoogleService-Info.plist` from your Firebase console
- Place it in the `ios/Runner/` directory
- Update your `ios/Podfile` to include Firebase pods

### Initialize Firebase

```dart
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  
  // Set up message handling for FCM
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  
  runApp(MyApp());
}

// Handle background messages
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  print("Handling a background message: ${message.messageId}");
}
```

## Authentication

### Firebase Auth Service

Create a service to handle Firebase authentication and API integration:

```dart
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class AuthService {
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  final String _apiBaseUrl = 'https://your-power-up-api.com/api';
  
  // Store the app token from our backend
  String? _appToken;
  String? get appToken => _appToken;
  
  // Check if user is authenticated
  bool get isAuthenticated => _firebaseAuth.currentUser != null;
  
  // Sign in with Google
  Future<UserCredential> signInWithGoogle() async {
    // Begin Google sign in process
    final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
    
    if (googleUser == null) {
      throw Exception('Google sign in aborted');
    }
    
    // Get authentication details
    final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
    
    // Create credential
    final credential = GoogleAuthProvider.credential(
      accessToken: googleAuth.accessToken,
      idToken: googleAuth.idToken,
    );
    
    // Sign in to Firebase
    final UserCredential userCredential = await _firebaseAuth.signInWithCredential(credential);
    
    // Get Firebase ID token
    final String? idToken = await userCredential.user?.getIdToken();
    
    if (idToken == null) {
      throw Exception('Failed to get Firebase ID token');
    }
    
    // Authenticate with our backend
    await authenticateWithBackend(idToken);
    
    return userCredential;
  }
  
  // Authenticate with our backend API
  Future<void> authenticateWithBackend(String firebaseToken) async {
    try {
      final response = await http.post(
        Uri.parse('$_apiBaseUrl/auth/social-login'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'firebaseToken': firebaseToken,
          'provider': 'google',
        }),
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _appToken = data['token'];
        
        // Store token securely - consider using flutter_secure_storage
      } else {
        throw Exception('Failed to authenticate with backend: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error during backend authentication: $e');
    }
  }
  
  // Sign out
  Future<void> signOut() async {
    _appToken = null;
    await _googleSignIn.signOut();
    await _firebaseAuth.signOut();
  }
}
```

### Usage in App

```dart
final authService = AuthService();

// Sign in button
ElevatedButton(
  onPressed: () async {
    try {
      await authService.signInWithGoogle();
      // Navigate to home screen
    } catch (e) {
      // Show error
    }
  },
  child: Text('Sign in with Google'),
)
```

## Push Notifications

### Notification Service

Create a service to handle device registration and notifications:

```dart
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'dart:io' show Platform;

class NotificationService {
  final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  final String _apiBaseUrl = 'https://your-power-up-api.com/api';
  final AuthService _authService;
  
  // Local notification setup
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin = 
      FlutterLocalNotificationsPlugin();
  
  NotificationService(this._authService);
  
  // Initialize notifications
  Future<void> initialize() async {
    // Request permission
    NotificationSettings settings = await _messaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );
    
    print('User notification permission: ${settings.authorizationStatus}');
    
    // Initialize local notifications
    final AndroidInitializationSettings androidInitSettings = 
        AndroidInitializationSettings('@drawable/app_icon');
        
    final DarwinInitializationSettings iosInitSettings = 
        DarwinInitializationSettings(
          requestAlertPermission: false,
          requestBadgePermission: false,
          requestSoundPermission: false,
        );
        
    final InitializationSettings initSettings = InitializationSettings(
      android: androidInitSettings,
      iOS: iosInitSettings,
    );
    
    await _flutterLocalNotificationsPlugin.initialize(
      initSettings,
      onDidReceiveNotificationResponse: (NotificationResponse response) {
        // Handle notification tap
        _handleNotificationTap(response);
      },
    );
    
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      _handleForegroundMessage(message);
    });
    
    // Handle notifications opened from terminated state
    FirebaseMessaging.instance.getInitialMessage().then((RemoteMessage? message) {
      if (message != null) {
        _handleNotificationTap(
          NotificationResponse(
            notificationResponseType: NotificationResponseType.selectedNotification,
            payload: json.encode(message.data),
          ),
        );
      }
    });
    
    // Register device token with backend
    await _registerDeviceToken();
  }
  
  // Register device token with backend
  Future<void> _registerDeviceToken() async {
    if (!_authService.isAuthenticated) return;
    
    final token = await _messaging.getToken();
    if (token == null) return;
    
    try {
      final response = await http.post(
        Uri.parse('$_apiBaseUrl/notifications/register-device'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${_authService.appToken}',
        },
        body: jsonEncode({
          'token': token,
          'deviceType': Platform.isIOS ? 'ios' : 'android',
          'deviceName': Platform.isIOS ? 'iOS Device' : 'Android Device',
        }),
      );
      
      if (response.statusCode == 201) {
        print('Device token registered successfully');
      } else {
        print('Failed to register device token: ${response.statusCode}');
      }
    } catch (e) {
      print('Error registering device token: $e');
    }
  }
  
  // Update notification preferences
  Future<void> updatePreferences({
    bool? taskReminders,
    bool? habitReminders,
    bool? streakAlerts,
    bool? milestoneCelebrations,
    bool? challengeInvitations,
    bool? challengeUpdates,
    bool? newMessages,
    bool? podcastReady,
  }) async {
    if (!_authService.isAuthenticated) return;
    
    try {
      final response = await http.patch(
        Uri.parse('$_apiBaseUrl/notifications/preferences'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${_authService.appToken}',
        },
        body: jsonEncode({
          if (taskReminders != null) 'taskReminders': taskReminders,
          if (habitReminders != null) 'habitReminders': habitReminders,
          if (streakAlerts != null) 'streakAlerts': streakAlerts,
          if (milestoneCelebrations != null) 'milestoneCelebrations': milestoneCelebrations,
          if (challengeInvitations != null) 'challengeInvitations': challengeInvitations,
          if (challengeUpdates != null) 'challengeUpdates': challengeUpdates,
          if (newMessages != null) 'newMessages': newMessages,
          if (podcastReady != null) 'podcastReady': podcastReady,
        }),
      );
      
      if (response.statusCode == 200) {
        print('Notification preferences updated');
      } else {
        print('Failed to update notification preferences: ${response.statusCode}');
      }
    } catch (e) {
      print('Error updating notification preferences: $e');
    }
  }
  
  // Handle foreground messages
  void _handleForegroundMessage(RemoteMessage message) {
    print('Foreground message received: ${message.messageId}');
    
    RemoteNotification? notification = message.notification;
    AndroidNotification? android = message.notification?.android;
    
    if (notification != null) {
      _flutterLocalNotificationsPlugin.show(
        notification.hashCode,
        notification.title,
        notification.body,
        NotificationDetails(
          android: AndroidNotificationDetails(
            'power_up_channel',
            'Power Up Notifications',
            channelDescription: 'Notifications from Power Up app',
            importance: Importance.max,
            priority: Priority.high,
            icon: android?.smallIcon,
          ),
          iOS: const DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
        payload: json.encode(message.data),
      );
    }
  }
  
  // Handle notification tap
  void _handleNotificationTap(NotificationResponse response) {
    if (response.payload == null) return;
    
    try {
      final Map<String, dynamic> data = json.decode(response.payload!);
      final String? type = data['type'];
      
      // Navigate based on notification type
      if (type == 'new-message') {
        // Navigate to message screen
        // Example: Navigator.pushNamed(context, '/messages', arguments: data);
      } else if (type == 'challenge-invitation') {
        // Navigate to challenge screen
        // Example: Navigator.pushNamed(context, '/challenges', arguments: data);
      }
      // Add more notification type handlers as needed
      
    } catch (e) {
      print('Error handling notification tap: $e');
    }
  }
}
```

### Setting Up Notification Preferences UI

```dart
class NotificationPreferencesScreen extends StatefulWidget {
  @override
  _NotificationPreferencesScreenState createState() => _NotificationPreferencesScreenState();
}

class _NotificationPreferencesScreenState extends State<NotificationPreferencesScreen> {
  final NotificationService _notificationService;
  
  bool _taskReminders = true;
  bool _habitReminders = true;
  bool _streakAlerts = true;
  bool _milestoneCelebrations = true;
  bool _challengeInvitations = true;
  bool _challengeUpdates = true;
  bool _newMessages = true;
  bool _podcastReady = true;
  
  _NotificationPreferencesScreenState(this._notificationService);
  
  // Fetch current preferences from API
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Notification Preferences')),
      body: ListView(
        children: [
          SwitchListTile(
            title: Text('Task Reminders'),
            value: _taskReminders,
            onChanged: (value) {
              setState(() {
                _taskReminders = value;
              });
              _notificationService.updatePreferences(taskReminders: value);
            },
          ),
          SwitchListTile(
            title: Text('Habit Reminders'),
            value: _habitReminders,
            onChanged: (value) {
              setState(() {
                _habitReminders = value;
              });
              _notificationService.updatePreferences(habitReminders: value);
            },
          ),
          // Add more preference switches here
        ],
      ),
    );
  }
}
```

## In-App Messaging

### Messaging Service

Create a service to handle in-app messaging:

```dart
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:firebase_database/firebase_database.dart';

class MessagingService {
  final String _apiBaseUrl = 'https://your-power-up-api.com/api';
  final AuthService _authService;
  final DatabaseReference _messagesRef = FirebaseDatabase.instance.ref('messages');
  
  MessagingService(this._authService);
  
  // Send a direct message
  Future<void> sendDirectMessage(String recipientId, String content) async {
    if (!_authService.isAuthenticated) throw Exception('User not authenticated');
    
    try {
      final response = await http.post(
        Uri.parse('$_apiBaseUrl/messaging/direct'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${_authService.appToken}',
        },
        body: jsonEncode({
          'recipientId': recipientId,
          'content': content,
        }),
      );
      
      if (response.statusCode != 201) {
        throw Exception('Failed to send message: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error sending direct message: $e');
    }
  }
  
  // Send a group message
  Future<void> sendGroupMessage(String groupId, String content) async {
    if (!_authService.isAuthenticated) throw Exception('User not authenticated');
    
    try {
      final response = await http.post(
        Uri.parse('$_apiBaseUrl/messaging/group'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${_authService.appToken}',
        },
        body: jsonEncode({
          'groupId': groupId,
          'content': content,
        }),
      );
      
      if (response.statusCode != 201) {
        throw Exception('Failed to send group message: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error sending group message: $e');
    }
  }
  
  // Get user conversations
  Future<List<dynamic>> getConversations() async {
    if (!_authService.isAuthenticated) throw Exception('User not authenticated');
    
    try {
      final response = await http.get(
        Uri.parse('$_apiBaseUrl/messaging/conversations'),
        headers: {
          'Authorization': 'Bearer ${_authService.appToken}',
        },
      );
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to load conversations: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error getting conversations: $e');
    }
  }
  
  // Get messages for a specific conversation
  Future<List<dynamic>> getMessages(String conversationId) async {
    if (!_authService.isAuthenticated) throw Exception('User not authenticated');
    
    try {
      final response = await http.get(
        Uri.parse('$_apiBaseUrl/messaging/conversations/$conversationId/messages'),
        headers: {
          'Authorization': 'Bearer ${_authService.appToken}',
        },
      );
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to load messages: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error getting messages: $e');
    }
  }
  
  // Listen for new messages in real-time
  Stream<DatabaseEvent> listenForMessages(String conversationId) {
    return _messagesRef.child(conversationId).onChildAdded;
  }
}
```

### Chat UI Example

```dart
class ChatScreen extends StatefulWidget {
  final String conversationId;
  final String recipientName;
  
  ChatScreen({required this.conversationId, required this.recipientName});
  
  @override
  _ChatScreenState createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final MessagingService _messagingService;
  final TextEditingController _messageController = TextEditingController();
  List<dynamic> _messages = [];
  
  _ChatScreenState(this._messagingService);
  
  @override
  void initState() {
    super.initState();
    _loadMessages();
    _listenForNewMessages();
  }
  
  Future<void> _loadMessages() async {
    try {
      final messages = await _messagingService.getMessages(widget.conversationId);
      setState(() {
        _messages = messages;
      });
    } catch (e) {
      // Handle error
    }
  }
  
  void _listenForNewMessages() {
    _messagingService
      .listenForMessages(widget.conversationId)
      .listen((event) {
        final newMessage = event.snapshot.value as Map<dynamic, dynamic>;
        setState(() {
          _messages.add(newMessage);
        });
      });
  }
  
  void _sendMessage() {
    if (_messageController.text.trim().isEmpty) return;
    
    _messagingService.sendDirectMessage(
      widget.conversationId,
      _messageController.text.trim(),
    );
    
    _messageController.clear();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.recipientName)),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                final message = _messages[index];
                final bool isMine = message['senderId'] == 'current-user-id';
                
                return Align(
                  alignment: isMine ? Alignment.centerRight : Alignment.centerLeft,
                  child: Container(
                    margin: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                    padding: EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: isMine ? Colors.blue : Colors.grey[300],
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Text(
                      message['content'],
                      style: TextStyle(
                        color: isMine ? Colors.white : Colors.black,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: InputDecoration(
                      hintText: 'Type a message',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                    ),
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.send),
                  onPressed: _sendMessage,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
```

## Troubleshooting

### Common Issues

1. **Firebase Auth token validation fails**
   - Ensure your app and server are using the same Firebase project
   - Check that the clock on your device is accurate
   - Verify your backend has the correct Firebase service account

2. **Push notifications not received**
   - For Android, ensure battery optimization is disabled for your app
   - For iOS, check notification permissions
   - Verify the device token is correctly registered with the backend

3. **Missing notifications while app is in background**
   - Make sure you've implemented background message handler
   - Verify you're using high priority for important notifications

4. **Social login issues**
   - Enable the authentication providers in Firebase Console
   - For Google Sign-In, ensure SHA-1 fingerprint is added to Firebase project
   - For Apple Sign-In, verify the Apple Developer credentials

### Debugging Tips

1. Add Firebase Analytics to track issues:

```dart
import 'package:firebase_analytics/firebase_analytics.dart';

final FirebaseAnalytics analytics = FirebaseAnalytics.instance;

// Track events
analytics.logEvent(
  name: 'notification_received',
  parameters: {'type': 'challenge_invitation'},
);
```

2. Use Firebase Crashlytics for error reporting:

```dart
import 'package:firebase_crashlytics/firebase_crashlytics.dart';

// Record errors
FirebaseCrashlytics.instance.recordError(
  error,
  stackTrace,
  reason: 'Push notification processing error',
);
```

3. Check the Firebase Cloud Messaging logs in the Firebase Console
