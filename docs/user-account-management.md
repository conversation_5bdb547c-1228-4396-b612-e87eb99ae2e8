# User Account Management - New Endpoints

This document describes the new endpoints added to the users module for account deletion (soft delete) and data export functionality.

## Features Added

### 1. Soft Delete Account
- **Endpoint**: `DELETE /users/account`
- **Authentication**: Required (JWT)
- **Purpose**: Allows users to delete their account (soft delete)

### 2. Export User Data
- **Endpoint**: `GET /users/data/export`
- **Authentication**: Required (JWT)
- **Purpose**: Exports all user data for GDPR compliance

### 3. Export User Data as PDF
- **Endpoint**: `GET /users/data/export/pdf`
- **Authentication**: Required (JWT)
- **Purpose**: Exports all user data as a formatted PDF document

## Database Changes

### User Entity Updates
- Added `deletedAt` field using TypeORM's `@DeleteDateColumn()`
- Updated all user queries to exclude soft-deleted users by default

### Export User Data as PDF

```http
GET /users/data/export/pdf
Authorization: Bearer <jwt_token>
```

**Response:**
- Content-Type: `application/pdf`
- Content-Disposition: `attachment; filename="user-data-export.pdf"`
- Binary PDF file containing formatted user data

**Notes:**
- Returns a downloadable PDF file with all user data
- Includes profile information, habits, and tasks in a formatted document
- Suitable for offline viewing and record-keeping
- GDPR compliant data export format

## API Endpoints

### Delete Account

```http
DELETE /users/account
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "password": "user_password",
  "reason": "Optional reason for deletion"
}
```

**Response:**
```json
{
  "message": "Account successfully deleted"
}
```

**Notes:**
- For local users (email/password), password verification is required
- For social login users (Google, Apple), password verification is skipped
- The account is soft-deleted, not permanently removed

### Export User Data

```http
GET /users/data/export
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "profile": {
    "id": "user_id",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "xp": 100,
    "badges": ["early_adopter"],
    "provider": "local",
    "picture": "profile_url",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-02T00:00:00.000Z"
  },
  "habits": [
    {
      "id": "habit_id",
      "name": "Exercise",
      "description": "Daily workout",
      "schedule": ["monday", "wednesday", "friday"],
      "currentStreak": 5,
      "longestStreak": 10,
      "completion": {
        "2024-01-01": true,
        "2024-01-02": false
      },
      "reminderSettings": {
        "enabled": true,
        "time": "08:00",
        "days": ["monday", "wednesday", "friday"]
      },
      "xpReward": 10,
      "lastCompletedAt": "2024-01-01T08:00:00.000Z",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T08:00:00.000Z"
    }
  ],
  "tasks": [
    {
      "id": "task_id",
      "title": "Complete project",
      "description": "Finish the important project",
      "completed": false,
      "completedAt": null,
      "priority": "high",
      "dueDate": "2024-01-10T00:00:00.000Z",
      "reminderSettings": {
        "enabled": true,
        "time": "09:00"
      },
      "notifiedOverdue": false,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "exportedAt": "2024-01-03T12:00:00.000Z"
}
```

**Notes:**
- Sensitive information (passwords) is excluded from the export
- All user-related data (habits, tasks) is included
- The export includes a timestamp of when the data was exported

## Implementation Details

### Files Modified/Created

1. **User Entity** (`src/users/entities/user.entity.ts`)
   - Added `@DeleteDateColumn() deletedAt: Date`

2. **Users Service** (`src/users/users.service.ts`)
   - Added `softDeleteAccount()` method
   - Added `exportUserData()` method
   - Added `exportUserDataAsPDF()` method
   - Updated all find methods to exclude soft-deleted users

3. **Users Controller** (`src/users/users.controller.ts`)
   - Added `DELETE /users/account` endpoint
   - Added `GET /users/data/export` endpoint
   - Added `GET /users/data/export/pdf` endpoint

4. **DTOs Created**
   - `DeleteAccountDto` - Request body for account deletion
   - `UserDataExportDto` - Response structure for data export

5. **Dependencies Added**
   - `pdfkit` - PDF generation library
   - `@types/pdfkit` - TypeScript definitions

### Security Considerations

- Password verification for local accounts before deletion
- JWT authentication required for both endpoints
- Soft delete preserves data integrity while making it inaccessible
- Data export excludes sensitive information

### GDPR Compliance

The data export endpoint provides users with:
- Complete profile information
- All habits and their completion history
- All tasks and their status
- Timestamps for all data creation and modification

This satisfies GDPR Article 20 (Right to data portability) requirements.

## Database Migration Required

You'll need to run a database migration to add the `deletedAt` column to the users table:

```sql
ALTER TABLE users ADD COLUMN deletedAt TIMESTAMP NULL;
```

## Testing

The implementation includes comprehensive test coverage for:
- Account deletion with password verification
- Account deletion for social login users
- Data export functionality
- Error handling for invalid scenarios

## Usage Examples

### Frontend Integration

```typescript
// Delete account
const deleteAccount = async (password: string, reason?: string) => {
  const response = await fetch('/users/account', {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ password, reason })
  });
  return response.json();
};

// Export data as JSON
const exportUserData = async () => {
  const response = await fetch('/users/data/export', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.json();
};

// Export data as PDF
const exportUserDataAsPDF = async () => {
  const response = await fetch('/users/data/export/pdf', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  if (response.ok) {
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'user-data-export.pdf';
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }
};
```

Both endpoints are fully integrated with Swagger/OpenAPI documentation and include proper error handling and validation.
