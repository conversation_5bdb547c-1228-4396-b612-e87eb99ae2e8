# Firebase Webhooks Integration

This document outlines the Firebase webhooks integration for the Power-Up API, which allows real-time synchronization between Firebase and your application database.

## Overview

Firebase webhooks provide a way to trigger actions in your API based on events that occur in Firebase services like Authentication, Firestore, and Cloud Functions. The Power-Up API has implemented handlers for these webhook events.

## Authentication Webhooks

### Setup

To enable Firebase Authentication webhooks, you need to:

1. Create a Firebase Cloud Function that listens to Auth triggers and forwards them to your API
2. Deploy the function to your Firebase project

### Example Cloud Function

Create a file called `functions/index.js` in your Firebase project:

```javascript
const functions = require('firebase-functions');
const admin = require('firebase-admin');
const fetch = require('node-fetch');

admin.initializeApp();

// Auth webhook for user creation
exports.onUserCreated = functions.auth.user().onCreate((user) => {
  const apiEndpoint = 'https://your-power-up-api.com/webhooks/firebase/auth';
  
  return fetch(apiEndpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.WEBHOOK_SECRET}`
    },
    body: JSON.stringify({
      event: {
        type: 'user.created',
        data: {
          uid: user.uid,
          email: user.email,
          metadata: {
            createdAt: user.metadata.creationTime
          }
        }
      }
    })
  })
  .then(response => response.json())
  .then(data => console.log('Success:', data))
  .catch(error => console.error('Error:', error));
});

// Auth webhook for user deletion
exports.onUserDeleted = functions.auth.user().onDelete((user) => {
  const apiEndpoint = 'https://your-power-up-api.com/webhooks/firebase/auth';
  
  return fetch(apiEndpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.WEBHOOK_SECRET}`
    },
    body: JSON.stringify({
      event: {
        type: 'user.deleted',
        data: {
          uid: user.uid
        }
      }
    })
  })
  .then(response => response.json())
  .then(data => console.log('Success:', data))
  .catch(error => console.error('Error:', error));
});
```

### Deploy the Cloud Functions

1. Install Firebase CLI if not already installed:
   ```bash
   npm install -g firebase-tools
   ```

2. Initialize Firebase Functions in your project:
   ```bash
   firebase init functions
   ```

3. Deploy the functions:
   ```bash
   firebase deploy --only functions
   ```

## API Webhook Endpoints

### Authentication Webhooks

**Endpoint:** `POST /webhooks/firebase/auth`

This endpoint handles authentication events like:
- `user.created` - When a new user signs up through Firebase Authentication
- `user.deleted` - When a user is deleted from Firebase Authentication

**Example Payload:**
```json
{
  "event": {
    "type": "user.created",
    "data": {
      "uid": "firebase-uid",
      "email": "<EMAIL>",
      "metadata": {
        "createdAt": "2023-05-20T12:00:00.000Z"
      }
    }
  }
}
```

**Response:**
```json
{
  "success": true
}
```

## Securing Webhooks

To secure your webhooks:

1. Generate a secret token and store it as an environment variable in both Firebase Functions and your API
2. Use this token in the Authorization header for all webhook requests
3. Verify the token in your webhook handler before processing the event

Example of verifying the token in your API:

```typescript
// In webhook controller
@Public()
@Post('auth')
@UseGuards(WebhookAuthGuard)
async handleAuthWebhook(@Body() payload: FirebaseAuthWebhookPayload) {
  // Process webhook
}
```

Create a `WebhookAuthGuard` that validates the secret token:

```typescript
@Injectable()
export class WebhookAuthGuard implements CanActivate {
  constructor(private configService: ConfigService) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return false;
    }
    
    const token = authHeader.split(' ')[1];
    return token === this.configService.get<string>('webhook.secret');
  }
}
```

## Testing Webhooks Locally

For local development and testing:

1. Use a tool like [ngrok](https://ngrok.com/) to create a secure tunnel to your local API
2. Update your Firebase Functions to point to your ngrok URL
3. Use Firebase Local Emulator Suite to test authentication events

Example:
```bash
# Start ngrok to expose your local API
ngrok http 3000

# Run Firebase emulators
firebase emulators:start
```

## Troubleshooting

Common issues:

1. **Webhook not receiving events:**
   - Check Firebase Functions logs for errors
   - Verify your API endpoint is accessible from the internet
   - Check for network issues or firewall restrictions

2. **Authentication failures:**
   - Verify the webhook secret token matches in both environments
   - Check Authorization header format in the request

3. **Event processing errors:**
   - Check your API logs for detailed error information
   - Verify that the payload format matches what your API expects
